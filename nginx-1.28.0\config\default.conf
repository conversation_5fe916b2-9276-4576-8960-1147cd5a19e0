# nginx 1.28.0 默认站点配置
# 包含HTTP和HTTPS配置示例

server {
    listen 80;
    listen [::]:80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html index.htm;

    # 访问日志
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;

    # 主页面
    location / {
        try_files $uri $uri/ =404;
    }

    # 静态文件缓存 (disabled - PCRE not available)
    # location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    #     expires 1y;
    #     add_header Cache-Control "public, immutable";
    # }

    # 禁止访问隐藏文件 (disabled - PCRE not available)
    # location ~ /\. {
    #     deny all;
    #     access_log off;
    #     log_not_found off;
    # }

    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}

# HTTPS配置示例（需要SSL证书）
# server {
#     listen 443 ssl http2;
#     listen [::]:443 ssl http2;
#     server_name localhost;
#     root /usr/share/nginx/html;
#     index index.html index.htm;
#
#     # SSL证书配置
#     ssl_certificate /etc/nginx/ssl/nginx.crt;
#     ssl_certificate_key /etc/nginx/ssl/nginx.key;
#
#     # SSL安全配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#
#     # HSTS
#     add_header Strict-Transport-Security "max-age=31536000" always;
#
#     location / {
#         try_files $uri $uri/ =404;
#     }
# }
