# PostgreSQL 11.19 Docker - 离线版本

完全离线构建的PostgreSQL 11.19 Docker镜像，基于CentOS 7和预下载的RPM包。

## 🚀 快速开始

### 方式一：Docker Compose（推荐）
```bash
# 一键启动PostgreSQL + pgAdmin（离线版本）
docker-compose up -d
```

### 方式二：直接Docker命令
```bash
# 1. 构建镜像（标准离线版本）
docker build -t postgresql:11.19-centos7-offline .

# 2. 启动容器
docker run -d --name postgresql-11.19-offline \
  -p 3433:3433 \
  -e PG_PASSWORD=postgres \
  postgresql:11.19-centos7-offline

# 3. 连接数据库
docker exec -it postgresql-11.19-offline psql -U postgres -d postgres -p 3433
```

## 📋 连接信息

### PostgreSQL数据库
- **主机**: localhost
- **端口**: 3433
- **用户名**: postgres
- **密码**: postgres
- **数据库**: postgres

### pgAdmin Web管理界面（仅Docker Compose）
- **地址**: http://localhost:8080
- **邮箱**: <EMAIL>
- **密码**: admin123

## 📁 文件说明

- `Dockerfile` - 标准离线构建文件
- `Dockerfile.offline` - 完全离线构建文件（更健壮）
- `docker-compose.yml` - Docker Compose配置
- `docker-entrypoint.sh` - 容器启动脚本
- `postgresql.conf` - PostgreSQL主配置文件
- `pg_hba.conf` - 认证配置文件
- `postgresql-11.19.tar.gz` - PostgreSQL源码包
- `rpm-packages/` - 离线RPM包目录
- `init-scripts/` - 数据库初始化脚本目录
- `download-missing-rpms.sh` - 下载缺失RPM包的脚本

## 🔧 构建选项

### 标准离线构建
```bash
docker build -t postgresql:11.19-centos7-offline .
```

### 完全离线构建（推荐用于完全隔离环境）
```bash
docker build -f Dockerfile.offline -t postgresql:11.19-centos7-offline .
```

## 🐳 使用Docker Compose

```bash
# 启动服务（PostgreSQL + pgAdmin）
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs postgresql

# 重新构建镜像
docker-compose build

# 完全清理（包括数据卷）
docker-compose down -v
```

## 💾 数据持久化

### 使用数据卷（推荐）
```bash
# 完整挂载启动容器
docker run -d --name postgresql-11.19-offline \
  -p 3433:3433 \
  -e PG_PASSWORD=postgres \
  -v postgresql-data-offline:/var/lib/postgresql/data \
  -v postgresql-logs-offline:/var/log/postgresql \
  -v ${PWD}/init-scripts:/docker-entrypoint-initdb.d \
  -v ${PWD}/backups:/var/backups/postgresql \
  postgresql:11.19-centos7-offline
```

## 🔒 数据安全说明

### Windows系统重启后的数据保护
- **✅ 数据安全**: Docker数据卷独立于容器，系统重启不会影响数据
- **✅ 智能启动**: PostgreSQL会检测现有数据，跳过重复初始化
- **✅ 自动恢复**: 重启后直接启动，无需手动干预

```bash
# 验证数据完整性
docker exec postgresql-11.19-offline psql -U postgres -d sample_db -p 3433 -c "SELECT COUNT(*) FROM users;"

# 查看启动日志（应显示"跳过初始化"）
docker-compose logs postgresql
```

## 🔍 故障排除

### 构建失败 - 缺少编译器
```bash
# 检查是否缺少关键RPM包
./download-missing-rpms.sh

# 使用完全离线版本构建
docker build -f Dockerfile.offline -t postgresql:11.19-centos7-offline .
```

### 端口被占用
```bash
# 检查端口占用
netstat -an | findstr 3433

# 使用其他端口
docker run -d --name postgresql-11.19-offline -p 5432:3433 -e PG_PASSWORD=postgres postgresql:11.19-centos7-offline
```

### 容器启动失败
```bash
# 查看日志
docker logs postgresql-11.19-offline

# 检查容器状态
docker ps -a

# 重新启动
docker restart postgresql-11.19-offline
```

### RPM包依赖问题
```bash
# 检查当前RPM包
ls -la rpm-packages/ | grep -E "(gcc|make|glibc)"

# 如果缺少关键包，运行下载脚本
./download-missing-rpms.sh

# 或手动添加缺失的RPM包到 rpm-packages/ 目录
```

## 📊 版本信息

- **PostgreSQL版本**: 11.19
- **基础镜像**: CentOS 7
- **构建方式**: 完全离线（基于预下载RPM包）
- **端口**: 3433

## 🎯 离线环境优势

1. **🔒 完全隔离**: 无需网络连接，适合安全环境
2. **📦 预打包**: 所有依赖已预下载
3. **🚀 快速部署**: 无需等待在线下载
4. **🛡️ 版本锁定**: 避免在线包版本变化导致的问题
5. **💾 可重复**: 相同环境可重复构建

## 📖 详细文档

如需更详细的使用指导，请参考在线版本的 `使用指导.html` 文档，操作方式基本相同。
