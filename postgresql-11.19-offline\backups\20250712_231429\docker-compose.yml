services:
  postgresql:
    build:
      context: .
      dockerfile: Dockerfile
    image: postgresql:11.19
    container_name: postgresql-11.19
    restart: unless-stopped
    
    # 环境变量配置
    environment:
      - PG_USER=postgres
      - PG_PASSWORD=postgres
      - PG_DB=postgres
      - PG_PORT=3433
      - PGDATA=/var/lib/postgresql/data
    
    # 端口映射
    ports:
      - "3433:3433"
    
    # 数据卷挂载
    volumes:
      - postgres_data_offline:/var/lib/postgresql/data
      - postgres_logs_offline:/var/log/postgresql
      - ./init-scripts:/docker-entrypoint-initdb.d
      - ./backups:/var/backups/postgresql
    
    # 健康检查
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -p 3433"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    
    # 网络配置
    networks:
      - postgres_network_offline

  # PostgreSQL管理工具 - pgAdmin (可选)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pgadmin4
    restart: unless-stopped
    
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=<EMAIL>
      - PGADMIN_LISTEN_PORT=80
    
    ports:
      - "8080:80"
    
    volumes:
      - pgadmin_data_offline:/var/lib/pgadmin
    
    networks:
      - postgres_network_offline
    
    depends_on:
      postgresql:
        condition: service_healthy

# 数据卷定义
volumes:
  postgres_data_offline:
    driver: local
    name: postgresql_11_19_data_offline
  postgres_logs_offline:
    driver: local
    name: postgresql_11_19_logs_offline
  pgadmin_data_offline:
    driver: local
    name: pgadmin_data_offline

# 网络定义
networks:
  postgres_network_offline:
    driver: bridge
    name: postgresql_network_offline
