﻿[2025-07-12 22:06:44] [INFO] 开始PostgreSQL完整恢复
[2025-07-12 22:06:44] [INFO] 备份路径: D:\Code\MicrosoftCode\postgresql-11.19-online\backups\20250712_220556
[2025-07-12 22:06:44] [INFO] 临时目录: D:\Code\MicrosoftCode\postgresql-11.19-online\temp_restore_20250712_220644
[2025-07-12 22:06:44] [SUCCESS] 临时目录创建成功
[2025-07-12 22:06:44] [INFO] 检查Docker环境...
[2025-07-12 22:06:44] [SUCCESS] Docker版本: Docker version 28.0.4, build b8034c0
[2025-07-12 22:06:44] [INFO] 验证备份文件...
[2025-07-12 22:06:44] [SUCCESS] 找到备份文件: postgres_data_volume.zip (6.03MB)
[2025-07-12 22:06:44] [SUCCESS] 找到备份文件: postgres_logs_volume.zip (0MB)
[2025-07-12 22:06:44] [INFO] 检测容器用户ID...
[2025-07-12 22:06:45] [SUCCESS] PostgreSQL用户ID: 1000
[2025-07-12 22:06:45] [INFO] 停止现有服务...
[2025-07-12 22:06:54] [SUCCESS] 现有服务已停止
[2025-07-12 22:06:54] [INFO] 删除现有数据卷...
[2025-07-12 22:06:54] [SUCCESS] 已删除数据卷: postgresql_11_19_data_online
[2025-07-12 22:06:54] [SUCCESS] 已删除数据卷: postgresql_11_19_logs_online
[2025-07-12 22:06:54] [SUCCESS] 已删除数据卷: pgadmin_data_online
[2025-07-12 22:06:55] [INFO] 恢复配置文件...
[2025-07-12 22:06:55] [SUCCESS] 已恢复: docker-compose.yml
[2025-07-12 22:06:55] [SUCCESS] 已恢复: Dockerfile
[2025-07-12 22:06:55] [SUCCESS] 已恢复: postgresql.conf
[2025-07-12 22:06:55] [SUCCESS] 已恢复: pg_hba.conf
[2025-07-12 22:06:55] [SUCCESS] 已恢复: docker-entrypoint.sh
[2025-07-12 22:06:55] [SUCCESS] 已恢复: init-scripts目录
[2025-07-12 22:06:55] [SUCCESS] 配置文件恢复完成，已恢复 6 个文件/目录
[2025-07-12 22:06:55] [INFO] 创建新数据卷...
[2025-07-12 22:06:55] [SUCCESS] 已创建数据卷: postgresql_11_19_data_online
[2025-07-12 22:06:55] [SUCCESS] 已创建数据卷: postgresql_11_19_logs_online
[2025-07-12 22:06:55] [SUCCESS] 已创建数据卷: pgadmin_data_online
[2025-07-12 22:06:55] [INFO] 恢复PostgreSQL主数据卷...
[2025-07-12 22:07:11] [INFO] 修复数据目录权限...
[2025-07-12 22:07:12] [SUCCESS] PostgreSQL主数据卷恢复完成
[2025-07-12 22:07:13] [INFO] 恢复PostgreSQL日志卷...
[2025-07-12 22:07:13] [INFO] 修复日志目录权限...
[2025-07-12 22:07:14] [SUCCESS] PostgreSQL日志卷恢复完成
[2025-07-12 22:07:14] [INFO] 恢复pgAdmin数据卷...
[2025-07-12 22:07:14] [INFO] 修复pgAdmin数据目录权限...
[2025-07-12 22:07:15] [SUCCESS] pgAdmin数据卷恢复完成，权限已修复
[2025-07-12 22:07:15] [INFO] 启动PostgreSQL服务...
[2025-07-12 22:07:22] [INFO] 等待PostgreSQL启动...
[2025-07-12 22:07:27] [INFO] 检查PostgreSQL状态 (尝试 1/20)...
[2025-07-12 22:07:27] [SUCCESS] PostgreSQL服务启动成功
[2025-07-12 22:07:27] [INFO] 等待pgAdmin启动...
[2025-07-12 22:07:35] [INFO] 检查pgAdmin状态 (尝试 1/15)...
[2025-07-12 22:07:49] [INFO] 检查pgAdmin状态 (尝试 2/15)...
[2025-07-12 22:08:02] [INFO] 检查pgAdmin状态 (尝试 3/15)...
[2025-07-12 22:08:15] [INFO] 检查pgAdmin状态 (尝试 4/15)...
[2025-07-12 22:08:28] [INFO] 检查pgAdmin状态 (尝试 5/15)...
[2025-07-12 22:08:41] [INFO] 检查pgAdmin状态 (尝试 6/15)...
[2025-07-12 22:08:54] [INFO] 检查pgAdmin状态 (尝试 7/15)...
[2025-07-12 22:09:07] [INFO] 检查pgAdmin状态 (尝试 8/15)...
[2025-07-12 22:09:20] [INFO] 检查pgAdmin状态 (尝试 9/15)...
[2025-07-12 22:09:33] [INFO] 检查pgAdmin状态 (尝试 10/15)...
[2025-07-12 22:09:47] [INFO] 检查pgAdmin状态 (尝试 11/15)...
[2025-07-12 22:10:00] [INFO] 检查pgAdmin状态 (尝试 12/15)...
[2025-07-12 22:10:13] [INFO] 检查pgAdmin状态 (尝试 13/15)...
[2025-07-12 22:10:26] [INFO] 检查pgAdmin状态 (尝试 14/15)...
[2025-07-12 22:10:39] [INFO] 检查pgAdmin状态 (尝试 15/15)...
[2025-07-12 22:10:44] [WARN] pgAdmin启动可能有问题，请检查容器日志: docker logs pgadmin4
[2025-07-12 22:10:44] [INFO] 恢复SQL数据...
[2025-07-12 22:10:44] [INFO] 从以下文件导入SQL数据: postgresql_all_databases.sql
[2025-07-12 22:10:45] [WARN] SQL数据导入完成，但验证失败
[2025-07-12 22:10:45] [INFO] 验证恢复结果...
[2025-07-12 22:10:45] [SUCCESS] PostgreSQL容器状态: 运行中
[2025-07-12 22:10:45] [SUCCESS] PostgreSQL数据库连接: 正常
[2025-07-12 22:10:45] [SUCCESS] PostgreSQL服务恢复成功
[2025-07-12 22:10:45] [SUCCESS] pgAdmin容器状态: 运行中
[2025-07-12 22:10:55] [WARN] pgAdmin Web界面: 无法访问 - 操作超时。
[2025-07-12 22:10:55] [INFO] 建议检查pgAdmin容器日志: docker logs pgadmin4
[2025-07-12 22:10:55] [INFO] 已清理临时目录
[2025-07-12 22:10:55] [SUCCESS] 恢复成功完成
[2025-07-12 22:10:55] [SUCCESS] PostgreSQL服务可在以下地址访问: localhost:3433
[2025-07-12 22:10:55] [SUCCESS] 用户名: postgres, 密码: postgres
[2025-07-12 22:10:55] [SUCCESS] pgAdmin: http://localhost:8080
