<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kubernetes进阶学习路径 - 从入门到精通</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --advanced-color: #9f7aea;
            --expert-color: #38b2ac;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 12px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 欢迎框样式 */
        .welcome-box {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .welcome-box::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 8s ease-in-out infinite;
        }

        .welcome-box h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .welcome-box p {
            font-size: 16px;
            line-height: 1.8;
            color: var(--text-primary);
            position: relative;
            z-index: 1;
        }

        /* 学习路径样式 */
        .learning-path {
            margin-top: 30px;
            position: relative;
            z-index: 1;
        }

        .learning-path h4 {
            color: var(--secondary-color);
            margin-bottom: 25px;
        }

        .path-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .path-step {
            background: var(--light-surface);
            border-radius: 15px;
            padding: 25px;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .path-step:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .step-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 20px;
            margin-bottom: 15px;
            box-shadow: var(--shadow-md);
        }

        .step-content h5 {
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .step-content p {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.6;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box,
        .advanced-box,
        .expert-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover,
        .advanced-box:hover,
        .expert-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        .advanced-box {
            background: linear-gradient(135deg, rgba(159, 122, 234, 0.1) 0%, rgba(159, 122, 234, 0.05) 100%);
            border-left-color: var(--advanced-color);
            color: #553c9a;
        }

        .expert-box {
            background: linear-gradient(135deg, rgba(56, 178, 172, 0.1) 0%, rgba(56, 178, 172, 0.05) 100%);
            border-left-color: var(--expert-color);
            color: #234e52;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: var(--light-surface);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        th,
        td {
            padding: 18px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 技能等级标签 */
        .skill-level {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin: 4px;
            box-shadow: var(--shadow-sm);
        }

        .level-beginner {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .level-intermediate {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
        }

        .level-advanced {
            background: linear-gradient(135deg, #9f7aea 0%, #805ad5 100%);
            color: white;
        }

        .level-expert {
            background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
            color: white;
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-decoration: none;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            z-index: 999;
            font-size: 20px;
        }

        .back-to-top:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .content-wrapper {
                padding: 30px;
            }

            .container {
                padding: 40px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
                border-radius: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 16px;
                margin-right: 15px;
            }

            .path-steps {
                grid-template-columns: 1fr;
            }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
        }
    </style>
</head>

<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-rocket"></i> K8s进阶路径</h2>
            <p>从入门到精通的学习指南</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#learning-assessment"><i class="fas fa-clipboard-check"></i>1. 学习评估</a></li>
                <li><a href="#intermediate-concepts"><i class="fas fa-layer-group"></i>2. 中级概念</a></li>
                <li><a href="#advanced-workloads"><i class="fas fa-cogs"></i>3. 高级工作负载</a></li>
                <li><a href="#networking-deep"><i class="fas fa-network-wired"></i>4. 网络深入</a></li>
                <li><a href="#storage-advanced"><i class="fas fa-database"></i>5. 存储进阶</a></li>
                <li><a href="#security-rbac"><i class="fas fa-shield-alt"></i>6. 安全与RBAC</a></li>
                <li><a href="#monitoring-logging"><i class="fas fa-chart-line"></i>7. 监控与日志</a></li>
                <li><a href="#cicd-gitops"><i class="fas fa-code-branch"></i>8. CI/CD与GitOps</a></li>
                <li><a href="#cluster-management"><i class="fas fa-server"></i>9. 集群管理</a></li>
                <li><a href="#ecosystem-tools"><i class="fas fa-toolbox"></i>10. 生态工具</a></li>
                <li><a href="#production-practices"><i class="fas fa-industry"></i>11. 生产实践</a></li>
                <li><a href="#career-path"><i class="fas fa-graduation-cap"></i>12. 职业发展</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-rocket"></i> Kubernetes进阶学习路径</h1>

                <div class="welcome-box">
                    <h3><i class="fas fa-star"></i> 恭喜你完成了K8s入门！</h3>
                    <p>既然你已经掌握了Kubernetes的基础知识，能够部署简单的应用，那么是时候踏上进阶之路了！这份指南将带你从K8s初学者成长为专业的云原生工程师。</p>

                    <div class="learning-path">
                        <h4><i class="fas fa-map-marked-alt"></i> 进阶学习路线图</h4>
                        <div class="path-steps">
                            <div class="path-step">
                                <div class="step-icon">🎯</div>
                                <div class="step-content">
                                    <h5>评估现状</h5>
                                    <p>了解自己的水平，制定学习计划</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">📚</div>
                                <div class="step-content">
                                    <h5>深化理论</h5>
                                    <p>学习高级概念和工作负载类型</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">🛠️</div>
                                <div class="step-content">
                                    <h5>实践技能</h5>
                                    <p>掌握网络、存储、安全等核心技能</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">🚀</div>
                                <div class="step-content">
                                    <h5>生产应用</h5>
                                    <p>学习生产环境的最佳实践</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <section id="learning-assessment">
                    <h2><span class="step-number">1</span>学习评估与规划</h2>

                    <div class="advanced-box">
                        <h3><i class="fas fa-clipboard-check"></i> 首先，让我们评估一下你的现状</h3>
                        <p>在开始进阶学习之前，我们需要确认你已经掌握了基础知识，并了解你的学习目标。这样可以制定更适合你的学习路径。</p>
                    </div>

                    <h3><i class="fas fa-check-circle"></i> 基础技能检查清单</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-list-check"></i> 请诚实地评估自己是否掌握以下技能：</h4>
                        <table>
                            <tr>
                                <th><i class="fas fa-tasks"></i> 技能项目</th>
                                <th><i class="fas fa-star"></i> 掌握程度</th>
                                <th><i class="fas fa-info-circle"></i> 验证方法</th>
                            </tr>
                            <tr>
                                <td>理解Pod、Deployment、Service概念</td>
                                <td><span class="level-beginner">必须掌握</span></td>
                                <td>能用自己的话解释这些概念</td>
                            </tr>
                            <tr>
                                <td>使用kubectl基本命令</td>
                                <td><span class="level-beginner">必须掌握</span></td>
                                <td>能独立部署nginx应用</td>
                            </tr>
                            <tr>
                                <td>编写基本的YAML文件</td>
                                <td><span class="level-beginner">必须掌握</span></td>
                                <td>能创建Deployment和Service的YAML</td>
                            </tr>
                            <tr>
                                <td>理解Label和Selector</td>
                                <td><span class="level-beginner">必须掌握</span></td>
                                <td>知道如何用标签选择Pod</td>
                            </tr>
                            <tr>
                                <td>基本的故障排查</td>
                                <td><span class="level-intermediate">建议掌握</span></td>
                                <td>能查看Pod日志和状态</td>
                            </tr>
                        </table>
                    </div>

                    <div class="warning-box">
                        <h4><i class="fas fa-exclamation-triangle"></i> 如果基础技能还不够扎实</h4>
                        <p>如果上面的技能你还没有完全掌握，建议先回到入门教程巩固基础：</p>
                        <ul>
                            <li>重新练习部署应用的完整流程</li>
                            <li>多写几个不同的YAML文件</li>
                            <li>尝试排查一些简单的问题</li>
                            <li>确保能够独立完成基本操作</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-target"></i> 学习目标设定</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-bullseye"></i> 选择你的学习目标（可多选）：</h4>
                        <div class="path-steps">
                            <div class="path-step">
                                <div class="step-icon">🏢</div>
                                <div class="step-content">
                                    <h5>企业应用开发</h5>
                                    <p>在公司项目中使用K8s部署应用</p>
                                    <span class="level-intermediate">中级目标</span>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">🔧</div>
                                <div class="step-content">
                                    <h5>运维工程师</h5>
                                    <p>管理和维护K8s集群</p>
                                    <span class="level-advanced">高级目标</span>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">🏗️</div>
                                <div class="step-content">
                                    <h5>架构师</h5>
                                    <p>设计云原生架构方案</p>
                                    <span class="level-expert">专家目标</span>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">📜</div>
                                <div class="step-content">
                                    <h5>认证考试</h5>
                                    <p>通过CKA/CKAD/CKS认证</p>
                                    <span class="level-advanced">高级目标</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h3><i class="fas fa-clock"></i> 学习时间规划</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-calendar-alt"></i> 根据你的目标，这里是建议的学习时间：</h4>
                        <table>
                            <tr>
                                <th><i class="fas fa-target"></i> 学习目标</th>
                                <th><i class="fas fa-clock"></i> 建议时间</th>
                                <th><i class="fas fa-route"></i> 学习重点</th>
                            </tr>
                            <tr>
                                <td>企业应用开发</td>
                                <td>2-3个月</td>
                                <td>工作负载、配置管理、CI/CD</td>
                            </tr>
                            <tr>
                                <td>运维工程师</td>
                                <td>4-6个月</td>
                                <td>集群管理、监控、安全、故障排查</td>
                            </tr>
                            <tr>
                                <td>架构师</td>
                                <td>6-12个月</td>
                                <td>全栈技能、生态工具、最佳实践</td>
                            </tr>
                            <tr>
                                <td>认证考试</td>
                                <td>3-4个月</td>
                                <td>考试大纲、实战练习、模拟考试</td>
                            </tr>
                        </table>
                    </div>

                    <h3><i class="fas fa-map-marked-alt"></i> 个性化学习路径</h3>
                    <div class="advanced-box">
                        <h4><i class="fas fa-route"></i> 根据你的目标选择学习路径：</h4>

                        <h5><i class="fas fa-code"></i> 路径A：应用开发者</h5>
                        <p><strong>适合：</strong>主要关注应用部署和管理的开发者</p>
                        <ol>
                            <li>中级概念（重点：工作负载类型）</li>
                            <li>配置与密钥管理</li>
                            <li>CI/CD与GitOps</li>
                            <li>监控与日志</li>
                            <li>生产实践</li>
                        </ol>

                        <h5><i class="fas fa-server"></i> 路径B：运维工程师</h5>
                        <p><strong>适合：</strong>负责集群管理和维护的运维人员</p>
                        <ol>
                            <li>网络深入学习</li>
                            <li>存储进阶</li>
                            <li>安全与RBAC</li>
                            <li>集群管理</li>
                            <li>监控与日志</li>
                            <li>生产实践</li>
                        </ol>

                        <h5><i class="fas fa-graduation-cap"></i> 路径C：全栈学习</h5>
                        <p><strong>适合：</strong>想要全面掌握K8s的学习者</p>
                        <ol>
                            <li>按章节顺序学习所有内容</li>
                            <li>每个章节都要动手实践</li>
                            <li>完成综合项目</li>
                            <li>参与开源项目</li>
                        </ol>
                    </div>

                    <h3><i class="fas fa-tools"></i> 学习环境准备</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-laptop-code"></i> 进阶学习需要更好的环境：</h4>
                        <ul>
                            <li><strong>本地集群：</strong>minikube或Docker Desktop（入门够用）</li>
                            <li><strong>多节点集群：</strong>使用kubeadm搭建或云厂商托管服务</li>
                            <li><strong>云环境：</strong>AWS EKS、Azure AKS、Google GKE等</li>
                            <li><strong>学习资源：</strong>准备一些测试应用和数据</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-rocket"></i> 准备好了吗？</h4>
                        <p>如果你已经：</p>
                        <ul>
                            <li>✅ 确认掌握了基础技能</li>
                            <li>✅ 明确了学习目标</li>
                            <li>✅ 制定了时间规划</li>
                            <li>✅ 选择了学习路径</li>
                            <li>✅ 准备好了学习环境</li>
                        </ul>
                        <p><strong>那么，让我们开始进阶学习之旅吧！🚀</strong></p>
                    </div>
                </section>

                <section id="intermediate-concepts">
                    <h2><span class="step-number">2</span>中级概念深入</h2>

                    <div class="advanced-box">
                        <h3><i class="fas fa-layer-group"></i> 从基础到进阶的跨越</h3>
                        <p>现在你已经掌握了Pod、Deployment、Service等基础概念，是时候学习更多的Kubernetes资源类型了。这些资源将帮你应对更复杂的应用场景。</p>
                    </div>

                    <h3><i class="fas fa-cube"></i> 高级工作负载类型</h3>

                    <h4><i class="fas fa-database"></i> 1. StatefulSet（有状态应用）</h4>
                    <div class="info-box">
                        <h5><i class="fas fa-lightbulb"></i> 什么时候需要StatefulSet？</h5>
                        <p>想象你要部署一个数据库集群，每个数据库实例都需要：</p>
                        <ul>
                            <li><strong>稳定的网络标识：</strong>db-0、db-1、db-2（固定的名字）</li>
                            <li><strong>持久化存储：</strong>每个实例都有自己的数据目录</li>
                            <li><strong>有序部署：</strong>必须先启动db-0，再启动db-1</li>
                            <li><strong>有序删除：</strong>删除时也要按顺序进行</li>
                        </ul>
                        <p>这就是StatefulSet的用武之地！</p>
                    </div>

                    <pre><code># StatefulSet示例：部署MySQL集群
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mysql
spec:
  serviceName: mysql
  replicas: 3
  selector:
    matchLabels:
      app: mysql
  template:
    metadata:
      labels:
        app: mysql
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        env:
        - name: MYSQL_ROOT_PASSWORD
          value: "password"
        volumeMounts:
        - name: mysql-storage
          mountPath: /var/lib/mysql
  volumeClaimTemplates:
  - metadata:
      name: mysql-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi</code></pre>

                    <div class="warning-box">
                        <h5><i class="fas fa-exclamation-triangle"></i> StatefulSet vs Deployment</h5>
                        <ul>
                            <li><strong>Deployment：</strong>适合无状态应用（如Web服务器）</li>
                            <li><strong>StatefulSet：</strong>适合有状态应用（如数据库、消息队列）</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-server"></i> 2. DaemonSet（守护进程）</h4>
                    <div class="success-box">
                        <h5><i class="fas fa-lightbulb"></i> 什么时候需要DaemonSet？</h5>
                        <p>想象你需要在每个节点上都运行一个监控代理，就像每栋楼都要有保安一样：</p>
                        <ul>
                            <li><strong>日志收集：</strong>每个节点都要有日志收集器</li>
                            <li><strong>监控代理：</strong>每个节点都要有监控程序</li>
                            <li><strong>网络插件：</strong>每个节点都要有网络组件</li>
                            <li><strong>存储驱动：</strong>每个节点都要有存储插件</li>
                        </ul>
                    </div>

                    <pre><code># DaemonSet示例：部署日志收集器
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluentd
spec:
  selector:
    matchLabels:
      app: fluentd
  template:
    metadata:
      labels:
        app: fluentd
    spec:
      containers:
      - name: fluentd
        image: fluentd:v1.14
        volumeMounts:
        - name: varlog
          mountPath: /var/log
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
      volumes:
      - name: varlog
        hostPath:
          path: /var/log
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers</code></pre>

                    <h4><i class="fas fa-clock"></i> 3. Job和CronJob（任务调度）</h4>
                    <div class="info-box">
                        <h5><i class="fas fa-lightbulb"></i> 什么时候需要Job？</h5>
                        <ul>
                            <li><strong>Job：</strong>运行一次性任务（如数据备份、批处理）</li>
                            <li><strong>CronJob：</strong>定时运行任务（如每天凌晨的数据清理）</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-graduation-cap"></i> 实践建议</h4>
                        <p>现在你可以尝试：</p>
                        <ul>
                            <li>部署一个MySQL StatefulSet</li>
                            <li>创建一个监控DaemonSet</li>
                            <li>编写一个数据备份Job</li>
                            <li>设置一个定时清理CronJob</li>
                        </ul>
                    </div>
                </section>

                <section id="advanced-workloads">
                    <h2><span class="step-number">3</span>高级工作负载管理</h2>

                    <div class="expert-box">
                        <h3><i class="fas fa-cogs"></i> 深入工作负载管理</h3>
                        <p>掌握了基本的工作负载类型后，我们需要学习如何更好地管理它们，包括资源限制、健康检查、自动伸缩等高级特性。</p>
                    </div>

                    <h3><i class="fas fa-heartbeat"></i> 健康检查与探针</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-stethoscope"></i> 三种探针类型</h4>
                        <ul>
                            <li><strong>Liveness Probe：</strong>检查容器是否还活着</li>
                            <li><strong>Readiness Probe：</strong>检查容器是否准备好接收流量</li>
                            <li><strong>Startup Probe：</strong>检查容器是否已经启动完成</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-expand-arrows-alt"></i> 自动伸缩（HPA/VPA）</h3>
                    <div class="advanced-box">
                        <h4><i class="fas fa-magic"></i> 让应用自动适应负载</h4>
                        <ul>
                            <li><strong>HPA（水平伸缩）：</strong>根据CPU/内存使用率自动增减Pod数量</li>
                            <li><strong>VPA（垂直伸缩）：</strong>自动调整Pod的资源请求和限制</li>
                        </ul>
                    </div>
                </section>

                <section id="networking-deep">
                    <h2><span class="step-number">4</span>网络深入学习</h2>

                    <div class="expert-box">
                        <h3><i class="fas fa-network-wired"></i> Kubernetes网络的奥秘</h3>
                        <p>网络是Kubernetes最复杂的部分之一。理解网络原理对于运维工程师和架构师来说至关重要。</p>
                    </div>

                    <h3><i class="fas fa-route"></i> 网络模型与CNI</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-puzzle-piece"></i> 主流CNI插件对比</h4>
                        <ul>
                            <li><strong>Flannel：</strong>简单易用，适合入门</li>
                            <li><strong>Calico：</strong>功能强大，支持网络策略</li>
                            <li><strong>Cilium：</strong>基于eBPF，性能优秀</li>
                            <li><strong>Weave：</strong>自动发现，配置简单</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-shield-alt"></i> 网络策略与安全</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-lock"></i> 网络安全最佳实践</h4>
                        <ul>
                            <li>使用NetworkPolicy限制Pod间通信</li>
                            <li>实施微分段策略</li>
                            <li>监控网络流量</li>
                            <li>定期安全审计</li>
                        </ul>
                    </div>
                </section>

                <section id="storage-advanced">
                    <h2><span class="step-number">5</span>存储进阶</h2>

                    <div class="expert-box">
                        <h3><i class="fas fa-database"></i> 存储是有状态应用的基石</h3>
                        <p>深入理解Kubernetes存储系统，掌握PV、PVC、StorageClass等概念，学会为不同类型的应用选择合适的存储方案。</p>
                    </div>

                    <h3><i class="fas fa-hdd"></i> 存储类型与选择</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-list"></i> 存储方案对比</h4>
                        <ul>
                            <li><strong>本地存储：</strong>性能最好，但不支持迁移</li>
                            <li><strong>网络存储：</strong>支持迁移，但性能稍差</li>
                            <li><strong>云存储：</strong>弹性扩展，管理简单</li>
                            <li><strong>分布式存储：</strong>高可用，适合大规模部署</li>
                        </ul>
                    </div>
                </section>

                <section id="security-rbac">
                    <h2><span class="step-number">6</span>安全与RBAC</h2>

                    <div class="danger-box">
                        <h3><i class="fas fa-shield-alt"></i> 安全是生产环境的生命线</h3>
                        <p>学习Kubernetes的安全机制，包括RBAC、Pod安全策略、网络策略等，确保集群和应用的安全。</p>
                    </div>

                    <h3><i class="fas fa-users-cog"></i> RBAC权限管理</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-key"></i> 权限管理最佳实践</h4>
                        <ul>
                            <li>最小权限原则</li>
                            <li>角色分离</li>
                            <li>定期权限审计</li>
                            <li>使用ServiceAccount</li>
                        </ul>
                    </div>
                </section>

                <section id="monitoring-logging">
                    <h2><span class="step-number">7</span>监控与日志</h2>

                    <div class="info-box">
                        <h3><i class="fas fa-chart-line"></i> 可观测性三大支柱</h3>
                        <p>学习如何构建完整的可观测性体系，包括指标监控、日志收集、链路追踪。</p>
                    </div>

                    <h3><i class="fas fa-eye"></i> 监控体系建设</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-tools"></i> 推荐工具栈</h4>
                        <ul>
                            <li><strong>Prometheus + Grafana：</strong>指标监控</li>
                            <li><strong>ELK/EFK Stack：</strong>日志收集分析</li>
                            <li><strong>Jaeger/Zipkin：</strong>分布式追踪</li>
                            <li><strong>AlertManager：</strong>告警管理</li>
                        </ul>
                    </div>
                </section>

                <section id="cicd-gitops">
                    <h2><span class="step-number">8</span>CI/CD与GitOps</h2>

                    <div class="advanced-box">
                        <h3><i class="fas fa-code-branch"></i> 现代化的应用交付</h3>
                        <p>学习如何构建云原生的CI/CD流水线，实践GitOps理念，实现应用的自动化部署和管理。</p>
                    </div>

                    <h3><i class="fas fa-git-alt"></i> GitOps实践</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-rocket"></i> GitOps工具</h4>
                        <ul>
                            <li><strong>ArgoCD：</strong>声明式GitOps工具</li>
                            <li><strong>Flux：</strong>CNCF孵化项目</li>
                            <li><strong>Jenkins X：</strong>云原生CI/CD</li>
                            <li><strong>Tekton：</strong>Kubernetes原生CI/CD</li>
                        </ul>
                    </div>
                </section>

                <section id="cluster-management">
                    <h2><span class="step-number">9</span>集群管理</h2>

                    <div class="expert-box">
                        <h3><i class="fas fa-server"></i> 生产级集群运维</h3>
                        <p>学习如何管理生产环境的Kubernetes集群，包括集群升级、备份恢复、性能调优等。</p>
                    </div>

                    <h3><i class="fas fa-tools"></i> 集群运维要点</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-cogs"></i> 关键运维任务</h4>
                        <ul>
                            <li>集群版本升级</li>
                            <li>etcd备份与恢复</li>
                            <li>节点管理与维护</li>
                            <li>资源配额管理</li>
                            <li>性能监控与调优</li>
                        </ul>
                    </div>
                </section>

                <section id="ecosystem-tools">
                    <h2><span class="step-number">10</span>生态工具</h2>

                    <div class="info-box">
                        <h3><i class="fas fa-toolbox"></i> 丰富的生态系统</h3>
                        <p>Kubernetes拥有庞大的生态系统，学习这些工具能大大提高你的工作效率。</p>
                    </div>

                    <h3><i class="fas fa-puzzle-piece"></i> 必学工具</h3>
                    <div class="path-steps">
                        <div class="path-step">
                            <div class="step-icon">📦</div>
                            <div class="step-content">
                                <h5>Helm</h5>
                                <p>Kubernetes包管理器</p>
                                <span class="level-intermediate">中级</span>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon">🕸️</div>
                            <div class="step-content">
                                <h5>Istio</h5>
                                <p>服务网格解决方案</p>
                                <span class="level-advanced">高级</span>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon">🔍</div>
                            <div class="step-content">
                                <h5>Lens</h5>
                                <p>Kubernetes IDE</p>
                                <span class="level-beginner">入门</span>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon">🛡️</div>
                            <div class="step-content">
                                <h5>Falco</h5>
                                <p>运行时安全监控</p>
                                <span class="level-expert">专家</span>
                            </div>
                        </div>
                    </div>
                </section>

                <section id="production-practices">
                    <h2><span class="step-number">11</span>生产实践</h2>

                    <div class="success-box">
                        <h3><i class="fas fa-industry"></i> 生产环境最佳实践</h3>
                        <p>总结生产环境中的最佳实践，帮你避免常见的坑，构建稳定可靠的Kubernetes集群。</p>
                    </div>

                    <h3><i class="fas fa-star"></i> 黄金法则</h3>
                    <div class="expert-box">
                        <h4><i class="fas fa-crown"></i> 生产环境十大原则</h4>
                        <ol>
                            <li>始终设置资源请求和限制</li>
                            <li>为所有应用配置健康检查</li>
                            <li>使用多副本确保高可用</li>
                            <li>实施适当的安全策略</li>
                            <li>建立完善的监控告警</li>
                            <li>定期备份重要数据</li>
                            <li>制定灾难恢复计划</li>
                            <li>持续进行安全扫描</li>
                            <li>保持集群版本更新</li>
                            <li>建立变更管理流程</li>
                        </ol>
                    </div>
                </section>

                <section id="career-path">
                    <h2><span class="step-number">12</span>职业发展路径</h2>

                    <div class="success-box">
                        <h3><i class="fas fa-graduation-cap"></i> 你的Kubernetes职业之路</h3>
                        <p>完成了这个进阶学习路径，你已经具备了在企业中使用Kubernetes的能力。让我们看看接下来的职业发展方向。</p>
                    </div>

                    <h3><i class="fas fa-briefcase"></i> 职业方向</h3>
                    <div class="path-steps">
                        <div class="path-step">
                            <div class="step-icon">👨‍💻</div>
                            <div class="step-content">
                                <h5>云原生开发工程师</h5>
                                <p>专注于云原生应用开发</p>
                                <span class="level-intermediate">中级</span>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon">🔧</div>
                            <div class="step-content">
                                <h5>DevOps工程师</h5>
                                <p>负责CI/CD和基础设施</p>
                                <span class="level-advanced">高级</span>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon">🏗️</div>
                            <div class="step-content">
                                <h5>云架构师</h5>
                                <p>设计云原生架构方案</p>
                                <span class="level-expert">专家</span>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon">🎯</div>
                            <div class="step-content">
                                <h5>平台工程师</h5>
                                <p>构建开发者平台</p>
                                <span class="level-expert">专家</span>
                            </div>
                        </div>
                    </div>

                    <h3><i class="fas fa-certificate"></i> 认证考试</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-medal"></i> Kubernetes官方认证</h4>
                        <table>
                            <tr>
                                <th><i class="fas fa-award"></i> 认证</th>
                                <th><i class="fas fa-target"></i> 适合人群</th>
                                <th><i class="fas fa-clock"></i> 准备时间</th>
                            </tr>
                            <tr>
                                <td><strong>CKAD</strong></td>
                                <td>应用开发者</td>
                                <td>2-3个月</td>
                            </tr>
                            <tr>
                                <td><strong>CKA</strong></td>
                                <td>集群管理员</td>
                                <td>3-4个月</td>
                            </tr>
                            <tr>
                                <td><strong>CKS</strong></td>
                                <td>安全专家</td>
                                <td>4-6个月</td>
                            </tr>
                        </table>
                    </div>

                    <h3><i class="fas fa-rocket"></i> 继续学习建议</h3>
                    <div class="advanced-box">
                        <h4><i class="fas fa-map"></i> 下一步学习方向</h4>
                        <ul>
                            <li><strong>深入源码：</strong>阅读Kubernetes源码，理解实现原理</li>
                            <li><strong>参与社区：</strong>加入Kubernetes社区，贡献代码</li>
                            <li><strong>实战项目：</strong>在真实项目中应用所学知识</li>
                            <li><strong>技术分享：</strong>写博客、做演讲，分享经验</li>
                            <li><strong>持续学习：</strong>关注新技术，保持技术敏感度</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-heart"></i> 结语</h4>
                        <p>Kubernetes的学习之路永无止境，但每一步都充满收获。记住：</p>
                        <ul>
                            <li><strong>实践出真知：</strong>理论结合实践，在项目中成长</li>
                            <li><strong>保持好奇心：</strong>技术在不断发展，保持学习热情</li>
                            <li><strong>分享与交流：</strong>与同行交流，共同进步</li>
                            <li><strong>关注业务价值：</strong>技术服务于业务，创造实际价值</li>
                        </ul>
                        <p><strong>祝你在云原生的世界里发光发热！🌟</strong></p>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <a href="#" class="back-to-top" id="backToTop"><i class="fas fa-arrow-up"></i></a>

    <script>
        // 移动端菜单切换
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const sidebar = document.getElementById('sidebar');

        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', function () {
                sidebar.classList.toggle('active');
            });
        }

        // 侧边栏导航高亮
        function updateActiveNav() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.sidebar a');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                if (window.pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        }

        // 返回顶部功能
        function toggleBackToTop() {
            const backToTop = document.getElementById("backToTop");
            if (document.body.scrollTop > 300 || document.documentElement.scrollTop > 300) {
                backToTop.style.display = "flex";
            } else {
                backToTop.style.display = "none";
            }
        }

        // 滚动事件监听
        window.addEventListener('scroll', function () {
            updateActiveNav();
            toggleBackToTop();
        });

        // 返回顶部点击事件
        const backToTopBtn = document.getElementById("backToTop");
        if (backToTopBtn) {
            backToTopBtn.addEventListener('click', function (e) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // 平滑滚动
        document.querySelectorAll('.sidebar a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
                // 移动端关闭菜单
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                }
            });
        });

        // 点击外部关闭移动端菜单
        document.addEventListener('click', function (e) {
            if (window.innerWidth <= 768 && !sidebar.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                sidebar.classList.remove('active');
            }
        });

        // 初始化导航高亮
        updateActiveNav();

        // 页面加载动画
        window.addEventListener('load', function () {
            document.body.style.opacity = '1';
        });
    </script>
</body>

</html>