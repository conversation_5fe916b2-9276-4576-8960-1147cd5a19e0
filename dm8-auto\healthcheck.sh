#!/bin/bash

# 健康检查脚本
# 检查达梦数据库服务是否正常运行

# 环境变量
DM_HOME=${DM_HOME:-/home/<USER>/dmdbms}
DM_PORT=${DM_PORT:-5236}
DM_USER=${DM_USER:-SYSDBA}
DM_PWD=${DM_PWD:-GDYtd@2025}

# 设置环境变量
export DM_HOME
export PATH=$DM_HOME/bin:$PATH
export LD_LIBRARY_PATH=$DM_HOME/bin:$LD_LIBRARY_PATH

# 检查端口是否监听
check_port() {
    netstat -ln | grep ":$DM_PORT " > /dev/null 2>&1
    return $?
}

# 检查进程是否存在
check_process() {
    pgrep -f "dmserver" > /dev/null 2>&1
    return $?
}

# 检查数据库连接
check_database() {
    cd "$DM_HOME/bin"
    
    # 使用disql工具检查数据库连接
    echo "SELECT 1;" | ./disql "$DM_USER/$DM_PWD@localhost:$DM_PORT" > /dev/null 2>&1
    return $?
}

# 执行健康检查
main() {
    # 检查进程
    if ! check_process; then
        echo "UNHEALTHY: 达梦数据库进程未运行"
        exit 1
    fi
    
    # 检查端口
    if ! check_port; then
        echo "UNHEALTHY: 端口 $DM_PORT 未监听"
        exit 1
    fi
    
    # 检查数据库连接（可选，因为可能需要更长时间）
    # if ! check_database; then
    #     echo "UNHEALTHY: 无法连接到数据库"
    #     exit 1
    # fi
    
    echo "HEALTHY: 达梦数据库服务正常"
    exit 0
}

# 执行主函数
main
