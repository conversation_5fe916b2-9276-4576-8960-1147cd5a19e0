#!/bin/bash

# nginx 1.28.0 离线包打包脚本
# 用于将完整的项目打包，便于传输到离线环境

set -e

echo "================================================================================"
echo "                    nginx 1.28.0 离线包打包脚本"
echo "================================================================================"

# 检查必需文件
echo "=== 检查项目文件完整性 ==="

check_file() {
    local file=$1
    local description=$2
    
    if [ -f "$file" ]; then
        echo "✅ $description: $file"
        return 0
    else
        echo "❌ $description缺失: $file"
        return 1
    fi
}

check_dir() {
    local dir=$1
    local description=$2
    local min_files=$3
    
    if [ -d "$dir" ]; then
        local file_count=$(find "$dir" -type f | wc -l)
        if [ "$file_count" -ge "$min_files" ]; then
            echo "✅ $description: $dir ($file_count 个文件)"
            return 0
        else
            echo "❌ $description文件不足: $dir (需要至少 $min_files 个文件，实际 $file_count 个)"
            return 1
        fi
    else
        echo "❌ $description目录不存在: $dir"
        return 1
    fi
}

# 检查核心文件
MISSING_FILES=0

check_file "Dockerfile" "Docker构建文件" || MISSING_FILES=$((MISSING_FILES + 1))
check_file "README.md" "项目说明文档" || MISSING_FILES=$((MISSING_FILES + 1))
check_file "指导教程.html" "详细教程文档" || MISSING_FILES=$((MISSING_FILES + 1))
check_file "download-packages.sh" "包下载脚本" || MISSING_FILES=$((MISSING_FILES + 1))
check_file "build-offline.sh" "离线构建脚本" || MISSING_FILES=$((MISSING_FILES + 1))
check_file "test-nginx.sh" "测试脚本" || MISSING_FILES=$((MISSING_FILES + 1))
check_file "scripts/build-nginx.sh" "nginx编译脚本" || MISSING_FILES=$((MISSING_FILES + 1))
check_file "config/nginx.conf" "nginx配置文件" || MISSING_FILES=$((MISSING_FILES + 1))
check_file "config/default.conf" "默认站点配置" || MISSING_FILES=$((MISSING_FILES + 1))

# 检查包目录
check_dir "packages" "nginx源码包" 1 || MISSING_FILES=$((MISSING_FILES + 1))
check_dir "centos7-rpms" "RPM依赖包" 20 || MISSING_FILES=$((MISSING_FILES + 1))

if [ $MISSING_FILES -gt 0 ]; then
    echo ""
    echo "❌ 发现 $MISSING_FILES 个缺失文件/目录"
    echo "请确保所有必需文件都存在后再运行打包脚本"
    echo ""
    echo "如果缺少依赖包，请先运行："
    echo "  chmod +x download-packages.sh"
    echo "  ./download-packages.sh"
    exit 1
fi

echo ""
echo "✅ 所有必需文件检查通过"

# 显示文件统计
echo ""
echo "=== 文件统计信息 ==="
echo "nginx源码包:"
ls -lh packages/

echo ""
echo "RPM包统计:"
RPM_COUNT=$(find centos7-rpms -name "*.rpm" | wc -l)
RPM_SIZE=$(du -sh centos7-rpms | cut -f1)
echo "总计: $RPM_COUNT 个RPM包，大小: $RPM_SIZE"

echo ""
echo "项目总大小:"
du -sh . | cut -f1

# 创建打包信息文件
echo ""
echo "=== 创建打包信息 ==="

cat > PACKAGE_INFO.txt << EOF
nginx 1.28.0 离线构建包
=======================

打包时间: $(date)
打包主机: $(hostname)
操作系统: $(uname -a)

包含内容:
- nginx 1.28.0 源码包
- CentOS 7 RPM依赖包 ($RPM_COUNT 个)
- Docker构建文件
- 编译和配置脚本
- 测试脚本
- 详细使用教程

使用方法:
1. 解压: tar -xzf nginx-1.28.0-offline-build.tar.gz
2. 进入目录: cd nginx-1.28.0-offline-build
3. 构建镜像: ./build-offline.sh
4. 测试验证: ./test-nginx.sh

支持的功能:
- SSL/TLS支持
- HTTP/2支持
- 真实IP模块
- 认证请求模块
- 安全链接模块
- 状态监控模块
- 静态gzip模块
- 线程支持
- 文件异步IO

技术规格:
- 基础镜像: CentOS 7
- nginx版本: 1.28.0
- 编译器: GCC 4.8.5
- OpenSSL: 1.0.2k
- 构建方式: 完全离线

注意事项:
- 需要Docker环境
- 建议2GB以上内存
- 需要2GB以上磁盘空间
- 支持Linux/macOS/Windows

更多信息请查看README.md和指导教程.html
EOF

echo "✅ 打包信息文件已创建: PACKAGE_INFO.txt"

# 开始打包
echo ""
echo "=== 开始打包 ==="

PACKAGE_NAME="nginx-1.28.0-offline-build-$(date +%Y%m%d-%H%M%S).tar.gz"
TEMP_DIR="nginx-1.28.0-offline-build"

echo "打包文件名: $PACKAGE_NAME"

# 创建临时目录并复制文件
if [ -d "$TEMP_DIR" ]; then
    rm -rf "$TEMP_DIR"
fi

mkdir -p "$TEMP_DIR"

echo "复制项目文件..."

# 复制所有文件，排除临时文件和隐藏文件
cp -r \
    Dockerfile \
    README.md \
    指导教程.html \
    PACKAGE_INFO.txt \
    *.sh \
    packages/ \
    centos7-rpms/ \
    scripts/ \
    config/ \
    "$TEMP_DIR/"

# 设置脚本执行权限
chmod +x "$TEMP_DIR"/*.sh
chmod +x "$TEMP_DIR"/scripts/*.sh

echo "创建压缩包..."
tar -czf "$PACKAGE_NAME" "$TEMP_DIR"

# 清理临时目录
rm -rf "$TEMP_DIR"
rm -f PACKAGE_INFO.txt

# 显示结果
PACKAGE_SIZE=$(du -sh "$PACKAGE_NAME" | cut -f1)

echo ""
echo "================================================================================"
echo "                              打包完成！"
echo "================================================================================"
echo ""
echo "打包文件: $PACKAGE_NAME"
echo "文件大小: $PACKAGE_SIZE"
echo "创建时间: $(date)"
echo ""
echo "传输到离线环境后的使用方法:"
echo ""
echo "1. 解压文件:"
echo "   tar -xzf $PACKAGE_NAME"
echo ""
echo "2. 进入目录:"
echo "   cd nginx-1.28.0-offline-build"
echo ""
echo "3. 查看教程:"
echo "   # 在浏览器中打开 指导教程.html"
echo "   # 或查看 README.md"
echo ""
echo "4. 构建镜像:"
echo "   chmod +x build-offline.sh"
echo "   ./build-offline.sh"
echo ""
echo "5. 测试验证:"
echo "   chmod +x test-nginx.sh"
echo "   ./test-nginx.sh"
echo ""
echo "🎉 nginx 1.28.0 离线构建包打包完成！"
echo "================================================================================"
