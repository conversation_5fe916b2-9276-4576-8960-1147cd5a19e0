# PostgreSQL 11.19 Online Edition pgAdmin Permission Fix Script
# Version: 1.0
# Author: AI Assistant
# Date: 2025-07-12
#
# Usage:
#   .\fix-pgadmin-permissions-en.ps1
#   .\fix-pgadmin-permissions-en.ps1 -Restart

param(
    [switch]$Restart
)

# Logging function
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogContent = "[$Time] [$Level] $Message"

    switch ($Level) {
        "ERROR" { Write-Host $LogContent -ForegroundColor Red }
        "WARN"  { Write-Host $LogContent -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $LogContent -ForegroundColor Green }
        default { Write-Host $LogContent -ForegroundColor White }
    }
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "pgAdmin Permission Fix Script v1.0" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

try {
    # Check Docker environment
    Write-Log "Checking Docker environment..."
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker is not running or not installed"
    }
    Write-Log "Docker version: $dockerVersion" "SUCCESS"

    # Check pgAdmin container status
    Write-Log "Checking pgAdmin container status..."
    $PgAdminStatus = docker inspect pgadmin4 --format='{{.State.Status}}' 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Log "pgAdmin container status: $PgAdminStatus"
        
        if ($PgAdminStatus -eq "restarting") {
            Write-Log "Detected pgAdmin is restarting, this is usually a permission issue" "WARN"
        }
    } else {
        Write-Log "pgAdmin container does not exist" "WARN"
        exit 1
    }

    # Stop pgAdmin container
    if ($Restart -or $PgAdminStatus -eq "restarting") {
        Write-Log "Stopping pgAdmin container..."
        docker stop pgadmin4 2>&1 | Out-Null
        Start-Sleep -Seconds 3
    }

    # Fix pgAdmin data volume permissions
    Write-Log "Fixing pgAdmin data volume permissions..."
    docker run --rm -v pgadmin_data_online:/data alpine sh -c "
        # Create necessary directory structure
        mkdir -p /data/storage
        mkdir -p /data/sessions
        mkdir -p /data/azurecredentialcache
        
        # Set correct owner and permissions
        chown -R 5050:5050 /data 2>/dev/null || chown -R 5050:0 /data 2>/dev/null || true
        chmod -R 755 /data
        
        # Set specific file permissions
        find /data -name '*.db' -exec chmod 644 {} \; 2>/dev/null || true
        find /data -name '*.log' -exec chmod 644 {} \; 2>/dev/null || true
        find /data -name '*.json' -exec chmod 644 {} \; 2>/dev/null || true
        find /data -name '*.conf' -exec chmod 644 {} \; 2>/dev/null || true
        
        # Ensure critical directory permissions
        chmod 755 /data/storage 2>/dev/null || true
        chmod 755 /data/sessions 2>/dev/null || true
        chmod 755 /data/azurecredentialcache 2>/dev/null || true
        
        echo 'pgAdmin permission fix completed'
    " 2>&1 | Out-Null

    if ($LASTEXITCODE -eq 0) {
        Write-Log "pgAdmin data volume permission fix successful" "SUCCESS"
    } else {
        Write-Log "pgAdmin data volume permission fix failed" "ERROR"
    }

    # Restart pgAdmin container
    if ($Restart -or $PgAdminStatus -eq "restarting") {
        Write-Log "Starting pgAdmin container..."
        docker start pgadmin4 2>&1 | Out-Null
        
        # Wait for pgAdmin to start
        Write-Log "Waiting for pgAdmin to start..."
        $Count = 0
        $MaxRetries = 10
        $Started = $false

        do {
            Start-Sleep -Seconds 5
            $Count++
            Write-Log "Checking pgAdmin status (attempt $Count/$MaxRetries)..."

            $Status = docker inspect pgadmin4 --format='{{.State.Status}}' 2>$null
            if ($Status -eq "running") {
                Write-Log "pgAdmin container running normally" "SUCCESS"

                # Check HTTP response with reduced timeout
                try {
                    $Response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 3 -UseBasicParsing -ErrorAction Stop
                    if ($Response.StatusCode -eq 200) {
                        Write-Log "pgAdmin started successfully, Web interface accessible" "SUCCESS"
                        $Started = $true
                        break
                    }
                } catch {
                    # If container has been running for more than 30 seconds, consider it started
                    $ContainerStartTime = docker inspect pgadmin4 --format='{{.State.StartedAt}}' 2>$null
                    if ($ContainerStartTime) {
                        try {
                            $StartTime = [DateTime]::Parse($ContainerStartTime.Replace('T', ' ').Replace('Z', ''))
                            $RunningTime = (Get-Date).ToUniversalTime() - $StartTime
                            if ($RunningTime.TotalSeconds -gt 30) {
                                Write-Log "pgAdmin container has been running for over 30 seconds, considered started successfully" "SUCCESS"
                                $Started = $true
                                break
                            }
                        } catch {
                            # Continue checking
                        }
                    }
                    Write-Log "Web interface not responding yet, continuing to wait..." "INFO"
                }
            } elseif ($Status -eq "restarting") {
                Write-Log "pgAdmin is still restarting..." "WARN"
            }
        } while ($Count -lt $MaxRetries)

        if (-not $Started) {
            Write-Log "pgAdmin startup check timeout, but container may be running normally" "WARN"
            Write-Log "Please manually visit http://localhost:8080 to check status" "INFO"
        }
    }

    # Final status check
    Write-Log "Final status check..."
    $FinalStatus = docker inspect pgadmin4 --format='{{.State.Status}}' 2>$null
    Write-Log "pgAdmin container final status: $FinalStatus"
    
    if ($FinalStatus -eq "running") {
        Write-Log "pgAdmin permission fix completed, service running normally" "SUCCESS"
        Write-Log "pgAdmin access URL: http://localhost:8080" "SUCCESS"
    } else {
        Write-Log "pgAdmin may still have issues, recommend checking logs: docker logs pgadmin4" "WARN"
    }

} catch {
    Write-Log "Permission fix failed: $($_.Exception.Message)" "ERROR"
    exit 1
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "pgAdmin Permission Fix Script Completed" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
