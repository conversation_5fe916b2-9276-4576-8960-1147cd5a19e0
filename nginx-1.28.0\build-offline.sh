#!/bin/bash

# nginx 1.28.0 离线构建脚本
# 用于在断网环境下构建nginx Docker镜像

set -e

echo "================================================================================"
echo "                    nginx 1.28.0 离线Docker镜像构建脚本"
echo "================================================================================"

# 检查Docker是否可用
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装或不可用"
    echo "请先安装Docker后再运行此脚本"
    exit 1
fi

echo "✅ Docker可用"

# 检查必需文件
echo ""
echo "=== 检查必需文件 ==="

check_file() {
    local file=$1
    local description=$2
    
    if [ -f "$file" ]; then
        echo "✅ $description: $file"
    else
        echo "❌ $description缺失: $file"
        return 1
    fi
}

check_dir() {
    local dir=$1
    local description=$2
    local min_files=$3
    
    if [ -d "$dir" ]; then
        local file_count=$(ls "$dir" | wc -l)
        if [ "$file_count" -ge "$min_files" ]; then
            echo "✅ $description: $dir ($file_count 个文件)"
        else
            echo "❌ $description文件不足: $dir (需要至少 $min_files 个文件，实际 $file_count 个)"
            return 1
        fi
    else
        echo "❌ $description目录不存在: $dir"
        return 1
    fi
}

# 检查文件
check_file "Dockerfile" "Docker构建文件"
check_file "scripts/build-nginx.sh" "nginx编译脚本"
check_file "config/nginx.conf" "nginx配置文件"
check_file "config/default.conf" "默认站点配置"
check_file "packages/nginx-1.28.0.tar.gz" "nginx源码包"

# 检查目录
check_dir "centos7-rpms" "RPM依赖包" 20

echo ""
echo "=== 显示文件信息 ==="
echo "nginx源码包:"
ls -lh packages/

echo ""
echo "RPM包统计:"
echo "总计: $(ls centos7-rpms/*.rpm 2>/dev/null | wc -l) 个RPM包"
ls -lh centos7-rpms/ | head -5
echo "..."

echo ""
echo "=== 开始构建Docker镜像 ==="

IMAGE_NAME="nginx-1.28.0-offline"
BUILD_START_TIME=$(date +%s)

echo "镜像名称: $IMAGE_NAME"
echo "构建开始时间: $(date)"

# 构建Docker镜像
if docker build -t "$IMAGE_NAME" .; then
    BUILD_END_TIME=$(date +%s)
    BUILD_DURATION=$((BUILD_END_TIME - BUILD_START_TIME))
    
    echo ""
    echo "================================================================================"
    echo "                              构建成功！"
    echo "================================================================================"
    echo ""
    echo "构建信息:"
    echo "  镜像名称: $IMAGE_NAME"
    echo "  构建时间: ${BUILD_DURATION} 秒"
    echo "  完成时间: $(date)"
    echo ""
    
    # 显示镜像信息
    echo "镜像详情:"
    docker images "$IMAGE_NAME"
    
    echo ""
    echo "=== 验证构建结果 ==="
    
    # 检查nginx版本
    echo "nginx版本:"
    docker run --rm "$IMAGE_NAME" /usr/local/nginx/sbin/nginx -v
    
    echo ""
    echo "nginx编译配置:"
    docker run --rm "$IMAGE_NAME" /usr/local/nginx/sbin/nginx -V 2>&1 | head -3
    
    echo ""
    echo "=== 使用说明 ==="
    echo ""
    echo "启动nginx容器:"
    echo "  # 前台运行"
    echo "  docker run --rm -p 80:80 $IMAGE_NAME"
    echo ""
    echo "  # 后台运行"
    echo "  docker run -d -p 80:80 --name nginx-server $IMAGE_NAME"
    echo ""
    echo "管理命令:"
    echo "  # 查看容器状态"
    echo "  docker ps"
    echo ""
    echo "  # 查看日志"
    echo "  docker logs nginx-server"
    echo ""
    echo "  # 进入容器"
    echo "  docker exec -it nginx-server /bin/bash"
    echo ""
    echo "  # 测试配置"
    echo "  docker exec nginx-server /usr/local/nginx/sbin/nginx -t"
    echo ""
    echo "  # 重载配置"
    echo "  docker exec nginx-server /usr/local/nginx/sbin/nginx -s reload"
    echo ""
    echo "🎉 nginx 1.28.0 离线构建完成！"
    
else
    echo ""
    echo "================================================================================"
    echo "                              构建失败！"
    echo "================================================================================"
    echo ""
    echo "请检查:"
    echo "1. 所有依赖文件是否完整"
    echo "2. Docker是否有足够的磁盘空间"
    echo "3. 查看上面的错误信息"
    echo ""
    echo "如需帮助，请查看构建日志或运行测试脚本"
    exit 1
fi
