# MySQL 5.7.44 在线版备份管理器快速启动脚本
# 版本: 1.0
# 作者: AI Assistant
# 日期: 2025-07-12

# 检查是否在正确的目录
if (!(Test-Path "docker-compose.yaml")) {
    Write-Host "错误: 请在MySQL项目根目录运行此脚本" -ForegroundColor Red
    Write-Host "当前目录: $(Get-Location)" -ForegroundColor Yellow
    Write-Host "应该包含: docker-compose.yaml" -ForegroundColor Yellow
    exit 1
}

# 检查备份脚本目录
if (!(Test-Path "backup-scripts\backup-manager.ps1")) {
    Write-Host "错误: 找不到备份管理器脚本" -ForegroundColor Red
    Write-Host "请确保 backup-scripts\backup-manager.ps1 文件存在" -ForegroundColor Yellow
    exit 1
}

# 显示欢迎信息
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "MySQL 5.7.44 在线版备份管理器" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查Docker状态
Write-Host "检查Docker环境..." -ForegroundColor Yellow
$dockerVersion = docker --version 2>$null
if ($LASTEXITCODE -ne 0) {
    Write-Host " Docker未运行或未安装" -ForegroundColor Red
    Write-Host "请启动Docker Desktop后重试" -ForegroundColor Yellow
    exit 1
} else {
    Write-Host " Docker环境正常: $dockerVersion" -ForegroundColor Green
}

# 检查MySQL容器状态
Write-Host "检查MySQL容器状态..." -ForegroundColor Yellow
$containerStatus = docker inspect mysql5.7.44 --format='{{.State.Status}}' 2>$null
if ($LASTEXITCODE -eq 0) {
    if ($containerStatus -eq "running") {
        Write-Host " MySQL容器运行正常" -ForegroundColor Green
    } else {
        Write-Host "  MySQL容器状态: $containerStatus" -ForegroundColor Yellow
    }
} else {
    Write-Host "  MySQL容器不存在或未创建" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "启动备份管理器..." -ForegroundColor Green
Write-Host ""

# 启动备份管理器
& ".\backup-scripts\backup-manager.ps1"
