# DM8 Docker 完整备份恢复说明
备份时间: 20250718_142506
备份名称: dm8_backup_20250718_142506
备份类型: 完整备份（项目文件 + Docker配置 + 数据卷）

## 备份内容
1. 项目文件目录 (project_files/):
   - docker-compose.yml
   - Dockerfile
   - docker-entrypoint.sh
   - install_dm8.exp
   - test-connection.py
   - dm8_20250506_x86_rh7_64/ (DM8安装文件)

2. 数据卷备份:
   - dm8_data_volume.tar.gz (完整数据库数据卷)

3. 配置信息:
   - container_config.json (容器配置)
   - backup_info.json (备份信息)

## 恢复命令
.\backup-scripts\dm8-restore-cn.ps1 -BackupPath ".\backups\dm8_backup_20250718_142506"

## 注意事项
- 恢复将完全替换现有数据和配置
- 确保Docker Desktop正在运行
- 恢复期间现有服务将被停止
- 恢复完成后服务将自动启动

## 访问信息
- DM8: localhost:5236
- 用户名: SYSDBA
- 密码: GDYtd@2025
