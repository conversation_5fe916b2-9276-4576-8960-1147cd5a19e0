﻿[2025-07-12 23:14:29] [SUCCESS] 备份目录创建成功
[2025-07-12 23:14:30] [INFO] 检查Docker环境...
[2025-07-12 23:14:30] [SUCCESS] Docker版本: Docker version 28.0.4, build b8034c0
[2025-07-12 23:14:30] [INFO] 检查PostgreSQL容器状态...
[2025-07-12 23:14:30] [SUCCESS] PostgreSQL容器运行正常
[2025-07-12 23:14:30] [INFO] 备份配置文件...
[2025-07-12 23:14:30] [SUCCESS] 已备份: docker-compose.yml
[2025-07-12 23:14:30] [SUCCESS] 已备份: Dockerfile
[2025-07-12 23:14:30] [SUCCESS] 已备份: postgresql.conf
[2025-07-12 23:14:30] [SUCCESS] 已备份: pg_hba.conf
[2025-07-12 23:14:30] [SUCCESS] 已备份: docker-entrypoint.sh
[2025-07-12 23:14:30] [SUCCESS] 已备份: init-scripts目录
[2025-07-12 23:14:30] [SUCCESS] 配置备份完成，已备份 6 个文件/目录
[2025-07-12 23:14:30] [INFO] 备份数据库SQL数据...
[2025-07-12 23:14:30] [INFO] 执行pg_dumpall命令...
[2025-07-12 23:14:30] [SUCCESS] SQL备份完成并压缩，大小: 0MB
[2025-07-12 23:14:30] [INFO] 停止PostgreSQL容器确保数据一致性...
[2025-07-12 23:14:36] [SUCCESS] 容器已停止
[2025-07-12 23:14:36] [INFO] 备份PostgreSQL数据卷...
[2025-07-12 23:14:36] [INFO] 备份主数据卷: postgresql_11_19_data_offline
[2025-07-12 23:14:50] [SUCCESS] 主数据卷备份完成
[2025-07-12 23:14:50] [INFO] 备份日志卷: postgresql_11_19_logs_offline
[2025-07-12 23:14:50] [SUCCESS] 日志卷备份完成，包含 2 个文件
[2025-07-12 23:14:50] [INFO] 备份pgAdmin数据卷: pgadmin_data_offline
[2025-07-12 23:14:51] [SUCCESS] pgAdmin数据卷备份完成
[2025-07-12 23:14:51] [SUCCESS] 已移动: postgres_data_volume.zip (5.14MB)
[2025-07-12 23:14:51] [SUCCESS] 已移动: postgres_logs_volume.zip (0MB)
[2025-07-12 23:14:51] [SUCCESS] 已移动: pgadmin_data_volume.zip (0.01MB)
[2025-07-12 23:14:51] [SUCCESS] 数据卷备份完成
[2025-07-12 23:14:51] [INFO] 重启所有服务...
[2025-07-12 23:15:00] [SUCCESS] PostgreSQL服务重启成功
[2025-07-12 23:15:00] [INFO] 已清理临时目录
[2025-07-12 23:15:00] [INFO] 创建恢复说明文档...
[2025-07-12 23:15:00] [SUCCESS] 备份位置: .\backups\20250712_231429
[2025-07-12 23:15:00] [SUCCESS] 备份大小: 5.17 MB
[2025-07-12 23:15:00] [SUCCESS] 备份内容: 配置文件 + SQL数据 + 数据卷
[2025-07-12 23:15:00] [SUCCESS] 恢复命令: .\backup-scripts\postgresql-restore-cn.ps1 -BackupPath ".\backups\20250712_231429"
