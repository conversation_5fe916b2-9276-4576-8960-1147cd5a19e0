<?xml version="1.0"?>
<DATABASE>
    <!-- 安装语言 -->
	<LANGUAGE>EN</LANGUAGE>
	<!--安装程序的时区配置，缺省为+08:00，取值范围：-12:59 ~ +14:00 -->
	<TIME_ZONE>+08:00</TIME_ZONE>
	<!-- key文件，试用可不填-->
	<KEY></KEY>
	<!-- 安装程序组件类型，取值范围：0、1、2，0 表示安装全部，1 表示安装服务器，2 表示安装客户端。缺省为0。 -->
	<INSTALL_TYPE>0</INSTALL_TYPE>
	<!-- 安装目录 -->
	<INSTALL_PATH>/home/<USER>/dmdbms</INSTALL_PATH>
	<!-- 是否初始化库，取值范围：Y/N、y/n，不允许为空。 -->
	<INIT_DB>Y</INIT_DB>
	<!--数据库实例参数 -->
	<DB_PARAMS>
	    <!--初始数据库存放的路径，不允许为空 -->
		<PATH>/home/<USER>/dmdbms/data</PATH>
		<!--初始化数据库名字，缺省为DAMENG，不超过128个字符 -->
		<DB_NAME>DAMENG</DB_NAME>
		<!--初始化数据库实例名字，缺省为DMSERVER，不超过128个字符 -->
		<INSTANCE_NAME>DMSERVER</INSTANCE_NAME>
		<!--初始化时设置dm.ini中的PORT_NUM，缺省为5236，取值范围：1024~65534 -->
		<PORT_NUM>5236</PORT_NUM>
		<CTL_PATH></CTL_PATH>
		<LOG_PATHS>
			<LOG_PATH>/home/<USER>/dmdbms/data/DAMENG</LOG_PATH>
			<LOG_PATH>/home/<USER>/dmdbms/log</LOG_PATH>
		</LOG_PATHS>
		<!--数据文件使用的簇大小，取值范围：16页、32页，缺省为16页 -->
		<EXTENT_SIZE>32</EXTENT_SIZE>
		<!--数据文件使用的页大小，取值范围：4K、8K、16K、32K，缺省为8K -->
		<PAGE_SIZE>32</PAGE_SIZE>
		<!--日志文件使用的簇大小，缺省为256，取值范围为64~2048之间的整数 -->
		<LOG_SIZE>256</LOG_SIZE>
		<!--标识符大小写敏感。取值范围：Y/N y/n 1/0，缺省为Y -->
		<CASE_SENSITIVE>Y</CASE_SENSITIVE>
		<!--字符集选项，缺省为0。0代表GB18030,1代表UTF-8,2代表韩文字符集EUC-KR -->
		<CHARSET>0</CHARSET>

		<USE_NEW_HASH>1</USE_NEW_HASH>
		<!--初始化时设置SYSDBA的密码，缺省为SYSDBA，长度在9到48个字符之间 -->
		<SYSDBA_PWD>GDYtd@2025</SYSDBA_PWD>
		<SYSAUDITOR_PWD>GDYtd@2025</SYSAUDITOR_PWD>
		<SYSSSO_PWD>GDYtd@2025</SYSSSO_PWD>
		<SYSDBO_PWD>GDYtd@2025</SYSDBO_PWD>
		<!--初始化时区，默认是东八区。格式为：正负号小时：分钟，取值范围：-12:59 ~ +14:00 -->
		<TIME_ZONE>+08:00</TIME_ZONE>
		<PAGE_CHECK>0</PAGE_CHECK>
		<EXTERNAL_CIPHER_NAME></EXTERNAL_CIPHER_NAME>
		<EXTERNAL_HASH_NAME></EXTERNAL_HASH_NAME>
		<EXTERNAL_CRYPTO_NAME></EXTERNAL_CRYPTO_NAME>
		<ENCRYPT_NAME></ENCRYPT_NAME>
		<USBKEY_PIN></USBKEY_PIN>
		<BLANK_PAD_MODE>0</BLANK_PAD_MODE>
		<SYSTEM_MIRROR_PATH></SYSTEM_MIRROR_PATH>
		<MAIN_MIRROR_PATH></MAIN_MIRROR_PATH>
		<ROLL_MIRROR_PATH></ROLL_MIRROR_PATH>
		<PRIV_FLAG>0</PRIV_FLAG>
		<ELOG_PATH></ELOG_PATH>
	</DB_PARAMS>
	<!--是否创建数据库实例的服务，取值范围： Y/N y/n，不允许为空，不初始化数据库将忽略此节点。非root用户不能创建数据库服务。 -->
	<CREATE_DB_SERVICE>Y</CREATE_DB_SERVICE>
	<!--是否启动数据库，取值范围： Y/N y/n，不允许为空，不创建数据库服务将忽略此节点。 -->
	<STARTUP_DB_SERVICE>y</STARTUP_DB_SERVICE>
</DATABASE>