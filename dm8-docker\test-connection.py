#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
达梦数据库连接测试脚本
"""

import socket
import time

def test_tcp_connection(host, port, timeout=5):
    """测试TCP连接"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"连接测试异常: {e}")
        return False

def main():
    host = "localhost"
    port = 5236
    
    print("=== 达梦数据库连接测试 ===")
    print(f"测试地址: {host}:{port}")
    
    # 测试TCP连接
    print("\n1. 测试TCP连接...")
    if test_tcp_connection(host, port):
        print("✓ TCP连接成功")
    else:
        print("✗ TCP连接失败")
        return
    
    print("\n=== 连接信息 ===")
    print("数据库类型: 达梦数据库 DM8")
    print("连接地址: localhost:5236")
    print("数据库名: DAMENG")
    print("实例名: DMSERVER")
    print("用户名: SYSDBA")
    print("密码: GDYtd@2025")
    print("\n=== JDBC连接字符串示例 ===")
    print("jdbc:dm://localhost:5236/DAMENG")
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
