#DaMeng Database Server Configuration file
#this is comments

#file location of dm.ctl
		CTL_PATH                        = /opt/dmdbms/data/DAMENG/dm.ctl     #ctl file path
		CTL_BAK_PATH                    = /opt/dmdbms/data/DAMENG/ctl_bak    #dm.ctl backup path
		CTL_BAK_NUM                     = 10                                          #backup number of dm.ctl, allowed to keep one more backup file besides specified number.
		SYSTEM_PATH                     = /opt/dmdbms/data/DAMENG              #system path
		CONFIG_PATH                     = /opt/dmdbms/data/DAMENG              #config path
		TEMP_PATH                       = /opt/dmdbms/data/DAMENG              #temporary file path
		BAK_PATH                        = /opt/dmdbms/data/DAMENG/bak        #backup file path
		XBOX_DUMP_PATH                  =                       #xbox_dump_path
		BCT_PATH                        = /opt/dmdbms/data/DAMENG              #BCT file path

#instance name
		INSTANCE_NAME                   = DMSERVER                                    #Instance name

#memory pool and buffer
		MAX_OS_MEMORY                   = 100                   #Maximum Percent Of OS Memory
		MEMORY_POOL                     = 500                   #Memory Pool Size In Megabyte
		MEMORY_N_POOLS                  = 1                     #Number of Memory Pool
		MEMORY_TARGET                   = 15000                 #Memory Share Pool Target Size In Megabyte
		MEMORY_EXTENT_SIZE              = 32                    #Memory Extent Size In Megabyte
		MEMORY_LEAK_CHECK               = 0                     #Memory Pool Leak Checking Flag
		MEMORY_MAGIC_CHECK              = 1                     #Memory Pool Magic Checking Flag
		HUGEPAGE_THRESHOLD              = 16                    #IF not zero, try using hugepage if allocating size >= threshold * 2M
		MEMORY_BAK_POOL                 = 4                     #Memory Backup Pool Size In Megabyte
		HUGE_MEMORY_PERCENTAGE          = 50                    #Maximum percent of HUGE buffer that can be allocated to work as common memory pool
		HUGE_BUFFER                     = 80                    #Initial Huge Buffer Size In Megabytes
		HUGE_BUFFER_POOLS               = 4                     #number of Huge buffer pools
		BUFFER                          = 8000                  #Initial System Buffer Size In Megabytes
		BUFFER_POOLS                    = 19                    #number of buffer pools
		FAST_POOL_PAGES                 = 3000                  #number of pages for fast pool
		FAST_ROLL_PAGES                 = 1000                  #number of pages for fast roll pages
		KEEP                            = 8                     #system KEEP buffer size in Megabytes
		RECYCLE                         = 300                   #system RECYCLE buffer size in Megabytes
		RECYCLE_POOLS                   = 19                    #Number of recycle buffer pools
		ROLLSEG                         = 1                     #system ROLLSEG buffer size in Megabytes
		ROLLSEG_POOLS                   = 19                    #Number of rollseg buffer pools
		MULTI_PAGE_GET_NUM              = 1                     #Maximum number of pages for each read of buffer
		PRELOAD_SCAN_NUM                = 0                     #The number of pages scanned continuously to start preload task
		PRELOAD_EXTENT_NUM              = 0                     #The number of clusters preloaded for the first time
		SORT_BUF_SIZE                   = 20                    #maximum sort buffer size in Megabytes
		SORT_BLK_SIZE                   = 1                     #maximum sort blk size in Megabytes
		SORT_BUF_GLOBAL_SIZE            = 1000                  #maximum global sort buffer size in Megabytes
		SORT_FLAG                       = 1                     #choose method of sort
		HAGR_HASH_SIZE                  = 100000                #hash table size for hagr
		HJ_BUF_GLOBAL_SIZE              = 5000                  #maximum hash buffer size for all hash join in Megabytes
		HJ_BUF_SIZE                     = 500                   #maximum hash buffer size for single hash join in Megabytes
		HJ_BLK_SIZE                     = 2                     #hash buffer size allocated each time for hash join in Megabytes
		HAGR_BUF_GLOBAL_SIZE            = 5000                  #maximum buffer size for all hagr in Megabytes
		HAGR_BUF_SIZE                   = 500                   #maximum buffer size for single hagr in Megabytes
		HAGR_BLK_SIZE                   = 2                     #buffer size allocated each time for hagr in Megabytes
		MTAB_MEM_SIZE                   = 8                     #memory table size in Kilobytes
		FTAB_MEM_SIZE                   = 0                     #file table package size in Kilobytes
		MMT_GLOBAL_SIZE                 = 4000                  #memory map table global size in megabytes
		MMT_SIZE                        = 0                     #memory map table size in megabytes
		MMT_FLAG                        = 1                     #ways of storing bdta data in memory map table
		DICT_BUF_SIZE                   = 50                    #dictionary buffer size in Megabytes
		HFS_CACHE_SIZE                  = 160                   #hfs cache size in Megabytes, used in huge horizon table for insert, update,delete
		VM_STACK_SIZE                   = 256                   #VM stack size in Kilobytes
		VM_POOL_SIZE                    = 64                    #VM pool size in Kilobytes
		VM_POOL_TARGET                  = 16384                 #VM pool target size in Kilobytes
		SESS_POOL_SIZE                  = 64                    #session pool size in Kilobytes
		SESS_POOL_TARGET                = 16384                 #session pool target size in Kilobytes
		RT_HEAP_TARGET                  = 8192                  #runtime heap target size in Kilobytes
		VM_MEM_HEAP                     = 0                     #Whether to allocate memory to VM from HEAP
		RFIL_RECV_BUF_SIZE              = 16                    #redo file recover buffer size in Megabytes
		HAGR_DISTINCT_HASH_TABLE_SIZE   = 10000                 #Size of hagr distinct hash table
		CNNTB_HASH_TABLE_SIZE           = 100                   #Size of hash table in connect-by operation
		GLOBAL_RTREE_BUF_SIZE           = 100                   #The total size of buffer for rtree
		SINGLE_RTREE_BUF_SIZE           = 10                    #The size of buffer for single rtree
		SORT_OPT_SIZE                   = 0                     #once max memory size of radix sort assist count array
		TSORT_OPT                       = 1                     #minimizing memory allocation during small rowset sorting if possible
		BIND_PLN_PERCENT                = 30                    #Maximum percent of bind plan in plan cache pool
		FBACK_HASH_SIZE                 = 10000                 #hash table size for flashback function
		XBOX_MEMORY_TARGET              = 1024                  #Memory target size in Megabyte of XBOX system
		SORT_BUF_SINGLE_SIZE            = 10000                 #maximum sort operator buffer size in Megabytes
		NDCT_CACHE_ADJUST_LEN          = 128                   #Length of each dict LRU list adjustment when NDCT_CACHE_PLL = 1
		NDCT_CACHE_PLL                 = 0                     #Whether to enable parallel dict cache looking up
		NDCT_LOAD_OPT                  = 1                     #Whether to enable dict load optimization
		REGEX_MEM_LIMIT                 = 20                    #REGEX memory limit(MB)

#thread
		WORKER_THREADS                  = 8                     #Number Of Worker Threads
		TASK_THREADS                    = 8                     #Number Of Task Threads
		FAST_RW_LOCK                    = 1                     #Fast Read Write Lock flag
		SPIN_TIME                       = 4000                  #Spin Time For Threads In Microseconds
		WORK_THRD_STACK_SIZE            = 8192                  #Worker Thread Stack Size In Kilobytes
		WORK_THRD_RESERVE_SIZE          = 512                   #Worker Thread Reserve Stack Size In Kilobytes
		WORKER_CPU_PERCENT              = 0                     #Percent of CPU number special for worker thread
		NESTED_C_STYLE_COMMENT          = 0                     #flag for C stype nested comment
		STHD_FLAG                       = 0                     #Whether to use SQL threads pool
		STHD_GRP_NUM                    = 8                     #The number of SQL thread groups
		STHD_THREAD_NUM                 = 8                     #The number of SQL threads initialized
		THRDS_POOL_INIT                     = 50                    #Number of threads initialized in threads pool
		THRDS_POOL_MAX                     = 0                     #Max number of threads in threads pool
		PTHD_THRD_POOLS                     = 16                    #Number of thread pools for pthd sys
		MAX_SEC_ASYNC_THREADS           = 0                     #Maximum number of asynchronous maintenance threads for second index
		ASYNC_THRD_MAX_PERCENT          = 100                   #Maximum percent of work threads for asynchronous tasks

#query
		USE_PLN_POOL                    = 1                     #Query Plan Reuse Mode, 0: Forbidden; 1:strictly reuse, 2:parsing reuse, 3:mixed parsing reuse
		DYN_SQL_CAN_CACHE               = 1                     #Dynamic SQL cache mode. 0: Forbidden; 1: Allowed if the USE_PLN_POOL is non-zero;
		VPD_CAN_CACHE                   = 0                     #VPD SQL cache mode. 0: Forbidden; 1: Allowed if the USE_PLN_POOL is non-zero;
		RS_CAN_CACHE                    = 0                     #Resultset cache mode. 0: Forbidden; 1: Allowed only if the USE_PLN_POOL is non-zero;
		RS_CACHE_TABLES                 =                       #Tables allowed to enable result set cache
		RS_CACHE_MIN_TIME               = 0                     #Least time for resultset to be cached
		RS_BDTA_FLAG                    = 0                     #Resultset mode. 0: row; 2: bdta;
		RS_BDTA_BUF_SIZE                = 32                    #Maximum size of message in Kilobytes for BDTA cursor, it's valid only if RS_BDTA_FLAG is set to 2
		RS_TUPLE_NUM_LIMIT              = 2000                  #Maximum number for resultset to be cached
		RESULT_SET_LIMIT                = 10000                 #Maximum Number Of  cached Resultsets
		RESULT_SET_FOR_QUERY            = 0                     #Whether to generate result set for non-query statement
		SESSION_RESULT_SET_LIMIT        = 10000                 #Maximum number of cached result sets for each session, 0 means unlimited
		BUILD_FORWARD_RS                = 0                     #Whether to generate result set for forward only cursor
		MAX_OPT_N_TABLES                = 6                     #Maximum Number Of Tables For Query Optimization
		MAX_N_GRP_PUSH_DOWN             = 5                     #Maximum Number Of Rels For Group push down Optimization
		CNNTB_MAX_LEVEL                 = 20000                 #Maximum Level Of Hierarchical Query
		CTE_MAXRECURSION                = 100                   #Maximum recursive Level Of Common Expression Table
		CTE_OPT_FLAG                    = 1                     #Optimize recursive with, 0: false, 1: convert refed subquery to invocation
		BATCH_PARAM_OPT                 = 0                     #optimize flag for DML with batch binded params
		CLT_CONST_TO_PARAM              = 0                     #Whether to convert constant to parameter
		LIKE_OPT_FLAG                   = 127                   #the optimized flag of LIKE expression
		FILTER_PUSH_DOWN                = 2                     #whether push down filter to base table
		USE_MCLCT                       = 2                     #mclct use flag for replace mgat
		PHF_NTTS_OPT                    = 1                     #phf ntts opt flag
		MPP_MOTION_SYNC                 = 200                   #mpp motion sync check number
		UPD_DEL_OPT                     = 2                     #update&delete opt flag, 0: false, 1: opt, 2: opt & ntts opt
		ENABLE_INJECT_HINT              = 0                     #enable inject hint
		FETCH_PACKAGE_SIZE              = 512                   #command fetch package size
		ENABLE_DIST_IN_SUBQUERY_OPT     = 0                     #Whether to enable in-subquery optimization
		MAX_OPT_N_OR_BEXPS              = 7                     #maximum number of OR bool expressions for query optimization
		USE_HAGR_FLAG                   = 0                     #Whether to use HAGR operator when can't use SAGR operator
		DTABLE_PULLUP_FLAG              = 1                     #the flag of pulling up derived table
		VIEW_PULLUP_FLAG                = 34                    #the flag of pulling up view
		GROUP_OPT_FLAG                  = 8252                  #the flag of opt group
		FROM_OPT_FLAG                   = 0                     #the flag of opt from
		HAGR_PARALLEL_OPT_FLAG          = 4                     #the flag of opt hagr in mpp or parallel
		HAGR_DISTINCT_OPT_FLAG          = 2                     #the flag of opt hagr distinct in mpp
		REFED_EXISTS_OPT_FLAG           = 1                     #Whether to optimize correlated exists-subquery into non-correlated in-subquery
		REFED_OPS_SUBQUERY_OPT_FLAG     = 1                     #Whether to optimize correlated op all/some/all-subquery into exists-subquery
		HASH_PLL_OPT_FLAG               = 107                   #the flag of cutting partitioned table when used hash join
		PARTIAL_JOIN_EVALUATION_FLAG    = 1                     #Whether to convert join type when upper operator is DISTINCT
		USE_FK_REMOVE_TABLES_FLAG       = 1                     #Whether to remove redundant join by taking advantage of foreign key constraint
		USE_FJ_REMOVE_TABLE_FLAG        = 1                     #Whether to remove redundant join by taking advantage of filter joining
		SLCT_ERR_PROCESS_FLAG           = 0                     #How to handle error when processing single row
		MPP_HASH_LR_RATE                = 10                    #The ratio of left child's cost to right child's cost of hash join in MPP environment that can influence the execution plan
		LPQ_HASH_LR_RATE                = 30                    #The ratio of left child's cost to right child's cost of hash join in LPQ environment that can influence the execution plan
		USE_HTAB                        = 1                     #Whether to use HTAB operator for the whole plan
		SEL_ITEM_HTAB_FLAG              = 0                     #Whether to use HTAB operator for correlated subquery in select items
		OR_CVT_HTAB_FLAG                = 1                     #Whether to use HTAB operator to optimizer or-expression
		ENHANCED_SUBQ_MERGING           = 3                     #Whether to use merging subquery opt
		CASE_WHEN_CVT_IFUN              = 9                     #Flag of converting subquery in case-when expression to IF operator
		OR_NBEXP_CVT_CASE_WHEN_FLAG     = 0                     #Whether to convert or-expression to case-when expression
		NONCONST_OR_CVT_IN_LST_FLAG     = 0                     #Whether to convert nonconst or-expression to in lst expression
		OUTER_CVT_INNER_PULL_UP_COND_FLAG = 11                  #Whether to pull up join condition when outer join converts to inner join
		OPT_OR_FOR_HUGE_TABLE_FLAG      = 0                     #Whether to use HFSEK to optimize or-expression for HUGE table
		ORDER_BY_NULLS_FLAG             = 0                     #Whether to place NULL values to the end of the result set when in ascending order
		SUBQ_CVT_SPL_FLAG               = 1                     #Flag of indicating how to convert correlated subquery
		ENABLE_RQ_TO_SPL                = 1                     #Whether to convert correlated subquery to SPOOL
		MULTI_IN_CVT_EXISTS             = 1                     #Whether to convert multi-column-in subquery to exists subquery
		PRJT_REPLACE_NPAR               = 1                     #Whether to replace NPAR tree in NSEL after projection
		ENABLE_RQ_TO_INV                = 0                     #Whether to convert correlated subquery to temporary function
		SUBQ_EXP_CVT_FLAG               = 193                   #whether convert refered subquery exp to non-refered subquery exp
		USE_REFER_TAB_ONLY              = 0                     #Whether to pull down correlated table only when dealing with correlated subquery
		REFED_SUBQ_CROSS_FLAG           = 1                     #Whether to replace hash join with cross join for correlated subquery
		IN_LIST_AS_JOIN_KEY             = 0                     #Whether to use in-list expression as join key
		OUTER_JOIN_FLATING_FLAG         = 1                     #Flag of indicating whether outer join will be flattened
		TOP_ORDER_OPT_FLAG              = 69                    #The flag of optimizing the query with the top clause and the order by clause
		TOP_ORDER_ESTIMATE_CARD         = 300                   #The estimated card of leaf node when optimize the query with the top clause and the order by clause
		PLACE_GROUP_BY_FLAG             = 0                     #The flag of optimizing the query with group_by and sfun by clause
		TOP_DIS_HASH_FLAG               = 1                     #Flag of disable hash join in TOP-N query
		ENABLE_RQ_TO_NONREF_SPL         = 1                     #Whether to convert correlated query to non-correlated query
		ENABLE_CHOOSE_BY_ESTIMATE_FLAG  = 0                     #Whether to choose different plan by estimating
		OPTIMIZER_MODE                  = 1                     #Optimizer_mode
		NEW_MOTION                      = 1                     #New Motion
		LDIS_NEW_FOLD_FUN               = 0                     #ldis use different fold fun with mdis
		DYNAMIC_CALC_NODES              = 0                     #different nodes of npln use different nubmer of calc sizes/threads
		OPTIMIZER_MAX_PERM              = 7200                  #Optimizer_max permutations
		ENABLE_INDEX_FILTER             = 1                     #enable index filter
		OPTIMIZER_DYNAMIC_SAMPLING      = 0                     #Dynamic sampling level
		TABLE_STAT_FLAG                 = 0                     #How to use stat of table
		AUTO_STAT_OBJ                   = 0                     #Flag of automatically collecting statistics and recording DML changing rows
		MONITOR_MODIFICATIONS           = 0                     #Flag of monitor statistics and recording DML modifications
		MON_CHECK_INTERVAL              = 3600                  #Server flush monitor modifications data to disk interval
		NONREFED_SUBQUERY_AS_CONST      = 1                     #Whether to convert non-correlated subquery to const
		HASH_CMP_OPT_FLAG               = 0                     #Flag of operators that enable optimization with static hash table
		OUTER_OPT_NLO_FLAG              = 0                     #Flag of enable index join for those whose right child is not base table
		DISTINCT_USE_INDEX_SKIP         = 2                     #Distinct whether to use index skip scan
		USE_INDEX_SKIP_SCAN             = 0                     #Whether to use index skip scan
		INDEX_SKIP_SCAN_RATE            = 0.0025                #Rate in index skip scan
		SPEED_SEMI_JOIN_PLAN            = 9                     #Flag of speeding up the generating process of semi join plan
		COMPLEX_VIEW_MERGING            = 2                     #Flag of merging complex view into query without complex view
		HLSM_FLAG                       = 1                     #Choose one method to realize hlsm operator
		DEL_HP_OPT_FLAG                 = 0                     #Optimize delete for horization partition table
		OPTIMIZER_OR_NBEXP              = 29                    #Flag of or-expression optimization method
		NPLN_OR_MAX_NODE                = 20                    #Max number of or-expression on join condition
		CNNTB_OPT_FLAG                  = 193                   #Optimize hierarchical query
		ADAPTIVE_NPLN_FLAG              = 3                     #Adaptive npln
		MULTI_UPD_OPT_FLAG              = 1                     #Optimize multi column update
		MULTI_UPD_MAX_COL_NUM           = 128                   #Max value of column counts when optimize multi column update
		ENHANCE_BIND_PEEKING            = 0                     #Enhanced bind peeking
		NBEXP_OPT_FLAG                  = 7                     #Whether to enable optimization for bool expressions
		HAGR_HASH_ALGORITHM_FLAG        = 0                     #HAGR hash algorithm choice
		DIST_HASH_ALGORITHM_FLAG        = 0                     #Distinct hash algorithm choice
		UNPIVOT_OPT_FLAG                = 0                     #Optimize UNPIVOT operator
		VIEW_FILTER_MERGING             = 138                   #Flag of merging view filter
		ENABLE_PARTITION_WISE_OPT       = 1                     #whether enable partition-wise optimization
		OPT_MEM_CHECK                   = 0                     #reduce search space when out of memory
		ENABLE_JOIN_FACTORIZATION       = 1                     #Whether to enable join factorization
		EXPLAIN_SHOW_FACTOR             = 1                     #factor of explain
		ERROR_COMPATIBLE_FLAG           = 0                     #enable/disable specified errors to be compatible with previous version
		ENABLE_NEST_LOOP_JOIN_CACHE     = 0                     #whether enable cache temporary result of nest loop join child
		ENABLE_TABLE_EXP_REF_FLAG       = 1                     #Whether allow table expression to reference brother tables
		BIND_PARAM_OPT_FLAG             = 3                     #flag of optimizer bind parameter
		VIEW_OPT_FLAG                   = 1                     #flag of optimize view
		USE_DHASH_FLAG                  = 0                     #use dhash flag: 0 means use static hash; 1,2,3 means use dynamic hash
		ENABLE_DBLINK_TO_INV            = 0                     #Whether to convert correlated subquery which has dblink to temporary function
		ENABLE_BLOB_CMP_FLAG            = 1                     #Whether BLOB/TEXT types are allowed to be compared;0:not allowed;1.allowed and text turn to char/varchar;2.allowed and char/varchar turn to text.
		ENABLE_ADJUST_NLI_COST          = 1                     #Whether adjust cost of nest loop inner join
		SORT_ADAPTIVE_FLAG              = 0                     #sort buf adaptive
		DPC_OPT_FLAG                    = 65535                 #optimizer control for DPC
		DPC_SYNC_STEP                   = 16                    #dpc motion sync check step
		DPC_SYNC_TOTAL                  = 0                     #dpc motion sync check total
		XBOX_DUMP_THRESHOLD             = 0                     #The xbox_sys mem used threshold of dump xbox_msg
		STMT_XBOX_REUSE                 = 1                     #Xbox resuse flag on statement
		ENABLE_ADJUST_DIST_COST         = 0                     #Whether adjust cost of distinct
		XBOX_SHORT_MSG_SIZE             = 1024                  #The xbox_sys short message threshold of dump xbox_msg
		MAX_HEAP_SIZE                   = 0                     #Maximum heap size in megabyte allowed to use during analysis phase
		PLAN_OP_FLAG                    = 0                     #flag of disabled plan operator
		DUAL_ENABLE_SELECT              = 1                     #Enable select dual/sysdual/sysdual2 in mount status. 0: no, 1: yes.
		LOAD_BINDED_PLN                 = 0                     #Whether to load binded plan
		STAT_CACHE_CAPACITY             = 1000                  #the capacity of cache on sp
		LIKE_PATTERN_NUM                = 300                   #The maximum length of like pattern-matching
		FORALL_OPT                      = 1                     #Whether to optimize FORALL statements
		HASH_OPT_FLAG                   = 1                     #Flag of operators that enable optimization with hash table
		SFUN_PUSH_DOWN_FLAG             = 1                     #sfun push down flag
		SKIP_CORRUPT_PAGE              = 0                    #Policy for dealing with corrupt pages, 0: raise an error, 1: skip them
		AUTO_GEN_PLAN                   = 0                     #Upper limit of iterative rounds for parameter iterative optimization; 0 means never try
		MAX_SCAN_PAGES                  = 32                    #Maximum scan pages per data fill
		XBOX_SPACE_LIMIT             = 0                     #Disk space limit of one xbox's all dump files
		XBOXS_SPACE_LIMIT            = 0                     #Disk space limit of all xbox's dump files
		BF_SIZE                        = 0                     #The size of Bloom-Filter in Megabytes; 0:internal default size, other value:user-specified size
		BF_OPT_FLAG                    = 0                     #How to use Bloom-Filter Optimization
		ENABLE_PLN_PRESEARCH            = 0                     #Enable plan presearch, 0: disable, 1: enable
		DDL_OPT_FLAG                 =  0                     #DDL related parameters
		DIGIT_AS_NUMBER                 = 0                     #Treat digit constants as number when using constant to parameter option
		DIST_OPT_FLAG                  = 0                     #Whether enable distinct Optimization

#checkpoint
		CKPT_RLOG_SIZE                  = 512                   #Checkpoint Rlog Size, 0: Ignore; else: Generate With Redo Log Size (减少到512MB)
		CKPT_DIRTY_PAGES                = 0                     #Checkpoint Dirty Pages, 0: Ignore; else: Generate With Dirty Pages
		CKPT_INTERVAL                   = 120                   #Checkpoint Interval In Seconds (更频繁的检查点)
		CKPT_FLUSH_RATE                 = 10.00                 #Checkpoint Flush Rate(0.0-100.0) (提高刷新率)
		CKPT_FLUSH_PAGES                = 1000                  #Minimum number of flushed pages for checkpoints
		CKPT_WAIT_PAGES                 = 1024                  #Maximum number of pages flushed for checkpoints
		FORCE_FLUSH_PAGES               = 8                     #number of periodic flushed pages
		ENABLE_CKPT_EVENT_TRIG          = 0                     #Whether enable checkpoint event trigger, 0: disable; 1: enable
		CKPT_FORCE_WAIT_PAGES           = 1024                  #Maximum number of pages flushed for checkpoints while unsafe rlog space

#IO
		DIRECT_IO                       = 0                     #Flag For Io Mode(Non-Windows Only), 0: Using File System Cache; 1: Without Using File System Cache
		IO_THR_GROUPS                   = 8                     #The Number Of Io Thread Groups(Non-Windows Only)
		HIO_THR_GROUPS                  = 2                     #The Number Of Huge Io Thread Groups(Non-Windows Only)
		FIL_CHECK_INTERVAL              = 0                     #Check file interval in Second,0 means no_check(Non-Windows Only)
		FAST_EXTEND_WITH_DS             = 1                     #How To Extend File's Size (Non-Windows Only), 0: Extend File With Hole; 1: Extend File With Disk Space
		FIL_CHECK_MODE                  = 0                     #Whether to halt when system file doesn't exist, 0:no_halt, 1:halt(Non-Windows Only)
		IO_MODE                         = 0                      #Mode to optimize IO, 0: No optimization; 1: Merge write

#database
		MAX_SESSIONS                    = 10000                 #Maximum number of concurrent sessions
		MAX_CONCURRENT_TRX              = 0                     #Maximum number of concurrent transactions
		CONCURRENT_DELAY                = 16                    #Delay time in seconds for concurrent control
		TRX_VIEW_SIZE                   = 512                   #The buffer size of local transaction ids in TRX_VIEW
		TRX_VIEW_MODE                   = 1                     #The transaction view mode, 0: Active ids snap; 1: Recycled id array
		TRX_CMTARR_SIZE                 = 10                    #The size of transaction commitment status array in 1M
		MAX_SESSION_STATEMENT           = 10000                 #Maximum number of statement handles for each session
		MAX_SESSION_MEMORY              = 0                     #Maximum memory(In Megabytes) a single session can use
		MAX_CONCURRENT_OLAP_QUERY       = 0                     #Maximum number of concurrent OLAP queries
		BIG_TABLE_THRESHHOLD            = 1000                  #Threshold value of a big table in 10k
		MAX_EP_SITES                    = 64                    #Maximum number of EP sites for MPP
		PORT_NUM                        = 5236                  #Port number on which the database server will listen
		LISTEN_IP                       =                       #IP address from which the database server can accept
		FAST_LOGIN                      = 2                     #Whether to log information without login
		DDL_AUTO_COMMIT                 = 1                     #ddl auto commit mode, 0: not auto commit; 1: auto commit
		COMPRESS_MODE                   = 0                     #Default Compress Mode For Tables That Users Created, 0: Not Compress; 1: Compress
		PK_WITH_CLUSTER                 = 0                     #Default Flag For Primary Key With Cluster, 0: Non-Cluster; 1: Cluster
		EXPR_N_LEVEL                    = 200                   #Maximum nesting levels for expression
		N_PARSE_LEVEL                   = 100                   #Maximum nesting levels for parsing object
		MAX_SQL_LEVEL                   = 500                   #Maximum nesting levels of VM stack frame for sql
		BDTA_SIZE                       = 300                   #batch data processing size.SIZE OF BDTA(1-10000)
		OLAP_FLAG                       = 2                     #OLAP FLAG, 1 means enable olap
		JOIN_HASH_SIZE                  = 500000                #the hash table size for hash join
		HFILES_OPENED                   = 256                   #maximum number of files can be opened at the same time for huge table
		ISO_IGNORE                      = 0                     #ignore isolation level flag
		TEMP_SIZE                       = 10                    #temporary file size  in Megabytes
		TEMP_SPACE_LIMIT                = 0                     #temp space limit in megabytes
		FILE_TRACE                      = 0                     #Whether to log operations of database files
		COMM_TRACE                      = 0                     #Whether to log warning information of communication
		ERROR_TRACE                     = 0                     #Whether to log error information, 1: NPAR ERROR
		CACHE_POOL_SIZE                 = 100                   #SQL buffer size in megabytes
		PLN_DICT_HASH_THRESHOLD         = 20                    #Threshold in megabytes for plan dictionary hash table creating
		STAT_COLLECT_SIZE               = 10000                 #minimum collect size in rows for statistics
		STAT_ALL                        = 0                     #if collect all the sub-tables of a partition table
		PHC_MODE_ENFORCE                = 0                     #join mode
		ENABLE_HASH_JOIN                = 1                     #enable hash join
		ENABLE_INDEX_JOIN               = 1                     #enable index join
		ENABLE_MERGE_JOIN               = 1                     #enable merge join
		MPP_INDEX_JOIN_OPT_FLAG         = 1                     #enhance index inner join in mpp
		MPP_NLI_OPT_FLAG                = 1                     #enhance nest loop inner join in mpp
		MAX_PARALLEL_DEGREE             = 1                     #Maximum degree of parallel query
		PARALLEL_POLICY                 = 0                     #Parallel policy
		PARALLEL_THRD_NUM               = 10                    #Thread number for parallel task
		PARALLEL_MODE_COMMON_DEGREE     = 1                     #the common degree of parallel query for parallel-mode
		PUSH_SUBQ                       = 0                     #Whether to push down semi join for correlated subquery
		OPTIMIZER_AGGR_GROUPBY_ELIM     = 1                     #Whether to attempt to eliminate group-by aggregations
		UPD_TAB_INFO                    = 0                     #Whether to update table info when startup
		ENABLE_IN_VALUE_LIST_OPT        = 518                   #Flag of optimization methods for in-list expression
		ENHANCED_BEXP_TRANS_GEN         = 3                     #Whether to enable enhanced transitive closure of boolean expressions
		ENABLE_DIST_VIEW_UPDATE         = 0                     #whether view with distinct can be updated
		STAR_TRANSFORMATION_ENABLED     = 0                     #Whether to enable star transformation for star join queries
		MONITOR_INDEX_FLAG              = 0                     #monitor index flag
		AUTO_COMPILE_FLAG               = 1                     #Whether to compile the invalid objects when loading
		RAISE_CASE_NOT_FOUND            = 0                     #Whether raise CASE_NOT_FOUND exception for no case item matched
		FIRST_ROWS                      = 100                   #maximum number of rows when first returned to clients
		LIST_TABLE                      = 0                     #Whether to convert tables to LIST tables when created
		ENABLE_SPACELIMIT_CHECK         = 1                     #flag for the space limit check, 0: disable 1: enable
		BUILD_VERTICAL_PK_BTREE         = 0                     #Whether to build physical B-tree for primary key on huge tables
		BDTA_PACKAGE_COMPRESS           = 0                     #Whether to compress BDTA packages
		HFINS_PARALLEL_FLAG             = 0                     #Flag of parallel policy for inserting on huge table
		HFINS_MAX_THRD_NUM              = 100                   #Maximum number of parallel threads that responsible for inserting on huge table
		LINK_CONN_KEEP_TIME             = 15                    #Max idle time in minute for DBLINK before being closed
		DETERMIN_CACHE_SIZE             = 5                     #deterministic function results cache size(M)
		NTYPE_MAX_OBJ_NUM               = 1000000               #Maximum number of objects and strings in composite data type
		CTAB_SEL_WITH_CONS              = 0                     #Whether to build constraints when creating table by query
		HLDR_BUF_SIZE                   = 8                     #HUGE table fast loader buffer size in Megabytes
		HLDR_BUF_TOTAL_SIZE             = 4294967294            #HUGE table fast loader buffer total size in Megabytes
		HLDR_REPAIR_FLAG                = 0                     #Flag of repairing huge table after exception, 0: NO 1: YES
		HLDR_FORCE_COLUMN_STORAGE       = 1                     #Whether force column storage for last section data, 0: NO 1: YES
		HLDR_FORCE_COLUMN_STORAGE_PERCENT = 80                  #Minimum percent of unfully section data for huge force column storage
		HLDR_HOLD_RATE                  = 1.50                  #THE minimum rate to hold hldr of column number(1-65535)
		HLDR_MAX_RATE                   = 2                     #THE minimum rate to create hldr of column number(2-65535)
		HUGE_ACID                       = 0                     #Flag of concurrent mechanism for HUGE tables
		HUGE_STAT_MODE                  = 2                     #Flag of default stat mode when create huge table, 0:NONE 1:NORMAL 2:ASYNCHRONOUS
		HFS_CHECK_SUM                   = 1                     #Whether to check sum val for hfs data
		HBUF_DATA_MODE                  = 0                     #Whether to uncompress and decrypt data before read into HUGE buffer
		DBLINK_OPT_FLAG                 = 509                   #optimize dblink query flag
		ELOG_REPORT_LINK_SQL            = 0                     #Whether to write the SQLs that sent to remote database by DBLINKs into error log file
		DBLINK_LOB_LEN                  = 8                     #BLOB/TEXT buffer size(KB) for dblink
		FILL_COL_DESC_FLAG              = 0                     #Whether to return columns description while database returns results
		BTR_SPLIT_MODE                  = 1                     #Split mode for BTREE leaf, 0: split half and half, 1: split at insert point
		TS_RESERVED_EXTENTS             = 64                    #Number of reserved extents for each tablespace when startup
		TS_SAFE_FREE_EXTENTS            = 512                   #Number of free extents which considered as safe value for each tablespace
		TS_MAX_ID                       = 8192                  #Maximum ID value for tablespaces in database
		TS_FIL_MAX_ID                   = 2048                  #Maximum ID value for files in tablespace
		DECIMAL_FIX_STORAGE             = 0                     #Whether convert decimal data to fixed length storage
		SAVEPOINT_LIMIT                 = 512                   #The upper limit of savepoint in a transaction
		SQL_SAFE_UPDATE_ROWS            = 0                     #Maximum rows can be effected in an update&delete statement
		ENABLE_HUGE_SECIND              = 1                     #Whether support huge second index, 0: disable, 1: enable
		TRXID_UNCOVERED                 = 0                     #Whether disable scanning 2nd index only when pseudo column trxid used, 0: disable, 1: enable
		LOB_MAX_INROW_LEN               = 900                   #Max lob data inrow len
		RS_PRE_FETCH                    = 0                     #Whether enable result pre-fetch
		GEN_SQL_MEM_RECLAIM             = 1                     #Whether reclaim memory space after generating each SQL's plan
		TIMER_TRIG_CHECK_INTERVAL       = 60                    #Server check timer trigger interval
		INNER_INDEX_DDL_SHOW            = 1                     #Whether to show inner index ddl.
		HP_STAT_SAMPLE_COUNT            = 50                    #Max sample count when stating on horizon partitions
		MAX_SEC_INDEX_SIZE              = 16384                 #Maximum size of second index
		USE_FORALL_ATTR                 = 0                     #Whether to use cursor attributes of FORALL statements
		ALTER_TABLE_OPT                 = 0                     #Whether to optimize ALTER TABLE operation(add, modify or drop column)
		ENHANCE_RECLAIM                 = 1                     #Whether enhance class instances
		ENABLE_PMEM                     = 0                     #Whether allow to  use  persistent memory
		HP_TAB_COUNT_PER_BP             = 1                     #hash partition count per BP when use DEFAULT
		SQC_GI_NUM_PER_TAB              = 1                     #sqc_gi_num_per_tab
		HP_DEF_LOCK_MODE                = 0                     #Default lock mode for partition table. 0:lock root, 1: lock partitions
		CODE_CONVERSE_MODE              = 1                     #judge how dblink do with incomplete str bytes, 1 represents report err, 0 represents discard incomplete bytes
		DBLINK_USER_AS_SCHEMA           = 1                     #Whether use login name as default schema name for dblink
		CTAB_MUST_PART                  = 0                     #Whether to create partition user table
		CTAB_WITH_LONG_ROW              = 0                     #Default Flag of using long row for create table
		TMP_DEL_OPT                     = 1                     #delete opt flag for temporary table, 0: false, 1: opt
		LOGIN_FAIL_TRIGGER              = 0                     #Whether support trigger when login failed
		INDEX_PARALLEL_FILL             = 0                     #Enable index parallel fill rows
		CIND_CHECK_DUP                  = 0                     #Check index and unique constraint duplicates, 0: index same type and key is forbidden and ignore check unique constraint; 1: index same key is forbidden and check unique constraint
		TSMV_RAFIL_SIZE                 = 64                    #Max size(Mbytes) of temporary archive file for tablespace move on dpc
		HUGE_ENABLE_DEL_UPD             = 1                     #Whether huge table enable delete or update, 0: disable update/delete; 1: enable update/delete; 2: disable update; 3: disable delete
		HUGE_DEFAULT_FILE_SIZE          = 64                    #Default file size for huge table
		HUGE_DEFAULT_SECTION_SIZE       = 65536                 #Default section size for huge table
		ENABLE_CS_CVT                   = 0                     #the ifun cvt function, if opened,can convert str from one charset to another
		HUGE_DEFAULT_FILE_INIT_SIZE     = 1                     #Default file inital size for huge table
		TSMV_WAIT_TIMEOUT               = 120                   #Deadline(Second) for dpc_tsmv_lock_table_dict
		STAT_OPT_FLAG                   = 0                     #new way to implement stat
		ALLOWED_CLIENT_VERSIONS         =                       #Client version(s) allowed to connect
		CSEK2_CHECK_LOCK                = 0                     #Whether check lock when simple mpln doing csek2
		DPC_DCT_REFRESH_POLICY          = 1                     #DPC dct info refresh policy: with tick check or not
		HANDLE_WARN_RATE                = 75                    #handle used warning rate
		CLOB_MAX_IFUN_LEN               = 20971520              #Max length of source clob for ifun
		MAX_UNCOMPRESS_RATIO            = 100                   #Max uncompress ratio
		MAX_UNCOMPRESS_LENGTH           = 1024                  #Max uncompress length(MB)
		ENET_SESS_CHECK_INTERVAL        = 10                    #ENET check session interval when mp not in service
		DPC_TABLESPACE_BALANCE          = 1                     #Enable DPC load balance by tablespace
		CYT_CHECK_FLAG                  = 0                     #Whether check encrypt and decrypt
		TS_FIL_USE_ABSOLUTE_PATH        = 0                     #Data file must use absolute directory when creating tablespace or adding file
		LIST_TABLE_BRANCH               = 0                     #concurrent branch number when list_table open
		LIST_TABLE_NON_BRANCH           = 0                     #non-concurrent branch number when list_table open
		DHASH3_MAX_CONFLICT             = 4                     #Maximum conflict on each cell with dynamic hash3 table
		DHASH3_SIZE_EXTEND_FACTOR       = 5                     #Expansion factor when dynamically extending hash table
		DB_FILE_NAME_CONVERT            =                       #Patterns to map primary database files to standby database files
		HFI_HP_MODE                     = 1                     #Lock mode when hfi process partiton table.0:lock partitions,1:lock root
		TRC_LOG_MODULE                  = ALL                   #Trace log module config
		DPC_GUP_SESS_TIMEOUT            = 180                   #Deadline(Second) for sessions to disconnect from upgrade SP
		TS_AUTO_EXTEND_SIZE             = 64                    #The size to automatically extend for a tablespace
		INI_SYNC                        = 1                     #Whether sync ini parameter from primary node
		TRUNC_CHECK_MODE                = 0                     #How to check table truncate, 0:check by root table; 1:check by subpartition table; 2:check by index
		PAGE_CHECK_POLICY               = 1                     #Policy to choose when database page check failed, 0: IGNORE, 1:HALT
		STAT_CACHE_FLAG                 = 1                     #cache stat on sp
		FAST_START_MTTR_TARGET          = 0                     #The number of seconds the database takes to perform crash recovery of a single instance.
		TSMV_FILE_COPY_PLL              = 1                     #Parallel degree of file copying
		TSMV_SEND_BUF_SIZE              = 4                     #Send buffer size of tablespace moving in Mbytes
		DEFAULT_FILLFACTOR              = 0                     #default fillfactor value
		SHADOW_CHECK_INTERVAL           = 60                    #Raft shadow node archivelog thread check interval
		PL_SQL_STRIP                    = 0                     #Combination value for strip SQL from, 0:No, 1:PL, 2:PROC&FUNC, 4:PKG&OBJ, 8:TRIG, 16:ORG_SQL, 32:MERGE INTO, 64:CURSOR, 128:TRACE SQL
		WM_CONCAT_LOB                   = 1                     #Whether to return CLOB value for function wm_concat
		CHECK_CONS_NAME                 = 1                     #Whether to check the constraint name when create or rename a constraint
		MAX_LINK_SESSIONS               = 10000                 #Maximum number of DBLINK concurrent sessions
		PKG_BUILD_WAIT_TIME             = 5                     #Maximum waiting time in seconds for pkg build
		PTX_ROLLBACK                    = 0                     #Whether to support ptx rollback, 0:no, 1:yes
		LOB_READ_LOCK                    = 1                     #Whether to lock table when reading lob
		DSC_QUOTA_RELOAD_INTERVAL       = 5                     #The quota of tablespace reload interval in DSC. (1 ~ 60)
		AUDIT_FLUSH_LEVEL               = 0                     #Level to flush audit record, 0: flush by statement; 1: flush by transaction; 2: flush delay. default is 0
		SUBSCRIBE_BUF_SIZE              = 8                     #Buffer size in Mbytes for subscribe log
		SUBSCRIBE_LOG_THRESHOLD         = 256                   #Threshold in MBytes per second for subscribe log on source
		VM_PINST_COUNT                  = 10                    #How many package instances can be cached on stackframe
		INSTRUCT_COMBINE                = 0                     #Whether to combine instructions for some complex expressions
		MAX_ESESSIONS                   = 100000                #Maximum number of esessions, 0 means unlimited
		PARALLEL_THRD_TARGET            = 0                     #Maximum thread number for parallel task, 0 means unlimited
		POOL_CLEAR_FLAG                 = 0                     #free all memory when pool clear(1) or save extend blk in target(0)
		IPV6_LSNR                       = 1                     #Whether to listen to ipv6, 0: not listen, 1: listen
		DBLINK_HEALTH_CHECK             = 1                     #DBLINK connection health check, 0:no check, 1: lightweight check, 2: heavyweight check
		PTX_BI_PERCENTAGE                = 50                    #Percentage of pages allocated from recycle pool for ptx bi register
		GEO2_CONS_CHECK                  = 0                    #Whether to check geom/geog column during constraint detection
		LOCK_DICT_HASH_SIZE            = 10000                 #Hash size for lock system
		DEFAULT_HASHPARTMAP             = 1                     #Default HASHPARTMAP value when create HASH partition table
		ODCI_DEF_FETCH_NUM              = 300                   #Set pipelined using func fetch num
		DPC_RW                          = 0                     #Whether enable DPC read/write separate, 0:false; 1:default leader; 2:default follower.
		DPC_RWPERCENT                   = 100                   #Read transaction percent of using standby instance when DPC read/write separate.
		DPC_RW_SWITCH                   = 0                     #Whether transaction read_policy can switch when DPC read/write separate, 0:false; 1:switch each sql; 2:switch each request.
		USER_READ_ONLY_MODE             = 0                     #Whether to disable read-only user to set the session as non read-only. 0:no; 1:yes. default:0.
		SQLTUNE_CATEGORY               = DEFAULT               #The category of SQL profiles that take effect in the session
		ENABLE_FAST_REFCURSOR           = 1                     #Whether to enable fast refcursor
		ENET_MODE                      = 0                     #Enet site manager mode, 0:leader site only; 1:all sites.
		DPC_RW_ROUTE                   = 1                     #DPC read/write separate route policy.
		HUGE_UNIQUE_CHECK              = 0                    #whether to check uniqueness of unique index key(s) in huge table, 0:no, 1:yes.
		PLN_INDEPENDENT                 = 1                     #Whether to build independent plan without refering plans of refering objects
		DEFERRED_SEGMENT_CREATION       = 0                      #Whether enable default defered segment creation
		RAFT_EXIT_TIMEOUT               = 30                   #Raft Database exit self timeout
		CONN_HEART_BEAT_INTERVAL       = 0                     #Connection heart beat interval. 0: off.
		CONN_HEART_BEAT_TIMEOUT        = 0                     #Connection heart beat timeout. 0: off.
		MAX_CLTN_MEM_SIZE               = 4096                  #Max cltn mem size, default is 4 * 1024 M
		MERGE_OPT_FLAG                  = 0                     #merge into opt flag
		TMP_OPT_POLICY                       = 1                   #Whether to enable temp_table optimization policy, 0: no, 1: yes
		UPDATE_OPT_FLAG                 = 0                     #Optimize flag for UPDATE
		DPC_CHECK_MP_INI                 = 1                     #MP_INI check or not, 1:check and modify mp.ini;0:not check
		ARCH_SPACE_CHECK_INTERVAL       = 0                     #Interval(second) of check space in arch disk.
		ARCH_SPACE_FREE_THRESHOLD       = 0                     #Threshold(MB) of free space in arch disk.
		ELOG_ARCH_TIMEOUT              =0                      #Archive task timeout printing time, if timeout occurs, print a log (ms).
		CSCN2_CHECK_LOCK                = 0                     #Whether check lock when update/delete mpln doing cscn2
		RPKG_PROCESS_TIMEOUT              =0                      #RLOG package process timeout time, if timeout occurs, generate a record (ms).
		TMP_MAX_RESERVE_EXTENT          = 50                    #The max reserve space of temp_table extents(MB) when open temp_table optimization policy
		LOBID_MODE                      = 1                      #Lobid mode; 0: from iid, 1: from seg header
		BTR_FREE_PAGE_STACK             = 0                     #BTree free page stack, 0: off, 1: on
		RANDOM_CRYPTO                   =                                             #Crypto of random algorithm.
		PLN_BUILD_WAIT_TIME             = 5                     #Maximum waiting time in seconds for pln build
		PKG_BUILD_WAIT_POLICY           = 1                     #Package build wait policy
		ESESS_RECYCLE_TIME               = 0                     #Esess recycle interval, 0: off
		PAGE_CHECK_INDEXID              = 1                     #Whether to check page's indexid
		ENABLE_PROFILER                 = 0                     #Enable profiler in plsql for DBMS_PROFILER

#pre-load
		LOAD_TABLE                      =                       #need to pre-load table
		LOAD_HTABLE                     =                       #need to pre-load htable

#client cache
		CLT_CACHE_TABLES                =                       #Tables that can be cached in client

#redo log
		RLOG_BUF_SIZE                   = 512                   #The Number Of Log Pages In One Log Buffer (减少缓冲区)
		RLOG_POOL_SIZE                  = 128                   #Redo Log Pool Size In Megabyte (减少池大小)
		RLOG_PARALLEL_ENABLE            = 1                     #Whether to enable database to write redo logs in parallel mode
		RLOG_IGNORE_TABLE_SET           = 1                     #Whether ignore table set
		RLOG_APPEND_LOGIC               = 0                     #Type of logic records in redo logs
		RLOG_APPEND_SYSTAB_LOGIC        = 0                     #Whether to write logic records of system tables in redo logs when RLOG_APPEND_LOGIC is set as 1
		RLOG_SAFE_SPACE                 = 128                   #Free redo log size in megabytes that can be considered as a save value (减少安全空间)
		RLOG_RESERVE_THRESHOLD          = 0                     #Redo subsystem try to keep the used space of online redo less than this target
		RLOG_RESERVE_SIZE               = 4096                  #Number of reserved redo log pages for each operation (减少保留大小)
		RLOG_SEND_APPLY_MON             = 64                    #Monitor recently send or apply rlog_buf info
		RLOG_COMPRESS_LEVEL             = 3                     #The redo compress level,value in [0,10],0:do not compress (启用压缩)
		RLOG_ENC_CMPR_THREAD            = 4                     #The redo log thread number of encrypt and compress task,value in [1,64],default 4
		RLOG_PKG_SEND_ECPR_ONLY         = 0                     #Only send encrypted or compressed data to standby instance without original data
		RLOG_HASH_NAME                  =                       #The name of the hash algorithm used for Redo log
		DPC_LOG_INTERVAL                = 0                     #Only MP is valid, control MP broadcasts generating logs of specific DW type regularly, value range (0,86400), the unit is in seconds, and the value of 0 means not to generate
		RLOG_PKG_SEND_NUM               = 1                     #Need wait standby database's response message after the number of rlog packages were sent
		RLOG_RAFT_NEED_WAIT             = 3                     #Whether to wait for sending RAFT rlog package when standby database is accumulated
		RLOG_RAFT_WAIT_TIME             = 1000                  #Waiting time of sending RAFT rlog package when standby database is accumulated
		LOG_FILE_POSTFIX_NAME           = log                   #The postfix name of archive log file
		RLOG_PKG_PAGE_CRC               = 0                     #Whether set crc for every 4k data of rpkg
		RLOG_PKG_SEND_TIME              = 0                     #Time to check async standby database's response in seconds, 0: not check
		RLOG_LLOG_UPD_TO_DEL_INS        = 0                      #While updating from one partition to another, logic log is update or delete+insert; 0: update, 1: delete+insert
		RLOG_LLOG_COMPRESS            = 0                     #Whether to compress logic log, 0: not compress, 1: compress
		LOGMNR_PARSE_LOB               = 0                    #Whether to parse LOB logic log using DBMS_LOGMNR, 0: FALSE, 1: TRUE
		LOGMNR_GEN_UNDO                 = 0                     #Whether to generate SQL undo for DBMS_LOGMNR, 0: FALSE, 1: TRUE

#redo redos
		REDO_PWR_OPT                    = 1                     #Whether to enable PWR optimization when system restarted after failure
		REDO_IGNORE_DB_VERSION          = 0                     #Whether to check database version while database is redoing logs
		REDO_BUF_SIZE                   = 64                    #The max buffer size of rlog redo In Megabyte
		REDOS_BUF_SIZE                  = 1024                  #The max buffer size of rlog redo for standby In Megabyte
		REDOS_MAX_DELAY                 = 1800                  #The permitted max delay for one rlog buf redo on standby In Second
		REDOS_BUF_NUM                   = 4096                  #The max apply rlog buffer num of standby
		REDOS_PARALLEL_NUM              = 1                     #The parallel redo thread num
		REDOS_ENABLE_SELECT             = 1                     #Enable select for standby
		REDOS_FILE_PATH_POLICY          = 0                     #Data files' path policy when standby instance applies CREATE TABLESPACE redo log. 0:use the same file name under system path, 1:use the same file path under the system path
		REDOS_PAGE_CRC_CHECK            = 0                     #Whether check page crc, 0:NO, 1:YES
		REDOS_RECV_PLL_NUM              = 0                     #The parallel redo thread num in recovery
		REDOS_RPKG_PARSE_NUM            = 16                    #The parallel thread num for rpkg parse
		REDOS_RPKG_FILL_NUM             = 64                    #The max rpkg num to filled for parallel redo
		REDOS_WAIT_TIMEOUT              = 60                    #Sync standby wait dps timeout time (s).
		COMMIT_BATCH                    = 1                     #Transaction commit redo log batch flush

#transaction
		ISOLATION_LEVEL                 = 1                     #Default Transaction Isolation Level, 1: Read Commited; 3: Serializable
		DDL_WAIT_TIME                   = 10                    #Maximum waiting time in seconds for DDLs
		BLDR_WAIT_TIME                  = 10                    #Maximum waiting time in seconds for BLDR
		MPP_WAIT_TIME                   = 10                    #Maximum waiting time in seconds for locks on MPP
		FAST_RELEASE_SLOCK              = 1                     #Whether to release share lock as soon as possible
		SESS_CHECK_INTERVAL             = 3                     #Interval time in seconds for checking status of sessions
		LOCK_TID_MODE                   = 1                     #Lock mode for select-for-update operation
		LOCK_TID_UPGRADE                = 0                     #Upgrade tid lock to X mode, 0:no, 1:yes
		NOWAIT_WHEN_UNIQUE_CONFLICT     = 0                     #Whether to return immediately when unique constraint violation conflict happens
		UNDO_EXTENT_NUM                 = 4                     #Number of initial undo extents for each worker thread
		MAX_DE_TIMEOUT                  = 10                    #Maximum external function wait time in Seconds
		TRX_RLOG_WAIT_MODE              = 0                     #Trx rlog wait mode
		TRANSACTIONS                    = 75                    #Maximum number of concurrent transactions
		MVCC_RETRY_TIMES                = 5                     #Maximum retry times while MVCC conflicts happen
		MVCC_PAGE_OPT                   = 1                     #Page visible optimize for MVCC
		ENABLE_FLASHBACK                = 0                     #Whether to enable flashback function
		UNDO_RETENTION                  = 90.000                #Maximum retention time in seconds for undo pages since relative transaction is committed
		PARALLEL_PURGE_FLAG             = 0                     #flag for parallel purge of undo logs
		PSEG_RECV                       = 3                     #Whether to rollback active transactions and purge committed transactions when system restarts after failure
		ENABLE_IGNORE_PURGE_REC         = 2                     #Whether to ignore purged records when returning -7120
		ENABLE_TMP_TAB_ROLLBACK         = 1                     #enable temp table rollback
		ROLL_ON_ERR                     = 0                     #Rollback mode on Error, 0: rollback current statement 1: rollback whole transaction
		XA_TRX_IDLE_TIME                = 60                    #Xa transaction idle time
		XA_TRX_LIMIT                    = 1024                  #Maximum number of Xa transaction
		LOB_MVCC                        = 1                     #Whether LOB access in MVCC mode
		LOCK_DICT_OPT                   = 2                     #lock dict optimize
		TRX_DICT_LOCK_NUM               = 64                    #Maximum ignorable dict lock number
		DEADLOCK_CHECK_INTERVAL         = 1000                  #Time interval of deadlock check
		COMMIT_WRITE                    = IMMEDIATE,WAIT        #wait means normal commit; nowait may speed up commit, but may not assure ACID,immediate,batch just support syntax parsing
		DPC_2PC                         = 1                     #enable two-phase commit for dpc, 0: disable, 1: enable, 2/3: use snap_seq cache, 5: use local cmt_seq, 6/7: use both snap_seq cache and local cmt_seq
		SWITCH_CONN                     = 0                     #switch connect flag
		UNDO_BATCH_FAST                 = 0                     #Whether to undo batch insert fast
		FLDR_LOCK_RETRY_TIMES           = 0                     #Maximum retry times while MVCC conflicts happen
		DPC_TRX_TIMEOUT                 = 10                    #Maximum trx waiting time in seconds for DPC
		SESSION_READONLY                = 0                     #session readonly parameter value
		SELECT_LOCK_MODE                = 0                     #Whether to use operator lock mode
		ENABLE_SEC_RPTR                 = 0                     #Whether to append roll address in secondary index
		FINS_UNDO_OPT                   = 1                     #Whether to undo fast insert in optimal mode, 0:no, 1:yes
		PLSQL_AUTO_COMMIT                = 0                     #Whether to commit the transaction after each statement in plsql
		TRX_VIEW_POLICY                 = 0                     #Transactions view policy: 0: sqls in plsql will not refresh transactions view; 1: each sql in plsql will refresh transactions view
		HP_DDL_LOCK_MODE                = 0                     #DDL lock policy of horization partition table
		UNDO_SPACE                     = 0                     #Maximum UNDO space (in Gigabyte) should committed transactions used
		RECYCLEBIN                      = 0                     #Whether to enable recycle bin, 0:disable 1:drop can use recycle bin 2:drop and truncate can use recycle bin.
		RECYCLEBIN_RETENTION            = 0                     #Maximum retention time of objects in the recycle bin
		DDL_PURGE_POLICY                = 0                     #Whether to purge DDL trx immediately, 0:no, 1:yes
		LONG_TRX_VISIBLE                = 0                     #Long Transactions's visibility
		COMMIT_BATCH_TIMEOUT           = 1                     #Transaction commit redo log batch flush wait time
		MAL_WAIT_TIMEOUT               = 0                     #Mal receive timeout in seconds
		ROLL_DEL_OPT                  = 0                     #Use opt for delete rollback, 0/1
		ENABLE_ENCRYPT                  = 0                     #Encrypt Mode For Communication, 0: Without Encryption; 1: SSL Encryption; 2: Only SSL Authentication; 3: GmSSL; 4: Only SSL Encryption
		CLIENT_UKEY                     = 0                     #Client ukey, 0: all, active by Client; 1: Force client ukey Authentication
		MIN_SSL_VERSION                 = 771                   #SSL minimum version For Communication, For example, 0: all, 0x0301: TLSv1, 0x0302: TLSv1.1, 0x0303: TLSv1.2, 0x0304: TLSv1.3
		ENABLE_UDP                      = 0                     #Enable udp For Communication, 0: disable; 1: single; 2: multi
		UDP_MAX_IDLE                    = 15                    #Udp max waiting time in second
		UDP_BTU_COUNT                   = 8                     #Count of udp batch transfer units
		ENABLE_IPC                      = 0                     #Enable ipc for communication, 0: disable; 1: enable
		AUDIT_FILE_FULL_MODE            = 3                     #operation mode when audit file is full,1: delete old file; 2: no longer to write audit records 3: Combination of 1 and 2
		AUDIT_SPACE_LIMIT               = 0                     #audit space limit in Megabytes
		AUDIT_MAX_FILE_SIZE             = 100                   #maximum audit file size in Megabytes
		AUDIT_IP_STYLE                  = 0                     #IP style in audit record, 0: IP, 1: IP(hostname), default 0
		MSG_COMPRESS_TYPE               = 2                     #Flag of message compression mode
		LDAP_HOST                       =                       #LDAP Server ip
		COMM_ENCRYPT_NAME               =                       #Communication encrypt name, if it is null then the communication is not encrypted
		COMM_VALIDATE                   = 1                     #Whether to validate message
		MESSAGE_CHECK                   = 0                     #Whether to check message body
		ENABLE_EXTERNAL_CALL            = 0                     #Whether permit external call
		EXTERNAL_JFUN_PORT              = 6363                  #DmAgent port for external java fun.
		EXTERNAL_AP_PORT                = 4236                  #DmAp port for external fun.
		ENABLE_PL_SYNONYM               = 0                     #Whether try to resolve PL object name by synonym.
		FORCE_CERTIFICATE_ENCRYPTION    = 0                     #Whether to encrypt login user name and password use certificate
		REGEXP_MATCH_PATTERN            = 0                     #Regular expression match pattern, 0: support non-greedy match; 1: only support greedy match
		UNIX_SOCKET_PATHNAME            =                       #Unix socket pathname.
		RESOURCE_FLAG                   = 0                     #Flag of user's resources, 1: reset session connecting time with second
		AUTH_ENCRYPT_NAME               =                       #User password encrypt name
		IPV6_ZID_FLAG                   = 1                     #To use the auto NIC-adaption of ipv6 link-local or not, 0: No; 1: Yes
		IPV6_ZID                        =                       #The NIC Zone id to use in connection
		GRANT_SCHEMA                    = 0                     #Whether to allow to grant to schema
		EFC_USE_AP                      = 1                     #Whether to use AP when call external cfun
		PASSWORD_VERIFICATION           = 0                    #Mode of password verification, 0: default, 1: replace
		AUDIT_KEEP_DAYS                = 0                     #The minimum number of days to retain audit log files
		SSL_SECURITY_LEVEL               = 0                     #SSL security level

#compatibility
		BACKSLASH_ESCAPE                = 0                     #Escape Mode For Backslash, 0: Not Escape; 1: Escape
		STR_LIKE_IGNORE_MATCH_END_SPACE = 1                     #Whether to ignore end space of strings in like clause
		CLOB_LIKE_MAX_LEN               = 10240                 #Maximum length in kilobytes of CLOB data that can be filtered by like clause
		EXCLUDE_DB_NAME                 =                       #THE db names which DM7 server can exclude
		MS_PARSE_PERMIT                 = 0                     #Whether to support SQLSERVER's parse style
		COMPATIBLE_MODE                 = 0                     #Server compatible mode, 0:none, 1:SQL92, 2:Oracle, 3:MS SQL Server, 4:MySQL, 5:DM6, 6:Teradata, 7:PG
		ORA_DATE_FMT                    = 0                     #Whether support oracle date fmt: 0:No, 1:Yes
		JSON_MODE                       = 0                     #Json compatible mode, 0:Oracle, 1:PG, 2:MySQL
		DATETIME_FMT_MODE               = 0                     #Datetime fmt compatible mode, 0:none, 1:Oracle
		DOUBLE_MODE                     = 0                     #Calc double fold mode, 0:8bytes, 1:6bytes
		CASE_COMPATIBLE_MODE            = 1                     #Case compatible mode, 0:none, 1:Oracle(simple case), 2:Oracle(simple case new rule), 4:Oracle(bool case)
		XA_COMPATIBLE_MODE              = 0                     #XA compatible mode, 0:none, 1:Oracle, 2:MySql
		EXCLUDE_RESERVED_WORDS          =                       #Reserved words to be exclude
		COUNT_64BIT                     = 1                     #Whether to set data type for COUNT as BIGINT
		CALC_AS_DECIMAL                 = 0                     #Whether integer CALC as decimal, 0: no, 1:only DIV, 2:all only has charactor, 3:all for digit
		CMP_AS_DECIMAL                  = 0                     #Whether integer compare as decimal, 0: no, 1:part, 2:all
		CAST_VARCHAR_MODE               = 1                     #Whether to convert illegal string to special pseudo value when converting string to integer
		PL_SQLCODE_COMPATIBLE           = 0                     #Whether to set SQLCODE in PL/SQL compatible with ORACLE as far as possible
		LEGACY_SEQUENCE                 = 0                     #Whether sequence in legacy mode, 0: no, 1:yes
		DM6_TODATE_FMT                  = 0                     #To Date' HH fmt hour system, 0: 12(default) , 1: 24(DM6)
		MILLISECOND_FMT                 = 1                     #Whether To show TO_CHAR' millisecond, 0: no, 1:yes
		NLS_DATE_FORMAT                 =                       #Date format string
		NLS_TIME_FORMAT                 =                       #Time format string
		NLS_TIMESTAMP_FORMAT            =                       #Timestamp format string
		NLS_TIME_TZ_FORMAT              =                       #Time_tz format string
		NLS_TIMESTAMP_TZ_FORMAT         =                       #Timestamp_tz format string
		PK_MAP_TO_DIS                   = 0                     #Whether map pk cols into distributed cols automatically
		PROXY_PROTOCOL_MODE             = 0                     #PROXY PROTOCOL mode, 0: close; 1: open
		SPACE_COMPARE_MODE              = 0                     #Whether to compare suffix space of strings, 0: default, 1: yes
		DATETIME_FAST_RESTRICT          = 1                     #Wherther To DATE's datetime with time.default:1. 1: No. 0: Yes.
		BASE64_LINE_FLAG                = 1                     #Whether base64 encode persection with line flag: CR and LF. default: 1. 1:YES. 2:NO.
		MY_STRICT_TABLES                = 1                     #Whether to tolerate data too long or varchar cast digit in MYSQL compatible mode. default: 1. 0:Both. 1:Only varchar cast digit. 2:Only data too long. 3:Neither.
		IN_CONTAINER                    = 0                     #judge if dm is run in container.default:0.
		NUMBER_MODE                     = 0                     #NUMBER mode, 0:DM; 1:ORACLE
		NLS_SORT_TYPE                   = 0                     #Chinese sort type, 0:default 1:pinyin 2:stroke 3:radical 4:thai 5:korean 6:binary 7:ganzhi
		ENABLE_RLS                      = 0                     #Whether enable rls
		NVARCHAR_LENGTH_IN_CHAR         = 1                     #Whether nchar/nvarchar convert to character/varchar(n char) 1:yes,0:no
		PARAM_DOUBLE_TO_DEC             = 0                     #Whether to convert double parameter to decimal, 0: disable, 1: enable with check, 2, enable without check
		INSERT_COLUMN_MATCH             = 0                     #Insert column match, 0: DM, 1: Aster, 2: PG
		AUTO_INCREMENT_INCREMENT        = 1                     #Increment for each insert in AUTO_INCREMENT column
		AUTO_INCREMENT_OFFSET           = 1                     #Offset for values in AUTO_INCREMENT column
		NO_AUTO_VALUE_ON_ZERO           = 1                     #Whether using default value when inserting 0
		IFUN_DATETIME_MODE              = 0                     #The definition of DATETIME in sys function, 0:DATETIME(6), 1:DATETIME(9)
		CAST_DIGIT_MODE                 = 1                     #Whether to convert stronger digit type to weak digit type col
		VIEW_ACCESS_MODE                = 0                     #View access mode, 0:DM, 1:Oracle
		ENABLE_FULL_WIDTH               = 1                     #Enable full-width characters. 0: no, 1: yes.
		IFUN_LEN_OPT                    = 3                     #How to adjust the ifun return string's length
		FLOAT_MODE                      = 0                     #FLOAT mode, 0:DM; 1:ORACLE
		DECIMAL_ENHANCED                = 1                     #Whether to enhance decimal calculate and storage
		DOUBLE_TO_DEC_MASK              = 1                     #Mask code used when casting double or float to decimal, 0: %g automaticly; 1: %e directly when %g choose %e; 2: restore to shortest decimal
		ARG_VARCHAR_ADJUST              = 0                     #Control whether to enlarge the length of the char/varchar type parameter,0: Not enlarge, 1: enlarge, 2: compatible with oracle
		CTI_SCORE_MODE                  = 1                     #How to calculate context index score, 0: Simplified algorithm, get score only using key word frequence; 1: Complete algorithm, get score from whole data;
		CASE_CONVERSION_ENHANCED         = 1                    #Whether to enhanced case conversion and compare, 0: default, 1: enhanced
		TIME_MODE                       = 1                     #TIME compatible mode. 0: parse from year; 1: parse from hour or year according COMPATIBLE_MODE
		CAST_CLOB_MODE                  = 1                     #Whether to push down cast for overlength string concat as clob. default:1. 1: yes, 0: no
		MD5_TYPE                        = 0                     #The type of FUNCTION MD5 return. default:0. 0: varbianry, 1: varchar
		VIEW_AUTHID_CHECK               = 1                     #Whether to check the user-defined view definition contains authid current_user. default:1, 1: yes, 0: no.
		ARG_DECIMAL_ADJUST              = 0                     #Control whether to enlarge the length of the decimal type parameter, 0: Not enlarge, 1: enlarge
		ENC_TYPE                        = 1                     #Type of encryption, 0: No EVP 1: EVP first
		ORA_REVERSE_MODE                = 1                     #Reverse by character or byte: 0:Byte, 1:Character
		DEC_BIN_CVT_MODE                = 0                     #DEC & BINARY convert mode, 0:support binary convert to dec only;1:support binary and dec conversion
		RECORD_COMPARE_MODE             = 1                     #Compare mode for record data type, default 1, 0: basic, 1: strict
		NLS_NUMERIC_CHARACTERS          = .,                    #Specify the decimal point character and group separator represented by the characters D and G in fmt
		JSONB_CHECK_MODE               = 1                     #Jsonb check mode, 0: not check, 1: check using CRC32
		RETURN_INTO_FLAG                = 0                     #RETURNING INTO multiple rows to param. 0: off, 1: on.
		ARGUMENT_MATCHING_MODE          = 1                     #Matching mode when the number of function/procedure arguments value does not match the number defined
		USE_JSON_DATATYPE               = 1                     #Whether to use json datatype, 0: not use, 1: use json datatype
		CHAR_CHECK_INTEGRATED          = 0                      #Whether to check character integrity, 0: default, 1: all check, 2: not check
		LITERAL_PREFIX                  = 1                     #Allow prefix on literals, 0: no, 1:yes
		DROP_CASCADE_VIEW               = 0                     #Whether to drop cascade view while dropping table or view
		BATCH_INSERT_ROWS               = 10                    #Batch insert rows
		INDEX_FINS_FLAG                 = 0                    #Flag of fast insert second index

#request trace
		SVR_LOG_NAME                    = SLOG_ALL              #Using which sql log sys in sqllog.ini
		SVR_LOG                         = 0                     #Whether the Sql Log sys Is open or close. 1:open, 0:close, 2:use switch and detail mode. 3:use not switch and simple mode.

#system trace
		GDB_THREAD_INFO                 = 0                     #Generate gdb thread info while dm_sys_halt. 1: yes; 0: no
		TRACE_PATH                      =                       #System trace path name
		SVR_OUTPUT                      = 0                     #Whether print at background
		SVR_ELOG_FREQ                   = 0                     #How to switch svr elog file. 0: by month. 1: by day, 2: by hour, 3, by limit
		ENABLE_OSLOG                    = 0                     #Whether to enable os log
		LOG_IN_SHOW_DEBUG               = 2147483647            #Whether record log info
		TRC_LOG                         = 0                     #Whether the trace Log sys Is open or close. 0:close, other: switch_mod*4 + asyn_flag*2 + 1(switch_mode: 0:no_switch;1:by num;2:by size;3:by interval).
		ELOG_SWITCH_LIMIT               = 0                     #The upper limit to switch elog
		ELOG_ERR_ARR                    =                       #dmerrs need to generate elog
		DICT_COMPLETE_CHECK             = 0                     #Whether to halt when get incomplete dict
		ELOG_FLAG                       = 0                     #whether elog file name is fixed . 0: not fixed. 1: fixed
		SMALL_TABLE_THRESHOLD           = 20480                 #The boundary between short tables and large tables
		ELOG_LANGUAGE_FLAG              = 0                     #elog_language_flag, 1:CN, 0:EN
		CORE_DUMP_FLAG                  = 1                     #Whether to dump some info before core
		AP_PORT_NUM                     = 0                     #Port number on which the database ap will listen

#monitor
		ENABLE_MONITOR                  = 1                     #Whether to enable monitor
		MONITOR_SQL_EXEC                = 0                     #Whether to enable monitor sql execute
		ENABLE_FREQROOTS                = 0                     #Whether to collect pages that used frequently
		MAX_FREQ_ROOTS                  = 200000                #Maximum number of frequently used pages that will be collected
		MIN_FREQ_CNT                    = 100000                #Minimum access counts of page that will be collected as frequently used pages
		LARGE_MEM_THRESHOLD             = 10000                 #Large mem used threshold by k
		ENABLE_MONITOR_DMSQL            = 1                     #Flag of performance monitoring:sql or method exec time.0: NO. 1: YES.
		ENABLE_TIMER_TRIG_LOG           = 0                     #Whether to enable timer trigger log
		IO_TIMEOUT                      = 300                   #Maximum time in seconds that read from disk or write to disk
		ENABLE_MONITOR_BP               = 1                     #Whether to enable monitor bind param. 1: TRUE. 0:FALSE. default:1
		LONG_EXEC_SQLS_CNT              = 1000                  #Max row count of V$LONG_EXEC_SQLS
		SYSTEM_LONG_EXEC_SQLS_CNT       = 300                   #Max row count of V$SYSTEM_LONG_EXEC_SQLS
		SQL_HISTORY_CNT                 = 10000                 #Max row count of V$SQL_HISTORY
		BP_ITEM_MAXSIZE                 = 50                    #The maximum memory size of BINDDATA column on V$SQL_BINDDATA_HISTORY in Kbytes
		MONITOR_SQL_PARSE               = 0                     #Whether to enable monitor sql parse
		RT_ERR_HISTORY_CNT              = 5000                  #Max row count of V$RUNTIME_ERR_HISTORY
		ENABLE_MONITOR_PLNHIST          = 0                     #Whether to enable monitor sql plan history
		BINDDATA_COLTYPE                = 0                     #The binddata col datatype of V$SQL_BINDDATA_HISTORY. default:1. 1: blob, 0: varbinary
		DMSQL_ET_CNT                 = 10000                 #Max row count of V$DMSQL_EXEC_TIME
		DMSQL_EXEC_THRESHOLD           = 0                     #Monitoring threshold for DMSQL execution time
		MONITOR_COLUMNS                 = 0                     #Whether to monitor the filtering condition feature infomation of the columns

#data watch
		DW_MAX_SVR_WAIT_TIME            = 0                     #Maximum time in seconds that server will wait for DMWATCHER to startup
		DW_INACTIVE_INTERVAL            = 60                    #Time in seconds that used to determine whether DMWATCHER exist
		DW_PORT                         = 0                     #Instance tcp port for watch2
		ALTER_MODE_STATUS               = 1                     #Whether to permit database user to alter database mode and status by SQLs, 2: yes, 1: yes when dmwatcher ERROR, 0: no
		ENABLE_OFFLINE_TS               = 1                     #Whether tablespace can be offline
		SESS_FREE_IN_SUSPEND            = 60                    #Time in seconds for releasing all sessions in suspend mode after archive failed
		SUSPEND_WORKER_TIMEOUT          = 180                   #Suspend worker thread timeout in seconds
		DW_CONSISTENCY_CHECK            = 0                     #Whether to check consistency for standby database, 1: yes, 0: no
		DW_ARCH_SPACE_CHECK             = 0                     #Whether to check archive space for standby database, 1: yes, 0: no
		DW_SUSPEND_TIME                 = 60                    #The duration of SUSPEND status in seconds for async recovery, 0: not check.
		DW_ARCH_HANG_CHECK              = 0                     #Whether to check archive space for primary database, 1: yes, 0: no

#for context index
		CTI_HASH_SIZE                   = 100000                #the hash table size for context index query
		CTI_HASH_BUF_SIZE               = 50                    #the hash table cache size in Megabytes for context index query
		USE_RDMA                        = 0                     #Whether to use rdma
		MAX_SEND_WR                     = 128                   #Maximum number of outstanding send requests in the send queue
		MAX_RECV_WR                     = 512                   #Maximum number of outstanding receive requests in the receive queue
		CQ_MOD                          = 100                   #Every N WRs will generate one completion queue event(CQE)

#configuration file
		MAL_INI                         = 0                     #dmmal.ini
		ARCH_INI                        = 0                     #dmarch.ini
		LLOG_INI                        = 0                     #dmllog.ini
		TIMER_INI                       = 0                     #dmtimer.ini
		MPP_INI                         = 0                     #dmmpp.ini
		DMTHRD_INI                      = 0                     #dmthrd.ini
		DSC_N_CTLS                      = 1028096               #Number Of LBS/GBS ctls
		DSC_N_POOLS                     = 19                    #Number Of LBS/GBS pools
		DSC_USE_SBT                     = 1                     #Use size balanced tree
		DSC_TRX_CMT_LSN_SYNC            = 3                     #Whether to adjust lsn when trx commit
		DSC_ENABLE_MONITOR              = 1                     #Whether to monitor request time
		DSC_TRX_VIEW_SYNC               = 1                     #Whether to wait response after broadcast trx view to other ep
		DSC_TRX_VIEW_BRO_INTERVAL       = 1000                  #Time interval of trx view broadcast
		DSC_REMOTE_READ_MODE            = 1                     #PAGE remote read optimize mode
		DSC_RESERVE_PERCENT             = 0.080                 #Start ctl reserve percent
		DSC_TABLESPACE_BALANCE          = 0                     #Enable DSC load balance by tablespace
		DSC_INSERT_LOCK_ROWS            = 0                     #Insert extra lock rows for DSC
		DSC_CRASH_RECV_POLICY           = 0                     #Policy of handling node crash
		DSC_LBS_REVOKE_DELAY            = 0                     #LBS revoke delay
		DSC_REQUEST_TIMEOUT             = 3600                  #DSC request timeout in seconds
		DSC_SLOT_WAIT_TIMEOUT           = 0                     #DSC request timeout in seconds
		DSC_IGNORE_INI_CHECK            = 0                     #Whether check dm.ini between dsc eps
		DSC_DV_PAGE_ACCESS_MODE         = 1                     #DSC dynamic table page get mode
		DSC_PI_MODE                    = 0                     #Whether to enable PI
		DSC_LBS_OPT_FLAG               = 1                     #Flag for optimization of DSC lbs request
		DCR_ONE_NODE                   = 0                      #Whether to reserve hpc logic like lbs/gbs in single mode

#other
		IDLE_MEM_THRESHOLD              = 50                    #minimum free memory warning size in Megabytes
		IDLE_DISK_THRESHOLD             = 1000                  #minimum free disk space warning size in Megabytes
		IDLE_SESS_THRESHOLD             = 5                     #minimum available session threshold value
		ENABLE_PRISVC                   = 0                     #Whether to enable priority service
		HA_INST_CHECK_IP                =                       #HA instance check IP
		HA_INST_CHECK_PORT              = 65534                 #HA instance check port
		PWR_FLUSH_PAGES                 = 10000                 #Make special PWR rrec when n pages flushed
		REDO_UNTIL_LSN                  =                       #redo until lsn
		IGNORE_FILE_SYS_CHECK           = 1                     #ignore file sys check while startup
		FILE_SCAN_PERCENT               = 100.00                #percent of data scanned when calculating file used space
		STARTUP_CHECKPOINT              = 0                     #checkpoint immediately when startup after redo
		CHECK_SVR_VERSION               = 1                     #Whether to check server version
		ID_RECYCLE_FLAG                 = 0                     #Enable ID recycle
		ENABLE_CREATE_BM_INDEX_FLAG     = 1                     #Allow bitmap index to be created
		CVIEW_STAR_WITH_PREFIX          = 1                     #Whether append prefix for star item when create view
		ENABLE_SEQ_REUSE                = 0                     #Whether allow reuse sequence expressions
		RLS_CACHE_SIZE                  = 100000                #Max number of objects for RLS cache.
		ID_RECYCLE_THRESHOLD            = 60                    #ID recycle threshold
		BAK_USE_AP                      = 1                     #backup use assistant plus-in, 1:use AP; 2:not use AP. default 1.
		BAK_DIRECT_IO                   = 0                     #whether to enable direct io during backup
		BAK_SAFE_CHECK                  = 7                     #Safety check policy of backup
		BAK_TIMEOUT                     = 30                    #Timeout interval of operations during backup
		ENABLE_BCT                      = 0                     #Whether to enable BCT
		BCT_POOL_SIZE                   = 256                   #BCT memory pool size in Megabytes
		ENABLE_BRCFG                    = 0                     #Whether to enable BRCFG
