{"Id": "d8e190c716ce9796edfe1f18ab7684741a8beffeb28b2226463e937dcab617c5", "Created": "2025-07-18T06:23:16.614533861Z", "Path": "/usr/local/bin/docker-entrypoint.sh", "Args": ["dmserver"], "State": {"Status": "exited", "Running": false, "Paused": false, "Restarting": false, "OOMKilled": false, "Dead": false, "Pid": 0, "ExitCode": 0, "Error": "", "StartedAt": "2025-07-18T06:23:16.748464132Z", "FinishedAt": "2025-07-18T06:25:10.065023619Z", "Health": {"Status": "unhealthy", "FailingStreak": 0, "Log": [{"Start": "2025-07-18T06:23:21.984820951Z", "End": "2025-07-18T06:23:22.010541342Z", "ExitCode": 0, "Output": "Active Internet connections (only servers)\nProto Recv-Q Send-Q Local Address           Foreign Address         State       PID/Program name    \ntcp        0      0 127.0.0.11:33621        0.0.0.0:*               LISTEN      -                   \n"}, {"Start": "2025-07-18T06:23:52.01202168Z", "End": "2025-07-18T06:23:52.033060007Z", "ExitCode": 0, "Output": "Active Internet connections (only servers)\nProto Recv-Q Send-Q Local Address           Foreign Address         State       PID/Program name    \ntcp        0      0 127.0.0.11:33621        0.0.0.0:*               LISTEN      -                   \ntcp6       0      0 :::5236                 :::*                    LISTEN      1/./dmserver        \n"}, {"Start": "2025-07-18T06:24:22.033471933Z", "End": "2025-07-18T06:24:22.052758684Z", "ExitCode": 0, "Output": "Active Internet connections (only servers)\nProto Recv-Q Send-Q Local Address           Foreign Address         State       PID/Program name    \ntcp        0      0 127.0.0.11:33621        0.0.0.0:*               LISTEN      -                   \ntcp6       0      0 :::5236                 :::*                    LISTEN      1/./dmserver        \n"}, {"Start": "2025-07-18T06:24:52.052043154Z", "End": "2025-07-18T06:24:52.071435496Z", "ExitCode": 0, "Output": "Active Internet connections (only servers)\nProto Recv-Q Send-Q Local Address           Foreign Address         State       PID/Program name    \ntcp        0      0 127.0.0.11:33621        0.0.0.0:*               LISTEN      -                   \ntcp6       0      0 :::5236                 :::*                    LISTEN      1/./dmserver        \n"}]}}, "Image": "sha256:8305afe53f393544dc9f462e0f59cf21c290f9bab5059b7322a124cf1f24d049", "ResolvConfPath": "/var/lib/docker/containers/d8e190c716ce9796edfe1f18ab7684741a8beffeb28b2226463e937dcab617c5/resolv.conf", "HostnamePath": "/var/lib/docker/containers/d8e190c716ce9796edfe1f18ab7684741a8beffeb28b2226463e937dcab617c5/hostname", "HostsPath": "/var/lib/docker/containers/d8e190c716ce9796edfe1f18ab7684741a8beffeb28b2226463e937dcab617c5/hosts", "LogPath": "/var/lib/docker/containers/d8e190c716ce9796edfe1f18ab7684741a8beffeb28b2226463e937dcab617c5/d8e190c716ce9796edfe1f18ab7684741a8beffeb28b2226463e937dcab617c5-json.log", "Name": "/dm8-server", "RestartCount": 0, "Driver": "overlay2", "Platform": "linux", "MountLabel": "", "ProcessLabel": "", "AppArmorProfile": "", "ExecIDs": null, "HostConfig": {"Binds": null, "ContainerIDFile": "", "LogConfig": {"Type": "json-file", "Config": {}}, "NetworkMode": "dm8_docker_network", "PortBindings": {"5236/tcp": [{"HostIp": "", "HostPort": "5236"}]}, "RestartPolicy": {"Name": "unless-stopped", "MaximumRetryCount": 0}, "AutoRemove": false, "VolumeDriver": "", "VolumesFrom": null, "ConsoleSize": [0, 0], "CapAdd": null, "CapDrop": null, "CgroupnsMode": "private", "Dns": null, "DnsOptions": null, "DnsSearch": null, "ExtraHosts": [], "GroupAdd": null, "IpcMode": "private", "Cgroup": "", "Links": null, "OomScoreAdj": 0, "PidMode": "", "Privileged": false, "PublishAllPorts": false, "ReadonlyRootfs": false, "SecurityOpt": null, "UTSMode": "", "UsernsMode": "", "ShmSize": 67108864, "Runtime": "runc", "Isolation": "", "CpuShares": 0, "Memory": 0, "NanoCpus": 0, "CgroupParent": "", "BlkioWeight": 0, "BlkioWeightDevice": null, "BlkioDeviceReadBps": null, "BlkioDeviceWriteBps": null, "BlkioDeviceReadIOps": null, "BlkioDeviceWriteIOps": null, "CpuPeriod": 0, "CpuQuota": 0, "CpuRealtimePeriod": 0, "CpuRealtimeRuntime": 0, "CpusetCpus": "", "CpusetMems": "", "Devices": null, "DeviceCgroupRules": null, "DeviceRequests": null, "MemoryReservation": 0, "MemorySwap": 0, "MemorySwappiness": null, "OomKillDisable": null, "PidsLimit": null, "Ulimits": null, "CpuCount": 0, "CpuPercent": 0, "IOMaximumIOps": 0, "IOMaximumBandwidth": 0, "Mounts": [{"Type": "volume", "Source": "dm8_data_volumes", "Target": "/opt/dmdbms/data", "VolumeOptions": {}}, {"Type": "volume", "Source": "dm8_logs_volumes", "Target": "/opt/dmdbms/log", "VolumeOptions": {}}], "MaskedPaths": ["/proc/asound", "/proc/acpi", "/proc/interrupts", "/proc/kcore", "/proc/keys", "/proc/latency_stats", "/proc/timer_list", "/proc/timer_stats", "/proc/sched_debug", "/proc/scsi", "/sys/firmware", "/sys/devices/virtual/powercap"], "ReadonlyPaths": ["/proc/bus", "/proc/fs", "/proc/irq", "/proc/sys", "/proc/sysrq-trigger"]}, "GraphDriver": {"Data": {"ID": "d8e190c716ce9796edfe1f18ab7684741a8beffeb28b2226463e937dcab617c5", "LowerDir": "/var/lib/docker/overlay2/832b08dc57c739897330478be4947ca9b8f5528f6310cd999f88a51ca6f36fd9-init/diff:/var/lib/docker/overlay2/owgdnq5q0duii4ml3ijbx9gh5/diff:/var/lib/docker/overlay2/rpwu7w4dbe12nubt8n7e58oel/diff:/var/lib/docker/overlay2/xwda5itefmbeyqcta3fjdhm39/diff:/var/lib/docker/overlay2/wyr0koypmtjz2nwmwz66b3t2q/diff:/var/lib/docker/overlay2/ybtk9o9npyare0x58qoh7lmtd/diff:/var/lib/docker/overlay2/egvknv1647v3ivmi8mnvonsid/diff:/var/lib/docker/overlay2/9170vfipcx5kas2r7vxl543n4/diff:/var/lib/docker/overlay2/c29lqfb7j8okxzjgvnu7fmi57/diff:/var/lib/docker/overlay2/vwgqg1j1kcatpflz69z4jv7vg/diff:/var/lib/docker/overlay2/m9xz8r6pfziy9e63fknfo39dl/diff:/var/lib/docker/overlay2/72cbd796fe648affe98124915c7b7020e26e8cc4c38a8b8027c1b604428634a2/diff", "MergedDir": "/var/lib/docker/overlay2/832b08dc57c739897330478be4947ca9b8f5528f6310cd999f88a51ca6f36fd9/merged", "UpperDir": "/var/lib/docker/overlay2/832b08dc57c739897330478be4947ca9b8f5528f6310cd999f88a51ca6f36fd9/diff", "WorkDir": "/var/lib/docker/overlay2/832b08dc57c739897330478be4947ca9b8f5528f6310cd999f88a51ca6f36fd9/work"}, "Name": "overlay2"}, "Mounts": [{"Type": "volume", "Name": "dm8_data_volumes", "Source": "/var/lib/docker/volumes/dm8_data_volumes/_data", "Destination": "/opt/dmdbms/data", "Driver": "local", "Mode": "z", "RW": true, "Propagation": ""}, {"Type": "volume", "Name": "dm8_logs_volumes", "Source": "/var/lib/docker/volumes/dm8_logs_volumes/_data", "Destination": "/opt/dmdbms/log", "Driver": "local", "Mode": "z", "RW": true, "Propagation": ""}], "Config": {"Hostname": "d8e190c716ce", "Domainname": "", "User": "dmdba", "AttachStdin": false, "AttachStdout": true, "AttachStderr": true, "ExposedPorts": {"5236/tcp": {}}, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": ["DM_INSTANCE_NAME=DMSERVER", "DM_PORT=5236", "DM_PWD=GDYtd@2025", "DM_DB_NAME=DAMENG", "PATH=/opt/dmdbms/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin", "DM_HOME=/opt/dmdbms", "LD_LIBRARY_PATH=/opt/dmdbms/bin:"], "Cmd": ["dmserver"], "Healthcheck": {"Test": ["CMD", "netstat", "-tlnp", "|", "grep", ":5236"], "Interval": 30000000000, "Timeout": 10000000000, "StartPeriod": 120000000000, "Retries": 3}, "Image": "dm8:latest", "Volumes": null, "WorkingDir": "/opt/dmdbms", "Entrypoint": ["/usr/local/bin/docker-entrypoint.sh"], "OnBuild": null, "Labels": {"com.docker.compose.config-hash": "3d90c2a172ae63c0d25c1d72ea4f1b6e801d8321dcb9b49c1b03ecc7aac4bf77", "com.docker.compose.container-number": "1", "com.docker.compose.depends_on": "", "com.docker.compose.image": "sha256:8305afe53f393544dc9f462e0f59cf21c290f9bab5059b7322a124cf1f24d049", "com.docker.compose.oneoff": "False", "com.docker.compose.project": "dm8-docker", "com.docker.compose.project.config_files": "D:\\Code\\MicrosoftCode\\dm8-docker\\docker-compose.yml", "com.docker.compose.project.working_dir": "D:\\Code\\MicrosoftCode\\dm8-docker", "com.docker.compose.service": "dm8", "com.docker.compose.version": "2.34.0"}}, "NetworkSettings": {"Bridge": "", "SandboxID": "", "SandboxKey": "", "Ports": {}, "HairpinMode": false, "LinkLocalIPv6Address": "", "LinkLocalIPv6PrefixLen": 0, "SecondaryIPAddresses": null, "SecondaryIPv6Addresses": null, "EndpointID": "", "Gateway": "", "GlobalIPv6Address": "", "GlobalIPv6PrefixLen": 0, "IPAddress": "", "IPPrefixLen": 0, "IPv6Gateway": "", "MacAddress": "", "Networks": {"dm8_docker_network": {"IPAMConfig": null, "Links": null, "Aliases": ["dm8-server", "dm8"], "MacAddress": "", "DriverOpts": null, "GwPriority": 0, "NetworkID": "66a4b0ba6cd65faf98baf10177c335fdec29b052efc194767f17473a349c164f", "EndpointID": "", "Gateway": "", "IPAddress": "", "IPPrefixLen": 0, "IPv6Gateway": "", "GlobalIPv6Address": "", "GlobalIPv6PrefixLen": 0, "DNSNames": ["dm8-server", "dm8", "d8e190c716ce"]}}}}