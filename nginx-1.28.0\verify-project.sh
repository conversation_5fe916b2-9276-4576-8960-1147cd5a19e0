#!/bin/bash

# nginx 1.28.0 项目文件完整性验证脚本
# 用于检查项目文件是否完整，可以进行离线构建

set -e

echo "================================================================================"
echo "                    nginx 1.28.0 项目文件完整性验证"
echo "================================================================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_file() {
    local file=$1
    local description=$2
    local required=${3:-true}
    
    if [ -f "$file" ]; then
        echo -e "${GREEN}✅${NC} $description: $file"
        return 0
    else
        if [ "$required" = "true" ]; then
            echo -e "${RED}❌${NC} $description缺失: $file"
            return 1
        else
            echo -e "${YELLOW}⚠️${NC}  $description可选: $file (缺失)"
            return 0
        fi
    fi
}

check_dir() {
    local dir=$1
    local description=$2
    local min_files=$3
    local required=${4:-true}
    
    if [ -d "$dir" ]; then
        local file_count=$(find "$dir" -type f | wc -l)
        if [ "$file_count" -ge "$min_files" ]; then
            echo -e "${GREEN}✅${NC} $description: $dir ($file_count 个文件)"
            return 0
        else
            echo -e "${RED}❌${NC} $description文件不足: $dir (需要至少 $min_files 个文件，实际 $file_count 个)"
            return 1
        fi
    else
        if [ "$required" = "true" ]; then
            echo -e "${RED}❌${NC} $description目录不存在: $dir"
            return 1
        else
            echo -e "${YELLOW}⚠️${NC}  $description目录可选: $dir (不存在)"
            return 0
        fi
    fi
}

# 初始化计数器
TOTAL_CHECKS=0
FAILED_CHECKS=0
WARNING_CHECKS=0

# 检查核心构建文件
echo -e "${BLUE}=== 检查核心构建文件 ===${NC}"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
check_file "Dockerfile" "Docker构建文件" || FAILED_CHECKS=$((FAILED_CHECKS + 1))

TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
check_file "scripts/build-nginx.sh" "nginx编译脚本" || FAILED_CHECKS=$((FAILED_CHECKS + 1))

TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
check_file "config/nginx.conf" "nginx主配置文件" || FAILED_CHECKS=$((FAILED_CHECKS + 1))

TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
check_file "config/default.conf" "默认站点配置" || FAILED_CHECKS=$((FAILED_CHECKS + 1))

# 检查源码包
echo ""
echo -e "${BLUE}=== 检查nginx源码包 ===${NC}"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if check_file "packages/nginx-1.28.0.tar.gz" "nginx 1.28.0源码包"; then
    # 检查文件大小
    if [ -f "packages/nginx-1.28.0.tar.gz" ]; then
        file_size=$(stat -c%s "packages/nginx-1.28.0.tar.gz" 2>/dev/null || stat -f%z "packages/nginx-1.28.0.tar.gz" 2>/dev/null || echo "0")
        if [ "$file_size" -gt 500000 ]; then
            echo -e "${GREEN}✅${NC} 文件大小正常: $(echo $file_size | awk '{printf "%.1fMB", $1/1024/1024}')"
        else
            echo -e "${YELLOW}⚠️${NC}  文件大小异常: $(echo $file_size | awk '{printf "%.1fMB", $1/1024/1024}')，可能下载不完整"
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
        fi
    fi
else
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
    echo -e "${YELLOW}💡${NC} 提示: 运行 ./download-packages.sh 下载nginx源码包"
fi

# 检查RPM依赖包
echo ""
echo -e "${BLUE}=== 检查RPM依赖包 ===${NC}"
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if check_dir "centos7-rpms" "CentOS 7 RPM包" 20; then
    # 检查关键RPM包
    key_packages=(
        "gcc-*.rpm"
        "openssl-*.rpm"
        "pcre-*.rpm"
        "zlib-*.rpm"
    )
    
    for pattern in "${key_packages[@]}"; do
        if ls centos7-rpms/$pattern 1> /dev/null 2>&1; then
            count=$(ls centos7-rpms/$pattern | wc -l)
            echo -e "${GREEN}✅${NC} 找到关键包: $pattern ($count 个)"
        else
            echo -e "${YELLOW}⚠️${NC}  关键包缺失: $pattern"
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
        fi
    done
else
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
    echo -e "${YELLOW}💡${NC} 提示: 运行 ./download-packages.sh 下载RPM依赖包"
fi

# 检查脚本文件
echo ""
echo -e "${BLUE}=== 检查脚本文件 ===${NC}"
scripts=(
    "download-packages.sh:包下载脚本:false"
    "build-offline.sh:离线构建脚本:true"
    "test-nginx.sh:测试脚本:true"
    "package-for-offline.sh:打包脚本:false"
)

for script_info in "${scripts[@]}"; do
    IFS=':' read -r script desc required <<< "$script_info"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if ! check_file "$script" "$desc" "$required"; then
        if [ "$required" = "true" ]; then
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
        else
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
        fi
    fi
done

# 检查文档文件
echo ""
echo -e "${BLUE}=== 检查文档文件 ===${NC}"
docs=(
    "README.md:项目说明文档:true"
    "指导教程.html:详细教程文档:true"
    "packages/README.txt:源码包说明:false"
    "centos7-rpms/README.txt:RPM包说明:false"
)

for doc_info in "${docs[@]}"; do
    IFS=':' read -r doc desc required <<< "$doc_info"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if ! check_file "$doc" "$desc" "$required"; then
        if [ "$required" = "true" ]; then
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
        else
            WARNING_CHECKS=$((WARNING_CHECKS + 1))
        fi
    fi
done

# 检查脚本执行权限
echo ""
echo -e "${BLUE}=== 检查脚本执行权限 ===${NC}"
executable_scripts=(
    "build-offline.sh"
    "test-nginx.sh"
    "download-packages.sh"
    "package-for-offline.sh"
    "scripts/build-nginx.sh"
)

for script in "${executable_scripts[@]}"; do
    if [ -f "$script" ]; then
        if [ -x "$script" ]; then
            echo -e "${GREEN}✅${NC} $script 有执行权限"
        else
            echo -e "${YELLOW}⚠️${NC}  $script 缺少执行权限，正在修复..."
            chmod +x "$script"
            echo -e "${GREEN}✅${NC} $script 执行权限已修复"
        fi
    fi
done

# 显示统计信息
echo ""
echo -e "${BLUE}=== 项目统计信息 ===${NC}"

if [ -d "packages" ]; then
    echo "nginx源码包:"
    ls -lh packages/ 2>/dev/null || echo "  (空目录)"
fi

if [ -d "centos7-rpms" ]; then
    rpm_count=$(find centos7-rpms -name "*.rpm" | wc -l)
    if [ "$rpm_count" -gt 0 ]; then
        rpm_size=$(du -sh centos7-rpms 2>/dev/null | cut -f1)
        echo "RPM包: $rpm_count 个，总大小: $rpm_size"
    else
        echo "RPM包: 0 个"
    fi
fi

total_size=$(du -sh . 2>/dev/null | cut -f1)
echo "项目总大小: $total_size"

# 显示验证结果
echo ""
echo "================================================================================"
echo -e "${BLUE}                              验证结果汇总${NC}"
echo "================================================================================"

echo "总检查项: $TOTAL_CHECKS"
echo -e "通过检查: ${GREEN}$((TOTAL_CHECKS - FAILED_CHECKS - WARNING_CHECKS))${NC}"
if [ $WARNING_CHECKS -gt 0 ]; then
    echo -e "警告项目: ${YELLOW}$WARNING_CHECKS${NC}"
fi
if [ $FAILED_CHECKS -gt 0 ]; then
    echo -e "失败项目: ${RED}$FAILED_CHECKS${NC}"
fi

echo ""
if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "${GREEN}🎉 项目文件验证通过！可以进行离线构建。${NC}"
    echo ""
    echo "下一步操作："
    echo "1. 构建镜像: ./build-offline.sh"
    echo "2. 测试验证: ./test-nginx.sh"
    echo "3. 查看教程: 打开 指导教程.html"
else
    echo -e "${RED}❌ 项目文件不完整，无法进行离线构建。${NC}"
    echo ""
    echo "解决方案："
    if [ ! -f "packages/nginx-1.28.0.tar.gz" ] || [ ! -d "centos7-rpms" ] || [ $(find centos7-rpms -name "*.rpm" | wc -l) -lt 20 ]; then
        echo "1. 运行下载脚本: ./download-packages.sh"
    fi
    echo "2. 检查网络连接和磁盘空间"
    echo "3. 重新运行验证: ./verify-project.sh"
fi

echo "================================================================================"

exit $FAILED_CHECKS
