# 基于CentOS 7的nginx离线编译镜像
FROM centos:7

# 设置工作目录
WORKDIR /tmp/build

# 复制所有必要的文件到容器中
COPY packages/ /tmp/packages/
COPY centos7-rpms/ /tmp/centos7-rpms/
COPY scripts/ /tmp/scripts/
COPY config/ /tmp/config/

# 安装编译依赖RPM包（离线安装）
RUN rpm -ivh /tmp/centos7-rpms/*.rpm --force --nodeps || true

# 创建nginx用户和组
RUN groupadd nginx 2>/dev/null || true && \
    useradd -g nginx -s /sbin/nologin -M nginx 2>/dev/null || true

# 设置执行权限并运行编译脚本
RUN chmod +x /tmp/scripts/build-nginx.sh && \
    /tmp/scripts/build-nginx.sh

# 创建nginx运行所需的目录
RUN mkdir -p /var/log/nginx /var/cache/nginx /etc/nginx/conf.d /usr/share/nginx/html

# 复制nginx配置文件
RUN cp /tmp/config/nginx.conf /etc/nginx/nginx.conf && \
    cp /tmp/config/default.conf /etc/nginx/conf.d/default.conf

# 创建默认首页
RUN echo '<html><head><title>Welcome to nginx!</title></head><body><h1>Welcome to nginx!</h1><p>If you see this page, the nginx web server is successfully installed and working.</p></body></html>' > /usr/share/nginx/html/index.html

# 清理临时文件
RUN rm -rf /tmp/build /tmp/packages /tmp/centos7-rpms /tmp/scripts /tmp/config

# 暴露端口
EXPOSE 80 443

# 设置启动命令
CMD ["/usr/local/nginx/sbin/nginx", "-g", "daemon off;"]
