# Docker构建忽略文件
# 排除不必要的文件以减小构建上下文

# 备份和日志文件
*.log
*.bak
*.backup
*.old
*.tmp
*.temp

# 备份目录
backups/
backup-scripts/

# 测试文件
test-*.py
test-*.sh
*test*

# 文档文件
*.md
*.txt
*.pdf
README*
CHANGELOG*
LICENSE*

# 版本控制
.git/
.gitignore
.gitattributes

# IDE和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
Thumbs.db
desktop.ini

# 构建产物和缓存
node_modules/
*.cache
.cache/

# 其他Docker文件（避免递归包含）
Dockerfile.backup
Dockerfile.original
docker-compose.override.yml

# PowerShell和脚本文件（除了必需的）
*.ps1
!docker-entrypoint.sh

# 压缩文件
*.zip
*.tar
*.tar.gz
*.rar

# 临时目录
tmp/
temp/
