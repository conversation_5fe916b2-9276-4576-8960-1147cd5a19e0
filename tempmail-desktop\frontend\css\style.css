/* 临时邮箱桌面应用样式文件 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    overflow: hidden;
}

#app {
    height: 100vh;
}

/* 应用容器 */
.app-container {
    height: 100vh;
    background: #ffffff;
    border-radius: 8px;
    margin: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 头部样式 */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0 20px;
    border-bottom: 1px solid #e0e0e0;
    height: 80px !important;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    gap: 20px;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 20px;
    font-weight: bold;
    flex-shrink: 0;
}

.logo .el-icon {
    margin-right: 8px;
    font-size: 24px;
}

/* 头部操作按钮区域 */
.header-actions {
    display: flex;
    gap: 12px;
    flex-shrink: 0;
    margin-left: auto;
}

/* 确保头部按钮大小一致 */
.header-actions .el-button {
    height: 40px;
    padding: 0 20px;
    font-size: 14px;
    font-weight: 500;
}

/* 侧边栏样式 */
.sidebar {
    background: linear-gradient(180deg, #f8f9fa 0%, #f0f2f5 100%);
    border-right: 2px solid #e0e6ed;
    padding: 20px;
    overflow-y: auto;
    box-shadow: inset -2px 0 8px rgba(0, 0, 0, 0.05);
}

.sidebar .el-card {
    margin-bottom: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.sidebar .el-card:hover {
    border-color: #409eff;
    box-shadow: 0 6px 20px rgba(64, 158, 255, 0.15);
    transform: translateY(-2px);
}

.email-info-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.email-info-card .el-card__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    margin: -20px -20px 20px -20px;
    padding: 18px 24px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.email-info-card .card-header span {
    color: white !important;
    font-weight: 600;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40px;
}

.card-header .el-button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    font-size: 13px;
    padding: 6px 12px;
    border-radius: 6px;
}

.card-header .el-button:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.card-header .el-button:active {
    transform: translateY(0);
}

/* 邮箱信息卡片 */
.email-info-card .email-address {
    font-size: 14px;
    font-weight: 600;
    color: #409eff;
    margin-bottom: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 12px 16px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
    border-radius: 8px;
    border: 1px solid #b3d8ff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.12);
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    min-height: 20px;
}

.email-info-card .email-address::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(180deg, #409eff 0%, #1890ff 100%);
    border-radius: 8px 0 0 8px;
}

.email-info-card .email-address:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(64, 158, 255, 0.25);
    background: linear-gradient(135deg, #e6f7ff 0%, #cceeff 100%);
    border-color: #409eff;
}

.email-info-card .email-address:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

/* 复制动画效果 */
@keyframes copySuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.email-address.copying {
    animation: copySuccess 0.3s ease;
    background: linear-gradient(135deg, #e6f7ff 0%, #d1f2eb 100%);
    border-color: #52c41a;
}

.email-address.copying::before {
    background: linear-gradient(180deg, #52c41a 0%, #389e0d 100%);
}

.email-meta {
    background: linear-gradient(135deg, #fafbfc 0%, #f5f7fa 100%);
    border-radius: 10px;
    padding: 16px;
    margin-top: 12px;
    border: 1px solid #e8eaed;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
}

.email-meta p {
    margin: 8px 0;
    font-size: 13px;
    color: #555;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid #f0f2f5;
}

.email-meta p:last-child {
    border-bottom: none;
}

.email-meta p strong {
    color: #333;
    font-weight: 500;
    min-width: 60px;
}

.email-meta p span {
    color: #666;
    font-size: 11px;
    text-align: right;
    flex: 1;
}

/* 设置卡片 */
.setting-item {
    margin-bottom: 16px;
}

.setting-item label {
    display: block;
    margin-bottom: 4px;
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

.setting-item .el-select,
.setting-item .el-input {
    width: 100%;
}

.setting-hint {
    margin-top: 4px;
    font-size: 11px;
}

.setting-item .el-checkbox {
    font-size: 13px;
    font-weight: 500;
}

.custom-prefix-container {
    margin-top: 8px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
}

.custom-prefix-container .el-input {
    margin-bottom: 8px;
}

.custom-prefix-container .el-button {
    font-weight: 500;
}

/* 统计卡片 */
.stats-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
}

/* 主内容区 */
.main-content {
    background: #ffffff;
    padding: 16px;
    overflow-y: auto;
}

/* 欢迎页面 */
.welcome-page {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

/* 邮件列表容器 */
.email-list-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* 工具栏 */
.toolbar {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

/* 邮件列表 */
.email-list {
    flex: 1;
    overflow-y: auto;
}

.email-item {
    padding: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #ffffff;
    position: relative;
}

.email-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.email-item.unread {
    border-left: 4px solid #409eff;
    background: #f0f9ff;
}

.email-item.selected {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.email-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.sender {
    font-weight: bold;
    color: #333;
    font-size: 14px;
}

.date {
    font-size: 12px;
    color: #999;
}

.subject {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.4;
}

.preview {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.email-actions {
    position: absolute;
    top: 16px;
    right: 16px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.email-item:hover .email-actions {
    opacity: 1;
}



.detail-item {
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.detail-item strong {
    display: inline-block;
    width: 80px;
    color: #666;
    font-size: 14px;
}

.detail-item span {
    color: #333;
    font-size: 14px;
}

.email-body {
    margin-top: 16px;
    padding: 16px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    line-height: 1.6;
    font-size: 14px;
    color: #333;
    max-height: 400px;
    overflow-y: auto;
    /* 确保文本可以被选择 */
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    position: relative;
}

/* 邮件内容复制按钮 */
.email-body-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.email-body-title {
    font-size: 14px;
    font-weight: 500;
    color: #666;
}

.copy-content-btn {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
}

.copy-content-btn:hover {
    background: #409eff;
    color: white;
    border-color: #409eff;
}

.copy-content-btn.copied {
    background: #67c23a;
    color: white;
    border-color: #67c23a;
}

/* 邮件内容区域 */
.email-content-area {
    /* 确保所有子元素都可以被选择 */
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
}

.email-content-area * {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
}

.email-body .no-content {
    text-align: center;
    padding: 20px;
    color: #999;
}

/* 邮件HTML内容样式 */
.email-body img {
    max-width: 100%;
    height: auto;
}

.email-body table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
}

.email-body table td,
.email-body table th {
    padding: 8px;
    border: 1px solid #ddd;
}

.email-body a {
    color: #409eff;
    text-decoration: none;
}

.email-body a:hover {
    text-decoration: underline;
}

/* 对话框样式 */
.dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* PIN码验证对话框样式 */
.pin-dialog-content {
    text-align: center;
    padding: 20px 0;
}

.pin-dialog-icon {
    margin-bottom: 20px;
}

.pin-dialog-text {
    margin-bottom: 20px;
}

.pin-dialog-content .el-input {
    max-width: 280px;
    margin: 0 auto;
}

.pin-dialog-content .el-input .el-input__wrapper {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 2px solid #e4e7ed;
    transition: all 0.3s ease;
}

.pin-dialog-content .el-input .el-input__wrapper:hover {
    border-color: #409eff;
}

.pin-dialog-content .el-input .el-input__wrapper.is-focus {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .sidebar {
        width: 250px !important;
    }

    /* 中等屏幕下弹窗适配 - 保持固定尺寸 */
    .email-detail-dialog {
        width: 1250px !important;
        height: 1050px !important;
    }
}

@media (max-width: 768px) {
    .app-container {
        margin: 0;
        border-radius: 0;
    }

    .sidebar {
        width: 200px !important;
    }

    .header-content {
        flex-direction: column;
        gap: 12px;
        padding: 12px 0;
    }

    .toolbar {
        flex-wrap: wrap;
    }

    /* 弹窗在小屏幕上的适配 - 保持固定尺寸 */
    .email-detail-dialog {
        width: 1250px !important;
        height: 1050px !important;
        margin: 0 auto;
    }

    .email-detail-dialog .el-dialog {
        margin-top: 60px !important;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.email-item {
    animation: fadeIn 0.3s ease;
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

/* 空状态样式 */
.el-empty {
    padding: 20px;
}

/* 邮件内容区域的空状态特殊样式 */
.email-content-area .el-empty {
    padding: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0 !important;
}

.email-content-area .el-empty .el-empty__image {
    margin: 0 !important;
    margin-bottom: 0 !important;
}

.email-content-area .el-empty .el-empty__description {
    margin: 0 !important;
    margin-top: 2px !important;
    padding: 0 !important;
    line-height: 1.2 !important;
}

/* 进一步优化，确保图标和文字之间没有任何空隙 */
.email-content-area .el-empty .el-empty__image svg,
.email-content-area .el-empty .el-empty__image img {
    display: block !important;
    margin: 0 !important;
    /* 移除 vertical-align，因为它对 block 元素无效 */
}

.email-content-area .el-empty .el-empty__bottom {
    margin-top: 0 !important;
}

/* 自定义按钮样式 */
.el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.el-button--primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 标签样式 */
.el-tag {
    border-radius: 12px;
    font-size: 12px;
}

/* 输入框焦点样式 */
.el-input__wrapper:focus-within {
    box-shadow: 0 0 0 1px #409eff inset;
}

/* 自定义消息样式 */
.copy-success-message {
    background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);
    border: 1px solid #b7eb8f;
    color: #389e0d;
    font-weight: 500;
}

/* 邮箱地址复制图标动画 */
.email-address .el-icon:last-child {
    transition: all 0.3s ease;
}

.email-address:hover .el-icon:last-child {
    opacity: 1 !important;
    transform: scale(1.1);
}

/* 改善卡片间距 */
.email-info-card .el-card__body {
    padding: 20px;
}

/* 状态标签样式 */
.email-meta .el-tag {
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
}

/* 邮件详情弹窗样式 */
.email-detail-dialog {
    border-radius: 16px;
    overflow: hidden;
    /* 固定弹窗大小 - 可在此处调整窗口尺寸 */
    width: 1300px !important;
    height: 1000px !important;
}

.email-detail-dialog .el-overlay-dialog {
    display: flex;
    align-items: center;
    justify-content: center;
}

.email-detail-dialog .el-dialog {
    /* 固定弹窗内部尺寸 */
    width: 1300px !important;
    height: 1000px !important;
    max-height: none !important;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
}

.email-detail-dialog .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    margin: 0;
    border-radius: 16px 16px 0 0;
    flex-shrink: 0;
}

.email-detail-dialog .el-dialog__title {
    color: white;
    font-weight: 600;
    font-size: 18px;
}

.email-detail-dialog .el-dialog__headerbtn {
    top: 20px;
    right: 24px;
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.email-detail-dialog .el-dialog__headerbtn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.email-detail-dialog .el-dialog__headerbtn .el-dialog__close {
    color: white;
    font-size: 18px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.email-detail-dialog .el-dialog__headerbtn .el-dialog__close:hover {
    color: rgba(255, 255, 255, 0.9);
    transform: rotate(90deg);
}

.email-detail-dialog .el-dialog__body {
    padding: 24px;
    background: white;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

.email-detail-dialog .el-dialog__footer {
    background: #f8f9fa;
    padding: 16px 24px;
    border-top: 1px solid #e9ecef;
    flex-shrink: 0;
    margin-top: auto;
}

/* 邮件详情弹窗内容 */
.email-detail-modal-content {
    color: #333;
}

.email-info-section {
    margin-bottom: 20px;
}

.email-info-section .detail-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #f8f9fa 0%, #f0f2f5 100%);
    border-radius: 10px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.email-info-section .detail-item:hover {
    background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
    border-color: #409eff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.email-info-section .detail-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
    min-width: 80px;
}

.email-info-section .detail-label .el-icon {
    color: #409eff;
    font-size: 16px;
}

.email-info-section .detail-value {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    justify-content: flex-end;
}

.email-info-section .detail-value span {
    color: #333;
    font-size: 14px;
    word-break: break-all;
    text-align: right;
}

/* 邮件内容区域 */
.email-content-section {
    margin-top: 12px;
}

.email-content-section .email-body-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    padding: 6px 10px;
    background: linear-gradient(135deg, #f8f9fa 0%, #f0f2f5 100%);
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.email-content-section .email-body-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.email-content-section .email-body-title .el-icon {
    color: #409eff;
    font-size: 18px;
}

.email-content-section .copy-buttons {
    display: flex;
    gap: 8px;
}

.email-content-section .email-content-area {
    background: #ffffff;
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    padding: 12px;
    /* 调整邮件正文区域高度 - 可在此处调整正文显示区域大小 */
    min-height: 550px;
    max-height: 550px;
    height: 550px;
    overflow-y: auto;
    line-height: 1.5;
    font-size: 14px;
    color: #24292e;
    user-select: text;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.email-content-section .email-content-area:hover {
    border-color: #c6cbd1;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

/* 确保邮件内容的第一个和最后一个元素没有多余的边距 */
.email-content-area > *:first-child {
    margin-top: 0 !important;
}

.email-content-area > *:last-child {
    margin-bottom: 0 !important;
}

.email-content-section .email-content-area * {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
}

.email-content-section .no-content {
    text-align: center;
    padding: 0;
    color: #999;
}

/* 优化空状态组件，让图标和文字紧密贴合 */
.email-content-area .el-empty {
    padding: 0 !important;
}

.email-content-area .el-empty__image {
    margin-bottom: 0 !important;
}

.email-content-area .el-empty__description {
    margin-top: 8px !important;
}

/* 邮件详情弹窗底部 */
.email-detail-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 16px;
}

.email-detail-footer .el-button {
    padding: 16px 28px;
    font-size: 15px;
    font-weight: 600;
    border-radius: 10px;
    min-width: 120px;
    height: 48px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.email-detail-footer .el-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.email-detail-footer .el-button:hover::before {
    left: 100%;
}

.email-detail-footer .el-button:not(.el-button--danger) {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
    color: #495057;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.email-detail-footer .el-button:not(.el-button--danger):hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
    border-color: #6c757d;
    color: #212529;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.email-detail-footer .el-button:not(.el-button--danger):active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.email-detail-footer .el-button--danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: 2px solid #dc3545;
    color: white;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
}

.email-detail-footer .el-button--danger:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
    border-color: #a71e2a;
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 20px rgba(220, 53, 69, 0.4);
}

.email-detail-footer .el-button--danger:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

/* 按钮图标动画 */
.email-detail-footer .el-button .el-icon {
    transition: all 0.3s ease;
}

.email-detail-footer .el-button:hover .el-icon {
    transform: scale(1.1);
}

.email-detail-footer .el-button--danger:hover .el-icon {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: scale(1.1) rotate(0deg); }
    25% { transform: scale(1.1) rotate(-5deg); }
    75% { transform: scale(1.1) rotate(5deg); }
}

/* 邮件HTML内容样式 */
.email-content-area img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
    margin: 4px 0;
}

/* 邮件内容文本样式 */
.email-content-area p {
    margin: 4px 0;
    line-height: 1.5;
}

.email-content-area h1,
.email-content-area h2,
.email-content-area h3,
.email-content-area h4,
.email-content-area h5,
.email-content-area h6 {
    margin: 8px 0 4px 0;
    color: #1f2328;
    font-weight: 600;
}

.email-content-area h1 { font-size: 24px; }
.email-content-area h2 { font-size: 20px; }
.email-content-area h3 { font-size: 18px; }
.email-content-area h4 { font-size: 16px; }

.email-content-area ul,
.email-content-area ol {
    margin: 4px 0;
    padding-left: 18px;
}

.email-content-area li {
    margin: 1px 0;
    line-height: 1.4;
}

.email-content-area blockquote {
    margin: 8px 0;
    padding: 6px 10px;
    border-left: 3px solid #d1d9e0;
    background: #f6f8fa;
    color: #656d76;
    font-style: italic;
}

.email-content-area code {
    background: #f6f8fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    color: #d73a49;
}

.email-content-area pre {
    background: #f6f8fa;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
    margin: 10px 0;
    border: 1px solid #e1e4e8;
}

.email-content-area pre code {
    background: none;
    padding: 0;
    color: #24292e;
}

.email-content-area table {
    width: 100%;
    border-collapse: collapse;
    margin: 8px 0;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06);
    border: 1px solid #e1e4e8;
}

.email-content-area table td,
.email-content-area table th {
    padding: 8px 12px;
    border-bottom: 1px solid #e1e4e8;
    text-align: left;
}

.email-content-area table th {
    background: #f6f8fa;
    color: #24292e;
    font-weight: 600;
    border-bottom: 2px solid #e1e4e8;
}

.email-content-area table tr:hover {
    background: #f6f8fa;
}

.email-content-area table tr:last-child td {
    border-bottom: none;
}

.email-content-area a {
    color: #0969da;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    border-bottom: 1px solid transparent;
}

.email-content-area a:hover {
    color: #0550ae;
    border-bottom-color: #0550ae;
    text-decoration: none;
}

.email-content-area a:visited {
    color: #8250df;
}

/* 弹窗动画效果 */
.email-detail-dialog .el-dialog {
    animation: dialogFadeIn 0.3s ease;
}

@keyframes dialogFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}
