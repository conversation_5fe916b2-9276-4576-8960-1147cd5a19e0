# DM8 数据库完整恢复脚本
# 版本: 1.0
# 作者: AI Assistant
# 日期: 2025-07-17

param(
    [Parameter(Mandatory=$true)]
    [string]$BackupPath,
    [switch]$ForceRestore,
    [switch]$VerboseLog
)

# 全局变量
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$TempDir = Join-Path (Get-Location) "temp_restore_$Timestamp"
$LogFile = Join-Path $BackupPath "restore_log_$Timestamp.txt"

# 日志记录函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogContent = "[$Time] [$Level] $Message"

    switch ($Level) {
        "ERROR" { Write-Host $LogContent -ForegroundColor Red }
        "WARN"  { Write-Host $LogContent -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $LogContent -ForegroundColor Green }
        default { Write-Host $LogContent -ForegroundColor White }
    }

    if (Test-Path $BackupPath) {
        try {
            $LogDir = Split-Path $LogFile -Parent
            if (!(Test-Path $LogDir)) {
                New-Item -ItemType Directory -Path $LogDir -Force | Out-Null
            }
            $LogContent | Out-File -FilePath $LogFile -Append -Encoding UTF8 -Force
        }
        catch {
            Write-Warning "无法写入日志文件: $($_.Exception.Message)"
        }
    }
}

# 进度显示函数
function Show-Progress {
    param([string]$Activity, [string]$Status, [int]$PercentComplete)
    Write-Progress -Activity $Activity -Status $Status -PercentComplete $PercentComplete
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "DM8 数据库数据卷恢复脚本 v1.0" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Log "开始DM8数据卷恢复"
Write-Log "备份路径: $BackupPath"
Write-Log "恢复模式: 数据卷恢复 (包含完整数据库数据)" "INFO"
Write-Log "临时目录: $TempDir"

# 验证备份路径
if (-not (Test-Path $BackupPath)) {
    Write-Log "错误: 备份路径不存在: $BackupPath" "ERROR"
    exit 1
}

# 创建临时目录
try {
    New-Item -ItemType Directory -Path $TempDir -Force | Out-Null
    Write-Log "临时目录创建成功" "SUCCESS"
} catch {
    Write-Log "创建临时目录失败: $($_.Exception.Message)" "ERROR"
    exit 1
}

try {
    # 步骤1: 检查Docker环境
    Show-Progress "环境检查" "检查Docker环境..." 5
    Write-Log "检查Docker环境..."
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker未运行或未安装"
    }
    Write-Log "Docker版本: $dockerVersion" "SUCCESS"

    # 步骤2: 验证备份文件
    Show-Progress "环境检查" "验证备份文件..." 10
    Write-Log "验证备份文件..."
    $RequiredFiles = @(
        "dm8_data_volume.zip",
        "dm8_config_backup.zip"
    )

    $MissingFiles = @()
    foreach ($File in $RequiredFiles) {
        $FilePath = Join-Path $BackupPath $File
        if (-not (Test-Path $FilePath)) {
            $MissingFiles += $File
            Write-Log "警告: 缺少备份文件 $File" "WARN"
        } else {
            $Size = [math]::Round((Get-Item $FilePath).Length / 1MB, 2)
            Write-Log "找到备份文件: $File (${Size}MB)" "SUCCESS"
        }
    }

    if ($MissingFiles.Count -gt 0 -and -not $ForceRestore) {
        throw "缺少关键备份文件，使用 -ForceRestore 参数继续"
    }

    # 步骤3: 停止现有服务
    Show-Progress "准备恢复" "停止现有服务..." 20
    Write-Log "停止现有服务..."
    docker-compose down 2>&1 | Out-Null
    Start-Sleep -Seconds 5
    Write-Log "现有服务已停止" "SUCCESS"

    # 步骤4: 删除现有数据卷
    Show-Progress "准备恢复" "删除现有数据卷..." 25
    Write-Log "删除现有数据卷..."
    $VolumeList = @("dm8_database_data", "dm8_database_logs")
    foreach ($VolumeName in $VolumeList) {
        docker volume rm $VolumeName -f 2>$null | Out-Null
        Write-Log "已删除数据卷: $VolumeName" "SUCCESS"
    }

    # 步骤5: 恢复配置文件
    Show-Progress "恢复配置" "恢复配置文件..." 30
    Write-Log "恢复配置文件..."
    $ConfigFileList = @("docker-compose.yml", "Dockerfile", "dm.ini")
    $RestoredFiles = 0
    foreach ($File in $ConfigFileList) {
        $BackupFile = Join-Path $BackupPath $File
        if (Test-Path $BackupFile) {
            Copy-Item $BackupFile -Destination . -Force
            Write-Log "已恢复: $File" "SUCCESS"
            $RestoredFiles++
        }
    }
    Write-Log "配置文件恢复完成，已恢复 $RestoredFiles 个文件" "SUCCESS"

    # 步骤6: 创建新数据卷
    Show-Progress "恢复数据卷" "创建新数据卷..." 35
    Write-Log "创建新数据卷..."
    foreach ($VolumeName in $VolumeList) {
        docker volume create $VolumeName 2>&1 | Out-Null
        Write-Log "已创建数据卷: $VolumeName" "SUCCESS"
    }

    # 步骤7: 恢复DM8主数据卷
    Show-Progress "恢复数据卷" "恢复DM8主数据卷..." 45
    Write-Log "恢复DM8主数据卷..."
    $DataVolumeFile = Join-Path $BackupPath "dm8_data_volume.zip"
    if (Test-Path $DataVolumeFile) {
        try {
            # 创建临时解压目录
            $TempExtractDir = Join-Path $env:TEMP "dm8_restore_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            New-Item -ItemType Directory -Path $TempExtractDir -Force | Out-Null

            # 解压备份文件
            Expand-Archive -Path $DataVolumeFile -DestinationPath $TempExtractDir -Force

            # 使用docker cp方法恢复数据卷
            $RestoreContainer = docker create -v dm8_database_data:/data alpine 2>&1
            if ($LASTEXITCODE -eq 0) {
                # 检查解压目录结构
                $DataSourcePath = if (Test-Path "${TempExtractDir}/dm8_data") { "${TempExtractDir}/dm8_data/." } else { "${TempExtractDir}/data/." }
                docker cp $DataSourcePath "${RestoreContainer}:/data/" 2>&1 | Out-Null
                docker rm $RestoreContainer 2>&1 | Out-Null

                # 修复数据目录权限
                Write-Log "修复数据目录权限..."
                docker run --rm -v dm8_database_data:/data alpine sh -c "chown -R 1001:1001 /data && chmod 755 /data" 2>&1 | Out-Null
                Write-Log "DM8主数据卷恢复完成" "SUCCESS"
            } else {
                Write-Log "创建恢复容器失败" "ERROR"
            }

            # 清理临时目录
            Remove-Item $TempExtractDir -Recurse -Force -ErrorAction SilentlyContinue
        } catch {
            Write-Log "DM8主数据卷恢复失败: $($_.Exception.Message)" "ERROR"
        }
    }

    # 步骤8: 恢复DM8日志数据卷
    Show-Progress "恢复数据卷" "恢复DM8日志数据卷..." 50
    Write-Log "恢复DM8日志数据卷..."
    $LogsVolumeFile = Join-Path $BackupPath "dm8_logs_volume.zip"
    if (Test-Path $LogsVolumeFile) {
        try {
            # 创建临时解压目录
            $TempExtractDir = Join-Path $env:TEMP "dm8_logs_restore_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            New-Item -ItemType Directory -Path $TempExtractDir -Force | Out-Null

            # 解压备份文件
            Expand-Archive -Path $LogsVolumeFile -DestinationPath $TempExtractDir -Force

            # 使用docker cp方法恢复日志卷
            $LogsRestoreContainer = docker create -v dm8_database_logs:/logs alpine 2>&1
            if ($LASTEXITCODE -eq 0) {
                # 检查解压目录结构
                $LogsSourcePath = if (Test-Path "${TempExtractDir}/dm8_logs") { "${TempExtractDir}/dm8_logs/." } else { "${TempExtractDir}/logs/." }
                docker cp $LogsSourcePath "${LogsRestoreContainer}:/logs/" 2>&1 | Out-Null
                docker rm $LogsRestoreContainer 2>&1 | Out-Null

                # 修复日志目录权限
                Write-Log "修复日志目录权限..."
                docker run --rm -v dm8_database_logs:/logs alpine sh -c "chown -R 1001:1001 /logs && chmod 755 /logs" 2>&1 | Out-Null
                Write-Log "DM8日志数据卷恢复完成" "SUCCESS"
            } else {
                Write-Log "创建日志恢复容器失败" "WARN"
            }

            # 清理临时目录
            Remove-Item $TempExtractDir -Recurse -Force -ErrorAction SilentlyContinue
        } catch {
            Write-Log "DM8日志数据卷恢复失败: $($_.Exception.Message)" "WARN"
        }
    } else {
        Write-Log "日志数据卷备份文件不存在，跳过恢复" "WARN"
    }

    # 恢复日志数据卷
    $LogsVolumeFile = Join-Path $BackupPath "dm8_logs_volume.zip"
    if (Test-Path $LogsVolumeFile) {
        try {
            # 创建临时目录
            $TempExtractDir = Join-Path (Get-Location) "temp_restore_logs_$Timestamp"
            New-Item -ItemType Directory -Path $TempExtractDir -Force | Out-Null

            # 解压日志卷文件
            Write-Log "解压日志卷文件..."
            Expand-Archive -Path $LogsVolumeFile -DestinationPath $TempExtractDir -Force

            # 使用docker cp方法恢复日志卷
            $LogsRestoreContainer = docker create -v dm8_database_logs:/logs alpine 2>&1
            if ($LASTEXITCODE -eq 0) {
                # 检查解压目录结构
                $LogsSourcePath = if (Test-Path "${TempExtractDir}/dm8_logs") { "${TempExtractDir}/dm8_logs/." } else { "${TempExtractDir}/logs/." }
                docker cp $LogsSourcePath "${LogsRestoreContainer}:/logs/" 2>&1 | Out-Null
                docker rm $LogsRestoreContainer 2>&1 | Out-Null

                # 修复日志目录权限
                Write-Log "修复日志目录权限..."
                docker run --rm -v dm8_database_logs:/logs alpine sh -c "chown -R 1001:1001 /logs && chmod 755 /logs" 2>&1 | Out-Null
                Write-Log "DM8日志数据卷恢复完成" "SUCCESS"
            } else {
                Write-Log "创建日志恢复容器失败" "WARN"
            }

            # 清理临时目录
            Remove-Item $TempExtractDir -Recurse -Force -ErrorAction SilentlyContinue
        } catch {
            Write-Log "DM8日志数据卷恢复失败: $($_.Exception.Message)" "WARN"
        }
    }

    # 步骤8: 启动服务
    Show-Progress "启动服务" "启动DM8服务..." 70
    Write-Log "启动DM8服务..."
    docker-compose up -d 2>&1 | Out-Null

    # 步骤9: 等待DM8启动
    Show-Progress "启动服务" "等待DM8启动..." 80
    Write-Log "等待DM8启动..."

    # 先等待60秒让DM8有足够时间启动
    Write-Log "等待60秒让DM8完全启动..."
    Start-Sleep -Seconds 60

    # 检查容器状态
    $ContainerStatus = docker inspect dm8-database --format='{{.State.Status}}' 2>$null
    if ($ContainerStatus -eq "running") {
        Write-Log "DM8容器启动成功" "SUCCESS"
    } else {
        Write-Log "DM8容器状态异常: $ContainerStatus" "WARN"
    }

    # 步骤10: 数据卷恢复完成
    Show-Progress "恢复完成" "数据卷恢复已包含完整数据库数据..." 85
    Write-Log "数据卷恢复完成 - 包含完整数据库数据，无需额外SQL导入" "SUCCESS"

    # 步骤11: 验证恢复
    Show-Progress "验证恢复" "验证恢复结果..." 95
    Write-Log "验证恢复结果..."

    # 检查容器状态
    $ContainerStatus = docker inspect dm8-database --format='{{.State.Status}}' 2>$null
    if ($ContainerStatus -eq "running") {
        Write-Log "容器状态: 运行中" "SUCCESS"

        # 检查容器健康状态
        $HealthStatus = docker inspect dm8-database --format='{{.State.Health.Status}}' 2>$null
        if ($HealthStatus -eq "healthy") {
            Write-Log "容器健康状态: 健康" "SUCCESS"
        } else {
            Write-Log "容器健康状态: $HealthStatus (可能需要更多时间启动)" "WARN"
        }

        # 简单的数据库连接测试（只测试一次）
        Write-Log "测试数据库连接..."
        $DbTest = docker exec dm8-database /home/<USER>/dmdbms/bin/disql SYSDBA/GDYtd@2025@localhost:5236 -c "select 1 from dual;" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Log "数据库连接: 正常" "SUCCESS"
        } else {
            Write-Log "数据库连接: 暂时无法连接 (数据库可能仍在启动中)" "WARN"
            Write-Log "建议等待几分钟后手动验证数据库状态" "INFO"
        }
    } else {
        Write-Log "容器状态: $ContainerStatus" "WARN"
    }

} catch {
    Write-Log "恢复失败: $($_.Exception.Message)" "ERROR"
    Write-Log "请检查Docker状态和备份文件完整性" "ERROR"
    exit 1
} finally {
    # 清理临时目录
    if (Test-Path $TempDir) {
        Remove-Item $TempDir -Recurse -Force
        Write-Log "已清理临时目录" "INFO"
    }
    Write-Progress -Activity "恢复完成" -Completed
}

Show-Progress "恢复完成" "恢复已完成!" 100
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "恢复完成!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Log "恢复成功完成" "SUCCESS"
Write-Log "DM8服务可在以下地址访问: localhost:5236" "SUCCESS"
Write-Log "用户名: SYSDBA, 密码: GDYtd@2025" "SUCCESS"
Write-Log "docker exec -it dm8-database /home/<USER>/dmdbms/bin/disql SYSDBA/'""GDYtd@2025""'@localhost:5236" "SUCCESS"
Write-Host "`n恢复脚本执行完成" -ForegroundColor Green
