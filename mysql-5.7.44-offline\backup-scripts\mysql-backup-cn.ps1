# MySQL 5.7.44 离线版完整备份脚本
# 版本: 2.0
# 作者: AI Assistant
# 日期: 2025-07-12

param(
    [string]$BackupDir = ".\backups",
    [switch]$ForceBackup,
    [switch]$VerboseLog
)

# 全局变量
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$BackupPath = Join-Path $BackupDir $Timestamp
$TempDir = Join-Path (Get-Location) "temp_backup_$Timestamp"
$LogFile = Join-Path $BackupPath "backup_log.txt"

# 日志记录函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogContent = "[$Time] [$Level] $Message"
    
    switch ($Level) {
        "ERROR" { Write-Host $LogContent -ForegroundColor Red }
        "WARN"  { Write-Host $LogContent -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $LogContent -ForegroundColor Green }
        default { Write-Host $LogContent -ForegroundColor White }
    }
    
    if (Test-Path $BackupPath) {
        $LogContent | Add-Content -Path $LogFile -Encoding UTF8
    }
}

# 进度显示函数
function Show-Progress {
    param([string]$Activity, [string]$Status, [int]$PercentComplete)
    Write-Progress -Activity $Activity -Status $Status -PercentComplete $PercentComplete
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "MySQL 5.7.44 离线版完整备份脚本 v2.0" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Log "开始MySQL完整备份"
Write-Log "备份目录: $BackupPath"
Write-Log "临时目录: $TempDir"

# 创建目录
try {
    New-Item -ItemType Directory -Path $BackupPath -Force | Out-Null
    New-Item -ItemType Directory -Path $TempDir -Force | Out-Null
    Write-Log "备份目录创建成功" "SUCCESS"
} catch {
    Write-Log "创建备份目录失败: $($_.Exception.Message)" "ERROR"
    exit 1
}

try {
    # 步骤1: 检查Docker环境
    Show-Progress "环境检查" "检查Docker环境..." 10
    Write-Log "检查Docker环境..."
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker未运行或未安装"
    }
    Write-Log "Docker版本: $dockerVersion" "SUCCESS"

    # 步骤2: 检查容器状态
    Show-Progress "环境检查" "检查MySQL容器状态..." 20
    Write-Log "检查MySQL容器状态..."
    $ContainerStatus = docker inspect mysql5.7.44 --format='{{.State.Status}}' 2>$null
    if ($ContainerStatus -ne "running") {
        Write-Log "警告: MySQL容器未运行，状态: $ContainerStatus" "WARN"
        if (-not $ForceBackup) {
            throw "容器未运行，使用 -ForceBackup 参数强制备份"
        }
    } else {
        Write-Log "MySQL容器运行正常" "SUCCESS"
    }

    # 步骤3: 备份配置文件
    Show-Progress "备份配置" "备份配置文件..." 30
    Write-Log "备份配置文件..."
    $ConfigFiles = @("docker-compose.yaml", "Dockerfile", "my.cnf", "init-mysql.sql")
    $BackedUpFiles = 0
    foreach ($File in $ConfigFiles) {
        if (Test-Path $File) {
            Copy-Item $File -Destination $BackupPath -Force
            Write-Log "已备份: $File" "SUCCESS"
            $BackedUpFiles++
        } else {
            Write-Log "文件不存在: $File" "WARN"
        }
    }
    Write-Log "配置备份完成，已备份 $BackedUpFiles 个文件" "SUCCESS"

    # 步骤4: 备份SQL数据（如果容器运行中）
    if ($ContainerStatus -eq "running") {
        Show-Progress "备份数据" "备份数据库SQL..." 40
        Write-Log "备份数据库SQL数据..."
        $SqlBackupFile = Join-Path $TempDir "mysql_all_databases.sql"
        
        try {
            Write-Log "执行mysqldump命令..."
            $DumpCommand = "mysqldump -u root -proot --all-databases --single-transaction --routines --triggers --default-character-set=utf8mb4"

            $BackupOutput = docker exec mysql5.7.44 sh -c "$DumpCommand 2>/dev/null"

            if ($LASTEXITCODE -eq 0 -and $BackupOutput -and $BackupOutput.Length -gt 0) {
                $FirstLine = ($BackupOutput | Select-Object -First 1).Trim()
                if ($FirstLine.StartsWith("--") -or $FirstLine.StartsWith("/*")) {
                    $BackupOutput | Out-File -FilePath $SqlBackupFile -Encoding UTF8

                    # 压缩SQL备份
                    $CompressedFile = Join-Path $BackupPath "mysql_all_databases.sql.zip"
                    Compress-Archive -Path $SqlBackupFile -DestinationPath $CompressedFile -Force

                    $FileSize = [math]::Round((Get-Item $CompressedFile).Length / 1MB, 2)
                    Write-Log "SQL备份完成并压缩，大小: ${FileSize}MB" "SUCCESS"
                } else {
                    Write-Log "SQL备份输出验证失败 - 格式无效" "WARN"
                }
            } else {
                Write-Log "SQL备份失败或数据为空 (退出码: $LASTEXITCODE)" "WARN"
            }
        } catch {
            Write-Log "SQL备份过程错误: $($_.Exception.Message)" "ERROR"
        }
    }

    # 步骤5: 停止容器确保数据一致性
    if ($ContainerStatus -eq "running") {
        Show-Progress "准备数据卷" "停止容器确保数据一致性..." 50
        Write-Log "停止MySQL容器确保数据一致性..."
        docker-compose stop mysql 2>&1 | Out-Null
        Start-Sleep -Seconds 5
        Write-Log "容器已停止" "SUCCESS"
    }

    # 步骤6: 备份数据卷
    Show-Progress "备份数据卷" "备份MySQL数据卷..." 60
    Write-Log "备份MySQL数据卷..."
    
    # 备份主数据卷
    Write-Log "备份主数据卷: mysql_data_offline"
    try {
        $DataBackupContainer = docker create -v mysql_data_offline:/data alpine 2>&1
        if ($LASTEXITCODE -eq 0) {
            docker cp "${DataBackupContainer}:/data" "${TempDir}/mysql_data" 2>&1 | Out-Null
            docker rm $DataBackupContainer 2>&1 | Out-Null

            if (Test-Path "${TempDir}/mysql_data") {
                Compress-Archive -Path "${TempDir}/mysql_data" -DestinationPath "${TempDir}/mysql_data_volume.zip" -Force
                Write-Log "主数据卷备份完成" "SUCCESS"
            } else {
                Write-Log "主数据卷数据复制失败" "ERROR"
            }
        } else {
            Write-Log "创建备份容器失败" "ERROR"
        }
    } catch {
        Write-Log "主数据卷备份异常: $($_.Exception.Message)" "ERROR"
    }
    
    # 备份日志卷
    Show-Progress "备份数据卷" "备份MySQL日志卷..." 70
    Write-Log "备份日志卷: mysql_logs_offline"
    try {
        $LogBackupContainer = docker create -v mysql_logs_offline:/data alpine 2>&1
        if ($LASTEXITCODE -eq 0) {
            docker cp "${LogBackupContainer}:/data" "${TempDir}/mysql_logs" 2>&1 | Out-Null
            docker rm $LogBackupContainer 2>&1 | Out-Null

            if (Test-Path "${TempDir}/mysql_logs") {
                $LogFiles = Get-ChildItem "${TempDir}/mysql_logs" -Recurse -File
                if ($LogFiles.Count -gt 0) {
                    Compress-Archive -Path "${TempDir}/mysql_logs" -DestinationPath "${TempDir}/mysql_logs_volume.zip" -Force
                    Write-Log "日志卷备份完成，包含 $($LogFiles.Count) 个文件" "SUCCESS"
                } else {
                    New-Item -ItemType Directory -Path "${TempDir}/empty_logs" -Force | Out-Null
                    New-Item -ItemType File -Path "${TempDir}/empty_logs/README.txt" -Force | Out-Null
                    "此日志卷在备份时为空。MySQL日志配置为写入 /usr/local/mysql/logs 目录。" | Out-File -FilePath "${TempDir}/empty_logs/README.txt" -Encoding UTF8
                    Compress-Archive -Path "${TempDir}/empty_logs" -DestinationPath "${TempDir}/mysql_logs_volume.zip" -Force
                    Write-Log "日志卷备份完成（卷为空）" "SUCCESS"
                    Remove-Item "${TempDir}/empty_logs" -Recurse -Force
                }
            } else {
                Write-Log "日志卷数据复制失败" "WARN"
            }
        } else {
            Write-Log "创建日志备份容器失败" "WARN"
        }
    } catch {
        Write-Log "日志卷备份异常: $($_.Exception.Message)" "WARN"
    }

    # 移动备份文件到备份目录
    $DataFile = Join-Path $TempDir "mysql_data_volume.zip"
    $LogFileCompressed = Join-Path $TempDir "mysql_logs_volume.zip"

    foreach ($File in @($DataFile, $LogFileCompressed)) {
        if (Test-Path $File) {
            $TargetFile = Join-Path $BackupPath (Split-Path $File -Leaf)
            Move-Item $File $TargetFile -Force
            $Size = [math]::Round((Get-Item $TargetFile).Length / 1MB, 2)
            Write-Log "已移动: $(Split-Path $File -Leaf) (${Size}MB)" "SUCCESS"
        }
    }

    Write-Log "数据卷备份完成" "SUCCESS"

    # 步骤7: 重启所有服务
    if ($ContainerStatus -eq "running") {
        Show-Progress "恢复服务" "重启所有服务..." 90
        Write-Log "重启所有服务..."
        docker-compose up -d 2>&1 | Out-Null
        
        # 等待MySQL启动
        $Count = 0
        do {
            Start-Sleep -Seconds 3
            $Count++
            $Status = docker exec mysql5.7.44 mysqladmin ping -u root -proot 2>$null
            if ($LASTEXITCODE -eq 0) {
                break
            }
        } while ($Count -lt 10)
        
        if ($Count -lt 10) {
            Write-Log "MySQL服务重启成功" "SUCCESS"
        } else {
            Write-Log "MySQL服务重启可能有问题" "WARN"
        }
    }

} catch {
    Write-Log "备份失败: $($_.Exception.Message)" "ERROR"
    exit 1
} finally {
    # 清理临时目录
    if (Test-Path $TempDir) {
        Remove-Item $TempDir -Recurse -Force
        Write-Log "已清理临时目录" "INFO"
    }
    Write-Progress -Activity "备份完成" -Completed
}

# 步骤8: 创建恢复说明
Show-Progress "生成文档" "创建恢复说明..." 95
Write-Log "创建恢复说明文档..."

$RestoreInstructions = @"
# MySQL 5.7.44 离线版完整备份恢复说明
备份时间: $Timestamp
备份类型: 完整备份（配置文件 + SQL数据 + 数据卷）

## 备份内容
1. 配置文件: docker-compose.yaml, Dockerfile, my.cnf, init-mysql.sql
2. SQL数据: mysql_all_databases.sql.zip
3. 数据卷:
   - mysql_data_volume.zip (主数据)
   - mysql_logs_volume.zip (日志)

## 恢复命令
.\backup-scripts\mysql-restore-cn.ps1 -BackupPath ".\backups\$Timestamp"

## 注意事项
- 恢复将完全替换现有数据
- 确保Docker Desktop正在运行
- 恢复期间现有服务将被停止
- 恢复完成后服务将自动启动

## 访问信息
- MySQL: localhost:3306
- 用户名: root
- 密码: root
- 数据库: mysql, information_schema, performance_schema, sys
"@

$RestoreInstructions | Out-File -FilePath (Join-Path $BackupPath "restore_instructions.txt") -Encoding UTF8

# 计算备份大小
$BackupSize = (Get-ChildItem $BackupPath -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB

Show-Progress "备份完成" "备份已完成!" 100
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "备份完成!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Log "备份位置: $BackupPath" "SUCCESS"
Write-Log "备份大小: $([math]::Round($BackupSize, 2)) MB" "SUCCESS"
Write-Log "备份内容: 配置文件 + SQL数据 + 数据卷" "SUCCESS"
Write-Log "恢复命令: .\backup-scripts\mysql-restore-cn.ps1 -BackupPath `".\backups\$Timestamp`"" "SUCCESS"

Write-Host "`n备份脚本执行完成" -ForegroundColor Green
