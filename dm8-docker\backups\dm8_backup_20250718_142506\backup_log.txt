[2025-07-18 14:25:06] [SUCCESS] 备份目录创建成功
[2025-07-18 14:25:06] [INFO] 检查Docker环境...
[2025-07-18 14:25:06] [SUCCESS] Docker版本: Docker version 28.0.4, build b8034c0
[2025-07-18 14:25:06] [INFO] 检查DM8容器状态...
[2025-07-18 14:25:06] [SUCCESS] DM8容器运行正常
[2025-07-18 14:25:06] [INFO] 创建备份信息文件
[2025-07-18 14:25:06] [SUCCESS] 备份信息文件创建成功
[2025-07-18 14:25:06] [INFO] 备份项目文件
[2025-07-18 14:25:06] [SUCCESS] 已备份: docker-compose.yml
[2025-07-18 14:25:06] [SUCCESS] 已备份: Dockerfile
[2025-07-18 14:25:06] [SUCCESS] 已备份: docker-entrypoint.sh
[2025-07-18 14:25:06] [SUCCESS] 已备份: install_dm8.exp
[2025-07-18 14:25:06] [SUCCESS] 已备份: test-connection.py
[2025-07-18 14:25:06] [SUCCESS] 已备份: dm.ini
[2025-07-18 14:25:06] [SUCCESS] 已备份: dm8_20250506_x86_rh7_64 目录
[2025-07-18 14:25:06] [SUCCESS] 项目文件备份完成，已备份 7 个文件/目录
[2025-07-18 14:25:06] [INFO] 停止DM8容器确保数据一致性...
[2025-07-18 14:25:15] [SUCCESS] 容器已停止
[2025-07-18 14:25:15] [INFO] 备份DM8数据卷...
[2025-07-18 14:25:15] [INFO] 备份主数据卷: dm8_data_volumes
[2025-07-18 14:26:09] [INFO] 数据卷大小: 8660.1MB
[2025-07-18 14:26:09] [WARN] 数据卷过大(8660.1MB)，跳过压缩直接复制文件夹
[2025-07-18 14:26:12] [SUCCESS] 主数据卷备份完成(未压缩)
[2025-07-18 14:26:12] [INFO] 备份日志数据卷: dm8_logs_volumes
[2025-07-18 14:26:13] [INFO] 日志卷大小: 0.41MB
[2025-07-18 14:26:13] [SUCCESS] 日志数据卷备份完成
[2025-07-18 14:26:13] [INFO] 备份容器配置信息
[2025-07-18 14:26:13] [SUCCESS] 容器配置信息备份完成
[2025-07-18 14:26:18] [SUCCESS] 已移动: dm8_data_volume (8660.1MB, 未压缩)
[2025-07-18 14:26:18] [SUCCESS] 已移动: dm8_logs_volume.zip (0.03MB)
[2025-07-18 14:26:18] [SUCCESS] 数据卷备份完成
[2025-07-18 14:26:18] [INFO] 重启所有服务...
[2025-07-18 14:26:19] [INFO] 等待30秒让DM8重启...
[2025-07-18 14:26:49] [SUCCESS] DM8服务重启成功
[2025-07-18 14:26:49] [INFO] 已清理临时目录
[2025-07-18 14:26:49] [INFO] 创建恢复说明文档...
[2025-07-18 14:26:49] [SUCCESS] 备份位置: .\backups\dm8_backup_20250718_142506
[2025-07-18 14:26:49] [SUCCESS] 备份大小: 9562.03 MB
