# nginx离线构建方案修正说明

## 问题描述

之前的方案存在矛盾：
1. 下载了PCRE、zlib、OpenSSL的RPM包
2. 但构建脚本中仍然尝试解压和编译这些库的源码包
3. 导致构建失败，提示找不到tar.gz文件

## 修正方案

### 核心原则
**只有nginx使用源码包编译，所有依赖库都使用RPM包安装**

### 修正内容

#### 1. 修正 `download-all-packages.sh`
- ✅ 只下载nginx源码包
- ✅ 下载所有依赖的RPM包
- ❌ 不再下载PCRE、zlib、OpenSSL源码包

#### 2. 修正 `scripts/build-nginx.sh`
- ✅ 使用 `rpm -ivh` 安装所有依赖RPM包
- ✅ 只解压nginx源码包
- ✅ nginx编译时使用系统安装的库（不指定源码路径）
- ❌ 不再编译PCRE、zlib、OpenSSL源码

#### 3. 修正 `build-nginx-offline.sh`
- ✅ 只检查nginx源码包是否存在
- ❌ 不再检查其他源码包

#### 4. Dockerfile保持不变
- ✅ 正确复制packages和centos7-rpms目录
- ✅ 正确安装RPM包

### 文件结构

```
nginx-offline-build/
├── packages/
│   └── nginx-1.24.0.tar.gz          # 只有nginx源码包
├── centos7-rpms/
│   ├── gcc-*.rpm                     # 编译工具RPM包
│   ├── pcre-devel-*.rpm             # PCRE开发包
│   ├── zlib-devel-*.rpm             # zlib开发包
│   ├── openssl-devel-*.rpm          # OpenSSL开发包
│   └── ...                          # 其他依赖RPM包
├── scripts/
│   └── build-nginx.sh               # 修正后的构建脚本
├── config/
│   ├── nginx.conf
│   └── default.conf
├── Dockerfile
├── download-all-packages.sh         # 修正后的下载脚本
└── build-nginx-offline.sh          # 修正后的构建脚本
```

### 构建流程

1. **在线环境**：
   ```bash
   ./download-all-packages.sh
   ```
   - 下载nginx-1.24.0.tar.gz
   - 下载所有依赖的RPM包

2. **传输到离线环境**：
   ```bash
   tar -czf nginx-offline-build.tar.gz .
   # 传输到离线环境并解压
   ```

3. **离线环境构建**：
   ```bash
   ./build-nginx-offline.sh
   ```
   - 检查nginx源码包
   - 检查RPM包
   - 构建Docker镜像

4. **Docker构建过程**：
   - 安装所有RPM依赖包
   - 解压nginx源码包
   - 编译nginx（使用系统库）
   - 配置和启动

### 关键修正点

#### nginx编译配置变化

**修正前**（错误）：
```bash
./configure \
    --with-pcre=/tmp/build/pcre-8.45 \
    --with-zlib=/tmp/build/zlib-1.2.13 \
    --with-openssl=/tmp/build/openssl-1.1.1w \
    # ... 其他选项
```

**修正后**（正确）：
```bash
./configure \
    --prefix=/usr/local/nginx \
    --with-http_ssl_module \
    --with-http_v2_module \
    # ... 其他选项
    # 不指定库路径，使用系统安装的库
```

#### 依赖安装方式

**修正前**（错误）：
```bash
# 解压源码包
tar -xzf pcre-8.45.tar.gz
tar -xzf zlib-1.2.13.tar.gz
tar -xzf openssl-1.1.1w.tar.gz

# 编译安装
cd pcre-8.45 && ./configure && make && make install
cd zlib-1.2.13 && ./configure && make && make install
cd openssl-1.1.1w && ./config && make && make install
```

**修正后**（正确）：
```bash
# 直接安装RPM包
rpm -ivh /tmp/centos7-rpms/*.rpm --force --nodeps
```

### 验证方法

运行以下命令验证修正是否成功：
```bash
# 检查只有nginx源码包
ls packages/
# 应该只看到: nginx-1.24.0.tar.gz

# 检查RPM包
ls centos7-rpms/ | grep -E "(pcre|zlib|openssl)"
# 应该看到: pcre-devel, zlib-devel, openssl-devel等RPM包

# 检查构建脚本
grep -n "tar.*tar.gz" scripts/build-nginx.sh
# 应该只看到nginx源码包的解压命令

# 检查RPM安装命令
grep -n "rpm -ivh" scripts/build-nginx.sh
# 应该看到RPM包安装命令
```

### 优势

1. **一致性**：方案逻辑一致，避免混用源码包和RPM包
2. **简化**：减少编译时间，只编译nginx本身
3. **稳定性**：使用系统RPM包，依赖关系更稳定
4. **维护性**：更容易更新依赖库版本

现在的方案完全符合你的要求：**除了nginx为tar.gz包外，其他都为rpm包**。
