# MySQL 5.7.44 离线版完整备份恢复说明
备份时间: 20250719_201156
备份类型: 完整备份（配置文件 + SQL数据 + 数据卷）

## 备份内容
1. 配置文件: docker-compose.yaml, Docker<PERSON>le, my.cnf, init-mysql.sql
2. SQL数据: mysql_all_databases.sql.zip
3. 数据卷:
   - mysql_data_volume.zip (主数据)
   - mysql_logs_volume.zip (日志)

## 恢复命令
.\backup-scripts\mysql-restore-cn.ps1 -BackupPath ".\backups\20250719_201156"

## 注意事项
- 恢复将完全替换现有数据
- 确保Docker Desktop正在运行
- 恢复期间现有服务将被停止
- 恢复完成后服务将自动启动

## 访问信息
- MySQL: localhost:3306
- 用户名: root
- 密码: root
- 数据库: mysql, information_schema, performance_schema, sys
