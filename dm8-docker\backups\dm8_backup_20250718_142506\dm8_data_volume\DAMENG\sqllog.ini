BUF_TOTAL_SIZE          = 10240         #SQLs Log Buffer Total Size(K)(1024~1024000)
    BUF_SIZE                = 1024          #SQLs Log Buffer Size(K)(50~102400)
    BUF_KEEP_CNT            = 6             #SQLs Log buffer keeped count(1~100)

    [SLOG_ALL]
    FILE_PATH    = ../log
    PART_STOR    = 0
    SWITCH_MODE  = 2
    SWITCH_LIMIT   = 128
    ASYNC_FLUSH   = 1
    FILE_NUM = 5
    ITEMS    = 0 
    SQL_TRACE_MASK  = 1 
    MIN_EXEC_TIME = 0 
    USER_MODE   = 0 
    USERS =
    EXECTIME_PREC_FLAG = 0 
    
    [SLOG_ERROR]
    SQL_TRACE_MASK = 23
    FILE_PATH      = ../log
    
    [SLOG_DDL]
    SQL_TRACE_MASK = 3
    
    [SLOG_LONG_SQL]
    SQL_TRACE_MASK = 25
    MIN_EXEC_TIME = 60000 
