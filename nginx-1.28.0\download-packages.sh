#!/bin/bash

# nginx 1.28.0 离线构建包下载脚本
# 用于在联网环境下载所有必需的包

set -e

echo "================================================================================"
echo "                    nginx 1.28.0 离线构建包下载脚本"
echo "================================================================================"

# 创建目录
mkdir -p packages centos7-rpms

echo "=== 下载nginx源码包 ==="

# 下载nginx 1.28.0（最新稳定版）
NGINX_VERSION="1.28.0"
NGINX_URL="http://nginx.org/download/nginx-${NGINX_VERSION}.tar.gz"

echo "下载nginx ${NGINX_VERSION}（最新稳定版）..."
if [ ! -f "packages/nginx-${NGINX_VERSION}.tar.gz" ]; then
    echo "从官网下载nginx源码包..."
    if curl -L -o "packages/nginx-${NGINX_VERSION}.tar.gz" "$NGINX_URL"; then
        echo "✓ nginx-${NGINX_VERSION}.tar.gz 下载完成"

        # 验证文件大小（nginx源码包通常在1-2MB之间）
        file_size=$(stat -c%s "packages/nginx-${NGINX_VERSION}.tar.gz" 2>/dev/null || stat -f%z "packages/nginx-${NGINX_VERSION}.tar.gz" 2>/dev/null || echo "0")
        if [ "$file_size" -gt 500000 ]; then
            echo "✓ 文件大小验证通过: $(echo $file_size | awk '{printf "%.1fMB", $1/1024/1024}')"
        else
            echo "⚠️  文件大小异常，请检查下载是否完整"
        fi
    else
        echo "❌ nginx源码包下载失败"
        echo "请手动下载 $NGINX_URL 到 packages/ 目录"
        return 1
    fi
else
    echo "✓ nginx-${NGINX_VERSION}.tar.gz 已存在"
fi

echo ""
echo "=== 下载CentOS 7 RPM依赖包 ==="

# CentOS 7 镜像源
CENTOS_MIRROR="http://mirrors.aliyun.com/centos/7"
EPEL_MIRROR="http://mirrors.aliyun.com/epel/7"

# 定义需要下载的RPM包
declare -a PACKAGES=(
    # 基础编译工具
    "gcc-4.8.5-44.el7.x86_64.rpm"
    "gcc-c++-4.8.5-44.el7.x86_64.rpm"
    "make-3.82-24.el7.x86_64.rpm"
    
    # 依赖库
    "pcre-8.32-17.el7.x86_64.rpm"
    "pcre-devel-8.32-17.el7.x86_64.rpm"
    "zlib-1.2.7-18.el7.x86_64.rpm"
    "zlib-devel-1.2.7-18.el7.x86_64.rpm"
    "openssl-1.0.2k-25.el7_9.x86_64.rpm"
    "openssl-devel-1.0.2k-25.el7_9.x86_64.rpm"
    "openssl-libs-1.0.2k-25.el7_9.x86_64.rpm"
    
    # GCC依赖
    "libgcc-4.8.5-44.el7.x86_64.rpm"
    "libstdc++-4.8.5-44.el7.x86_64.rpm"
    "libstdc++-devel-4.8.5-44.el7.x86_64.rpm"
    "gmp-6.0.0-15.el7.x86_64.rpm"
    "mpfr-3.1.1-4.el7.x86_64.rpm"
    "libmpc-1.0.1-3.el7.x86_64.rpm"
    
    # 系统库
    "glibc-2.17-326.el7_9.x86_64.rpm"
    "glibc-devel-2.17-326.el7_9.x86_64.rpm"
    "glibc-headers-2.17-326.el7_9.x86_64.rpm"
    "kernel-headers-3.10.0-1160.102.1.el7.x86_64.rpm"
    
    # 开发工具
    "libverto-devel-0.2.5-4.el7.x86_64.rpm"
    "libsepol-devel-2.5-10.el7.x86_64.rpm"
    "libselinux-devel-2.5-15.el7.x86_64.rpm"
    "libcom_err-devel-1.42.9-19.el7.x86_64.rpm"
    "keyutils-libs-devel-1.5.8-3.el7.x86_64.rpm"
    "krb5-devel-1.15.1-55.el7_9.x86_64.rpm"
    
    # 可选：GeoIP支持
    "GeoIP-1.5.0-14.el7.x86_64.rpm"
    "GeoIP-devel-1.5.0-14.el7.x86_64.rpm"
)

# 下载函数
download_rpm() {
    local package=$1
    local url=$2
    
    if [ ! -f "centos7-rpms/$package" ]; then
        echo "下载 $package..."
        curl -L -o "centos7-rpms/$package" "$url/$package" || {
            echo "⚠️  从主源下载失败，尝试备用源..."
            # 尝试其他镜像源
            curl -L -o "centos7-rpms/$package" "http://mirrors.163.com/centos/7/os/x86_64/Packages/$package" || {
                echo "❌ $package 下载失败"
                return 1
            }
        }
        echo "✓ $package 下载完成"
    else
        echo "✓ $package 已存在"
    fi
}

# 下载所有RPM包
for package in "${PACKAGES[@]}"; do
    # 根据包名选择合适的源
    if [[ $package == *"GeoIP"* ]]; then
        download_rpm "$package" "$EPEL_MIRROR/x86_64/Packages/g"
    else
        download_rpm "$package" "$CENTOS_MIRROR/os/x86_64/Packages"
    fi
done

echo ""
echo "=== 验证下载的文件 ==="

echo "nginx源码包:"
ls -lh packages/

echo ""
echo "RPM依赖包:"
ls -lh centos7-rpms/ | head -10
echo "..."
echo "总计 $(ls centos7-rpms/ | wc -l) 个RPM包"

echo ""
echo "=== 创建包信息文件 ==="

cat > packages/README.txt << EOF
nginx 1.28.0 离线构建包
========================

包含文件:
- nginx-${NGINX_VERSION}.tar.gz: nginx源码包

下载时间: $(date)
下载来源: http://nginx.org/
EOF

cat > centos7-rpms/README.txt << EOF
CentOS 7 RPM依赖包
==================

包含 $(ls centos7-rpms/*.rpm | wc -l) 个RPM包，用于nginx编译环境搭建。

主要包括:
- GCC编译器及相关工具
- OpenSSL开发库
- PCRE正则表达式库
- zlib压缩库
- 系统开发头文件

下载时间: $(date)
下载来源: 阿里云CentOS镜像
EOF

echo ""
echo "================================================================================"
echo "                              下载完成！"
echo "================================================================================"
echo ""
echo "下载统计:"
echo "  nginx源码包: $(ls packages/*.tar.gz | wc -l) 个"
echo "  RPM依赖包: $(ls centos7-rpms/*.rpm | wc -l) 个"
echo ""
echo "接下来可以:"
echo "1. 将整个目录打包: tar -czf nginx-1.28.0-offline-build.tar.gz ."
echo "2. 传输到离线环境"
echo "3. 解压后运行: docker build -t nginx-1.28.0-offline ."
echo ""
echo "🎉 所有包下载完成，可以进行离线构建了！"
