# 达梦数据库 DM8 Docker 镜像
# 基于银河麒麟操作系统

FROM hxsoong/kylin:v10-sp3

# 设置工作目录
WORKDIR /tmp

# 安装必要的依赖包
RUN yum update -y && \
    yum install -y \
    libaio \
    numactl-libs \
    unzip \
    wget \
    net-tools \
    lsof \
    which \
    sudo \
    psmisc \
    openssl \
    expect \
    glibc-langpack-zh \
    && yum clean all

# 创建dmdba用户和组
RUN groupadd -g 1001 dmdba && \
    useradd -u 1001 -g dmdba -m -s /bin/bash dmdba && \
    echo "dmdba:$(openssl passwd -1 dmdba)" | chpasswd -e && \
    echo "dmdba ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# 复制达梦数据库安装文件和脚本
COPY dm8_20250506_x86_rh7_64/ /tmp/dm8_setup/
COPY install_dm8.exp /tmp/install_dm8.exp
COPY docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh
COPY dm.ini /tmp/dm.ini

# 设置文件权限并运行安装
RUN echo "开始安装达梦数据库..." && \
    echo "设置文件权限..." && \
    chmod 755 /tmp/dm8_setup/DMInstall.bin && \
    chmod +x /tmp/install_dm8.exp && \
    chmod +x /usr/local/bin/docker-entrypoint.sh && \
    echo "执行expect自动安装脚本..." && \
    /tmp/install_dm8.exp && \
    echo "清理安装文件..." && \
    rm -rf /tmp/dm8_setup /tmp/install_dm8.exp

# 创建数据目录并设置权限，复制配置文件
RUN mkdir -p /opt/dmdbms/data && \
    mkdir -p /opt/dmdbms/log && \
    cp /tmp/dm.ini /opt/dmdbms/bin/dm.ini && \
    chown -R dmdba:dmdba /opt/dmdbms && \
    rm -f /tmp/dm.ini

# 设置环境变量
ENV DM_HOME=/opt/dmdbms
ENV PATH=$DM_HOME/bin:$PATH
ENV LD_LIBRARY_PATH=$DM_HOME/bin:$LD_LIBRARY_PATH

# 暴露达梦数据库默认端口
EXPOSE 5236

# 切换到dmdba用户
USER dmdba

# 设置工作目录为达梦安装目录
WORKDIR /opt/dmdbms

# 设置启动命令
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
CMD ["dmserver"]
