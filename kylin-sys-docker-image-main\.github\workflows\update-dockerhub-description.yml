name: Update Docker Hub Repo Description

on:
  workflow_dispatch:

jobs:
  update_hub_description:
    name: Update Docker Hub Repo Description
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Update repo description
        uses: peter-evans/dockerhub-description@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}
          repository: hxsoong/kylin