# PowerShell脚本：下载最新版本的nginx和依赖库
# 获取更新的软件包以避免安全隐患

Write-Host "================================================================================"
Write-Host "                    下载最新版本的nginx和依赖库"
Write-Host "================================================================================"

# 创建下载目录
$downloadDir = "packages-latest"
if (!(Test-Path $downloadDir)) {
    New-Item -ItemType Directory -Path $downloadDir
}

# 最新软件包下载列表
$packages = @{
    # nginx最新稳定版
    "nginx-1.24.0.tar.gz" = "http://nginx.org/download/nginx-1.24.0.tar.gz"
    
    # 更新的OpenSSL版本（支持更多安全特性）
    "openssl-1.1.1w.tar.gz" = "https://www.openssl.org/source/openssl-1.1.1w.tar.gz"
    
    # 更新的PCRE版本
    "pcre-8.45.tar.gz" = "https://sourceforge.net/projects/pcre/files/pcre/8.45/pcre-8.45.tar.gz/download"
    
    # 更新的zlib版本
    "zlib-1.3.1.tar.gz" = "https://zlib.net/zlib-1.3.1.tar.gz"
    
    # 备用下载源
    "pcre-8.45-alt.tar.gz" = "https://github.com/PCRE2Project/pcre2/releases/download/pcre2-10.42/pcre2-10.42.tar.gz"
}

Write-Host "开始下载最新版本的软件包..."
Write-Host ""

$successCount = 0
$failedPackages = @()

foreach ($packageInfo in $packages.GetEnumerator()) {
    $filename = $packageInfo.Key
    $url = $packageInfo.Value
    $outputPath = "$downloadDir\$filename"
    
    Write-Host "下载: $filename" -ForegroundColor Cyan
    Write-Host "  源: $url" -ForegroundColor Gray
    
    try {
        # 检查文件是否已存在
        if (Test-Path $outputPath) {
            Write-Host "  ✓ 文件已存在，跳过下载" -ForegroundColor Yellow
            $successCount++
            continue
        }
        
        # 对于SourceForge的特殊处理
        if ($url -like "*sourceforge.net*") {
            # SourceForge会重定向，需要特殊处理
            $webClient = New-Object System.Net.WebClient
            $webClient.DownloadFile($url, $outputPath)
        } else {
            Invoke-WebRequest -Uri $url -OutFile $outputPath -ErrorAction Stop
        }
        
        Write-Host "  ✓ 下载成功" -ForegroundColor Green
        $successCount++
    }
    catch {
        Write-Host "  ❌ 下载失败: $($_.Exception.Message)" -ForegroundColor Red
        $failedPackages += $filename
        
        # 尝试备用源
        $altUrls = @()
        
        if ($filename -like "*nginx*") {
            $altUrls = @(
                "https://nginx.org/download/nginx-1.24.0.tar.gz",
                "http://nginx.org/download/nginx-1.24.0.tar.gz"
            )
        } elseif ($filename -like "*openssl*") {
            $altUrls = @(
                "https://github.com/openssl/openssl/archive/refs/tags/OpenSSL_1_1_1w.tar.gz",
                "https://www.openssl.org/source/old/1.1.1/openssl-1.1.1w.tar.gz"
            )
        } elseif ($filename -like "*pcre*") {
            $altUrls = @(
                "https://github.com/PhilipHazel/pcre2/releases/download/pcre2-10.42/pcre2-10.42.tar.gz",
                "https://sourceforge.net/projects/pcre/files/pcre/8.45/pcre-8.45.tar.gz"
            )
        } elseif ($filename -like "*zlib*") {
            $altUrls = @(
                "https://github.com/madler/zlib/archive/refs/tags/v1.3.1.tar.gz",
                "https://zlib.net/fossils/zlib-1.3.1.tar.gz"
            )
        }
        
        $downloaded = $false
        foreach ($altUrl in $altUrls) {
            try {
                Write-Host "  尝试备用源..." -ForegroundColor Yellow
                Invoke-WebRequest -Uri $altUrl -OutFile $outputPath -ErrorAction Stop
                Write-Host "  ✓ 从备用源下载成功" -ForegroundColor Green
                $downloaded = $true
                $successCount++
                $failedPackages = $failedPackages | Where-Object { $_ -ne $filename }
                break
            }
            catch {
                # 静默失败，继续尝试下一个源
            }
        }
        
        if (!$downloaded) {
            Write-Host "  ❌ 所有源都下载失败" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "================================================================================"
Write-Host "                        下载完成统计"
Write-Host "================================================================================"
Write-Host "总包数: $($packages.Count)" -ForegroundColor White
Write-Host "成功下载: $successCount" -ForegroundColor Green
Write-Host "下载失败: $($failedPackages.Count)" -ForegroundColor Red

if ($failedPackages.Count -gt 0) {
    Write-Host ""
    Write-Host "失败的包:" -ForegroundColor Red
    foreach ($pkg in $failedPackages) {
        Write-Host "  - $pkg" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "下载的包保存在: $downloadDir\"
Write-Host ""
Write-Host "版本信息:"
Write-Host "- nginx: 1.24.0 (最新稳定版，支持HTTP/3等新特性)"
Write-Host "- OpenSSL: 1.1.1w (长期支持版本，安全性更好)"
Write-Host "- PCRE: 8.45 (更好的正则表达式支持)"
Write-Host "- zlib: 1.3.1 (最新压缩库)"
Write-Host ""
Write-Host "接下来的步骤:"
Write-Host "1. 将新下载的包复制到packages目录:"
Write-Host "   Copy-Item $downloadDir\*.tar.gz packages\"
Write-Host ""
Write-Host "2. 更新构建脚本以使用新版本"
Write-Host "3. 重新运行Docker构建"
Write-Host "================================================================================"
