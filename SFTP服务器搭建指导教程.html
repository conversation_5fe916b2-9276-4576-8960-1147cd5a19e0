<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SFTP服务器搭建指导教程 - 小白版</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
            margin-top: 25px;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .warning {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .step {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }
        .step-number {
            background-color: #3498db;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        .faq {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .faq-question {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }
        .faq-answer {
            color: #6c757d;
        }
        ul, ol {
            padding-left: 30px;
        }
        li {
            margin: 8px 0;
        }
        .nav {
            background-color: #34495e;
            padding: 15px;
            margin: -30px -30px 30px -30px;
            border-radius: 10px 10px 0 0;
        }
        .nav a {
            color: white;
            text-decoration: none;
            margin-right: 20px;
            padding: 8px 15px;
            border-radius: 5px;
            transition: background-color 0.3s;
        }
        .nav a:hover {
            background-color: #2c3e50;
        }
        .image-placeholder {
            background-color: #ecf0f1;
            border: 2px dashed #bdc3c7;
            padding: 40px;
            text-align: center;
            margin: 15px 0;
            border-radius: 5px;
            color: #7f8c8d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav">
            <a href="#what-is">什么是SFTP</a>
            <a href="#preparation">准备工作</a>
            <a href="#method1">方法一：基础配置</a>
            <a href="#method2">方法二：安全配置</a>
            <a href="#testing">测试连接</a>
            <a href="#kylin-specific">银河麒麟特有</a>
            <a href="#faq">常见问题</a>
        </div>

        <h1>🚀 银河麒麟v10 SP3 SFTP服务器搭建指导教程</h1>
        <p style="text-align: center; color: #7f8c8d; font-size: 18px;">
            专为银河麒麟系统设计的详细教程，让你轻松搭建SFTP服务器
        </p>

        <section id="what-is">
            <h2>📚 什么是SFTP？为什么需要它？</h2>

            <div class="highlight">
                <h3>🤔 技术人员说的话是什么意思？</h3>
                <p><strong>"IV区和V区都需要自建SFTP服务器"</strong> 的意思是：</p>
                <ul>
                    <li><strong>IV区和V区</strong>：这是网络安全分区，就像把一个大房子分成不同的房间，每个房间有不同的安全级别</li>
                    <li><strong>自建SFTP服务器</strong>：在你的电脑或服务器上安装一个安全的文件传输服务</li>
                    <li><strong>内外网传输</strong>：可以在公司内部网络和外部网络之间安全地传输文件</li>
                </ul>
            </div>

            <div class="step">
                <h3>💡 简单理解</h3>
                <p>想象SFTP就像一个<strong>安全的快递服务</strong>：</p>
                <ul>
                    <li>📦 你的电脑是快递站点</li>
                    <li>🔐 SFTP是加密的快递箱</li>
                    <li>👤 用户名和密码是取件码</li>
                    <li>🚚 文件通过加密通道安全传输</li>
                </ul>
            </div>
        </section>

        <section id="preparation">
            <h2>🛠️ 准备工作</h2>

            <div class="warning">
                <h3>⚠️ 开始前请确认</h3>
                <ul>
                    <li>你的系统是银河麒麟v10 SP3（基于Linux）</li>
                    <li>你有root权限或sudo权限（能够安装软件和修改系统配置）</li>
                    <li>你的网络连接正常</li>
                    <li>系统防火墙状态（稍后需要配置）</li>
                </ul>
            </div>

            <div class="step">
                <span class="step-number">1</span>
                <strong>检查你的IP地址</strong>
                <p>打开终端（按 <kbd>Ctrl + Alt + T</kbd>），然后输入：</p>
                <div class="code">ip addr show</div>
                <p>或者使用传统命令：</p>
                <div class="code">ifconfig</div>
                <p>记下显示的IP地址（通常是eth0或ens33网卡的inet地址），稍后会用到。</p>
            </div>
        </section>

        <section id="method1">
            <h2>🎯 方法一：使用系统内置OpenSSH（推荐）</h2>

            <div class="success">
                <h3>✅ 为什么推荐这个方法？</h3>
                <ul>
                    <li>银河麒麟系统通常预装OpenSSH</li>
                    <li>稳定可靠，安全性高</li>
                    <li>配置简单，维护方便</li>
                    <li>官方支持，兼容性好</li>
                </ul>
            </div>

            <div class="step">
                <span class="step-number">1</span>
                <strong>检查OpenSSH是否已安装</strong>
                <p>打开终端，输入以下命令检查：</p>
                <div class="code">systemctl status sshd</div>
                <p>如果显示"Active (running)"说明已经安装并运行。如果显示"Unit sshd.service could not be found"，需要安装。</p>
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>安装OpenSSH服务器（如果未安装）</strong>
                <p>使用包管理器安装：</p>
                <div class="code">sudo yum install openssh-server</div>
                <p>或者使用dnf（较新版本）：</p>
                <div class="code">sudo dnf install openssh-server</div>
                <p>安装完成后会显示"Complete!"</p>
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <strong>启动SSH服务</strong>
                <p>启动SSH服务并设置开机自启：</p>
                <div class="code">sudo systemctl start sshd<br>sudo systemctl enable sshd</div>
                <p>检查服务状态：</p>
                <div class="code">sudo systemctl status sshd</div>
                <p>如果显示"Active (running)"说明服务已经成功启动。</p>
            </div>

            <div class="step">
                <span class="step-number">4</span>
                <strong>创建SFTP用户</strong>
                <div class="highlight">
                    <p><strong>重要：</strong>这一步创建的用户名和密码就是要提供给技术人员的！</p>
                </div>
                <p>创建专门用于SFTP的用户：</p>
                <div class="code">sudo useradd -m -s /bin/bash sftpuser</div>
                <p>设置用户密码：</p>
                <div class="code">sudo passwd sftpuser</div>
                <p>系统会提示你输入密码两次，请设置一个强密码（包含大小写字母、数字和特殊字符）。</p>

                <div class="highlight">
                    <p><strong>可选：创建专用的SFTP目录</strong></p>
                    <div class="code">sudo mkdir -p /home/<USER>/sftp_files<br>sudo chown sftpuser:sftpuser /home/<USER>/sftp_files</div>
                </div>
            </div>

            <div class="step">
                <span class="step-number">5</span>
                <strong>配置防火墙</strong>
                <p>银河麒麟系统通常使用firewalld防火墙，需要开放SSH端口：</p>
                <div class="code">sudo firewall-cmd --permanent --add-service=ssh<br>sudo firewall-cmd --reload</div>
                <p>检查防火墙状态：</p>
                <div class="code">sudo firewall-cmd --list-services</div>
                <p>应该能看到"ssh"在列表中。</p>

                <div class="warning">
                    <p><strong>如果使用iptables：</strong></p>
                    <div class="code">sudo iptables -A INPUT -p tcp --dport 22 -j ACCEPT<br>sudo service iptables save</div>
                </div>
            </div>

            <div class="step">
                <span class="step-number">6</span>
                <strong>测试SSH服务</strong>
                <p>检查SSH服务是否正在监听22端口：</p>
                <div class="code">sudo netstat -tlnp | grep :22</div>
                <p>或者使用ss命令：</p>
                <div class="code">sudo ss -tlnp | grep :22</div>
                <p>应该能看到类似"0.0.0.0:22"的输出，说明服务正在运行。</p>

                <div class="success">
                    <p>🎉 恭喜！你的SFTP服务器已经运行了！</p>
                </div>
            </div>
        </section>

        <section id="method2">
            <h2>🔧 方法二：配置SFTP专用环境（增强安全性）</h2>

            <div class="highlight">
                <h3>💡 为什么需要专用SFTP环境？</h3>
                <p>标准SSH允许用户执行命令，而专用SFTP环境可以限制用户只能传输文件，无法执行命令，大大提高安全性。</p>
            </div>

            <div class="step">
                <span class="step-number">1</span>
                <strong>创建SFTP组和用户</strong>
                <p>创建专用的SFTP组：</p>
                <div class="code">sudo groupadd sftpgroup</div>
                <p>创建SFTP用户并添加到组：</p>
                <div class="code">sudo useradd -g sftpgroup -s /sbin/nologin -m -d /home/<USER>/div>
                <p>设置用户密码：</p>
                <div class="code">sudo passwd sftpuser</div>
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>创建SFTP根目录</strong>
                <p>创建SFTP根目录和上传目录：</p>
                <div class="code">sudo mkdir -p /sftp/sftpuser/upload</div>
                <p>设置目录权限：</p>
                <div class="code">sudo chown root:root /sftp<br>sudo chown root:root /sftp/sftpuser<br>sudo chown sftpuser:sftpgroup /sftp/sftpuser/upload<br>sudo chmod 755 /sftp<br>sudo chmod 755 /sftp/sftpuser<br>sudo chmod 775 /sftp/sftpuser/upload</div>
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <strong>配置SSH服务</strong>
                <p>编辑SSH配置文件：</p>
                <div class="code">sudo vi /etc/ssh/sshd_config</div>
                <p>或者使用图形编辑器：</p>
                <div class="code">sudo gedit /etc/ssh/sshd_config</div>
                <p>找到并注释掉这一行（在前面加#）：</p>
                <div class="code">#Subsystem sftp /usr/lib/openssh/sftp-server</div>
                <p>在文件末尾添加以下内容：</p>
                <div class="code">
# SFTP配置
Subsystem sftp internal-sftp

# SFTP用户组配置
Match Group sftpgroup
    ChrootDirectory /sftp/%u
    ForceCommand internal-sftp
    X11Forwarding no
    AllowTcpForwarding no
                </div>
            </div>

            <div class="step">
                <span class="step-number">4</span>
                <strong>重启SSH服务</strong>
                <p>保存配置文件后重启SSH服务：</p>
                <div class="code">sudo systemctl restart sshd</div>
                <p>检查服务状态：</p>
                <div class="code">sudo systemctl status sshd</div>
                <p>如果显示错误，请检查配置文件是否有语法错误。</p>
            </div>
        </section>

        <section id="testing">
            <h2>🧪 测试SFTP连接</h2>

            <div class="step">
                <span class="step-number">1</span>
                <strong>本地测试</strong>
                <p>在银河麒麟系统上，可以使用命令行工具进行本地测试：</p>
                <div class="code">sftp sftpuser@localhost</div>
                <p>输入密码后，如果看到sftp提示符，说明服务正常运行。</p>
                <p>可以尝试一些基本命令：</p>
                <div class="code">
pwd     # 显示当前远程目录
ls      # 列出远程文件
mkdir test    # 创建测试目录
quit    # 退出
                </div>
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>使用客户端测试</strong>
                <p>推荐使用FileZilla（跨平台免费客户端）：</p>
                <ol>
                    <li>在其他电脑上安装FileZilla</li>
                    <li>打开FileZilla</li>
                    <li>在顶部填写连接信息：</li>
                    <ul>
                        <li><strong>主机</strong>：你的服务器IP地址（之前记下的）</li>
                        <li><strong>用户名</strong>：你创建的SFTP用户名</li>
                        <li><strong>密码</strong>：你设置的密码</li>
                        <li><strong>端口</strong>：22（或你自定义的端口）</li>
                    </ul>
                    <li>点击"快速连接"</li>
                </ol>
                <div class="success">
                    <p>如果连接成功，你会看到文件列表，说明SFTP服务器工作正常！</p>
                </div>
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <strong>提供给技术人员的信息</strong>
                <div class="highlight">
                    <h4>📋 连接信息清单</h4>
                    <ul>
                        <li><strong>服务器IP地址</strong>：_____________</li>
                        <li><strong>端口</strong>：22（或自定义端口）</li>
                        <li><strong>用户名</strong>：_____________</li>
                        <li><strong>密码</strong>：_____________</li>
                        <li><strong>协议</strong>：SFTP</li>
                        <li><strong>系统类型</strong>：银河麒麟v10 SP3</li>
                    </ul>
                    <p><strong>注意：</strong>如果需要外网访问，还需要配置防火墙和路由器端口转发。</p>
                </div>
            </div>
        </section>

        <section id="kylin-specific">
            <h2>🐉 银河麒麟系统特有配置</h2>

            <div class="highlight">
                <h3>💡 银河麒麟v10 SP3系统说明</h3>
                <p>银河麒麟是基于Linux内核的国产操作系统，在SFTP配置上与标准Linux系统基本相同，但有一些特殊注意事项。</p>
            </div>

            <div class="step">
                <span class="step-number">1</span>
                <strong>检查系统版本</strong>
                <p>确认你的银河麒麟系统版本：</p>
                <div class="code">cat /etc/kylin-release</div>
                <p>或者：</p>
                <div class="code">uname -a</div>
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>包管理器使用</strong>
                <p>银河麒麟v10 SP3通常使用yum或dnf作为包管理器：</p>
                <div class="code">
# 更新软件包列表
sudo yum update

# 或者使用dnf（较新版本）
sudo dnf update
                </div>
            </div>

            <div class="step">
                <span class="step-number">3</span>
                <strong>SELinux配置</strong>
                <p>银河麒麟系统通常启用SELinux，可能需要配置相关策略：</p>
                <div class="code">
# 检查SELinux状态
getenforce

# 如果是Enforcing模式，可能需要设置SFTP相关策略
sudo setsebool -P ssh_chroot_rw_homedirs on
                </div>
                <div class="warning">
                    <p>⚠️ 如果遇到权限问题，可以临时设置SELinux为宽松模式进行测试：</p>
                    <div class="code">sudo setenforce 0</div>
                    <p>测试完成后记得恢复：</p>
                    <div class="code">sudo setenforce 1</div>
                </div>
            </div>

            <div class="step">
                <span class="step-number">4</span>
                <strong>网络配置检查</strong>
                <p>银河麒麟系统的网络配置文件位置：</p>
                <div class="code">
# 查看网络接口
ip link show

# 查看网络配置
cat /etc/sysconfig/network-scripts/ifcfg-*
                </div>
            </div>

            <div class="step">
                <span class="step-number">5</span>
                <strong>系统服务管理</strong>
                <p>银河麒麟使用systemd管理服务，常用命令：</p>
                <div class="code">
# 查看所有服务状态
systemctl list-units --type=service

# 查看SSH服务详细信息
systemctl show sshd

# 重新加载systemd配置
sudo systemctl daemon-reload
                </div>
            </div>
        </section>

        <section id="faq">
            <h2>❓ 常见问题解答</h2>

            <div class="faq">
                <div class="faq-question">Q1: 为什么连接不上SFTP服务器？</div>
                <div class="faq-answer">
                    <p><strong>可能的原因和解决方法：</strong></p>
                    <ul>
                        <li><strong>防火墙阻止</strong>：检查银河麒麟防火墙设置，确保22端口开放</li>
                        <li><strong>服务未启动</strong>：运行 <code>systemctl status sshd</code> 检查服务状态</li>
                        <li><strong>端口被占用</strong>：尝试更改端口号（在 <code>/etc/ssh/sshd_config</code> 中修改 Port 参数）</li>
                        <li><strong>IP地址错误</strong>：使用 <code>ip addr show</code> 重新确认服务器的IP地址</li>
                        <li><strong>用户名密码错误</strong>：检查用户名和密码是否正确</li>
                        <li><strong>权限问题</strong>：检查目录权限，使用 <code>ls -la</code> 查看</li>
                    </ul>
                </div>
            </div>

            <div class="faq">
                <div class="faq-question">Q2: 如何让外网也能访问我的SFTP服务器？</div>
                <div class="faq-answer">
                    <p><strong>需要配置路由器端口转发：</strong></p>
                    <ol>
                        <li>登录你的路由器管理界面（通常是***********或***********）</li>
                        <li>找到"端口转发"或"虚拟服务器"设置</li>
                        <li>添加新规则：外部端口22 → 内部IP（你的服务器IP）端口22</li>
                        <li>保存设置并重启路由器</li>
                    </ol>
                    <div class="warning">
                        <p>⚠️ <strong>安全提醒：</strong>开放外网访问会增加安全风险，建议使用强密码并定期更换。最好配置SSH密钥认证替代密码认证。</p>
                    </div>
                </div>
            </div>

            <div class="faq">
                <div class="faq-question">Q3: SFTP和FTP有什么区别？</div>
                <div class="faq-answer">
                    <ul>
                        <li><strong>SFTP（安全）</strong>：基于SSH协议，数据传输过程中会加密，更安全</li>
                        <li><strong>FTP（普通）</strong>：数据传输是明文的，容易被窃听</li>
                        <li><strong>端口不同</strong>：SFTP使用22端口，FTP使用21端口</li>
                        <li><strong>建议</strong>：银河麒麟系统推荐使用SFTP，因为更安全且已内置支持</li>
                    </ul>
                </div>
            </div>

            <div class="faq">
                <div class="faq-question">Q4: 如何限制用户只能访问特定文件夹？</div>
                <div class="faq-answer">
                    <p><strong>使用Chroot环境：</strong></p>
                    <p>这就是我们在方法二中介绍的配置方式，通过修改 <code>/etc/ssh/sshd_config</code> 文件：</p>
                    <div class="code">
Match Group sftpgroup
    ChrootDirectory /sftp/%u
    ForceCommand internal-sftp
                    </div>
                    <p>这样配置后，用户将被限制在 <code>/sftp/用户名</code> 目录中，无法访问系统其他目录。</p>
                </div>
            </div>

            <div class="faq">
                <div class="faq-question">Q5: 服务器重启后SFTP服务会自动启动吗？</div>
                <div class="faq-answer">
                    <p>如果你按照教程设置了服务自启动，重启后会自动运行：</p>
                    <div class="code">sudo systemctl enable sshd</div>
                    <p>检查服务是否设置为自启动：</p>
                    <div class="code">systemctl is-enabled sshd</div>
                    <p>如果显示"enabled"，说明已设置为自启动。</p>
                </div>
            </div>

            <div class="faq">
                <div class="faq-question">Q6: 如何查看SFTP服务器的连接日志？</div>
                <div class="faq-answer">
                    <p>在银河麒麟系统中，SSH/SFTP日志通常位于：</p>
                    <div class="code">
/var/log/auth.log  # 或者
/var/log/secure    # 在某些系统中
                    </div>
                    <p>查看最近的日志：</p>
                    <div class="code">sudo tail -f /var/log/auth.log</div>
                    <p>或者使用journalctl：</p>
                    <div class="code">sudo journalctl -u sshd</div>
                </div>
            </div>

            <div class="faq">
                <div class="faq-question">Q7: 如何提高SFTP服务器的安全性？</div>
                <div class="faq-answer">
                    <ul>
                        <li><strong>使用SSH密钥认证</strong>：比密码更安全</li>
                        <li><strong>禁用root登录</strong>：在sshd_config中设置 <code>PermitRootLogin no</code></li>
                        <li><strong>更改默认端口</strong>：避免自动扫描攻击</li>
                        <li><strong>使用fail2ban</strong>：自动封禁多次失败登录的IP</li>
                        <li><strong>定期更新系统</strong>：保持系统和OpenSSH最新版本</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="security">
            <h2>🔒 安全建议</h2>

            <div class="warning">
                <h3>🛡️ 重要安全提醒</h3>
                <ul>
                    <li><strong>使用强密码</strong>：至少12位，包含大小写字母、数字和特殊字符</li>
                    <li><strong>定期更换密码</strong>：建议每3个月更换一次</li>
                    <li><strong>限制访问权限</strong>：使用Chroot环境限制用户只能访问特定目录</li>
                    <li><strong>监控连接日志</strong>：定期检查 <code>/var/log/auth.log</code> 是否有异常连接</li>
                    <li><strong>及时更新系统</strong>：保持银河麒麟系统和OpenSSH为最新版本</li>
                    <li><strong>备份重要数据</strong>：定期备份服务器上的重要文件</li>
                </ul>
            </div>

            <div class="step">
                <span class="step-number">💡</span>
                <strong>银河麒麟系统特有安全措施</strong>
                <ul>
                    <li><strong>使用SSH密钥认证</strong>：配置 <code>~/.ssh/authorized_keys</code> 实现无密码安全登录</li>
                    <li><strong>更改默认端口</strong>：在 <code>/etc/ssh/sshd_config</code> 中修改端口（如2222）可以减少自动攻击</li>
                    <li><strong>配置防火墙规则</strong>：使用 <code>firewall-cmd</code> 限制只允许特定IP连接</li>
                    <li><strong>安装fail2ban</strong>：自动封禁多次尝试失败登录的IP地址</li>
                    <li><strong>SELinux配置</strong>：确保SELinux策略正确配置，增强系统安全性</li>
                </ul>
            </div>
        </section>

        <section id="summary">
            <h2>📝 总结</h2>

            <div class="success">
                <h3>🎉 恭喜你完成了SFTP服务器搭建！</h3>
                <p>现在你已经成功搭建了SFTP服务器，可以安全地与技术人员进行文件传输了。</p>
            </div>

            <div class="highlight">
                <h3>📋 下一步要做的事情</h3>
                <ol>
                    <li><strong>测试连接</strong>：确保SFTP服务器工作正常</li>
                    <li><strong>准备连接信息</strong>：整理好IP地址、用户名、密码等信息</li>
                    <li><strong>联系技术人员</strong>：将连接信息提供给他们</li>
                    <li><strong>协助测试</strong>：配合技术人员进行连接测试</li>
                    <li><strong>定期维护</strong>：定期检查服务状态和安全性</li>
                </ol>
            </div>

            <div class="step">
                <h3>📞 需要帮助？</h3>
                <p>如果在搭建过程中遇到问题，可以：</p>
                <ul>
                    <li>重新阅读相关步骤</li>
                    <li>检查常见问题解答部分</li>
                    <li>确认所有设置都按照教程进行</li>
                    <li>寻求技术支持帮助</li>
                </ul>
            </div>

            <div class="highlight">
                <h3>🔄 定期维护清单</h3>
                <ul>
                    <li>每周检查服务器运行状态</li>
                    <li>每月查看连接日志</li>
                    <li>每季度更换密码</li>
                    <li>及时安装软件更新</li>
                    <li>定期备份重要数据</li>
                </ul>
            </div>
        </section>

        <footer style="margin-top: 50px; padding-top: 30px; border-top: 2px solid #ecf0f1; text-align: center; color: #7f8c8d;">
            <p>📚 本教程专为银河麒麟v10 SP3系统设计，如有疑问请仔细阅读相关章节</p>
            <p>🐉 适用系统：银河麒麟v10 SP3（基于Linux）</p>
            <p>🔄 最后更新：2025年7月</p>
            <p style="margin-top: 20px;">
                <a href="#what-is" style="color: #3498db; text-decoration: none;">返回顶部</a>
            </p>
        </footer>

    </div>

    <script>
        // 简单的页面交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有的步骤添加点击展开/收缩功能
            const steps = document.querySelectorAll('.step');
            steps.forEach(function(step) {
                step.style.cursor = 'pointer';
                step.addEventListener('click', function() {
                    if (this.style.opacity === '0.7') {
                        this.style.opacity = '1';
                        this.style.transform = 'scale(1)';
                    } else {
                        this.style.opacity = '0.7';
                        this.style.transform = 'scale(0.98)';
                    }
                });
            });

            // 平滑滚动到锚点
            const navLinks = document.querySelectorAll('.nav a');
            navLinks.forEach(function(link) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>