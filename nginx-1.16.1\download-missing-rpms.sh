#!/bin/bash

# 下载缺失的CentOS 7 RPM包脚本
# 这个脚本用于下载nginx离线构建所需的缺失RPM包

set -e

echo "================================================================================"
echo "                    下载缺失的CentOS 7 RPM包"
echo "================================================================================"

# 创建下载目录
DOWNLOAD_DIR="centos7-rpms-additional"
mkdir -p "$DOWNLOAD_DIR"

# CentOS 7镜像源（使用vault.centos.org因为CentOS 7已经EOL）
BASE_URL="http://vault.centos.org/7.9.2009/os/x86_64/Packages"

# 需要下载的包列表
PACKAGES=(
    "libmpc-1.0.1-3.el7.x86_64.rpm"
    "mpfr-3.1.1-4.el7.x86_64.rpm"
    "gmp-6.0.0-15.el7.x86_64.rpm"
)

echo "开始下载缺失的RPM包..."

for package in "${PACKAGES[@]}"; do
    echo "下载: $package"
    if curl -L -o "$DOWNLOAD_DIR/$package" "$BASE_URL/$package"; then
        echo "✓ $package 下载成功"
    else
        echo "❌ $package 下载失败，尝试备用源..."
        # 尝试其他镜像源
        ALT_URLS=(
            "http://mirror.centos.org/centos/7/os/x86_64/Packages"
            "https://buildlogs.centos.org/centos/7/os/x86_64/Packages"
        )
        
        downloaded=false
        for alt_url in "${ALT_URLS[@]}"; do
            echo "尝试: $alt_url/$package"
            if curl -L -o "$DOWNLOAD_DIR/$package" "$alt_url/$package"; then
                echo "✓ $package 从备用源下载成功"
                downloaded=true
                break
            fi
        done
        
        if [ "$downloaded" = false ]; then
            echo "❌ $package 从所有源下载失败"
            echo "请手动下载此包或检查网络连接"
        fi
    fi
done

echo ""
echo "================================================================================"
echo "                        下载完成"
echo "================================================================================"
echo "下载的包保存在: $DOWNLOAD_DIR/"
echo ""
echo "请将下载的包复制到 centos7-rpms/ 目录中："
echo "cp $DOWNLOAD_DIR/*.rpm centos7-rpms/"
echo ""
echo "然后重新运行Docker构建："
echo "docker build -t nginx-offline-build -f Dockerfile ."
echo "================================================================================"
