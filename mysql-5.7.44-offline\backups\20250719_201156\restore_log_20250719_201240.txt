[2025-07-19 20:12:40] [INFO] 开始MySQL完整恢复
[2025-07-19 20:12:40] [INFO] 备份路径: D:\Code\MicrosoftCode\mysql-5.7.44-offline\backups\20250719_201156
[2025-07-19 20:12:40] [INFO] 临时目录: D:\Code\MicrosoftCode\mysql-5.7.44-offline\temp_restore_20250719_201240
[2025-07-19 20:12:40] [SUCCESS] 临时目录创建成功
[2025-07-19 20:12:40] [INFO] 检查Docker环境...
[2025-07-19 20:12:40] [SUCCESS] Docker版本: Docker version 28.0.4, build b8034c0
[2025-07-19 20:12:40] [INFO] 验证备份文件...
[2025-07-19 20:12:40] [SUCCESS] 找到备份文件: mysql_data_volume.zip (2.29MB)
[2025-07-19 20:12:40] [SUCCESS] 找到备份文件: mysql_logs_volume.zip (0.01MB)
[2025-07-19 20:12:40] [INFO] 检测容器用户ID...
[2025-07-19 20:12:41] [WARN] 无法检测MySQL用户ID，使用默认值999
[2025-07-19 20:12:41] [INFO] 停止现有服务...
[2025-07-19 20:12:58] [SUCCESS] 现有服务已停止
[2025-07-19 20:12:58] [INFO] 删除现有数据卷...
[2025-07-19 20:12:58] [SUCCESS] 已删除数据卷: mysql_data_offline
[2025-07-19 20:12:58] [SUCCESS] 已删除数据卷: mysql_logs_offline
[2025-07-19 20:12:58] [INFO] 恢复配置文件...
[2025-07-19 20:12:58] [SUCCESS] 已恢复: docker-compose.yaml
[2025-07-19 20:12:58] [SUCCESS] 已恢复: Dockerfile
[2025-07-19 20:12:58] [SUCCESS] 已恢复: my.cnf
[2025-07-19 20:12:58] [SUCCESS] 已恢复: init-mysql.sql
[2025-07-19 20:12:58] [SUCCESS] 配置文件恢复完成，已恢复 4 个文件
[2025-07-19 20:12:58] [INFO] 创建新数据卷...
[2025-07-19 20:12:58] [SUCCESS] 已创建数据卷: mysql_data_offline
[2025-07-19 20:12:58] [SUCCESS] 已创建数据卷: mysql_logs_offline
[2025-07-19 20:12:58] [INFO] 恢复MySQL主数据卷...
[2025-07-19 20:13:20] [INFO] 修复数据目录权限...
[2025-07-19 20:13:21] [SUCCESS] MySQL主数据卷恢复完成
[2025-07-19 20:13:21] [INFO] 恢复MySQL日志卷...
[2025-07-19 20:13:21] [INFO] 修复日志目录权限...
[2025-07-19 20:13:22] [SUCCESS] MySQL日志卷恢复完成
[2025-07-19 20:13:22] [INFO] 启动MySQL服务...
[2025-07-19 20:13:22] [INFO] 等待MySQL启动...
[2025-07-19 20:13:27] [INFO] 检查MySQL状态 (尝试 1/20)...
[2025-07-19 20:13:33] [INFO] 检查MySQL状态 (尝试 2/20)...
[2025-07-19 20:13:33] [SUCCESS] MySQL服务启动成功
[2025-07-19 20:13:33] [INFO] 恢复SQL数据...
[2025-07-19 20:13:33] [INFO] 从以下文件导入SQL数据: mysql_all_databases.sql
[2025-07-19 20:13:34] [SUCCESS] SQL数据导入成功完成
[2025-07-19 20:13:34] [INFO] 验证恢复结果...
[2025-07-19 20:13:34] [SUCCESS] 容器状态: 运行中
[2025-07-19 20:13:34] [SUCCESS] 数据库连接: 正常
[2025-07-19 20:13:34] [INFO] 可用数据库: Database, information_schema, mysql, performance_schema, sys, test_persistence
[2025-07-19 20:13:34] [INFO] 已清理临时目录
[2025-07-19 20:13:34] [SUCCESS] 恢复成功完成
[2025-07-19 20:13:34] [SUCCESS] MySQL服务可在以下地址访问: localhost:3306
[2025-07-19 20:13:34] [SUCCESS] 用户名: root, 密码: root
