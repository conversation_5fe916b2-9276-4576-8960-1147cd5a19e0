<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kuboard Web UI安装部署完整教程</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 13px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 机器标识样式 */
        .machine-tag {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            margin: 0 8px 12px 0;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .machine-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .machine-tag:hover::before {
            left: 100%;
        }

        .machine-master {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
        }

        .machine-client {
            background: linear-gradient(135deg, #a55eea 0%, #8e44ad 100%);
            color: white;
        }

        .machine-browser {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: var(--light-surface);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        th,
        td {
            padding: 18px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 下载链接 */
        .download-link {
            background: linear-gradient(135deg, var(--success-color) 0%, #38a169 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            display: inline-flex;
            align-items: center;
            margin: 8px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            font-size: 14px;
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
        }

        .download-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .download-link:hover::before {
            left: 100%;
        }

        .download-link:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
        }

        .download-link i {
            margin-right: 8px;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .content-wrapper {
                padding: 30px;
            }

            .container {
                padding: 40px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 1001;
                background: var(--primary-color);
                color: white;
                border: none;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                font-size: 20px;
                cursor: pointer;
                box-shadow: var(--shadow-lg);
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
                border-radius: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 16px;
                margin-right: 15px;
            }
        }
    </style>
</head>

<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-desktop"></i> Kuboard教程</h2>
            <p>Kubernetes Web UI管理界面</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#introduction"><i class="fas fa-info-circle"></i>1. Kuboard简介</a></li>
                <li><a href="#prerequisites"><i class="fas fa-check-circle"></i>2. 前置条件</a></li>
                <li><a href="#installation-methods"><i class="fas fa-cogs"></i>3. 安装方式选择</a></li>
                <li><a href="#docker-install"><i class="fab fa-docker"></i>4. Docker安装方式</a></li>
                <li><a href="#k8s-install"><i class="fas fa-dharmachakra"></i>5. Kubernetes安装方式</a></li>
                <li><a href="#first-access"><i class="fas fa-sign-in-alt"></i>6. 首次访问配置</a></li>
                <li><a href="#cluster-import"><i class="fas fa-plus-circle"></i>7. 导入K8s集群</a></li>
                <li><a href="#basic-usage"><i class="fas fa-play"></i>8. 基本使用指南</a></li>
                <li><a href="#advanced-features"><i class="fas fa-star"></i>9. 高级功能</a></li>
                <li><a href="#troubleshooting"><i class="fas fa-bug"></i>10. 故障排查</a></li>
                <li><a href="#security"><i class="fas fa-shield-alt"></i>11. 安全配置</a></li>
                <li><a href="#deployment-verification"><i class="fas fa-check-double"></i>12. 部署验证</a></li>
                <li><a href="#summary"><i class="fas fa-flag-checkered"></i>13. 总结</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-desktop"></i> Kuboard Web UI安装部署完整教程</h1>

                <div class="info-box">
                    <strong><i class="fas fa-info-circle"></i>
                        教程说明：</strong>本教程将详细介绍如何安装和配置Kuboard，这是一个功能强大的Kubernetes Web
                    UI管理界面。我们会从最基础的概念开始，逐步指导您完成安装、配置和使用的全过程。
                </div>

                <div class="warning-box">
                    <strong><i class="fas fa-exclamation-triangle"></i> 机器标识说明：</strong>
                    <div style="margin-top: 15px;">
                        <span class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</span> -
                        在Kubernetes Master节点执行<br>
                        <span class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</span> -
                        在有kubectl的机器上执行<br>
                        <span class="machine-tag machine-browser"><i class="fas fa-globe"></i> 浏览器访问</span> -
                        通过Web浏览器操作
                    </div>
                </div>

                <section id="introduction">
                    <h2><span class="step-number">1</span>Kuboard简介</h2>

                    <h3><i class="fas fa-question-circle"></i> 1.1 什么是Kuboard？</h3>
                    <p>Kuboard是一个功能强大的Kubernetes Web
                        UI管理界面，它为Kubernetes集群提供了直观、易用的图形化管理功能。作为初学者，您可以把Kuboard理解为Kubernetes的"桌面管理工具"，就像Windows的资源管理器一样，让您可以通过点击鼠标来管理复杂的Kubernetes集群。
                    </p>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 为什么需要Kuboard？</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>简化操作：</strong>将复杂的kubectl命令转换为直观的图形界面操作</li>
                            <li><strong>可视化管理：</strong>以图形化方式展示Pod、Service、Deployment等资源</li>
                            <li><strong>实时监控：</strong>提供集群资源使用情况的实时监控</li>
                            <li><strong>日志查看：</strong>方便地查看容器日志和事件信息</li>
                            <li><strong>多集群管理：</strong>可以同时管理多个Kubernetes集群</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-star"></i> 1.2 Kuboard的核心功能</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-cogs"></i> 功能模块</th>
                            <th><i class="fas fa-info-circle"></i> 功能描述</th>
                            <th><i class="fas fa-users"></i> 适用场景</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-tachometer-alt"></i> 集群概览</td>
                            <td>显示集群整体状态、资源使用情况</td>
                            <td>快速了解集群健康状态</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-cubes"></i> 工作负载管理</td>
                            <td>管理Deployment、StatefulSet、DaemonSet等</td>
                            <td>应用部署和更新</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-network-wired"></i> 服务与网络</td>
                            <td>管理Service、Ingress、NetworkPolicy等</td>
                            <td>配置应用访问和网络策略</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-hdd"></i> 存储管理</td>
                            <td>管理PV、PVC、StorageClass等</td>
                            <td>配置持久化存储</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-key"></i> 配置管理</td>
                            <td>管理ConfigMap、Secret等</td>
                            <td>应用配置和密钥管理</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-chart-line"></i> 监控告警</td>
                            <td>集成Prometheus监控和告警</td>
                            <td>系统监控和故障预警</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-code-branch"></i> 1.3 版本说明</h3>
                    <div class="success-box">
                        <strong><i class="fas fa-info-circle"></i> 推荐版本：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>Kuboard v3.x：</strong>最新稳定版本，支持多集群管理</li>
                            <li><strong>支持的Kubernetes版本：</strong>v1.20 - v1.28</li>
                            <li><strong>浏览器要求：</strong>Chrome 70+、Firefox 65+、Safari 12+</li>
                        </ul>
                    </div>
                </section>

                <section id="prerequisites">
                    <h2><span class="step-number">2</span>前置条件</h2>

                    <h3><i class="fas fa-check-circle"></i> 2.1 环境要求</h3>
                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i>
                            重要提醒：</strong>在开始安装Kuboard之前，请确保您已经有一个正常运行的Kubernetes集群。如果还没有，请先参考我们的《银河麒麟K8s安装教程》完成集群搭建。
                    </div>

                    <table>
                        <tr>
                            <th><i class="fas fa-list"></i> 检查项目</th>
                            <th><i class="fas fa-cogs"></i> 要求</th>
                            <th><i class="fas fa-terminal"></i> 验证命令</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-dharmachakra"></i> Kubernetes集群</td>
                            <td>v1.20+，集群状态正常</td>
                            <td><code>kubectl get nodes</code></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-terminal"></i> kubectl工具</td>
                            <td>能够正常连接集群</td>
                            <td><code>kubectl cluster-info</code></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-network-wired"></i> 网络连通性</td>
                            <td>能够访问集群API Server</td>
                            <td><code>kubectl get pods -A</code></td>
                        </tr>
                        <tr>
                            <td><i class="fab fa-docker"></i> 容器运行时</td>
                            <td>Docker或containerd正常运行</td>
                            <td><code>docker version</code> 或 <code>crictl version</code></td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-tools"></i> 2.2 环境检查步骤</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>
                    <p>在开始安装之前，让我们先检查一下环境是否满足要求：</p>

                    <h4><i class="fas fa-check"></i> 2.2.1 检查Kubernetes集群状态</h4>
                    <pre><code># 检查集群节点状态
kubectl get nodes -o wide

# 检查系统Pod状态
kubectl get pods -n kube-system

# 检查集群信息
kubectl cluster-info</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 正常输出示例：</strong>
                        <ul style="margin-top: 15px;">
                            <li>所有节点状态应该显示为 <code>Ready</code></li>
                            <li>kube-system命名空间下的Pod应该都是 <code>Running</code> 状态</li>
                            <li>cluster-info应该显示API Server地址</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-key"></i> 2.2.2 检查权限配置</h4>
                    <pre><code># 检查当前用户权限
kubectl auth can-i "*" "*" --all-namespaces

# 检查是否可以创建命名空间
kubectl auth can-i create namespaces

# 检查是否可以创建RBAC资源
kubectl auth can-i create clusterroles</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i>
                            权限说明：</strong>Kuboard需要较高的集群权限来管理各种Kubernetes资源。如果您使用的是非管理员账户，可能需要先配置相应的RBAC权限。
                    </div>

                    <h3><i class="fas fa-cog"></i> 2.3 环境准备脚本</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 说明：</strong>运行此脚本可以自动完成Kuboard部署的环境准备工作
                    </div>

                    <pre><code>#!/bin/bash
# Kuboard环境准备脚本

echo "=== Kuboard环境准备开始 ==="

# 1. 检查kubectl命令
echo "1. 检查kubectl命令..."
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl命令未找到，请先安装kubectl"
    exit 1
fi
echo "✅ kubectl命令可用"

# 2. 检查K8s集群连接
echo "2. 检查K8s集群连接..."
kubectl cluster-info &> /dev/null
if [ $? -ne 0 ]; then
    echo "❌ 无法连接到K8s集群"
    exit 1
fi
echo "✅ K8s集群连接正常"

# 3. 检查集群版本
echo "3. 检查集群版本..."
k8s_version=$(kubectl version --short --client=false 2>/dev/null | grep "Server Version" | awk '{print $3}')
echo "K8s集群版本: $k8s_version"

# 4. 检查节点状态
echo "4. 检查节点状态..."
ready_nodes=$(kubectl get nodes --no-headers | grep Ready | wc -l)
total_nodes=$(kubectl get nodes --no-headers | wc -l)
echo "节点状态: $ready_nodes/$total_nodes Ready"

if [ $ready_nodes -eq 0 ]; then
    echo "❌ 没有Ready状态的节点"
    kubectl get nodes
    exit 1
fi
echo "✅ 节点状态正常"

# 5. 检查权限
echo "5. 检查权限..."
kubectl auth can-i "*" "*" --all-namespaces &> /dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  当前用户可能没有集群管理员权限"
    echo "Kuboard需要较高权限，请确保有足够的权限"
else
    echo "✅ 权限检查通过"
fi

# 6. 检查网络连通性
echo "6. 检查网络连通性..."
kubectl get pods -A --field-selector=status.phase=Running | grep -q Running
if [ $? -ne 0 ]; then
    echo "❌ 集群中没有运行中的Pod，可能存在网络问题"
    exit 1
fi
echo "✅ 网络连通性正常"

# 7. 创建配置目录
echo "7. 创建配置目录..."
mkdir -p /k8s/kuboard-config
echo "✅ 配置目录创建完成"

# 8. 检查端口占用（Docker安装时需要）
echo "8. 检查端口占用..."
if command -v netstat &> /dev/null; then
    port_80=$(netstat -tlnp 2>/dev/null | grep ":80 " | wc -l)
    port_10081=$(netstat -tlnp 2>/dev/null | grep ":10081 " | wc -l)

    if [ $port_80 -gt 0 ]; then
        echo "⚠️  端口80已被占用，Docker安装时请使用其他端口"
    fi

    if [ $port_10081 -gt 0 ]; then
        echo "⚠️  端口10081已被占用，Docker安装时请使用其他端口"
    fi

    if [ $port_80 -eq 0 ] && [ $port_10081 -eq 0 ]; then
        echo "✅ 端口80和10081可用"
    fi
else
    echo "⚠️  netstat命令不可用，无法检查端口占用"
fi

echo "=== Kuboard环境准备完成 ==="
echo "🎉 环境准备成功，可以开始安装Kuboard！"

# 显示下一步操作提示
echo ""
echo "=== 下一步操作 ==="
echo "选择安装方式："
echo "1. Docker安装 - 适合测试环境和个人学习"
echo "2. Kubernetes安装 - 适合生产环境和团队使用"</code></pre>

                    <h3><i class="fas fa-play"></i> 2.4 运行环境准备脚本</h3>
                    <pre><code># 保存环境准备脚本
cat > /k8s/kuboard-config/prepare-env.sh << 'EOF'
# 将上面的脚本内容粘贴到这里
EOF

# 设置执行权限
chmod +x /k8s/kuboard-config/prepare-env.sh

# 运行环境准备脚本
/k8s/kuboard-config/prepare-env.sh</code></pre>
                </section>

                <section id="installation-methods">
                    <h2><span class="step-number">3</span>安装方式选择</h2>

                    <h3><i class="fas fa-route"></i> 3.1 安装方式对比</h3>
                    <p>Kuboard提供了多种安装方式，每种方式都有其适用场景。作为初学者，我们推荐您根据自己的环境选择最合适的方式：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-cogs"></i> 安装方式</th>
                            <th><i class="fas fa-thumbs-up"></i> 优点</th>
                            <th><i class="fas fa-thumbs-down"></i> 缺点</th>
                            <th><i class="fas fa-users"></i> 适用场景</th>
                        </tr>
                        <tr>
                            <td><i class="fab fa-docker"></i> Docker安装</td>
                            <td>简单快速、易于管理、资源占用少</td>
                            <td>需要单独的服务器、不在K8s集群内</td>
                            <td>测试环境、个人学习</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-dharmachakra"></i> Kubernetes安装</td>
                            <td>与集群集成、高可用、统一管理</td>
                            <td>配置相对复杂、占用集群资源</td>
                            <td>生产环境、团队使用</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-server"></i> 二进制安装</td>
                            <td>性能最佳、完全控制</td>
                            <td>配置复杂、维护困难</td>
                            <td>特殊需求、高级用户</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-lightbulb"></i> 3.2 选择建议</h3>
                    <div class="info-box">
                        <strong><i class="fas fa-graduation-cap"></i> 初学者建议：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>如果您是第一次使用Kuboard：</strong>推荐使用Docker安装方式，简单快速</li>
                            <li><strong>如果您有独立的管理服务器：</strong>Docker安装是最佳选择</li>
                            <li><strong>如果您希望Kuboard高可用：</strong>选择Kubernetes安装方式</li>
                            <li><strong>如果您的集群资源紧张：</strong>Docker安装占用资源更少</li>
                        </ul>
                    </div>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 注意事项：</strong>
                        <ul style="margin-top: 15px;">
                            <li>Docker安装方式需要在集群外的服务器上运行</li>
                            <li>Kubernetes安装方式会在集群内创建Pod运行Kuboard</li>
                            <li>无论选择哪种方式，都需要能够访问Kubernetes API Server</li>
                            <li>建议先在测试环境中尝试，熟悉后再部署到生产环境</li>
                        </ul>
                    </div>
                </section>

                <section id="docker-install">
                    <h2><span class="step-number">4</span>Docker安装方式</h2>

                    <h3><i class="fab fa-docker"></i> 4.1 Docker安装概述</h3>
                    <p>Docker安装是最简单快速的方式，适合初学者和测试环境。这种方式会在独立的服务器上运行Kuboard容器，通过网络连接到您的Kubernetes集群。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> Docker安装的优势：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>简单易用：</strong>只需要一条docker run命令即可启动</li>
                            <li><strong>资源独立：</strong>不占用Kubernetes集群资源</li>
                            <li><strong>易于维护：</strong>可以轻松停止、重启、更新</li>
                            <li><strong>快速部署：</strong>几分钟内即可完成部署</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-server"></i> 4.2 准备Docker环境</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>
                    <p>首先，我们需要在一台能够访问Kubernetes集群的服务器上安装Docker：</p>

                    <h4><i class="fas fa-download"></i> 4.2.1 安装Docker（如果尚未安装）</h4>
                    <pre><code># 在CentOS/RHEL系统上安装Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 验证Docker安装
docker --version
sudo docker run hello-world</code></pre>

                    <h4><i class="fas fa-user-plus"></i> 4.2.2 配置Docker权限（可选）</h4>
                    <pre><code># 将当前用户添加到docker组，避免每次使用sudo
sudo usermod -aG docker $USER

# 重新登录或执行以下命令使权限生效
newgrp docker

# 测试是否可以无sudo运行docker
docker ps</code></pre>

                    <h3><i class="fas fa-rocket"></i> 4.3 启动Kuboard容器</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>

                    <h4><i class="fas fa-play"></i> 4.3.1 基本启动命令</h4>
                    <pre><code># 启动Kuboard v3最新版本（示例使用*************）
docker run -d \
  --restart=unless-stopped \
  --name=kuboard \
  -p 80:80/tcp \
  -p 10081:10081/tcp \
  -e KUBOARD_ENDPOINT="http://*************:80" \
  -e KUBOARD_AGENT_SERVER_TCP_PORT="10081" \
  -v /k8s/kuboard-data:/data \
  eipwork/kuboard:v3

# 注意：请将*************替换为您的实际服务器IP地址</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要说明：</strong>
                        <ul style="margin-top: 15px;">
                            <li>请将 <code>*************</code> 替换为您的实际服务器IP地址</li>
                            <li>确保80和10081端口没有被其他服务占用</li>
                            <li>数据目录 <code>/k8s/kuboard-data</code> 用于持久化存储配置</li>
                            <li>建议使用完整路径避免数据丢失</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-cogs"></i> 4.3.2 自定义配置启动</h4>
                    <p>如果您需要自定义端口或其他配置，可以使用以下命令：</p>
                    <pre><code># 自定义端口启动（例如使用8080端口）
docker run -d \
  --restart=unless-stopped \
  --name=kuboard \
  -p 8080:80/tcp \
  -p 10081:10081/tcp \
  -e KUBOARD_ENDPOINT="http://*************:8080" \
  -e KUBOARD_AGENT_SERVER_TCP_PORT="10081" \
  -v /k8s/kuboard-data:/data \
  eipwork/kuboard:v3

# 注意：请将*************替换为您的实际服务器IP地址</code></pre>

                    <h4><i class="fas fa-check"></i> 4.3.3 验证容器状态</h4>
                    <pre><code># 检查容器是否正常运行
docker ps | grep kuboard

# 查看容器日志
docker logs kuboard

# 查看容器详细信息
docker inspect kuboard</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 成功标志：</strong>
                        <ul style="margin-top: 15px;">
                            <li>容器状态显示为 <code>Up</code></li>
                            <li>日志中没有错误信息</li>
                            <li>可以通过浏览器访问 <code>http://服务器IP:端口</code></li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-tools"></i> 4.4 Docker安装故障排查</h3>

                    <h4><i class="fas fa-bug"></i> 4.4.1 常见问题及解决方案</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-exclamation-triangle"></i> 问题现象</th>
                            <th><i class="fas fa-search"></i> 可能原因</th>
                            <th><i class="fas fa-wrench"></i> 解决方案</th>
                        </tr>
                        <tr>
                            <td>容器启动失败</td>
                            <td>端口被占用</td>
                            <td>检查端口占用：<code>netstat -tlnp | grep :80</code></td>
                        </tr>
                        <tr>
                            <td>无法访问Web界面</td>
                            <td>防火墙阻止</td>
                            <td>开放端口：<code>firewall-cmd --add-port=80/tcp --permanent</code></td>
                        </tr>
                        <tr>
                            <td>数据丢失</td>
                            <td>未挂载数据卷</td>
                            <td>确保使用 <code>-v</code> 参数挂载数据目录</td>
                        </tr>
                        <tr>
                            <td>镜像拉取失败</td>
                            <td>网络问题</td>
                            <td>使用国内镜像源或手动下载镜像</td>
                        </tr>
                    </table>

                    <h4><i class="fas fa-terminal"></i> 4.4.2 诊断命令</h4>
                    <pre><code># 检查Docker服务状态
systemctl status docker

# 检查端口占用情况
netstat -tlnp | grep -E ':(80|10081)'

# 检查防火墙状态
firewall-cmd --list-ports

# 测试网络连通性
curl -I http://localhost:80

# 查看详细的容器日志
docker logs -f kuboard</code></pre>
                </section>

                <section id="k8s-install">
                    <h2><span class="step-number">5</span>Kubernetes安装方式</h2>

                    <h3><i class="fas fa-dharmachakra"></i> 5.1 Kubernetes安装概述</h3>
                    <p>Kubernetes安装方式将Kuboard作为Pod运行在您的Kubernetes集群内部，这种方式更适合生产环境，可以享受Kubernetes的高可用性和自动恢复能力。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-star"></i> Kubernetes安装的优势：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>高可用性：</strong>利用Kubernetes的自愈能力</li>
                            <li><strong>统一管理：</strong>与其他应用一起管理</li>
                            <li><strong>资源调度：</strong>自动分配到合适的节点</li>
                            <li><strong>扩展性：</strong>可以轻松扩展多个副本</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-download"></i> 5.2 下载安装文件</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>

                    <h4><i class="fas fa-file-download"></i> 5.2.1 获取官方YAML文件</h4>
                    <pre><code># 创建工作目录
mkdir -p /k8s/kuboard-config
cd /k8s/kuboard-config

# 下载Kuboard v3的Kubernetes部署文件
wget https://addons.kuboard.cn/kuboard/kuboard-v3.yaml

# 或者使用curl下载
curl -O https://addons.kuboard.cn/kuboard/kuboard-v3.yaml</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 网络问题解决：</strong>如果下载失败，可以直接访问 <a
                            href="https://addons.kuboard.cn/kuboard/kuboard-v3.yaml"
                            target="_blank">https://addons.kuboard.cn/kuboard/kuboard-v3.yaml</a> 复制内容到本地文件。
                    </div>

                    <h4><i class="fas fa-eye"></i> 5.2.2 查看和理解YAML文件</h4>
                    <pre><code># 查看YAML文件内容
cat /k8s/kuboard-config/kuboard-v3.yaml

# 查看文件中包含的资源类型
grep -E "^kind:" /k8s/kuboard-config/kuboard-v3.yaml</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> YAML文件包含的资源：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>Namespace：</strong>kuboard 命名空间</li>
                            <li><strong>ServiceAccount：</strong>kuboard 服务账户</li>
                            <li><strong>ClusterRoleBinding：</strong>集群角色绑定</li>
                            <li><strong>Deployment：</strong>Kuboard应用部署</li>
                            <li><strong>Service：</strong>服务暴露</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-rocket"></i> 5.3 部署Kuboard到集群</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>

                    <h4><i class="fas fa-play"></i> 5.3.1 执行部署</h4>
                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 启动顺序：</strong>
                        <ol style="margin-top: 10px;">
                            <li>确保K8s集群正常运行</li>
                            <li>确保kubectl可以正常连接集群</li>
                            <li>应用Kuboard YAML文件</li>
                            <li>等待Pod启动完成</li>
                            <li>配置服务访问方式</li>
                        </ol>
                    </div>
                    <pre><code># 应用YAML文件到集群
kubectl apply -f /k8s/kuboard-config/kuboard-v3.yaml

# 检查部署状态
kubectl get all -n kuboard

# 查看Pod详细信息
kubectl describe pod -n kuboard</code></pre>

                    <h4><i class="fas fa-clock"></i> 5.3.2 等待部署完成</h4>
                    <pre><code># 实时监控Pod状态
kubectl get pods -n kuboard -w

# 查看Pod日志
kubectl logs -f deployment/kuboard-v3 -n kuboard</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 部署成功标志：</strong>
                        <ul style="margin-top: 15px;">
                            <li>Pod状态显示为 <code>Running</code></li>
                            <li>Ready状态显示为 <code>1/1</code></li>
                            <li>日志中显示服务启动成功</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-network-wired"></i> 5.4 配置服务访问</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>

                    <h4><i class="fas fa-door-open"></i> 5.4.1 查看服务信息</h4>
                    <pre><code># 查看Kuboard服务
kubectl get svc -n kuboard

# 查看服务详细信息
kubectl describe svc kuboard-v3 -n kuboard</code></pre>

                    <h4><i class="fas fa-globe"></i> 5.4.2 配置外部访问</h4>
                    <p>默认情况下，Kuboard服务类型为ClusterIP，只能在集群内部访问。我们需要修改为NodePort或LoadBalancer类型：</p>

                    <pre><code># 方法1：使用kubectl patch修改服务类型
kubectl patch svc kuboard-v3 -n kuboard -p '{"spec":{"type":"NodePort"}}'

# 方法2：使用kubectl edit交互式编辑
kubectl edit svc kuboard-v3 -n kuboard

# 查看修改后的服务信息
kubectl get svc kuboard-v3 -n kuboard</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 服务类型说明：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>ClusterIP：</strong>只能在集群内部访问</li>
                            <li><strong>NodePort：</strong>通过节点IP+端口访问</li>
                            <li><strong>LoadBalancer：</strong>通过负载均衡器访问（需要云环境支持）</li>
                        </ul>
                    </div>
                </section>

                <section id="first-access">
                    <h2><span class="step-number">6</span>首次访问配置</h2>

                    <h3><i class="fas fa-globe"></i> 6.1 访问Kuboard界面</h3>
                    <div class="machine-tag machine-browser"><i class="fas fa-globe"></i> 浏览器访问</div>
                    <p>无论您选择哪种安装方式，现在都可以通过浏览器访问Kuboard了。让我们一步步完成首次配置：</p>

                    <h4><i class="fas fa-link"></i> 6.1.1 确定访问地址</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 访问地址说明：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>Docker安装：</strong><code>http://服务器IP:端口</code>（如：http://*************:80）</li>
                            <li><strong>Kubernetes安装（NodePort）：</strong><code>http://节点IP:NodePort端口</code></li>
                            <li><strong>Kubernetes安装（LoadBalancer）：</strong><code>http://负载均衡器IP:端口</code></li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-search"></i> 6.1.2 获取NodePort端口（Kubernetes安装）</h4>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>
                    <pre><code># 查看Kuboard服务的NodePort端口
kubectl get svc kuboard-v3 -n kuboard

# 输出示例：
# NAME         TYPE       CLUSTER-IP      EXTERNAL-IP   PORT(S)        AGE
# kuboard-v3   NodePort   ************    <none>        80:32567/TCP   5m

# 在这个例子中，NodePort端口是32567
# 访问地址就是：http://任意节点IP:32567</code></pre>

                    <h3><i class="fas fa-user-plus"></i> 6.2 初始化管理员账户</h3>
                    <div class="machine-tag machine-browser"><i class="fas fa-globe"></i> 浏览器访问</div>

                    <h4><i class="fas fa-sign-in-alt"></i> 6.2.1 首次登录</h4>
                    <p>打开浏览器，输入Kuboard的访问地址。首次访问时，您会看到初始化页面：</p>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 首次访问步骤：</strong>
                        <ol style="margin-top: 15px;">
                            <li>在浏览器中输入Kuboard访问地址</li>
                            <li>如果看到"欢迎使用Kuboard"页面，说明访问成功</li>
                            <li>点击"初始化"按钮开始配置</li>
                            <li>设置管理员用户名和密码</li>
                        </ol>
                    </div>

                    <h4><i class="fas fa-key"></i> 6.2.2 设置管理员密码</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-shield-alt"></i> 密码安全建议：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>长度：</strong>至少8位字符</li>
                            <li><strong>复杂性：</strong>包含大小写字母、数字和特殊字符</li>
                            <li><strong>唯一性：</strong>不要使用常见密码或与其他系统相同的密码</li>
                            <li><strong>记录：</strong>请妥善保存密码，忘记后需要重新初始化</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-check-circle"></i> 6.2.3 完成初始化</h4>
                    <p>设置完管理员账户后，系统会自动跳转到登录页面。使用刚才设置的用户名和密码登录即可进入Kuboard主界面。</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 初始化成功标志：</strong>
                        <ul style="margin-top: 15px;">
                            <li>能够成功登录Kuboard界面</li>
                            <li>看到Kuboard的主控制台</li>
                            <li>左侧显示集群管理菜单</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-cog"></i> 6.3 基本界面介绍</h3>
                    <div class="machine-tag machine-browser"><i class="fas fa-globe"></i> 浏览器访问</div>

                    <h4><i class="fas fa-desktop"></i> 6.3.1 主界面布局</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-map"></i> 区域</th>
                            <th><i class="fas fa-info-circle"></i> 功能说明</th>
                            <th><i class="fas fa-mouse-pointer"></i> 主要操作</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-bars"></i> 左侧菜单</td>
                            <td>集群管理、工作负载、配置等功能入口</td>
                            <td>点击展开各功能模块</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-chart-bar"></i> 中央面板</td>
                            <td>显示当前选中功能的详细内容</td>
                            <td>查看和操作Kubernetes资源</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-user"></i> 右上角</td>
                            <td>用户信息、设置、退出等</td>
                            <td>账户管理和系统设置</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-bell"></i> 通知区域</td>
                            <td>显示系统通知和告警信息</td>
                            <td>查看集群状态和事件</td>
                        </tr>
                    </table>

                    <h4><i class="fas fa-lightbulb"></i> 6.3.2 界面使用技巧</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-graduation-cap"></i> 新手使用建议：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>从概览开始：</strong>先查看集群概览了解整体状态</li>
                            <li><strong>逐步探索：</strong>按照左侧菜单顺序逐个了解功能</li>
                            <li><strong>善用搜索：</strong>使用搜索功能快速找到资源</li>
                            <li><strong>查看帮助：</strong>点击问号图标查看功能说明</li>
                        </ul>
                    </div>
                </section>

                <section id="cluster-import">
                    <h2><span class="step-number">7</span>导入K8s集群</h2>

                    <h3><i class="fas fa-plus-circle"></i> 7.1 添加集群概述</h3>
                    <div class="machine-tag machine-browser"><i class="fas fa-globe"></i> 浏览器访问</div>
                    <p>成功登录Kuboard后，下一步就是将您的Kubernetes集群添加到Kuboard中进行管理。Kuboard支持管理多个集群，每个集群都可以独立配置和监控。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 集群导入说明：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>本地集群：</strong>Kuboard运行在集群内部时，会自动发现本地集群</li>
                            <li><strong>远程集群：</strong>需要手动添加集群的连接信息</li>
                            <li><strong>多集群：</strong>可以同时管理多个不同的Kubernetes集群</li>
                            <li><strong>权限控制：</strong>每个集群可以设置不同的访问权限</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-search"></i> 7.2 检查自动发现的集群</h3>
                    <div class="machine-tag machine-browser"><i class="fas fa-globe"></i> 浏览器访问</div>

                    <h4><i class="fas fa-eye"></i> 7.2.1 查看集群列表</h4>
                    <p>登录Kuboard后，按照以下步骤查看集群：</p>
                    <ol>
                        <li>在Kuboard主界面，点击左侧菜单的"集群管理"</li>
                        <li>查看是否已经自动发现了您的集群</li>
                        <li>如果看到集群名称，说明自动发现成功</li>
                        <li>点击集群名称进入集群管理界面</li>
                    </ol>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i>
                            自动发现成功：</strong>如果Kuboard是通过Kubernetes方式安装的，通常会自动发现当前集群。您可以直接开始使用，无需手动添加。
                    </div>

                    <h3><i class="fas fa-plus"></i> 7.3 手动添加集群</h3>
                    <div class="machine-tag machine-browser"><i class="fas fa-globe"></i> 浏览器访问</div>
                    <p>如果没有自动发现集群，或者需要添加其他集群，可以手动添加：</p>

                    <h4><i class="fas fa-plus-circle"></i> 7.3.1 添加集群步骤</h4>
                    <ol>
                        <li>在集群管理页面，点击"添加集群"按钮</li>
                        <li>选择集群类型（通常选择"标准Kubernetes集群"）</li>
                        <li>填写集群基本信息：
                            <ul>
                                <li><strong>集群名称：</strong>为集群起一个容易识别的名字</li>
                                <li><strong>集群描述：</strong>简要描述集群用途</li>
                                <li><strong>API Server地址：</strong>Kubernetes API Server的访问地址</li>
                            </ul>
                        </li>
                        <li>配置认证信息（下一步详细说明）</li>
                        <li>测试连接并保存</li>
                    </ol>

                    <h4><i class="fas fa-key"></i> 7.3.2 配置集群认证</h4>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>
                    <p>添加集群时需要提供认证信息，最常用的方式是使用kubeconfig文件：</p>

                    <pre><code># 查看当前的kubeconfig文件
cat ~/.kube/config

# 或者查看特定集群的kubeconfig
kubectl config view --raw

# 复制输出的内容，粘贴到Kuboard的认证配置中</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 安全提醒：</strong>
                        <ul style="margin-top: 15px;">
                            <li>kubeconfig文件包含集群的访问凭证，请妥善保管</li>
                            <li>建议为Kuboard创建专用的ServiceAccount而不是使用管理员权限</li>
                            <li>定期轮换访问密钥以提高安全性</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-check"></i> 7.3.3 测试集群连接</h4>
                    <p>配置完认证信息后，Kuboard会自动测试与集群的连接：</p>
                    <ul>
                        <li><strong>连接成功：</strong>显示绿色的"连接正常"状态</li>
                        <li><strong>连接失败：</strong>检查API Server地址和认证信息是否正确</li>
                        <li><strong>权限不足：</strong>确认提供的凭证具有足够的集群访问权限</li>
                    </ul>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 添加成功：</strong>集群添加成功后，您就可以在Kuboard中查看和管理该集群的所有资源了。
                    </div>
                </section>

                <section id="basic-usage">
                    <h2><span class="step-number">8</span>基本使用指南</h2>

                    <h3><i class="fas fa-play"></i> 8.1 集群概览</h3>
                    <div class="machine-tag machine-browser"><i class="fas fa-globe"></i> 浏览器访问</div>
                    <p>成功添加集群后，让我们开始探索Kuboard的基本功能。首先从集群概览开始：</p>

                    <h4><i class="fas fa-tachometer-alt"></i> 8.1.1 查看集群状态</h4>
                    <ol>
                        <li>点击集群名称进入集群管理界面</li>
                        <li>在概览页面可以看到：
                            <ul>
                                <li><strong>节点状态：</strong>显示所有节点的健康状况</li>
                                <li><strong>资源使用：</strong>CPU、内存、存储的使用情况</li>
                                <li><strong>Pod统计：</strong>运行中、等待中、失败的Pod数量</li>
                                <li><strong>事件列表：</strong>最近发生的集群事件</li>
                            </ul>
                        </li>
                    </ol>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 概览页面的作用：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>健康检查：</strong>快速了解集群整体健康状况</li>
                            <li><strong>资源监控：</strong>监控资源使用情况，避免资源不足</li>
                            <li><strong>问题发现：</strong>及时发现异常Pod和事件</li>
                            <li><strong>趋势分析：</strong>观察集群使用趋势</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-cubes"></i> 8.2 工作负载管理</h3>
                    <div class="machine-tag machine-browser"><i class="fas fa-globe"></i> 浏览器访问</div>

                    <h4><i class="fas fa-list"></i> 8.2.1 查看现有工作负载</h4>
                    <p>在左侧菜单中点击"工作负载"，可以看到集群中的各种工作负载：</p>
                    <ul>
                        <li><strong>Deployments：</strong>无状态应用部署</li>
                        <li><strong>StatefulSets：</strong>有状态应用部署</li>
                        <li><strong>DaemonSets：</strong>守护进程集</li>
                        <li><strong>Jobs：</strong>一次性任务</li>
                        <li><strong>CronJobs：</strong>定时任务</li>
                    </ul>

                    <h4><i class="fas fa-plus"></i> 8.2.2 创建简单应用</h4>
                    <p>让我们创建一个简单的nginx应用来熟悉Kuboard的操作：</p>
                    <ol>
                        <li>点击"工作负载" → "Deployments"</li>
                        <li>点击"创建Deployment"按钮</li>
                        <li>填写基本信息：
                            <ul>
                                <li><strong>名称：</strong>nginx-demo</li>
                                <li><strong>命名空间：</strong>default</li>
                                <li><strong>副本数：</strong>2</li>
                            </ul>
                        </li>
                        <li>配置容器：
                            <ul>
                                <li><strong>镜像：</strong>nginx:1.20</li>
                                <li><strong>端口：</strong>80</li>
                            </ul>
                        </li>
                        <li>点击"创建"完成部署</li>
                    </ol>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i>
                            创建成功：</strong>创建成功后，您可以在Deployments列表中看到nginx-demo应用，状态应该显示为"运行中"。
                    </div>

                    <h3><i class="fas fa-network-wired"></i> 8.3 服务和网络</h3>
                    <div class="machine-tag machine-browser"><i class="fas fa-globe"></i> 浏览器访问</div>

                    <h4><i class="fas fa-globe"></i> 8.3.1 创建Service暴露应用</h4>
                    <p>创建了Deployment后，需要创建Service来暴露应用：</p>
                    <ol>
                        <li>点击"服务发现" → "Services"</li>
                        <li>点击"创建Service"按钮</li>
                        <li>填写Service信息：
                            <ul>
                                <li><strong>名称：</strong>nginx-service</li>
                                <li><strong>类型：</strong>NodePort</li>
                                <li><strong>选择器：</strong>选择nginx-demo应用</li>
                                <li><strong>端口映射：</strong>80:80</li>
                            </ul>
                        </li>
                        <li>点击"创建"完成配置</li>
                    </ol>

                    <h4><i class="fas fa-external-link-alt"></i> 8.3.2 访问应用</h4>
                    <p>Service创建成功后，可以通过NodePort访问应用：</p>
                    <ol>
                        <li>在Services列表中找到nginx-service</li>
                        <li>查看NodePort端口号（例如：30080）</li>
                        <li>在浏览器中访问：<code>http://节点IP:30080</code></li>
                        <li>应该能看到nginx的欢迎页面</li>
                    </ol>

                    <div class="info-box">
                        <strong><i class="fas fa-graduation-cap"></i> 学习建议：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>多实践：</strong>尝试创建不同类型的工作负载</li>
                            <li><strong>观察日志：</strong>学会查看Pod日志排查问题</li>
                            <li><strong>监控资源：</strong>关注CPU和内存使用情况</li>
                            <li><strong>理解概念：</strong>深入理解Kubernetes的核心概念</li>
                        </ul>
                    </div>
                </section>

                <section id="advanced-features">
                    <h2><span class="step-number">9</span>高级功能</h2>

                    <h3><i class="fas fa-star"></i> 9.1 监控和日志</h3>
                    <div class="machine-tag machine-browser"><i class="fas fa-globe"></i> 浏览器访问</div>

                    <h4><i class="fas fa-chart-line"></i> 9.1.1 资源监控</h4>
                    <p>Kuboard提供了丰富的监控功能，帮助您实时了解集群和应用的运行状态：</p>
                    <ul>
                        <li><strong>节点监控：</strong>查看每个节点的CPU、内存、磁盘使用情况</li>
                        <li><strong>Pod监控：</strong>监控Pod的资源消耗和性能指标</li>
                        <li><strong>网络监控：</strong>查看网络流量和连接状态</li>
                        <li><strong>存储监控：</strong>监控持久化卷的使用情况</li>
                    </ul>

                    <h4><i class="fas fa-file-alt"></i> 9.1.2 日志管理</h4>
                    <p>通过Kuboard可以方便地查看和管理容器日志：</p>
                    <ol>
                        <li>在Pod列表中点击要查看的Pod</li>
                        <li>点击"日志"标签页</li>
                        <li>可以实时查看日志输出</li>
                        <li>支持日志搜索和过滤功能</li>
                    </ol>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 日志查看技巧：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>实时跟踪：</strong>开启"跟踪"模式查看实时日志</li>
                            <li><strong>时间范围：</strong>设置时间范围查看历史日志</li>
                            <li><strong>关键字搜索：</strong>使用搜索功能快速定位问题</li>
                            <li><strong>多容器：</strong>在多容器Pod中切换查看不同容器的日志</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-users"></i> 9.2 用户和权限管理</h3>
                    <div class="machine-tag machine-browser"><i class="fas fa-globe"></i> 浏览器访问</div>

                    <h4><i class="fas fa-user-plus"></i> 9.2.1 添加用户</h4>
                    <p>Kuboard支持多用户管理，可以为不同的用户分配不同的权限：</p>
                    <ol>
                        <li>点击右上角用户头像 → "用户管理"</li>
                        <li>点击"添加用户"按钮</li>
                        <li>填写用户信息：用户名、密码、邮箱等</li>
                        <li>分配角色和权限</li>
                        <li>保存用户配置</li>
                    </ol>

                    <h4><i class="fas fa-shield-alt"></i> 9.2.2 权限控制</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-user-tag"></i> 角色类型</th>
                            <th><i class="fas fa-key"></i> 权限范围</th>
                            <th><i class="fas fa-users"></i> 适用场景</th>
                        </tr>
                        <tr>
                            <td>集群管理员</td>
                            <td>完全访问权限</td>
                            <td>系统管理员</td>
                        </tr>
                        <tr>
                            <td>命名空间管理员</td>
                            <td>特定命名空间的管理权限</td>
                            <td>项目负责人</td>
                        </tr>
                        <tr>
                            <td>只读用户</td>
                            <td>只能查看，不能修改</td>
                            <td>监控人员、审计人员</td>
                        </tr>
                        <tr>
                            <td>开发者</td>
                            <td>应用部署和调试权限</td>
                            <td>开发团队成员</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-download"></i> 9.3 备份和恢复</h3>
                    <div class="machine-tag machine-browser"><i class="fas fa-globe"></i> 浏览器访问</div>

                    <h4><i class="fas fa-save"></i> 9.3.1 配置备份</h4>
                    <p>定期备份Kuboard配置和集群资源定义：</p>
                    <ul>
                        <li><strong>Kuboard配置：</strong>备份用户、角色、集群连接信息</li>
                        <li><strong>资源定义：</strong>导出重要的Kubernetes资源YAML</li>
                        <li><strong>自动备份：</strong>设置定时备份任务</li>
                        <li><strong>版本管理：</strong>保留多个备份版本</li>
                    </ul>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 备份重要性：</strong>
                        <ul style="margin-top: 15px;">
                            <li>定期备份可以防止配置丢失</li>
                            <li>在升级或迁移时提供回滚能力</li>
                            <li>备份文件应存储在安全的位置</li>
                            <li>定期测试备份的完整性和可恢复性</li>
                        </ul>
                    </div>
                </section>

                <section id="troubleshooting">
                    <h2><span class="step-number">10</span>故障排查</h2>

                    <h3><i class="fas fa-bug"></i> 10.1 常见问题诊断</h3>

                    <h4><i class="fas fa-exclamation-triangle"></i> 10.1.1 访问问题</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-question-circle"></i> 问题现象</th>
                            <th><i class="fas fa-search"></i> 可能原因</th>
                            <th><i class="fas fa-tools"></i> 解决方案</th>
                        </tr>
                        <tr>
                            <td>无法访问Kuboard界面</td>
                            <td>服务未启动或端口被阻止</td>
                            <td>检查服务状态和防火墙配置</td>
                        </tr>
                        <tr>
                            <td>登录失败</td>
                            <td>用户名密码错误或账户被锁定</td>
                            <td>重置密码或检查账户状态</td>
                        </tr>
                        <tr>
                            <td>页面加载缓慢</td>
                            <td>网络延迟或资源不足</td>
                            <td>检查网络连接和服务器性能</td>
                        </tr>
                        <tr>
                            <td>功能异常</td>
                            <td>浏览器兼容性问题</td>
                            <td>更新浏览器或清除缓存</td>
                        </tr>
                    </table>

                    <h4><i class="fas fa-link"></i> 10.1.2 集群连接问题</h4>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>
                    <pre><code># 检查集群连接状态
kubectl cluster-info

# 检查API Server是否可访问
curl -k https://API_SERVER_IP:6443/version

# 检查认证配置
kubectl auth can-i get pods --all-namespaces

# 查看Kuboard Pod日志（Kubernetes安装方式）
kubectl logs -f deployment/kuboard-v3 -n kuboard</code></pre>

                    <h3><i class="fas fa-tools"></i> 10.2 性能优化</h3>

                    <h4><i class="fas fa-tachometer-alt"></i> 10.2.1 系统性能调优</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-rocket"></i> 性能优化建议：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>资源配置：</strong>根据集群规模调整Kuboard的CPU和内存配置</li>
                            <li><strong>数据库优化：</strong>定期清理历史数据和日志</li>
                            <li><strong>网络优化：</strong>确保Kuboard与集群之间的网络延迟较低</li>
                            <li><strong>缓存配置：</strong>合理配置浏览器缓存策略</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-chart-bar"></i> 10.2.2 监控指标</h4>
                    <p>关注以下关键指标来评估Kuboard的性能：</p>
                    <ul>
                        <li><strong>响应时间：</strong>页面加载和API响应时间</li>
                        <li><strong>资源使用：</strong>CPU、内存、磁盘使用率</li>
                        <li><strong>并发用户：</strong>同时在线用户数量</li>
                        <li><strong>错误率：</strong>请求失败率和错误日志</li>
                    </ul>

                    <h3><i class="fas fa-life-ring"></i> 10.3 获取帮助</h3>

                    <h4><i class="fas fa-book"></i> 10.3.1 文档资源</h4>
                    <ul>
                        <li><strong>官方文档：</strong><a href="https://kuboard.cn" target="_blank">https://kuboard.cn</a>
                        </li>
                        <li><strong>GitHub仓库：</strong><a href="https://github.com/eip-work/kuboard-press"
                                target="_blank">https://github.com/eip-work/kuboard-press</a></li>
                        <li><strong>社区论坛：</strong>参与社区讨论和问题解答</li>
                        <li><strong>视频教程：</strong>观看官方和社区制作的教学视频</li>
                    </ul>

                    <h4><i class="fas fa-comments"></i> 10.3.2 社区支持</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-users"></i> 获取帮助的途径：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>GitHub Issues：</strong>报告bug和功能请求</li>
                            <li><strong>QQ群/微信群：</strong>加入用户交流群</li>
                            <li><strong>技术博客：</strong>阅读相关技术文章</li>
                            <li><strong>培训课程：</strong>参加官方或第三方培训</li>
                        </ul>
                    </div>
                </section>

                <section id="security">
                    <h2><span class="step-number">11</span>安全配置</h2>

                    <h3><i class="fas fa-shield-alt"></i> 11.1 安全最佳实践</h3>

                    <h4><i class="fas fa-lock"></i> 11.1.1 访问控制</h4>
                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 安全建议：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>强密码策略：</strong>要求用户使用复杂密码</li>
                            <li><strong>定期更换：</strong>定期更换管理员密码</li>
                            <li><strong>最小权限：</strong>按需分配最小必要权限</li>
                            <li><strong>审计日志：</strong>启用操作审计和日志记录</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-certificate"></i> 11.1.2 HTTPS配置</h4>
                    <p>为Kuboard配置HTTPS加密访问：</p>
                    <ol>
                        <li>获取SSL证书（自签名或CA签发）</li>
                        <li>配置反向代理（Nginx或Apache）</li>
                        <li>设置HTTPS重定向</li>
                        <li>验证证书配置</li>
                    </ol>

                    <h3><i class="fas fa-network-wired"></i> 11.2 网络安全</h3>

                    <h4><i class="fas fa-firewall"></i> 11.2.1 防火墙配置</h4>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>
                    <pre><code># 开放Kuboard访问端口
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --permanent --add-port=10081/tcp
firewall-cmd --reload

# 限制访问来源（可选）
firewall-cmd --permanent --add-rich-rule="rule family='ipv4' source address='************/24' port protocol='tcp' port='80' accept"</code></pre>

                    <h4><i class="fas fa-user-shield"></i> 11.2.2 访问限制</h4>
                    <ul>
                        <li><strong>IP白名单：</strong>限制允许访问的IP地址范围</li>
                        <li><strong>VPN访问：</strong>通过VPN提供安全的远程访问</li>
                        <li><strong>多因素认证：</strong>启用2FA增强安全性</li>
                        <li><strong>会话管理：</strong>设置合理的会话超时时间</li>
                    </ul>
                </section>

                <section id="deployment-verification">
                    <h2><span class="step-number">12</span>部署验证脚本</h2>

                    <h3><i class="fas fa-check-circle"></i> 12.1 完整验证脚本</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 说明：</strong>运行此脚本可以全面验证Kuboard的部署状态和功能
                    </div>

                    <pre><code>#!/bin/bash
# Kuboard部署验证脚本

echo "=== Kuboard部署验证开始 ==="

# 检查安装方式
INSTALL_TYPE=""
if docker ps | grep -q kuboard; then
    INSTALL_TYPE="docker"
    echo "检测到Docker安装方式"
elif kubectl get pods -n kuboard | grep -q kuboard; then
    INSTALL_TYPE="kubernetes"
    echo "检测到Kubernetes安装方式"
else
    echo "❌ 未检测到Kuboard安装"
    exit 1
fi

# Docker安装验证
if [ "$INSTALL_TYPE" = "docker" ]; then
    echo ""
    echo "=== Docker安装验证 ==="

    # 1. 检查容器状态
    echo "1. 检查容器状态..."
    container_status=$(docker ps --filter "name=kuboard" --format "{{.Status}}")
    if [[ $container_status == *"Up"* ]]; then
        echo "✅ Kuboard容器运行正常"
        echo "   状态: $container_status"
    else
        echo "❌ Kuboard容器状态异常"
        docker ps -a --filter "name=kuboard"
        exit 1
    fi

    # 2. 检查端口映射
    echo "2. 检查端口映射..."
    ports=$(docker port kuboard)
    if [[ $ports == *"80/tcp"* ]] && [[ $ports == *"10081/tcp"* ]]; then
        echo "✅ 端口映射正常"
        echo "$ports"
    else
        echo "❌ 端口映射异常"
        echo "$ports"
        exit 1
    fi

    # 3. 检查容器日志
    echo "3. 检查容器日志..."
    log_errors=$(docker logs kuboard 2>&1 | grep -i error | wc -l)
    if [ $log_errors -eq 0 ]; then
        echo "✅ 容器日志无错误"
    else
        echo "⚠️  容器日志中发现 $log_errors 个错误"
        echo "最近的错误日志:"
        docker logs kuboard 2>&1 | grep -i error | tail -5
    fi

    # 4. 检查数据卷
    echo "4. 检查数据卷..."
    data_mount=$(docker inspect kuboard | grep -o '/k8s/kuboard-data:/data')
    if [ -n "$data_mount" ]; then
        echo "✅ 数据卷挂载正常: $data_mount"
        if [ -d "/k8s/kuboard-data" ]; then
            echo "✅ 数据目录存在"
        else
            echo "⚠️  数据目录不存在，数据可能丢失"
        fi
    else
        echo "⚠️  数据卷挂载可能有问题"
    fi

    # 5. 测试Web访问
    echo "5. 测试Web访问..."
    web_port=$(docker port kuboard 80/tcp | cut -d: -f2)
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:$web_port | grep -q "200\|302"; then
        echo "✅ Web界面访问正常"
        echo "   访问地址: http://localhost:$web_port"
    else
        echo "❌ Web界面访问失败"
        echo "   请检查防火墙和网络配置"
    fi
fi

# Kubernetes安装验证
if [ "$INSTALL_TYPE" = "kubernetes" ]; then
    echo ""
    echo "=== Kubernetes安装验证 ==="

    # 1. 检查命名空间
    echo "1. 检查命名空间..."
    kubectl get namespace kuboard &> /dev/null
    if [ $? -eq 0 ]; then
        echo "✅ kuboard命名空间存在"
    else
        echo "❌ kuboard命名空间不存在"
        exit 1
    fi

    # 2. 检查Pod状态
    echo "2. 检查Pod状态..."
    pod_status=$(kubectl get pods -n kuboard --no-headers | awk '{print $3}')
    if [[ $pod_status == "Running" ]]; then
        echo "✅ Kuboard Pod运行正常"
        ready_status=$(kubectl get pods -n kuboard --no-headers | awk '{print $2}')
        echo "   Ready状态: $ready_status"
    else
        echo "❌ Kuboard Pod状态异常: $pod_status"
        kubectl describe pods -n kuboard
        exit 1
    fi

    # 3. 检查服务
    echo "3. 检查服务..."
    kubectl get svc -n kuboard | grep -q kuboard
    if [ $? -eq 0 ]; then
        echo "✅ Kuboard服务存在"
        svc_type=$(kubectl get svc kuboard-v3 -n kuboard -o jsonpath='{.spec.type}')
        echo "   服务类型: $svc_type"

        if [ "$svc_type" = "NodePort" ]; then
            nodeport=$(kubectl get svc kuboard-v3 -n kuboard -o jsonpath='{.spec.ports[0].nodePort}')
            echo "   NodePort端口: $nodeport"
        fi
    else
        echo "❌ Kuboard服务不存在"
        exit 1
    fi

    # 4. 检查RBAC权限
    echo "4. 检查RBAC权限..."
    kubectl get clusterrolebinding | grep -q kuboard
    if [ $? -eq 0 ]; then
        echo "✅ RBAC权限配置正常"
    else
        echo "⚠️  RBAC权限可能有问题"
    fi

    # 5. 检查Pod日志
    echo "5. 检查Pod日志..."
    log_errors=$(kubectl logs -n kuboard deployment/kuboard-v3 | grep -i error | wc -l)
    if [ $log_errors -eq 0 ]; then
        echo "✅ Pod日志无错误"
    else
        echo "⚠️  Pod日志中发现 $log_errors 个错误"
        echo "最近的错误日志:"
        kubectl logs -n kuboard deployment/kuboard-v3 | grep -i error | tail -5
    fi
fi

echo ""
echo "=== 访问信息 ==="
if [ "$INSTALL_TYPE" = "docker" ]; then
    web_port=$(docker port kuboard 80/tcp | cut -d: -f2)
    echo "Docker安装访问地址:"
    echo "  http://localhost:$web_port"
    echo "  http://*************:$web_port"
elif [ "$INSTALL_TYPE" = "kubernetes" ]; then
    svc_type=$(kubectl get svc kuboard-v3 -n kuboard -o jsonpath='{.spec.type}')
    if [ "$svc_type" = "NodePort" ]; then
        nodeport=$(kubectl get svc kuboard-v3 -n kuboard -o jsonpath='{.spec.ports[0].nodePort}')
        echo "Kubernetes安装访问地址:"
        echo "  http://*************:$nodeport"
        echo "  http://*************:$nodeport"
        echo "  http://*************:$nodeport"
        echo "  http://*************:$nodeport"
    else
        echo "Kubernetes安装 - 集群内访问"
        echo "  需要配置NodePort或LoadBalancer类型的服务"
    fi
fi

echo ""
echo "=== Kuboard部署验证完成 ==="
echo "🎉 验证通过，Kuboard部署成功！"
echo ""
echo "下一步操作："
echo "1. 在浏览器中访问Kuboard"
echo "2. 完成初始化配置"
echo "3. 导入Kubernetes集群"</code></pre>

                    <h3><i class="fas fa-play"></i> 12.2 运行验证脚本</h3>
                    <pre><code># 保存验证脚本
cat > /k8s/kuboard-config/verify-kuboard.sh << 'EOF'
# 将上面的脚本内容粘贴到这里
EOF

# 设置执行权限
chmod +x /k8s/kuboard-config/verify-kuboard.sh

# 运行验证脚本
/k8s/kuboard-config/verify-kuboard.sh</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 验证成功标志：</strong>
                        <ul>
                            <li>所有检查项显示 ✅ 通过</li>
                            <li>显示Kuboard访问地址</li>
                            <li>可以正常访问Kuboard Web界面</li>
                            <li>容器或Pod状态正常</li>
                        </ul>
                    </div>
                </section>

                <section id="summary">
                    <h2><span class="step-number">13</span>总结</h2>

                    <h3><i class="fas fa-flag-checkered"></i> 12.1 学习回顾</h3>
                    <p>恭喜您完成了Kuboard的安装和基本配置！让我们回顾一下本教程涵盖的主要内容：</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 您已经学会了：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>Kuboard基础：</strong>了解了Kuboard的功能和优势</li>
                            <li><strong>环境准备：</strong>检查和配置必要的前置条件</li>
                            <li><strong>安装部署：</strong>掌握Docker和Kubernetes两种安装方式</li>
                            <li><strong>首次配置：</strong>完成初始化和集群导入</li>
                            <li><strong>基本使用：</strong>学会了基本的集群管理操作</li>
                            <li><strong>高级功能：</strong>了解了监控、用户管理等高级特性</li>
                            <li><strong>故障排查：</strong>掌握了常见问题的诊断和解决方法</li>
                            <li><strong>安全配置：</strong>学会了基本的安全加固措施</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-road"></i> 12.2 下一步学习建议</h3>
                    <div class="info-box">
                        <strong><i class="fas fa-graduation-cap"></i> 继续学习的方向：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>深入实践：</strong>在实际项目中使用Kuboard管理应用</li>
                            <li><strong>高级特性：</strong>探索CI/CD集成、监控告警等功能</li>
                            <li><strong>多集群管理：</strong>学习管理多个Kubernetes集群</li>
                            <li><strong>自动化运维：</strong>结合其他工具实现自动化运维</li>
                            <li><strong>社区参与：</strong>参与开源社区，贡献代码或文档</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-heart"></i> 12.3 结语</h3>
                    <p>Kuboard作为一个优秀的Kubernetes管理工具，可以大大简化集群管理的复杂性。通过本教程的学习，您已经具备了使用Kuboard的基本能力。</p>

                    <p>记住，学习是一个持续的过程。建议您：</p>
                    <ul>
                        <li><strong>多动手实践：</strong>理论结合实践，在实际环境中验证所学知识</li>
                        <li><strong>关注更新：</strong>定期关注Kuboard的版本更新和新功能</li>
                        <li><strong>分享交流：</strong>与其他用户分享经验，共同进步</li>
                        <li><strong>持续学习：</strong>深入学习Kubernetes和云原生技术</li>
                    </ul>

                    <div class="success-box">
                        <strong><i class="fas fa-trophy"></i> 恭喜您！</strong>
                        <p style="margin-top: 15px;">您已经成功掌握了Kuboard的安装和基本使用。现在可以开始您的Kubernetes可视化管理之旅了！</p>
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-envelope"></i> 反馈与建议：</strong>
                        <p style="margin-top: 15px;">如果您在使用过程中遇到问题或有改进建议，欢迎通过GitHub Issues或社区群组反馈。您的意见对我们非常宝贵！</p>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <script>
        // 移动端菜单切换
        document.getElementById('mobileMenuBtn').addEventListener('click', function () {
            document.getElementById('sidebar').classList.toggle('active');
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 高亮当前章节
        window.addEventListener('scroll', function () {
            const sections = document.querySelectorAll('section');
            const navLinks = document.querySelectorAll('.sidebar a');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (scrollY >= (sectionTop - 200)) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>

</html>