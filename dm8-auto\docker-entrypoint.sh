#!/bin/bash
set -e

# 达梦数据库Docker容器启动脚本 - 静默安装版本

# 环境变量默认值 - 根据auto.xml配置
DM_HOME=${DM_HOME:-/home/<USER>/dmdbms}
DM_DATA=${DM_DATA:-/home/<USER>/dmdbms/data}
DM_DB_NAME=${DM_DB_NAME:-DAMENG}
DM_INSTANCE_NAME=${DM_INSTANCE_NAME:-DMSERVER}
DM_PORT=${DM_PORT:-5236}

echo "=== 达梦数据库容器启动 (静默安装版本) ==="
echo "DM_HOME: $DM_HOME"
echo "DM_DATA: $DM_DATA"
echo "DM_DB_NAME: $DM_DB_NAME"
echo "DM_INSTANCE_NAME: $DM_INSTANCE_NAME"
echo "DM_PORT: $DM_PORT"

# 检查许可证状态
echo "=== 检查许可证状态 ==="
if [ -f "$DM_HOME/bin/dm.key" ]; then
    echo "✓ 找到许可证文件"
else
    echo "⚠ 未找到许可证文件，将使用试用版本"
    echo "⚠ 试用版本功能有限，建议获取正式许可证"
fi

# 检查数据库是否已初始化
check_database_initialized() {
    if [ -f "$DM_DATA/$DM_DB_NAME/dm.ctl" ] && [ -f "$DM_DATA/$DM_DB_NAME/dm.ini" ]; then
        return 0  # 已初始化
    else
        return 1  # 未初始化
    fi
}

# 初始化数据库
initialize_database() {
    echo "=== 首次运行，正在初始化数据库 ==="

    # 确保目录存在
    mkdir -p "$DM_DATA"
    mkdir -p "/home/<USER>/dmdbms/log"

    # 使用dminit工具初始化数据库
    cd "$DM_HOME/bin"

    echo "执行数据库初始化..."
    # 设置必需的密码参数
    ./dminit \
        path="$DM_DATA" \
        db_name="$DM_DB_NAME" \
        instance_name="$DM_INSTANCE_NAME" \
        port_num="$DM_PORT" \
        page_size=32 \
        extent_size=32 \
        case_sensitive=y \
        charset=0 \
        log_size=256 \
        sysdba_pwd="GDYtd@2025" \
        sysauditor_pwd="GDYtd@2025"

    if [ $? -eq 0 ]; then
        echo "✓ 数据库初始化成功"
    else
        echo "✗ 数据库初始化失败，尝试使用简化参数重新初始化..."

        # 如果失败，尝试使用最基本的参数
        ./dminit \
            path="$DM_DATA" \
            db_name="$DM_DB_NAME" \
            instance_name="$DM_INSTANCE_NAME" \
            port_num="$DM_PORT" \
            sysdba_pwd="GDYtd@2025"

        if [ $? -eq 0 ]; then
            echo "✓ 数据库初始化成功（使用基本配置）"
        else
            echo "✗ 数据库初始化失败"
            echo "错误详情："
            ls -la "$DM_DATA" || echo "数据目录访问失败"
            exit 1
        fi
    fi
}

# 检查是否需要初始化数据库
if ! check_database_initialized; then
    initialize_database
fi

# 查找配置文件
INI_FILE=""
if [ -f "$DM_DATA/dm.ini" ]; then
    INI_FILE="$DM_DATA/dm.ini"
elif [ -f "$DM_DATA/DAMENG.ini" ]; then
    INI_FILE="$DM_DATA/DAMENG.ini"
elif [ -f "$DM_DATA/${DM_DB_NAME}.ini" ]; then
    INI_FILE="$DM_DATA/${DM_DB_NAME}.ini"
elif [ -f "$DM_DATA/$DM_DB_NAME/dm.ini" ]; then
    INI_FILE="$DM_DATA/$DM_DB_NAME/dm.ini"
elif [ -f "$DM_DATA/$DM_DB_NAME/$DM_DB_NAME.ini" ]; then
    INI_FILE="$DM_DATA/$DM_DB_NAME/$DM_DB_NAME.ini"
else
    echo "✗ 找不到配置文件"
    echo "注意：静默安装版本已在构建时完成数据库初始化"
    echo "如果这是首次运行，请检查安装是否成功"
    echo "查找路径："
    echo "  $DM_DATA/dm.ini"
    echo "  $DM_DATA/DAMENG.ini"
    echo "  $DM_DATA/${DM_DB_NAME}.ini"
    echo "  $DM_DATA/$DM_DB_NAME/dm.ini"
    echo "  $DM_DATA/$DM_DB_NAME/$DM_DB_NAME.ini"
    echo "实际文件："
    ls -la "$DM_DATA" || echo "数据目录不存在"
    if [ -d "$DM_DATA/$DM_DB_NAME" ]; then
        echo "子目录 $DM_DB_NAME 内容："
        ls -la "$DM_DATA/$DM_DB_NAME"
    fi
    exit 1
fi

echo "=== 使用配置文件: $INI_FILE ==="

# 启动数据库服务器
echo "=== 启动达梦数据库服务器 ==="
cd "$DM_HOME/bin"

# 如果是交互式启动，直接运行
if [ "$1" = "dmserver" ] || [ "$1" = "" ]; then
    echo "启动命令: ./dmserver path=$INI_FILE"
    exec ./dmserver "path=$INI_FILE"
else
    # 执行传入的命令
    exec "$@"
fi
