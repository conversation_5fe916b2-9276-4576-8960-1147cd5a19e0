<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>临时邮箱 - TempMail Desktop</title>

    <!-- ElementPlus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">

    <!-- 自定义样式 -->
    <link rel="stylesheet" href="css/style.css">

    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <!-- ElementPlus -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>

    <!-- ElementPlus 图标 -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>

    <!-- Axios -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
</head>
<body>
    <div id="app">
        <el-container class="app-container">
            <!-- 头部 -->
            <el-header class="app-header">
                <div class="header-content">
                    <div class="logo">
                        <el-icon><Message /></el-icon>
                        <span>临时邮箱</span>
                    </div>

                    <div class="header-actions">
                        <!-- 自定义邮箱按钮 - 移动到随机邮箱左侧 -->
                        <el-button
                            type="success"
                            @click="showCustomEmailDialog = true"
                            icon="Edit">
                            自定义邮箱
                        </el-button>
                        <el-button
                            type="primary"
                            @click="generateNewEmail"
                            :loading="loading"
                            icon="Refresh">
                            随机邮箱
                        </el-button>
                        <el-button
                            type="danger"
                            @click="showDestroyDialog = true"
                            :disabled="!currentEmail"
                            icon="Delete">
                            销毁邮箱
                        </el-button>
                        <el-button
                            type="warning"
                            @click="clearAllEmails"
                            :disabled="!currentEmail || emails.length === 0"
                            icon="DeleteFilled">
                            清空邮件
                        </el-button>
                    </div>
                </div>
            </el-header>

            <el-container>
                <!-- 侧边栏 -->
                <el-aside width="400px" class="sidebar">
                    <!-- 当前邮箱信息 -->
                    <el-card class="email-info-card" v-if="currentEmail" shadow="hover">
                        <template #header>
                            <div class="card-header">
                                <span style="font-weight: 600; color: white; font-size: 15px; display: flex; align-items: center;">
                                    <el-icon style="margin-right: 8px; font-size: 16px;"><Message /></el-icon>
                                    当前邮箱
                                </span>
                                <el-button
                                    type="primary"
                                    size="small"
                                    @click="copyEmail"
                                    icon="CopyDocument">
                                    复制
                                </el-button>
                            </div>
                        </template>
                        <div class="email-address" @click="copyEmail" title="点击复制邮箱地址">
                            <el-icon style="margin-right: 10px; color: #409eff; font-size: 16px; flex-shrink: 0;"><Message /></el-icon>
                            <span style="flex: 1; overflow: hidden; text-overflow: ellipsis;">{{ currentEmail && currentEmail.address ? currentEmail.address : '邮箱地址加载中...' }}</span>
                            <el-icon style="margin-left: 10px; opacity: 0.6; font-size: 14px; flex-shrink: 0;"><CopyDocument /></el-icon>
                        </div>

                        <div class="email-meta">
                            <p>
                                <strong>域名:</strong>
                                <span>{{ currentEmail && currentEmail.domain ? currentEmail.domain : '未知' }}</span>
                            </p>
                            <p>
                                <strong>创建时间:</strong>
                                <span>{{ currentEmail && currentEmail.created_time ? formatTime(currentEmail.created_time) : '未知' }}</span>
                            </p>
                            <p>
                                <strong>过期时间:</strong>
                                <span>{{ currentEmail && currentEmail.expire_time ? formatTime(currentEmail.expire_time) : '未知' }}</span>
                            </p>
                            <p>
                                <strong>状态:</strong>
                                <span>
                                    <el-tag size="small" type="success">活跃</el-tag>
                                </span>
                            </p>
                        </div>
                    </el-card>

                    <!-- 邮箱设置 -->
                    <el-card class="settings-card">
                        <template #header>
                            <span>设置</span>
                        </template>



                        <!-- 域名选择 -->
                        <div class="setting-item">
                            <label>选择域名:</label>
                            <el-select v-model="selectedDomain" placeholder="选择域名">
                                <el-option
                                    v-for="domain in availableDomains"
                                    :key="domain"
                                    :label="domain"
                                    :value="domain">
                                </el-option>
                            </el-select>
                        </div>

                        <!-- 时效设置 -->
                        <div class="setting-item">
                            <label>邮箱时效:</label>
                            <el-select v-model="selectedDuration" placeholder="选择时效">
                                <el-option label="10 分钟" :value="10"></el-option>
                                <el-option label="60 分钟" :value="60"></el-option>
                                <el-option label="2 天" :value="2880"></el-option>
                                <el-option label="7 天" :value="10080"></el-option>
                            </el-select>
                        </div>

                        <!-- 直接输入邮箱地址 -->
                        <div class="setting-item">
                            <label>直接使用邮箱:</label>
                            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                <el-input
                                    v-model="emailPrefix"
                                    placeholder="输入邮箱前缀，如: example"
                                    style="flex: 1; margin-right: 8px;">
                                </el-input>
                                <span style="margin: 0 4px; color: #666; font-weight: bold;">@</span>
                                <el-select
                                    v-model="selectedDomainForDirect"
                                    placeholder="选择域名"
                                    style="width: 140px;">
                                    <el-option
                                        v-for="domain in availableDomains"
                                        :key="domain"
                                        :label="domain"
                                        :value="domain">
                                    </el-option>
                                </el-select>
                            </div>
                            <div style="margin-bottom: 8px; color: #666; font-size: 12px;">
                                完整邮箱: {{ getFullEmailAddress() }}
                            </div>
                            <el-button
                                type="warning"
                                size="small"
                                @click="useDirectEmail"
                                :disabled="!emailPrefix.trim() || !selectedDomainForDirect"
                                style="width: 100%;">
                                使用此邮箱
                            </el-button>
                        </div>

                        <!-- PIN 码设置 -->
                        <div class="setting-item">
                            <label>设置 PIN 码:</label>
                            <el-input
                                v-model="pinCode"
                                type="password"
                                placeholder="输入 PIN 码"
                                show-password>
                            </el-input>
                            <div style="margin-top: 8px; display: flex; gap: 8px; flex-wrap: wrap;">
                                <el-button
                                    type="primary"
                                    size="small"
                                    @click="setPinCode">
                                    设置 PIN
                                </el-button>
                                <el-button
                                    type="warning"
                                    size="small"
                                    @click="testPinProtection"
                                    v-if="currentEmail">
                                    测试保护
                                </el-button>
                                <el-button
                                    type="success"
                                    size="small"
                                    @click="testPinAccess"
                                    v-if="currentEmail && pinCode">
                                    测试访问
                                </el-button>
                            </div>
                        </div>
                    </el-card>

                    <!-- 统计信息 -->
                    <!-- <el-card class="stats-card">
                        <template #header>
                            <span>统计</span>
                        </template>
                        <div class="stats-item">
                            <span>收件箱邮件:</span>
                            <el-tag>{{ emails.length }}</el-tag>
                        </div>
                        <div class="stats-item">
                            <span>未读邮件:</span>
                            <el-tag type="warning">{{ unreadCount }}</el-tag>
                        </div>
                    </el-card>-->
                </el-aside>

                <!-- 主内容区 -->
                <el-main class="main-content">
                    <!-- 欢迎页面 -->
                    <div v-if="!currentEmail" class="welcome-page">
                        <el-empty description="还没有邮箱地址">
                            <el-button type="primary" @click="generateNewEmail">
                                生成临时邮箱
                            </el-button>
                        </el-empty>
                    </div>

                    <!-- 邮件列表 -->
                    <div v-else class="email-list-container">
                        <!-- 工具栏 -->
                        <div class="toolbar">
                            <el-button
                                type="primary"
                                @click="refreshEmails"
                                :loading="refreshing"
                                icon="Refresh">
                                刷新
                            </el-button>
                            <el-button
                                type="success"
                                @click="showComposeDialog = true"
                                icon="Edit">
                                撰写邮件
                            </el-button>
                            <el-button
                                type="warning"
                                @click="markAllAsRead"
                                :disabled="unreadCount === 0"
                                icon="Check">
                                全部标记为已读
                            </el-button>
                        </div>

                        <!-- 邮件列表 -->
                        <div class="email-list">
                            <el-empty v-if="emails.length === 0" description="收件箱为空">
                                <el-button type="primary" @click="refreshEmails">
                                    刷新邮件
                                </el-button>
                            </el-empty>

                            <div v-else>
                                <div
                                    v-for="email in emails"
                                    :key="email.id"
                                    class="email-item"
                                    :class="{ 'unread': !email.read, 'selected': selectedEmail?.id === email.id }"
                                    @click="selectEmail(email)">

                                    <div class="email-header">
                                        <div class="sender">{{ email.from }}</div>
                                        <div class="date">{{ formatTime(email.date) }}</div>
                                    </div>

                                    <div class="subject">{{ getEmailSubject(email) }}</div>

                                    <div class="preview">{{ getEmailPreview(email) }}</div>

                                    <div class="email-actions">
                                        <el-button
                                            type="text"
                                            size="small"
                                            @click.stop="deleteEmail(email.id)"
                                            icon="Delete">
                                        </el-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-main>


            </el-container>
        </el-container>

        <!-- 撰写邮件对话框 -->
        <el-dialog
            v-model="showComposeDialog"
            title="撰写邮件"
            width="600px">

            <el-form :model="composeForm" label-width="80px">
                <el-form-item label="收件人:">
                    <el-input v-model="composeForm.to" placeholder="输入收件人邮箱地址"></el-input>
                </el-form-item>

                <el-form-item label="主题:">
                    <el-input v-model="composeForm.subject" placeholder="输入邮件主题"></el-input>
                </el-form-item>

                <el-form-item label="内容:">
                    <el-input
                        v-model="composeForm.body"
                        type="textarea"
                        :rows="8"
                        placeholder="输入邮件内容">
                    </el-input>
                </el-form-item>
            </el-form>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showComposeDialog = false">取消</el-button>
                    <el-button type="primary" @click="sendEmail" :loading="sending">
                        发送
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 销毁邮箱确认对话框 -->
        <el-dialog
            v-model="showDestroyDialog"
            title="确认销毁邮箱"
            width="400px">

            <p>您真的要销毁当前邮箱吗？此操作不可撤销。</p>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showDestroyDialog = false">取消</el-button>
                    <el-button type="danger" @click="destroyMailbox">
                        确认销毁
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 自定义邮箱对话框 -->
        <el-dialog
            v-model="showCustomEmailDialog"
            title="生成自定义邮箱"
            width="500px">

            <el-form :model="customEmailForm" label-width="100px">
                <el-form-item label="邮箱前缀">
                    <el-input
                        v-model="customEmailForm.prefix"
                        placeholder="输入自定义前缀"
                        maxlength="20"
                        show-word-limit
                        @keyup.enter="generateCustomEmail">
                        <template #suffix>
                            <el-text size="small" type="info">@</el-text>
                        </template>
                    </el-input>
                    <el-text size="small" type="info" style="margin-top: 5px; display: block;">
                        只允许字母、数字、点(.)、下划线(_)和连字符(-)
                    </el-text>
                </el-form-item>
                <el-form-item label="选择域名">
                    <el-select
                        v-model="customEmailForm.domain"
                        placeholder="选择邮箱域名"
                        style="width: 100%;">
                        <el-option
                            v-for="domain in availableDomains"
                            :key="domain"
                            :label="domain"
                            :value="domain">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="有效期">
                    <el-select
                        v-model="customEmailForm.duration"
                        style="width: 100%;">
                        <el-option label="10 分钟" :value="10"></el-option>
                        <el-option label="30 分钟" :value="30"></el-option>
                        <el-option label="60 分钟" :value="60"></el-option>
                        <el-option label="120 分钟" :value="120"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="预览">
                    <el-input
                        :value="customEmailPreview"
                        readonly
                        style="background-color: #f5f7fa;">
                    </el-input>
                </el-form-item>
            </el-form>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="showCustomEmailDialog = false">取消</el-button>
                    <el-button
                        type="primary"
                        @click="generateCustomEmail"
                        :loading="loading"
                        :disabled="!customEmailForm.prefix.trim()">
                        生成邮箱
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- PIN 码验证对话框 -->
        <el-dialog
            v-model="showPinDialog"
            title="收件箱PIN码保护"
            width="400px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            center>

            <div class="pin-dialog-content">
                <div class="pin-dialog-icon">
                    <el-icon size="48" color="#409eff"><Lock /></el-icon>
                </div>
                <div class="pin-dialog-text">
                    <p style="font-size: 16px; color: #333; margin-bottom: 8px;">此邮箱已设置PIN码保护</p>
                    <p style="font-size: 14px; color: #666; margin-bottom: 20px;">请输入PIN码以访问邮件内容</p>
                </div>
                <el-input
                    v-model="inputPin"
                    type="password"
                    placeholder="PIN码"
                    size="large"
                    show-password
                    @keyup.enter="verifyPin"
                    style="margin-bottom: 20px;">
                    <template #prefix>
                        <el-icon><Key /></el-icon>
                    </template>
                </el-input>
            </div>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cancelPinVerification" size="large">取消</el-button>
                    <el-button
                        type="primary"
                        @click="verifyPin"
                        size="large"
                        :loading="verifyingPin"
                        style="background: #00d084; border-color: #00d084;">
                        确认
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 邮件详情弹窗 -->
        <el-dialog
            v-model="showEmailDetailDialog"
            :title="selectedEmail ? (selectedEmail.subject || '(无主题)') : '邮件详情'"
            width="1200px"
            class="email-detail-dialog"
            :close-on-click-modal="false"
            destroy-on-close
            top="60px">

            <div v-if="selectedEmail" class="email-detail-modal-content">
                <!-- 邮件基本信息 -->
                <div class="email-info-section">
                    <div class="detail-item">
                        <div class="detail-label">
                            <el-icon><User /></el-icon>
                            <strong>发件人:</strong>
                        </div>
                        <div class="detail-value">
                            <span>{{ selectedEmail.from }}</span>
                            <el-button
                                type="text"
                                size="small"
                                @click="copyText(selectedEmail.from)"
                                icon="CopyDocument"
                                title="复制发件人">
                            </el-button>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">
                            <el-icon><Message /></el-icon>
                            <strong>主题:</strong>
                        </div>
                        <div class="detail-value">
                            <span>{{ selectedEmail.subject || '(无主题)' }}</span>
                            <el-button
                                type="text"
                                size="small"
                                @click="copyText(selectedEmail.subject || '(无主题)')"
                                icon="CopyDocument"
                                title="复制主题">
                            </el-button>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-label">
                            <el-icon><Clock /></el-icon>
                            <strong>时间:</strong>
                        </div>
                        <div class="detail-value">
                            <span>{{ formatTime(selectedEmail.date) }}</span>
                            <el-button
                                type="text"
                                size="small"
                                @click="copyText(formatTime(selectedEmail.date))"
                                icon="CopyDocument"
                                title="复制时间">
                            </el-button>
                        </div>
                    </div>
                </div>

                <el-divider></el-divider>

                <!-- 邮件内容 -->
                <div class="email-content-section">
                    <div class="email-body-header">
                        <div class="email-body-title">
                            <el-icon><Document /></el-icon>
                            邮件内容
                        </div>
                        <div class="copy-buttons">
                            <el-button
                                size="small"
                                @click="copySelectedText($event)"
                                :type="selectedTextCopied ? 'success' : 'default'"
                                icon="CopyDocument">
                                {{ selectedTextCopied ? '已复制选中!' : '复制选中' }}
                            </el-button>
                            <el-button
                                size="small"
                                @click="copyEmailContent"
                                :type="contentCopied ? 'success' : 'primary'"
                                icon="DocumentCopy">
                                {{ contentCopied ? '已复制正文!' : '复制正文' }}
                            </el-button>
                        </div>
                    </div>

                    <div class="email-content-area">
                        <!-- 如果有HTML内容，优先显示HTML -->
                        <div v-if="selectedEmail.html_body" v-html="selectedEmail.html_body"></div>
                        <!-- 否则显示纯文本内容 -->
                        <div v-else-if="selectedEmail.body" v-html="formatEmailBody(selectedEmail.body)"></div>
                        <!-- 如果都没有内容 -->
                        <div v-else class="no-content">
                            <el-empty description="邮件内容为空" :image-size="60"></el-empty>
                        </div>
                    </div>
                </div>
            </div>

            <template #footer>
                <div class="email-detail-footer">
                    <el-button @click="showEmailDetailDialog = false" icon="Close">
                        关闭
                    </el-button>
                    <el-button
                        type="danger"
                        @click="deleteEmailFromDialog"
                        icon="Delete"
                        v-if="selectedEmail">
                        删除邮件
                    </el-button>
                </div>
            </template>
        </el-dialog>
    </div>

    <!-- 应用脚本 -->
    <script src="js/app.js"></script>
</body>
</html>
