# DM8 数据库完整备份恢复说明
备份时间: 20250718_114012
备份类型: 完整备份（配置文件 + SQL数据 + 数据卷）

## 备份内容
1. 配置文件: docker-compose.yml, Dockerfile, dm.ini
2. 数据卷 (包含完整数据库数据):
   - dm8_data_volume.zip (主数据 - 包含所有数据库文件)
   - dm8_logs_volume.zip (日志数据)
   - dm8_config_backup.zip (配置)

## 恢复命令
.\backup-scripts\dm8-restore-cn.ps1 -BackupPath ".\backups\20250718_114012"

## 注意事项
- 恢复将完全替换现有数据
- 确保Docker Desktop正在运行
- 恢复期间现有服务将被停止
- 恢复完成后服务将自动启动

## 访问信息
- DM8: localhost:5236
- 用户名: SYSDBA
- 密码: SYSdba123456
