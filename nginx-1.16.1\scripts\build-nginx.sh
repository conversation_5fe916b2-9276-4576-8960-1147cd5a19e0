#!/bin/bash

# nginx编译安装脚本（使用RPM包依赖）
# 用途：在Docker容器内安装RPM依赖包并编译nginx

set -e

echo "================================================================================"
echo "                        nginx编译安装脚本（RPM依赖版本）"
echo "================================================================================"

# 定义版本号
NGINX_VERSION="1.16.1"

# 定义目录
BUILD_DIR="/tmp/build"
PACKAGES_DIR="/tmp/packages"
NGINX_PREFIX="/usr/local/nginx"

echo "开始安装RPM依赖包并编译nginx..."
echo "nginx版本: $NGINX_VERSION"

# 安装RPM依赖包
echo ""
echo "=== 安装RPM依赖包 ==="
echo "安装开发工具和依赖库..."

# 安装所有RPM包
rpm -ivh /tmp/centos7-rpms/*.rpm --force --nodeps

echo "✓ RPM依赖包安装完成"

# 创建cc符号链接（如果不存在）
if [ ! -f "/usr/bin/cc" ] && [ -f "/usr/bin/gcc" ]; then
    echo "创建cc符号链接..."
    ln -s /usr/bin/gcc /usr/bin/cc
    echo "✓ cc符号链接创建完成"
fi

# 强制确保cc链接存在
if [ ! -f "/usr/bin/cc" ]; then
    echo "强制创建cc符号链接..."
    ln -sf /usr/bin/gcc /usr/bin/cc
fi

# 验证cc链接
echo "验证cc链接:"
ls -la /usr/bin/cc

# 验证编译器
echo ""
echo "=== 验证编译器 ==="

# 检查常见的gcc路径
GCC_PATH=""
for path in /usr/bin/gcc /bin/gcc /usr/local/bin/gcc; do
    if [ -f "$path" ]; then
        echo "找到gcc: $path"
        GCC_PATH="$path"
        break
    fi
done

if [ -n "$GCC_PATH" ]; then
    echo "✓ gcc编译器可用: $(gcc --version | head -n1)"
    echo "gcc路径: $GCC_PATH"
else
    echo "❌ gcc编译器不可用"
    exit 1
fi

if command -v cc >/dev/null 2>&1; then
    echo "✓ cc编译器可用: $(cc --version | head -n1)"
else
    echo "❌ cc编译器不可用"
    exit 1
fi

# 验证关键库是否安装成功
echo ""
echo "=== 验证依赖库安装 ==="
echo "检查PCRE库..."
if pkg-config --exists libpcre; then
    echo "✓ PCRE库安装成功"
    pkg-config --modversion libpcre
else
    echo "⚠️  PCRE库检查失败，但继续构建"
fi

echo "检查zlib库..."
if pkg-config --exists zlib; then
    echo "✓ zlib库安装成功"
    pkg-config --modversion zlib
else
    echo "⚠️  zlib库检查失败，但继续构建"
fi

echo "检查OpenSSL库..."
if pkg-config --exists openssl; then
    echo "✓ OpenSSL库安装成功"
    pkg-config --modversion openssl
else
    echo "⚠️  OpenSSL库检查失败，但继续构建"
fi

# 进入构建目录
cd $BUILD_DIR

# 解压nginx源码包
echo ""
echo "=== 解压nginx源码包 ==="
echo "解压nginx源码包..."
tar -xzf $PACKAGES_DIR/nginx-${NGINX_VERSION}.tar.gz
echo "✓ nginx源码包解压完成"

# 编译nginx（使用系统安装的库）
echo ""
echo "=== 编译nginx ==="
cd nginx-${NGINX_VERSION}

echo "配置nginx编译选项（使用系统库）..."

# 不设置CC环境变量，让nginx自己找编译器
# 但确保PATH包含gcc的路径
export PATH="/usr/bin:/bin:/usr/local/bin:$PATH"

# 显示编译器信息
echo "PATH: $PATH"
echo "可用的编译器:"
ls -la /usr/bin/gcc /usr/bin/g++ 2>/dev/null || echo "编译器文件检查完成"

# 测试编译器是否可以直接调用
echo "测试gcc调用:"
gcc --version 2>/dev/null || echo "gcc调用失败"

echo "测试cc调用:"
cc --version 2>/dev/null || echo "cc调用失败"

# 检查gcc组件
echo "检查gcc组件:"
find /usr -name "cc1" 2>/dev/null || echo "cc1未找到"
find /usr -name "collect2" 2>/dev/null || echo "collect2未找到"

# 检查gcc版本目录
echo "检查gcc版本目录:"
ls -la /usr/libexec/gcc/x86_64-redhat-linux/ 2>/dev/null || echo "gcc版本目录不存在"

# 检查具体的gcc程序目录
echo "检查gcc 4.8.5目录:"
ls -la /usr/libexec/gcc/x86_64-redhat-linux/4.8.5/ 2>/dev/null || echo "gcc 4.8.5目录不存在"

echo "检查gcc 4.8.2目录:"
ls -la /usr/libexec/gcc/x86_64-redhat-linux/4.8.2/ 2>/dev/null || echo "gcc 4.8.2目录不存在"

# 检查是否缺少cc1
if [ ! -f "/usr/libexec/gcc/x86_64-redhat-linux/4.8.5/cc1" ] && [ ! -f "/usr/libexec/gcc/x86_64-redhat-linux/4.8.2/cc1" ]; then
    echo "❌ cc1文件缺失，这是gcc安装不完整的标志"
    echo "尝试查找系统中是否有cc1文件:"
    find /usr -name "cc1" -type f 2>/dev/null || echo "系统中没有找到cc1文件"

    # 尝试使用cc1plus作为cc1的替代（临时解决方案）
    if [ -f "/usr/libexec/gcc/x86_64-redhat-linux/4.8.5/cc1plus" ]; then
        echo "尝试创建cc1符号链接指向cc1plus（临时解决方案）..."
        ln -sf cc1plus /usr/libexec/gcc/x86_64-redhat-linux/4.8.5/cc1
        echo "✓ cc1符号链接已创建"
    elif [ -f "/usr/libexec/gcc/x86_64-redhat-linux/4.8.2/cc1plus" ]; then
        echo "尝试创建cc1符号链接指向cc1plus（临时解决方案）..."
        ln -sf cc1plus /usr/libexec/gcc/x86_64-redhat-linux/4.8.2/cc1
        echo "✓ cc1符号链接已创建"
    else
        echo "❌ 连cc1plus也不存在，无法创建替代方案"
        exit 1
    fi
else
    echo "✓ cc1文件存在"
fi

# 检查libmpc库
echo "检查libmpc库:"
find /usr -name "libmpc.so*" 2>/dev/null || echo "libmpc库未找到"

# 检查是否通过yum安装了libmpc
echo "检查已安装的libmpc包:"
rpm -qa | grep libmpc || echo "未找到libmpc包"

# 如果找到了libmpc库，尝试创建符号链接
if find /usr -name "libmpc.so*" 2>/dev/null | grep -q libmpc; then
    echo "找到libmpc库，检查版本..."
    find /usr -name "libmpc.so*" 2>/dev/null

    # 尝试创建libmpc.so.3符号链接
    LIBMPC_PATH=$(find /usr -name "libmpc.so.*" 2>/dev/null | head -n1)
    if [ -n "$LIBMPC_PATH" ]; then
        echo "创建libmpc.so.3符号链接..."
        ln -sf "$LIBMPC_PATH" /usr/lib64/libmpc.so.3
        ldconfig
        echo "✓ libmpc.so.3符号链接已创建"
    fi
fi

# 检查系统中的所有.so.3库
echo "检查系统中的.so.3库:"
find /usr -name "*.so.3" 2>/dev/null | head -10 || echo "未找到.so.3库"

# 尝试使用ldconfig查看库路径
echo "ldconfig缓存:"
ldconfig -p | grep mpc || echo "ldconfig中未找到mpc库"

# 检查gcc的库路径
echo "gcc库路径:"
gcc -print-search-dirs 2>/dev/null || echo "无法获取gcc搜索路径"

# 尝试使用gcc而不是cc进行编译测试
echo "测试gcc编译:"
echo 'int main(){return 0;}' > test.c
gcc -o test test.c && echo "✓ gcc编译测试成功" || echo "❌ gcc编译测试失败"
rm -f test.c test

# 测试简单的编译
echo "测试cc编译:"
echo 'int main(){return 0;}' > test.c
cc -o test test.c && echo "✓ cc编译测试成功" || echo "❌ cc编译测试失败"
rm -f test.c test

# 检查当前目录
echo "当前目录: $(pwd)"

# 修复nginx源码中的C++关键字冲突问题
echo "修复nginx源码中的C++关键字冲突..."

# 修复ngx_palloc.c中的'new'变量名冲突
if [ -f "src/core/ngx_palloc.c" ]; then
    echo "修复ngx_palloc.c中的'new'变量名..."
    sed -i 's/ngx_pool_t  \*p, \*new;/ngx_pool_t  *p, *new_pool;/g' src/core/ngx_palloc.c
    sed -i 's/new = (ngx_pool_t \*) m;/new_pool = (ngx_pool_t *) m;/g' src/core/ngx_palloc.c
    sed -i 's/new->d\.end/new_pool->d.end/g' src/core/ngx_palloc.c
    sed -i 's/new->d\.next/new_pool->d.next/g' src/core/ngx_palloc.c
    sed -i 's/new->d\.failed/new_pool->d.failed/g' src/core/ngx_palloc.c
    sed -i 's/new->d\.last/new_pool->d.last/g' src/core/ngx_palloc.c
    sed -i 's/p->d\.next = new;/p->d.next = new_pool;/g' src/core/ngx_palloc.c
    echo "✓ ngx_palloc.c修复完成"
fi

# 修复ngx_array.c中的'new'变量名冲突
if [ -f "src/core/ngx_array.c" ]; then
    echo "修复ngx_array.c中的'new'变量名..."
    sed -i 's/void        \*elt, \*new;/void        *elt, *new_ptr;/g' src/core/ngx_array.c
    sed -i 's/new = ngx_palloc/new_ptr = ngx_palloc/g' src/core/ngx_array.c
    sed -i 's/if (new == NULL)/if (new_ptr == NULL)/g' src/core/ngx_array.c
    sed -i 's/ngx_memcpy(new,/ngx_memcpy(new_ptr,/g' src/core/ngx_array.c
    sed -i 's/a->elts = new;/a->elts = new_ptr;/g' src/core/ngx_array.c
    echo "✓ ngx_array.c修复完成"
fi

# 修复ngx_resolver.c中的'class'和'export'关键字冲突
if [ -f "src/core/ngx_resolver.c" ]; then
    echo "修复ngx_resolver.c中的C++关键字冲突..."
    # 修复'class'变量名
    sed -i 's/ngx_uint_t                  type, class, qident/ngx_uint_t                  type, rr_class, qident/g' src/core/ngx_resolver.c
    sed -i 's/ngx_uint_t                  type, qident, class/ngx_uint_t                  type, qident, rr_class/g' src/core/ngx_resolver.c
    sed -i 's/ngx_uint_t            mask, type, class, qident/ngx_uint_t            mask, type, rr_class, qident/g' src/core/ngx_resolver.c
    sed -i 's/class = (an->class_hi << 8) + an->class_lo;/rr_class = (an->class_hi << 8) + an->class_lo;/g' src/core/ngx_resolver.c
    sed -i 's/if (class != 1)/if (rr_class != 1)/g' src/core/ngx_resolver.c
    sed -i 's/"unexpected RR class %ui", class/"unexpected RR class %ui", rr_class/g' src/core/ngx_resolver.c

    # 修复'export'标签名
    sed -i 's/goto export;/goto export_data;/g' src/core/ngx_resolver.c
    sed -i 's/export:/export_data:/g' src/core/ngx_resolver.c

    echo "✓ ngx_resolver.c修复完成"
fi

# 修复ngx_http_request.c中的'new'变量名冲突
if [ -f "src/http/ngx_http_request.c" ]; then
    echo "修复ngx_http_request.c中的'new'变量名..."
    sed -i 's/u_char                    \*old, \*new;/u_char                    *old, *new_ptr;/g' src/http/ngx_http_request.c
    sed -i 's/new = b->start;/new_ptr = b->start;/g' src/http/ngx_http_request.c
    sed -i 's/ngx_memcpy(new, old,/ngx_memcpy(new_ptr, old,/g' src/http/ngx_http_request.c
    sed -i 's/b->pos = new +/b->pos = new_ptr +/g' src/http/ngx_http_request.c
    sed -i 's/b->last = new +/b->last = new_ptr +/g' src/http/ngx_http_request.c
    sed -i 's/r->request_start = new;/r->request_start = new_ptr;/g' src/http/ngx_http_request.c
    sed -i 's/= new + (/= new_ptr + (/g' src/http/ngx_http_request.c
    sed -i 's/r->header_name_start = new;/r->header_name_start = new_ptr;/g' src/http/ngx_http_request.c
    echo "✓ ngx_http_request.c修复完成"
fi

# 修复ngx_http_script.c中的'new'变量名冲突
if [ -f "src/http/ngx_http_script.c" ]; then
    echo "修复ngx_http_script.c中的'new'变量名..."
    sed -i 's/void    \*new;/void    *new_ptr;/g' src/http/ngx_http_script.c
    sed -i 's/new = ngx_array_push_n/new_ptr = ngx_array_push_n/g' src/http/ngx_http_script.c
    sed -i 's/if (new == NULL)/if (new_ptr == NULL)/g' src/http/ngx_http_script.c
    sed -i 's/return new;/return new_ptr;/g' src/http/ngx_http_script.c
    echo "✓ ngx_http_script.c修复完成"
fi

# 修复ngx_http_userid_filter_module.c中的'new'变量名冲突
if [ -f "src/http/modules/ngx_http_userid_filter_module.c" ]; then
    echo "修复ngx_http_userid_filter_module.c中的'new'变量名..."
    sed -i 's/u_char  \*p, \*new;/u_char  *p, *new_ptr;/g' src/http/modules/ngx_http_userid_filter_module.c
    sed -i 's/new = ngx_pnalloc/new_ptr = ngx_pnalloc/g' src/http/modules/ngx_http_userid_filter_module.c
    sed -i 's/if (new == NULL)/if (new_ptr == NULL)/g' src/http/modules/ngx_http_userid_filter_module.c
    sed -i 's/ngx_cpymem(new,/ngx_cpymem(new_ptr,/g' src/http/modules/ngx_http_userid_filter_module.c
    sed -i 's/->data = new;/->data = new_ptr;/g' src/http/modules/ngx_http_userid_filter_module.c
    echo "✓ ngx_http_userid_filter_module.c修复完成"
fi

# 修复ngx_http_proxy_module.c中的重复定义问题
if [ -f "src/http/modules/ngx_http_proxy_module.c" ]; then
    echo "修复ngx_http_proxy_module.c中的重复定义..."
    # 创建一个临时文件来重新组织代码
    cp src/http/modules/ngx_http_proxy_module.c src/http/modules/ngx_http_proxy_module.c.bak

    # 提取模块定义部分（从定义开始到结束）
    sed -n '/^ngx_module_t  ngx_http_proxy_module = {$/,/^};$/p' src/http/modules/ngx_http_proxy_module.c > /tmp/module_def.txt

    # 删除原文件中的模块定义和声明
    sed -i '/^ngx_module_t  ngx_http_proxy_module;$/d' src/http/modules/ngx_http_proxy_module.c
    sed -i '/^ngx_module_t  ngx_http_proxy_module = {$/,/^};$/d' src/http/modules/ngx_http_proxy_module.c

    # 在文件末尾添加模块定义
    cat /tmp/module_def.txt >> src/http/modules/ngx_http_proxy_module.c

    echo "✓ ngx_http_proxy_module.c修复完成"
fi

# 设置编译器标志以处理C++兼容性问题
export CFLAGS="-O2 -g -pipe -Wall -Wp,-D_FORTIFY_SOURCE=2 -fexceptions -fstack-protector-strong --param=ssp-buffer-size=4 -grecord-gcc-switches -m64 -mtune=generic"
export CXXFLAGS="$CFLAGS"

./configure \
    --prefix=/usr/local/nginx \
    --sbin-path=/usr/local/nginx/sbin/nginx \
    --conf-path=/etc/nginx/nginx.conf \
    --error-log-path=/var/log/nginx/error.log \
    --http-log-path=/var/log/nginx/access.log \
    --pid-path=/var/run/nginx.pid \
    --lock-path=/var/run/nginx.lock \
    --http-client-body-temp-path=/var/cache/nginx/client_temp \
    --http-proxy-temp-path=/var/cache/nginx/proxy_temp \
    --http-fastcgi-temp-path=/var/cache/nginx/fastcgi_temp \
    --http-uwsgi-temp-path=/var/cache/nginx/uwsgi_temp \
    --http-scgi-temp-path=/var/cache/nginx/scgi_temp \
    --user=nginx \
    --group=nginx \
    --with-http_ssl_module \
    --with-http_v2_module \
    --with-http_realip_module \
    --with-http_auth_request_module \
    --with-http_secure_link_module \
    --with-http_stub_status_module \
    --with-http_gzip_static_module \
    --with-threads \
    --with-file-aio \
    --without-http_proxy_module \
    --without-http_fastcgi_module \
    --without-http_uwsgi_module \
    --without-http_scgi_module \
    --without-http_grpc_module \
    --with-cc-opt="-O2 -g -pipe -Wp,-D_FORTIFY_SOURCE=2 -fexceptions -fstack-protector-strong --param=ssp-buffer-size=4 -grecord-gcc-switches -m64 -mtune=generic -fpermissive -Wno-error -std=gnu89"

echo "开始编译nginx..."
make && make install

echo "✓ nginx编译安装完成"

# 创建必要的目录
echo ""
echo "=== 创建运行目录 ==="
mkdir -p /var/cache/nginx/client_temp
mkdir -p /var/cache/nginx/proxy_temp
mkdir -p /var/cache/nginx/fastcgi_temp
mkdir -p /var/cache/nginx/uwsgi_temp
mkdir -p /var/cache/nginx/scgi_temp

# 设置权限
chown -R nginx:nginx /var/cache/nginx
chown -R nginx:nginx /var/log/nginx

echo "✓ 运行目录创建完成"

# 验证安装
echo ""
echo "=== 验证nginx安装 ==="
if [ -f "/usr/local/nginx/sbin/nginx" ]; then
    echo "✓ nginx可执行文件存在"
    
    # 显示nginx版本和编译信息
    echo ""
    echo "nginx版本信息："
    /usr/local/nginx/sbin/nginx -V
    
    echo ""
    echo "✓ nginx编译安装验证完成"
else
    echo "❌ nginx可执行文件不存在，编译可能失败"
    exit 1
fi

echo ""
echo "================================================================================"
echo "                        nginx编译安装完成"
echo "================================================================================"
echo "安装路径: /usr/local/nginx"
echo "配置文件: /etc/nginx/nginx.conf"
echo "日志目录: /var/log/nginx"
echo "缓存目录: /var/cache/nginx"
echo "================================================================================"
