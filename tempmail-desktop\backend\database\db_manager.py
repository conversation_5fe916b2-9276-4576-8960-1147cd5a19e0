#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理模块
处理 SQLite 数据库操作
"""

import sqlite3
import os
import sys
import json
from datetime import datetime
from contextlib import contextmanager

class DatabaseManager:
    """数据库管理器"""

    def __init__(self, db_path=None):
        if db_path is None:
            db_path = self.get_default_db_path()
        self.db_path = db_path
        self.ensure_db_directory()
        self.init_database()

    def get_default_db_path(self):
        """获取默认数据库路径，使用用户应用数据目录"""
        try:
            # 获取用户应用数据目录
            if os.name == 'nt':  # Windows
                app_data = os.environ.get('APPDATA', os.path.expanduser('~'))
                app_dir = os.path.join(app_data, 'TempMail Desktop')
            else:  # Linux/Mac
                app_dir = os.path.expanduser('~/.tempmail-desktop')

            # 确保应用目录存在
            if not os.path.exists(app_dir):
                os.makedirs(app_dir, exist_ok=True)

            return os.path.join(app_dir, 'tempmail.db')
        except Exception as e:
            print(f"[ERROR] 无法创建应用数据目录，使用临时目录: {e}")
            # 如果无法创建应用数据目录，使用临时目录
            import tempfile
            temp_dir = tempfile.gettempdir()
            return os.path.join(temp_dir, 'tempmail_desktop.db')

    def ensure_db_directory(self):
        """确保数据库目录存在"""
        try:
            db_dir = os.path.dirname(self.db_path)
            if db_dir and not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)
                print(f"[OK] 创建数据库目录: {db_dir}")
        except Exception as e:
            print(f"[ERROR] 创建数据库目录失败: {e}")
            # 如果无法创建目录，尝试使用临时目录
            import tempfile
            temp_dir = tempfile.gettempdir()
            self.db_path = os.path.join(temp_dir, 'tempmail_desktop.db')
            print(f"[WARNING] 使用临时数据库路径: {self.db_path}")

    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使查询结果可以像字典一样访问
        try:
            yield conn
        finally:
            conn.close()

    def init_database(self):
        """初始化数据库表结构"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 创建邮箱账户表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS email_accounts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email_address TEXT UNIQUE NOT NULL,
                    domain TEXT NOT NULL,
                    token TEXT,
                    pin_code TEXT,
                    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expire_time TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1
                )
            ''')

            # 创建邮件表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS emails (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    account_id INTEGER NOT NULL,
                    email_id TEXT,
                    sender TEXT NOT NULL,
                    subject TEXT,
                    body TEXT,
                    received_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_read BOOLEAN DEFAULT 0,
                    is_deleted BOOLEAN DEFAULT 0,
                    FOREIGN KEY (account_id) REFERENCES email_accounts (id)
                )
            ''')

            # 创建设置表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE NOT NULL,
                    value TEXT,
                    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            conn.commit()

    def create_email(self, email_address, expire_time, domain=None, token=None):
        """创建新的邮箱账户"""
        if domain is None:
            domain = email_address.split('@')[1]

        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 首先检查是否已存在相同的邮箱地址
            cursor.execute('''
                SELECT id FROM email_accounts
                WHERE email_address = ?
            ''', (email_address,))
            existing = cursor.fetchone()

            if existing:
                # 如果存在，先删除旧记录
                print(f"[REFRESH] 邮箱地址 {email_address} 已存在，删除旧记录...")
                cursor.execute('''
                    DELETE FROM emails WHERE account_id = ?
                ''', (existing['id'],))
                cursor.execute('''
                    DELETE FROM email_accounts WHERE id = ?
                ''', (existing['id'],))
                print(f"[OK] 旧记录已删除")

            # 创建新记录
            cursor.execute('''
                INSERT INTO email_accounts (email_address, domain, token, expire_time)
                VALUES (?, ?, ?, ?)
            ''', (email_address, domain, token, expire_time))
            conn.commit()
            return cursor.lastrowid

    def get_email_account(self, email_address):
        """获取邮箱账户信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM email_accounts
                WHERE email_address = ? AND is_active = 1
            ''', (email_address,))
            row = cursor.fetchone()
            return dict(row) if row else None

    def update_email_pin(self, account_id, pin_code):
        """更新邮箱 PIN 码"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE email_accounts
                SET pin_code = ?
                WHERE id = ?
            ''', (pin_code, account_id))
            conn.commit()

    def save_email(self, account_id, sender, subject, body, received_time=None, email_id=None):
        """保存邮件到数据库"""
        if received_time is None:
            received_time = datetime.now()

        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO emails (account_id, email_id, sender, subject, body, received_time)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (account_id, email_id, sender, subject, body, received_time))
            conn.commit()
            return cursor.lastrowid

    def get_emails(self, account_id, include_deleted=False):
        """获取邮箱中的邮件列表"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            if include_deleted:
                cursor.execute('''
                    SELECT * FROM emails
                    WHERE account_id = ?
                    ORDER BY received_time DESC
                ''', (account_id,))
            else:
                cursor.execute('''
                    SELECT * FROM emails
                    WHERE account_id = ? AND is_deleted = 0
                    ORDER BY received_time DESC
                ''', (account_id,))

            rows = cursor.fetchall()
            return [dict(row) for row in rows]

    def mark_email_read(self, email_id):
        """标记邮件为已读"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE emails
                SET is_read = 1
                WHERE id = ?
            ''', (email_id,))
            conn.commit()

    def delete_email(self, email_id):
        """删除邮件（软删除）"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE emails
                SET is_deleted = 1
                WHERE id = ?
            ''', (email_id,))
            conn.commit()

    def clear_emails_by_mailbox(self, account_id):
        """清空指定邮箱的所有邮件"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            # 先获取要删除的邮件数量
            cursor.execute('''
                SELECT COUNT(*) as count FROM emails
                WHERE account_id = ? AND is_deleted = 0
            ''', (account_id,))
            count_before = cursor.fetchone()['count']

            # 软删除所有邮件
            cursor.execute('''
                UPDATE emails
                SET is_deleted = 1
                WHERE account_id = ? AND is_deleted = 0
            ''', (account_id,))

            deleted_count = cursor.rowcount
            conn.commit()

            print(f"[DELETE] 清空邮箱 {account_id} 的 {deleted_count} 封本地邮件")
            return deleted_count

    def delete_email_account(self, account_id):
        """删除邮箱账户"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 软删除账户
            cursor.execute('''
                UPDATE email_accounts
                SET is_active = 0
                WHERE id = ?
            ''', (account_id,))

            # 删除相关邮件
            cursor.execute('''
                UPDATE emails
                SET is_deleted = 1
                WHERE account_id = ?
            ''', (account_id,))

            conn.commit()

    def get_setting(self, key, default_value=None):
        """获取设置值"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT value FROM settings
                WHERE key = ?
            ''', (key,))
            row = cursor.fetchone()
            return row['value'] if row else default_value

    def set_setting(self, key, value):
        """设置配置值"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO settings (key, value, updated_time)
                VALUES (?, ?, CURRENT_TIMESTAMP)
            ''', (key, value))
            conn.commit()

    def cleanup_expired_emails(self):
        """清理过期的邮箱和邮件"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 获取过期的邮箱账户
            cursor.execute('''
                SELECT id FROM email_accounts
                WHERE expire_time < CURRENT_TIMESTAMP AND is_active = 1
            ''')
            expired_accounts = cursor.fetchall()

            # 删除过期账户及其邮件
            for account in expired_accounts:
                self.delete_email_account(account['id'])

    def get_statistics(self):
        """获取统计信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 活跃邮箱数量
            cursor.execute('SELECT COUNT(*) as count FROM email_accounts WHERE is_active = 1')
            active_accounts = cursor.fetchone()['count']

            # 总邮件数量
            cursor.execute('SELECT COUNT(*) as count FROM emails WHERE is_deleted = 0')
            total_emails = cursor.fetchone()['count']

            # 未读邮件数量
            cursor.execute('SELECT COUNT(*) as count FROM emails WHERE is_read = 0 AND is_deleted = 0')
            unread_emails = cursor.fetchone()['count']

            return {
                'active_accounts': active_accounts,
                'total_emails': total_emails,
                'unread_emails': unread_emails
            }

    def get_recent_emails(self, limit=10):
        """获取最近的邮件记录（用于恢复邮箱地址）"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT DISTINCT ea.email_address, e.received_time
                FROM emails e
                JOIN email_accounts ea ON e.account_id = ea.id
                WHERE e.is_deleted = 0
                ORDER BY e.received_time DESC
                LIMIT ?
            ''', (limit,))

            rows = cursor.fetchall()
            return [{'email_address': row['email_address'], 'received_time': row['received_time']} for row in rows]
