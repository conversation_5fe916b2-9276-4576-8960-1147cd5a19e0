#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时邮箱桌面应用打包脚本
使用 PyInstaller 打包为可执行文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def check_pyinstaller():
    """检查 PyInstaller 是否已安装"""
    try:
        import PyInstaller
        return True
    except ImportError:
        return False

def install_pyinstaller():
    """安装 PyInstaller"""
    print("正在安装 PyInstaller...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
        print("PyInstaller 安装完成!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"PyInstaller 安装失败: {e}")
        return False

def create_spec_file():
    """创建 PyInstaller 规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('frontend', 'frontend'),
        ('backend', 'backend'),
    ],
    hiddenimports=[
        'backend.app',
        'backend.api.tempmail_api',
        'backend.database.db_manager',
        'backend.utils.helpers',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='TempMail-Desktop',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
'''
    
    with open('tempmail.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("已创建 PyInstaller 规格文件: tempmail.spec")

def build_application():
    """构建应用程序"""
    print("正在构建应用程序...")
    
    try:
        # 使用 PyInstaller 构建
        cmd = [
            sys.executable, '-m', 'PyInstaller',
            '--clean',
            '--noconfirm',
            'tempmail.spec'
        ]
        
        subprocess.check_call(cmd)
        print("应用程序构建完成!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"构建失败: {e}")
        return False

def create_assets():
    """创建资源文件"""
    assets_dir = Path('assets')
    assets_dir.mkdir(exist_ok=True)
    
    # 这里可以添加图标文件等资源
    print("资源目录已创建: assets/")

def clean_build():
    """清理构建文件"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    files_to_clean = ['tempmail.spec']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已清理目录: {dir_name}")
    
    for file_name in files_to_clean:
        if os.path.exists(file_name):
            os.remove(file_name)
            print(f"已清理文件: {file_name}")

def copy_additional_files():
    """复制额外的文件到输出目录"""
    dist_dir = Path('dist')
    if not dist_dir.exists():
        return
    
    # 复制 README 文件
    readme_file = Path('README.md')
    if readme_file.exists():
        shutil.copy2(readme_file, dist_dir / 'README.md')
        print("已复制 README.md")
    
    # 创建启动说明
    instructions = """
临时邮箱桌面应用

使用说明:
1. 双击 TempMail-Desktop.exe 启动应用
2. 点击"生成新邮箱"创建临时邮箱地址
3. 使用生成的邮箱地址接收邮件
4. 可以设置 PIN 码保护邮箱
5. 支持撰写和发送邮件

注意事项:
- 临时邮箱有时效限制，请及时查看邮件
- 应用数据保存在本地，卸载前请备份重要邮件
- 如遇问题，请检查网络连接

版本: 1.0.0
"""
    
    with open(dist_dir / '使用说明.txt', 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("已创建使用说明文件")

def main():
    """主函数"""
    print("=" * 50)
    print("临时邮箱桌面应用打包工具")
    print("=" * 50)
    
    # 检查 PyInstaller
    if not check_pyinstaller():
        response = input("PyInstaller 未安装，是否自动安装? (y/n): ").lower().strip()
        if response == 'y':
            if not install_pyinstaller():
                print("PyInstaller 安装失败，程序退出")
                return
        else:
            print("程序退出")
            return
    
    # 询问是否清理之前的构建
    if os.path.exists('dist') or os.path.exists('build'):
        response = input("是否清理之前的构建文件? (y/n): ").lower().strip()
        if response == 'y':
            clean_build()
    
    # 创建资源文件
    create_assets()
    
    # 创建规格文件
    create_spec_file()
    
    # 构建应用
    if build_application():
        print("\n构建成功!")
        
        # 复制额外文件
        copy_additional_files()
        
        print(f"\n可执行文件位置: {os.path.abspath('dist')}")
        print("打包完成!")
        
        # 询问是否清理临时文件
        response = input("\n是否清理临时构建文件? (y/n): ").lower().strip()
        if response == 'y':
            if os.path.exists('build'):
                shutil.rmtree('build')
            if os.path.exists('tempmail.spec'):
                os.remove('tempmail.spec')
            print("临时文件已清理")
    else:
        print("构建失败!")

if __name__ == '__main__':
    main()
