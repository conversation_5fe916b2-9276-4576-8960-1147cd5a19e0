<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>nginx 1.28.0 离线构建指导教程</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }

        h3 {
            color: #2980b9;
            margin-top: 25px;
        }

        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }

        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        .danger {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }

        .step {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }

        .file-tree {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            font-family: monospace;
            white-space: pre-line;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .toc {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }

        .toc li {
            margin: 5px 0;
        }

        .toc a {
            text-decoration: none;
            color: #3498db;
        }

        .toc a:hover {
            text-decoration: underline;
        }

        .badge {
            display: inline-block;
            padding: 3px 8px;
            font-size: 12px;
            font-weight: bold;
            border-radius: 3px;
            margin-right: 5px;
        }

        .badge-success {
            background-color: #28a745;
            color: white;
        }

        .badge-warning {
            background-color: #ffc107;
            color: black;
        }

        .badge-danger {
            background-color: #dc3545;
            color: white;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🚀 nginx 1.28.0 离线构建指导教程</h1>

        <div class="highlight success">
            <strong>📋 教程概述</strong><br>
            本教程将指导您在完全离线的环境中构建nginx 1.28.0 Docker镜像。适用于企业内网、受限网络环境或需要标准化部署的场景。
        </div>

        <div class="toc">
            <h3>📑 目录</h3>
            <ul>
                <li><a href="#overview">1. 项目概述</a></li>
                <li><a href="#requirements">2. 环境要求</a></li>
                <li><a href="#structure">3. 文件结构</a></li>
                <li><a href="#preparation">4. 准备工作</a></li>
                <li><a href="#offline-build">5. 离线构建</a></li>
                <li><a href="#testing">6. 测试验证</a></li>
                <li><a href="#usage">7. 使用说明</a></li>
                <li><a href="#troubleshooting">8. 故障排除</a></li>
                <li><a href="#customization">9. 自定义配置</a></li>
                <li><a href="#faq">10. 常见问题</a></li>
            </ul>
        </div>

        <h2 id="overview">1. 📖 项目概述</h2>

        <h3>🎯 项目特点</h3>
        <ul>
            <li><span class="badge badge-success">✓</span> <strong>完全离线</strong>：所有依赖预先打包，无需网络连接</li>
            <li><span class="badge badge-success">✓</span> <strong>基于RPM</strong>：使用系统RPM包而非源码编译依赖</li>
            <li><span class="badge badge-success">✓</span> <strong>自动修复</strong>：自动处理nginx源码兼容性问题</li>
            <li><span class="badge badge-success">✓</span> <strong>模块丰富</strong>：包含SSL、HTTP/2等常用模块</li>
        </ul>

        <h3>📦 包含的nginx模块</h3>
        <table>
            <tr>
                <th>模块</th>
                <th>功能描述</th>
                <th>状态</th>
            </tr>
            <tr>
                <td>--with-http_ssl_module</td>
                <td>SSL/TLS支持</td>
                <td><span class="badge badge-success">启用</span></td>
            </tr>
            <tr>
                <td>--with-http_v2_module</td>
                <td>HTTP/2支持</td>
                <td><span class="badge badge-success">启用</span></td>
            </tr>
            <tr>
                <td>--with-http_realip_module</td>
                <td>真实IP模块</td>
                <td><span class="badge badge-success">启用</span></td>
            </tr>
            <tr>
                <td>--with-http_auth_request_module</td>
                <td>认证请求模块</td>
                <td><span class="badge badge-success">启用</span></td>
            </tr>
            <tr>
                <td>--with-http_secure_link_module</td>
                <td>安全链接模块</td>
                <td><span class="badge badge-success">启用</span></td>
            </tr>
            <tr>
                <td>--with-http_stub_status_module</td>
                <td>状态监控模块</td>
                <td><span class="badge badge-success">启用</span></td>
            </tr>
            <tr>
                <td>--with-http_gzip_static_module</td>
                <td>静态gzip模块</td>
                <td><span class="badge badge-success">启用</span></td>
            </tr>
            <tr>
                <td>--with-threads</td>
                <td>线程支持</td>
                <td><span class="badge badge-success">启用</span></td>
            </tr>
            <tr>
                <td>--with-file-aio</td>
                <td>文件异步IO</td>
                <td><span class="badge badge-success">启用</span></td>
            </tr>
        </table>

        <div class="highlight warning">
            <strong>⚠️ 注意</strong><br>
            为确保编译成功，以下模块已被禁用：proxy、fastcgi、uwsgi、scgi、grpc模块。这些模块在某些环境下可能导致编译错误。
        </div>

        <h2 id="requirements">2. 🔧 环境要求</h2>

        <h3>💻 系统要求</h3>
        <ul>
            <li><strong>操作系统</strong>：Linux、macOS、Windows（支持Docker）</li>
            <li><strong>Docker版本</strong>：≥ 18.09</li>
            <li><strong>磁盘空间</strong>：≥ 2GB 可用空间</li>
            <li><strong>内存</strong>：≥ 2GB RAM</li>
        </ul>

        <h3>📋 软件依赖</h3>
        <div class="code">
            # 检查Docker版本
            docker --version

            # 检查Docker是否运行
            docker ps
        </div>

        <h2 id="structure">3. 📁 文件结构</h2>

        <div class="file-tree">nginx-1.28.0-offline-build/
            ├── Dockerfile # Docker构建文件
            ├── download-packages.sh # 包下载脚本（联网环境使用）
            ├── build-offline.sh # 离线构建脚本
            ├── test-nginx.sh # 测试脚本
            ├── 指导教程.html # 本教程文件
            ├── packages/ # nginx源码包目录
            │ ├── nginx-1.28.0.tar.gz # nginx源码包
            │ └── README.txt # 包信息说明
            ├── centos7-rpms/ # CentOS 7 RPM依赖包
            │ ├── gcc-*.rpm # GCC编译器
            │ ├── openssl-*.rpm # OpenSSL库
            │ ├── pcre-*.rpm # PCRE正则库
            │ ├── zlib-*.rpm # zlib压缩库
            │ ├── ... # 其他依赖包
            │ └── README.txt # RPM包说明
            ├── scripts/ # 构建脚本目录
            │ └── build-nginx.sh # nginx编译脚本
            └── config/ # nginx配置文件
            ├── nginx.conf # 主配置文件
            └── default.conf # 默认站点配置</div>

        <h2 id="preparation">4. 🛠️ 准备工作</h2>

        <div class="step">
            <h3>步骤1：获取项目文件</h3>
            <p>如果您在联网环境中，可以使用下载脚本获取所有依赖：</p>
            <div class="code">
                # 给脚本执行权限
                chmod +x download-packages.sh

                # 下载所有依赖包
                ./download-packages.sh
            </div>
            <p>如果您已经有完整的项目包，请解压到工作目录。</p>
        </div>

        <div class="step">
            <h3>步骤2：验证文件完整性</h3>
            <div class="code">
                # 检查nginx源码包
                ls -la packages/nginx-1.28.0.tar.gz

                # 检查RPM包数量
                ls centos7-rpms/*.rpm | wc -l
                # 应该显示20+个RPM包

                # 检查脚本文件
                ls -la *.sh scripts/*.sh
            </div>
        </div>

        <h2 id="offline-build">5. 🏗️ 离线构建</h2>

        <div class="step">
            <h3>步骤1：运行构建脚本</h3>
            <div class="code">
                # 给构建脚本执行权限
                chmod +x build-offline.sh

                # 开始构建（这个过程可能需要10-20分钟）
                ./build-offline.sh
            </div>
        </div>

        <div class="step">
            <h3>步骤2：手动构建（可选）</h3>
            <p>如果您希望手动控制构建过程：</p>
            <div class="code">
                # 直接使用Docker构建
                docker build -t nginx-1.28.0-offline .

                # 查看构建的镜像
                docker images nginx-1.28.0-offline
            </div>
        </div>

        <div class="highlight success">
            <strong>✅ 构建成功标志</strong><br>
            当您看到类似以下输出时，表示构建成功：
            <div class="code">
                Successfully built 1234567890ab
                Successfully tagged nginx-1.28.0-offline:latest
            </div>
        </div>

        <h2 id="testing">6. 🧪 测试验证</h2>

        <div class="step">
            <h3>步骤1：运行测试脚本</h3>
            <div class="code">
                # 给测试脚本执行权限
                chmod +x test-nginx.sh

                # 运行完整测试
                ./test-nginx.sh
            </div>
        </div>

        <div class="step">
            <h3>步骤2：手动测试</h3>
            <div class="code">
                # 检查nginx版本
                docker run --rm nginx-1.28.0-offline /usr/local/nginx/sbin/nginx -v

                # 检查编译配置
                docker run --rm nginx-1.28.0-offline /usr/local/nginx/sbin/nginx -V

                # 启动测试容器
                docker run -d -p 8080:80 --name nginx-test nginx-1.28.0-offline

                # 测试HTTP连接
                curl http://localhost:8080

                # 清理测试容器
                docker stop nginx-test && docker rm nginx-test
            </div>
        </div>

        <h2 id="usage">7. 📚 使用说明</h2>

        <h3>🚀 启动nginx容器</h3>
        <div class="code">
            # 前台运行（用于调试）
            docker run --rm -p 80:80 nginx-1.28.0-offline

            # 后台运行（生产环境）
            docker run -d -p 80:80 --name nginx-server nginx-1.28.0-offline

            # 使用自定义配置
            docker run -d -p 80:80 \
            -v /path/to/your/nginx.conf:/etc/nginx/nginx.conf \
            -v /path/to/your/html:/usr/share/nginx/html \
            --name nginx-server nginx-1.28.0-offline
        </div>

        <h3>🔧 管理命令</h3>
        <div class="code">
            # 查看容器状态
            docker ps

            # 查看容器日志
            docker logs nginx-server

            # 进入容器
            docker exec -it nginx-server /bin/bash

            # 测试nginx配置
            docker exec nginx-server /usr/local/nginx/sbin/nginx -t

            # 重载nginx配置
            docker exec nginx-server /usr/local/nginx/sbin/nginx -s reload

            # 停止容器
            docker stop nginx-server

            # 删除容器
            docker rm nginx-server
        </div>

        <h2 id="troubleshooting">8. 🔍 故障排除</h2>

        <h3>❌ 常见构建错误</h3>

        <div class="highlight danger">
            <strong>错误：Docker构建失败</strong><br>
            <strong>解决方案：</strong>
            <ul>
                <li>检查Docker是否有足够的磁盘空间（至少2GB）</li>
                <li>确认所有依赖文件完整</li>
                <li>重新运行构建命令</li>
            </ul>
        </div>

        <div class="highlight danger">
            <strong>错误：RPM包安装失败</strong><br>
            <strong>解决方案：</strong>
            <ul>
                <li>检查centos7-rpms目录中是否有足够的RPM包</li>
                <li>确认RPM包没有损坏</li>
                <li>重新下载依赖包</li>
            </ul>
        </div>

        <div class="highlight danger">
            <strong>错误：nginx编译失败</strong><br>
            <strong>解决方案：</strong>
            <ul>
                <li>检查gcc编译器是否正确安装</li>
                <li>确认所有开发库都已安装</li>
                <li>查看编译日志中的具体错误信息</li>
            </ul>
        </div>

        <h3>🔧 调试技巧</h3>
        <div class="code">
            # 进入构建容器调试
            docker run -it --rm centos:7 /bin/bash

            # 手动安装RPM包测试
            rpm -ivh /path/to/package.rpm --force --nodeps

            # 检查编译器状态
            gcc --version
            which gcc

            # 查看系统库
            ldconfig -p | grep ssl
        </div>

        <h2 id="customization">9. ⚙️ 自定义配置</h2>

        <h3>📝 修改nginx配置</h3>
        <p>编辑 <code>config/nginx.conf</code> 和 <code>config/default.conf</code> 文件，然后重新构建镜像。</p>

        <h3>🔧 添加nginx模块</h3>
        <p>在 <code>scripts/build-nginx.sh</code> 中的configure命令中添加更多的 <code>--with-*</code> 选项：</p>
        <div class="code">
            ./configure \
            --prefix=/usr/local/nginx \
            # ... 其他选项 ...
            --with-http_geoip_module \ # 添加GeoIP模块
            --with-http_image_filter_module # 添加图像过滤模块
        </div>

        <h3>🔄 更新nginx版本</h3>
        <ol>
            <li>下载新的nginx源码包到 <code>packages/</code> 目录</li>
            <li>更新 <code>scripts/build-nginx.sh</code> 中的版本号</li>
            <li>重新构建镜像</li>
        </ol>

        <h2 id="faq">10. ❓ 常见问题</h2>

        <h3>Q: 为什么禁用了一些nginx模块？</h3>
        <p><strong>A:</strong> 为了确保在CentOS 7环境下编译成功，我们禁用了可能导致编译错误的模块（如proxy、fastcgi等）。如果您需要这些模块，可以尝试在更新的系统环境中编译。</p>

        <h3>Q: 可以在其他Linux发行版上使用吗？</h3>
        <p><strong>A:</strong> 本方案专门针对CentOS 7优化。如需在其他发行版使用，需要相应调整RPM包和依赖库。</p>

        <h3>Q: 如何添加SSL证书？</h3>
        <p><strong>A:</strong> 将证书文件挂载到容器中，并修改nginx配置：</p>
        <div class="code">
            docker run -d -p 443:443 \
            -v /path/to/ssl:/etc/nginx/ssl \
            -v /path/to/nginx.conf:/etc/nginx/nginx.conf \
            nginx-1.28.0-offline
        </div>

        <h3>Q: 如何查看详细的构建日志？</h3>
        <p><strong>A:</strong> 使用以下命令查看详细构建过程：</p>
        <div class="code">
            docker build --no-cache --progress=plain -t nginx-1.28.0-offline .
        </div>

        <div class="highlight success">
            <h3>🎉 恭喜！</h3>
            <p>您已经成功完成了nginx 1.28.0的离线构建。这个Docker镜像可以在任何支持Docker的环境中运行，无需网络连接。</p>
            <p>如果您在使用过程中遇到问题，请参考故障排除部分或查看相关日志文件。</p>
        </div>

        <h2 id="advanced">11. 🚀 高级用法</h2>

        <h3>🔄 Docker Compose部署</h3>
        <p>创建 <code>docker-compose.yml</code> 文件：</p>
        <div class="code">
            version: '3.8'
            services:
            nginx:
            image: nginx-1.28.0-offline
            container_name: nginx-server
            ports:
            - "80:80"
            - "443:443"
            volumes:
            - ./html:/usr/share/nginx/html
            - ./conf/nginx.conf:/etc/nginx/nginx.conf
            - ./logs:/var/log/nginx
            restart: unless-stopped
            networks:
            - web

            networks:
            web:
            driver: bridge
        </div>

        <h3>🏭 生产环境部署建议</h3>
        <ul>
            <li><strong>资源限制</strong>：设置合适的CPU和内存限制</li>
            <li><strong>日志管理</strong>：配置日志轮转和持久化存储</li>
            <li><strong>监控</strong>：启用nginx状态页面和健康检查</li>
            <li><strong>安全</strong>：定期更新镜像和配置安全头</li>
        </ul>

        <div class="code">
            # 生产环境启动命令示例
            docker run -d \
            --name nginx-prod \
            --restart=unless-stopped \
            --memory=512m \
            --cpus=1.0 \
            -p 80:80 \
            -p 443:443 \
            -v /data/nginx/html:/usr/share/nginx/html:ro \
            -v /data/nginx/conf:/etc/nginx:ro \
            -v /data/nginx/logs:/var/log/nginx \
            nginx-1.28.0-offline
        </div>

        <h3>📊 性能优化建议</h3>
        <table>
            <tr>
                <th>配置项</th>
                <th>建议值</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>worker_processes</td>
                <td>auto</td>
                <td>自动检测CPU核心数</td>
            </tr>
            <tr>
                <td>worker_connections</td>
                <td>1024-4096</td>
                <td>根据服务器性能调整</td>
            </tr>
            <tr>
                <td>keepalive_timeout</td>
                <td>65</td>
                <td>保持连接时间</td>
            </tr>
            <tr>
                <td>gzip</td>
                <td>on</td>
                <td>启用压缩节省带宽</td>
            </tr>
        </table>

        <h2 id="security">12. 🔒 安全配置</h2>

        <h3>🛡️ 基础安全配置</h3>
        <div class="code">
            # 在nginx.conf中添加安全头
            add_header X-Frame-Options DENY;
            add_header X-Content-Type-Options nosniff;
            add_header X-XSS-Protection "1; mode=block";
            add_header Strict-Transport-Security "max-age=31536000" always;
            add_header Referrer-Policy "strict-origin-when-cross-origin";

            # 隐藏nginx版本信息
            server_tokens off;

            # 限制请求大小
            client_max_body_size 10M;

            # 限制请求频率
            limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
        </div>

        <h3>🔐 SSL/TLS配置示例</h3>
        <div class="code">
            server {
            listen 443 ssl http2;
            server_name example.com;

            ssl_certificate /etc/nginx/ssl/cert.pem;
            ssl_certificate_key /etc/nginx/ssl/key.pem;

            # 现代SSL配置
            ssl_protocols TLSv1.2 TLSv1.3;
            ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
            ssl_prefer_server_ciphers off;
            ssl_session_cache shared:SSL:10m;
            ssl_session_timeout 10m;

            # OCSP Stapling
            ssl_stapling on;
            ssl_stapling_verify on;

            location / {
            root /usr/share/nginx/html;
            index index.html;
            }
            }
        </div>

        <h2 id="backup">13. 💾 备份与恢复</h2>

        <h3>📦 镜像备份</h3>
        <div class="code">
            # 导出Docker镜像
            docker save nginx-1.28.0-offline > nginx-1.28.0-offline.tar

            # 压缩镜像文件
            gzip nginx-1.28.0-offline.tar

            # 在其他机器上导入镜像
            docker load < nginx-1.28.0-offline.tar.gz </div>

                <h3>📋 配置备份</h3>
                <div class="code">
                    # 备份nginx配置
                    docker exec nginx-server tar -czf /tmp/nginx-config-backup.tar.gz /etc/nginx

                    # 复制备份文件到主机
                    docker cp nginx-server:/tmp/nginx-config-backup.tar.gz ./

                    # 恢复配置
                    docker cp nginx-config-backup.tar.gz nginx-server:/tmp/
                    docker exec nginx-server tar -xzf /tmp/nginx-config-backup.tar.gz -C /
                </div>

                <footer
                    style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #eee; text-align: center; color: #666;">
                    <p>nginx 1.28.0 离线构建项目 | 基于CentOS 7 | 支持完全离线部署</p>
                    <p>构建时间:
                        <script>document.write(new Date().toLocaleString());</script>
                    </p>
                    <p>版本: v1.0 | 最后更新: 2024年</p>
                </footer>
        </div>
</body>

</html>