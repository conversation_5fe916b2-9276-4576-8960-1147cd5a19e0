-- MySQL初始化脚本
-- 设置root用户密码和远程访问权限

-- 使用mysql数据库
USE mysql;

-- 更新root用户密码
UPDATE user SET authentication_string = PASSWORD('root') WHERE User = 'root';

-- 创建允许远程访问的root用户
CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY 'root';

-- 授予所有权限给root用户
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;

-- 删除匿名用户
DELETE FROM user WHERE User = '';

-- 删除test数据库
DROP DATABASE IF EXISTS test;
DELETE FROM db WHERE Db = 'test' OR Db = 'test\\_%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 创建一个测试数据库
CREATE DATABASE IF NOT EXISTS testdb DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 显示当前用户信息
SELECT User, Host FROM user WHERE User = 'root';
