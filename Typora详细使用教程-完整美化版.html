<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Typora详细使用教程-完整美化版</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 13px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 功能标识样式 */
        .feature-tag {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            margin: 0 8px 12px 0;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .feature-tag:hover::before {
            left: 100%;
        }

        .feature-basic {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
        }

        .feature-advanced {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
        }

        .feature-pro {
            background: linear-gradient(135deg, #f9ca24 0%, #f0932b 100%);
            color: white;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: var(--light-surface);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        th,
        td {
            padding: 18px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-decoration: none;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            z-index: 999;
            font-size: 20px;
        }

        .back-to-top:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .content-wrapper {
                padding: 30px;
            }

            .container {
                padding: 40px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            h3 {
                font-size: 20px;
            }
        }

        /* 滚动条样式 */
        .main-content::-webkit-scrollbar {
            width: 8px;
        }

        .main-content::-webkit-scrollbar-track {
            background: rgba(102, 126, 234, 0.1);
        }

        .main-content::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.3);
            border-radius: 4px;
        }

        .main-content::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.6);
        }

        /* 平滑滚动效果 */
        html {
            scroll-behavior: smooth;
        }

        /* 图片样式 */
        img {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: var(--shadow-md);
            margin: 20px 0;
        }

        /* 列表样式 */
        ul, ol {
            padding-left: 30px;
            margin: 15px 0;
        }

        li {
            margin: 8px 0;
            line-height: 1.6;
        }

        /* 快捷键样式 */
        .shortcut-key {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            font-weight: 600;
            box-shadow: var(--shadow-sm);
            margin: 0 2px;
            display: inline-block;
        }
    </style>
</head>

<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-edit"></i> Typora使用教程</h2>
            <p>专业Markdown编辑器完全指南</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#overview"><i class="fas fa-eye"></i>1. 概述</a></li>
                <li><a href="#installation"><i class="fas fa-download"></i>2. 安装与配置</a></li>
                <li><a href="#basic-usage"><i class="fas fa-play"></i>3. 基础使用</a></li>
                <li><a href="#markdown-syntax"><i class="fas fa-code"></i>4. Markdown语法</a></li>
                <li><a href="#advanced-features"><i class="fas fa-magic"></i>5. 高级功能</a></li>
                <li><a href="#themes-customization"><i class="fas fa-palette"></i>6. 主题与自定义</a></li>
                <li><a href="#export-import"><i class="fas fa-file-export"></i>7. 导出与导入</a></li>
                <li><a href="#plugins-extensions"><i class="fas fa-puzzle-piece"></i>8. 插件与扩展</a></li>
                <li><a href="#shortcuts"><i class="fas fa-keyboard"></i>9. 快捷键大全</a></li>
                <li><a href="#tips-tricks"><i class="fas fa-lightbulb"></i>10. 技巧与窍门</a></li>
                <li><a href="#troubleshooting"><i class="fas fa-bug"></i>11. 故障排查</a></li>
                <li><a href="#summary"><i class="fas fa-flag-checkered"></i>12. 总结</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-edit"></i> Typora详细使用教程-完整美化版</h1>

                <div class="info-box">
                    <strong><i class="fas fa-info-circle"></i>
                        教程说明：</strong>本教程详细介绍了Typora这款优秀的Markdown编辑器的各项功能和使用技巧，从基础安装到高级自定义，从语法入门到专业技巧，帮助您快速掌握这款强大的写作工具。无论您是Markdown新手还是有经验的用户，都能从中获得有价值的内容。
                </div>

                <div class="warning-box">
                    <strong><i class="fas fa-exclamation-triangle"></i> 软件版本说明：</strong>
                    <div style="margin-top: 15px;">
                        <span class="feature-tag feature-basic"><i class="fas fa-star"></i> 免费版本</span> -
                        基础功能完整，适合个人使用<br>
                        <span class="feature-tag feature-advanced"><i class="fas fa-crown"></i> 付费版本</span> -
                        高级功能解锁，支持更多导出格式<br>
                        <span class="feature-tag feature-pro"><i class="fas fa-rocket"></i> 专业版</span> -
                        完整功能，商业使用授权<br>
                    </div>
                    <div style="margin-top: 15px; padding: 10px; background: rgba(255, 193, 7, 0.1); border-left: 4px solid #ffc107;">
                        <strong><i class="fas fa-info-circle"></i> 版本特别说明：</strong><br>
                        本教程基于Typora 1.7.6版本编写，涵盖所有主要功能和最新特性。
                    </div>
                </div>

                <!-- 概述部分 -->
                <section id="overview">
                    <h2><span class="step-number">1</span>概述</h2>

                    <h3><i class="fas fa-bookmark"></i> 1.1 什么是Typora</h3>
                    <p>Typora是一款简洁而强大的Markdown编辑器，它提供了所见即所得(WYSIWYG)的编辑体验。与传统的Markdown编辑器不同，Typora将编辑和预览合二为一，让您在编写时就能看到最终的渲染效果。</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Typora的核心优势：</strong>
                        <ul style="list-style-type: none; padding-left: 20px; margin-top: 10px;">
                            <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 实时预览，所见即所得的编辑体验</li>
                            <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 简洁优雅的界面设计，专注于写作</li>
                            <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 丰富的导出格式支持(PDF、HTML、Word等)</li>
                            <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 强大的数学公式和图表支持</li>
                            <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 多种主题和自定义选项</li>
                            <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 跨平台支持(Windows、macOS、Linux)</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-sitemap"></i> 1.2 Typora的主要特性</h3>
                    <p>Typora集成了众多实用功能，使其成为Markdown编辑的首选工具：</p>
                    <table>
                        <tr>
                            <th><i class="fas fa-layer-group"></i> 功能类别</th>
                            <th><i class="fas fa-box"></i> 具体功能</th>
                            <th><i class="fas fa-info-circle"></i> 功能描述</th>
                        </tr>
                        <tr>
                            <td rowspan="4">编辑功能</td>
                            <td>实时预览</td>
                            <td>边写边看，无需切换预览模式</td>
                        </tr>
                        <tr>
                            <td>语法高亮</td>
                            <td>支持多种编程语言的代码高亮</td>
                        </tr>
                        <tr>
                            <td>自动补全</td>
                            <td>智能提示Markdown语法和文件路径</td>
                        </tr>
                        <tr>
                            <td>拖拽插入</td>
                            <td>支持拖拽插入图片、文件等</td>
                        </tr>
                        <tr>
                            <td rowspan="3">扩展功能</td>
                            <td>数学公式</td>
                            <td>支持LaTeX数学公式渲染</td>
                        </tr>
                        <tr>
                            <td>图表绘制</td>
                            <td>支持Mermaid、流程图等图表</td>
                        </tr>
                        <tr>
                            <td>表格编辑</td>
                            <td>可视化表格编辑和格式化</td>
                        </tr>
                        <tr>
                            <td rowspan="2">导出功能</td>
                            <td>多格式导出</td>
                            <td>PDF、HTML、Word、图片等格式</td>
                        </tr>
                        <tr>
                            <td>自定义样式</td>
                            <td>支持CSS自定义导出样式</td>
                        </tr>
                    </table>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 适用场景：</strong>Typora适合技术文档编写、学术论文撰写、博客文章创作、项目README编写、个人笔记整理等多种场景，是程序员、研究人员、作家和学生的理想选择。
                    </div>

                    <h3><i class="fas fa-tasks"></i> 1.3 学习路径规划</h3>
                    <p>本教程按照从基础到高级的顺序组织，建议按以下路径学习：</p>
                    <table>
                        <tr>
                            <th><i class="fas fa-graduation-cap"></i> 学习阶段</th>
                            <th><i class="fas fa-clock"></i> 预计时间</th>
                            <th><i class="fas fa-list-ul"></i> 学习内容</th>
                            <th><i class="fas fa-target"></i> 学习目标</th>
                        </tr>
                        <tr>
                            <td>入门阶段</td>
                            <td>1-2小时</td>
                            <td>安装配置、基础使用、基本语法</td>
                            <td>能够创建和编辑简单的Markdown文档</td>
                        </tr>
                        <tr>
                            <td>进阶阶段</td>
                            <td>2-3小时</td>
                            <td>高级功能、主题定制、导出设置</td>
                            <td>掌握高级编辑技巧和个性化配置</td>
                        </tr>
                        <tr>
                            <td>专家阶段</td>
                            <td>3-4小时</td>
                            <td>插件扩展、自定义CSS、工作流优化</td>
                            <td>能够高效使用Typora进行专业写作</td>
                        </tr>
                    </table>
                </section>

                <!-- 安装与配置部分 -->
                <section id="installation">
                    <h2><span class="step-number">2</span>安装与配置</h2>

                    <h3><i class="fas fa-download"></i> 2.1 下载与安装</h3>
                    <p>Typora支持Windows、macOS和Linux三大平台，您可以从官方网站下载适合您系统的版本。</p>

                    <h4><i class="fas fa-windows"></i> Windows系统安装</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 系统要求：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• <strong>操作系统：</strong> Windows 7/8/10/11 (64位)</li>
                            <li>• <strong>内存：</strong> 至少4GB RAM</li>
                            <li>• <strong>存储：</strong> 至少200MB可用空间</li>
                            <li>• <strong>其他：</strong> 需要.NET Framework 4.6.1或更高版本</li>
                        </ul>
                    </div>

                    <p><strong>安装步骤：</strong></p>
                    <ol>
                        <li>访问Typora官方网站：<code>https://typora.io/</code></li>
                        <li>点击"Download"按钮，选择Windows版本</li>
                        <li>下载完成后，双击安装包开始安装</li>
                        <li>按照安装向导提示完成安装过程</li>
                        <li>安装完成后，从开始菜单或桌面快捷方式启动Typora</li>
                    </ol>

                    <h4><i class="fas fa-apple-alt"></i> macOS系统安装</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 系统要求：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• <strong>操作系统：</strong> macOS 10.13或更高版本</li>
                            <li>• <strong>架构：</strong> 支持Intel和Apple Silicon(M1/M2)芯片</li>
                            <li>• <strong>内存：</strong> 至少4GB RAM</li>
                            <li>• <strong>存储：</strong> 至少200MB可用空间</li>
                        </ul>
                    </div>

                    <p><strong>安装步骤：</strong></p>
                    <ol>
                        <li>从官网下载macOS版本的.dmg文件</li>
                        <li>双击.dmg文件挂载磁盘映像</li>
                        <li>将Typora应用拖拽到Applications文件夹</li>
                        <li>从Launchpad或Applications文件夹启动Typora</li>
                        <li>首次启动时可能需要在系统偏好设置中允许运行</li>
                    </ol>

                    <h4><i class="fas fa-linux"></i> Linux系统安装</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 支持的发行版：</strong>
                        Ubuntu、Debian、CentOS、Fedora、openSUSE、Arch Linux等主流发行版
                    </div>

                    <p><strong>Ubuntu/Debian安装：</strong></p>
                    <pre><code># 添加Typora的GPG密钥
wget -qO - https://typora.io/linux/public-key.asc | sudo apt-key add -

# 添加Typora仓库
echo 'deb https://typora.io/linux ./' | sudo tee /etc/apt/sources.list.d/typora.list

# 更新包列表并安装
sudo apt update
sudo apt install typora</code></pre>

                    <p><strong>CentOS/Fedora安装：</strong></p>
                    <pre><code># 下载RPM包
wget https://typora.io/linux/typora-linux-x64.tar.gz

# 解压并安装
tar -xzf typora-linux-x64.tar.gz
sudo mv Typora-linux-x64 /opt/typora
sudo ln -s /opt/typora/Typora /usr/local/bin/typora</code></pre>

                    <h3><i class="fas fa-cog"></i> 2.2 首次配置</h3>
                    <p>安装完成后，首次启动Typora时需要进行一些基本配置以获得最佳使用体验。</p>

                    <h4><i class="fas fa-language"></i> 语言设置</h4>
                    <p>Typora支持多种语言界面，包括中文、英文、日文等：</p>
                    <ol>
                        <li>打开Typora，点击菜单栏的"File" → "Preferences"</li>
                        <li>在"General"选项卡中找到"Language"设置</li>
                        <li>选择您偏好的语言（如"简体中文"）</li>
                        <li>重启Typora使设置生效</li>
                    </ol>

                    <h4><i class="fas fa-palette"></i> 主题选择</h4>
                    <p>Typora内置了多种精美主题，您可以根据个人喜好选择：</p>
                    <div class="success-box">
                        <strong><i class="fas fa-paint-brush"></i> 内置主题介绍：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• <strong>GitHub：</strong> 经典的GitHub风格，简洁明了</li>
                            <li>• <strong>Newsprint：</strong> 报纸风格，适合长文阅读</li>
                            <li>• <strong>Night：</strong> 深色主题，保护眼睛</li>
                            <li>• <strong>Pixyll：</strong> 现代简约风格</li>
                            <li>• <strong>Whitey：</strong> 纯白背景，专注写作</li>
                        </ul>
                    </div>

                    <p><strong>主题切换方法：</strong></p>
                    <ol>
                        <li>点击菜单栏"Themes"</li>
                        <li>从下拉列表中选择喜欢的主题</li>
                        <li>主题会立即应用，无需重启</li>
                    </ol>

                    <h4><i class="fas fa-folder-open"></i> 文件管理设置</h4>
                    <p>配置文件管理选项以提高工作效率：</p>
                    <pre><code>文件 → 偏好设置 → 通用
├── 启动选项
│   ├── 重新打开上次的文件和文件夹
│   ├── 启动时创建新文档
│   └── 启动时显示文件树
├── 保存设置
│   ├── 自动保存
│   ├── 保存时自动格式化
│   └── 备份设置
└── 默认文件夹
    ├── 图片文件夹
    ├── 文档文件夹
    └── 导出文件夹</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要配置建议：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 建议开启"自动保存"功能，避免意外丢失内容</li>
                            <li>• 设置合适的图片存储路径，便于文档管理</li>
                            <li>• 配置备份选项，确保重要文档安全</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-key"></i> 2.3 许可证激活</h3>
                    <p>Typora从1.0版本开始采用付费模式，需要购买许可证才能使用完整功能。</p>

                    <h4><i class="fas fa-shopping-cart"></i> 购买许可证</h4>
                    <p>您可以通过以下方式购买Typora许可证：</p>
                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 许可证类型</th>
                            <th><i class="fas fa-dollar-sign"></i> 价格</th>
                            <th><i class="fas fa-users"></i> 适用范围</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td>个人许可证</td>
                            <td>$14.99</td>
                            <td>个人使用</td>
                            <td>支持3台设备，终身使用</td>
                        </tr>
                        <tr>
                            <td>教育许可证</td>
                            <td>$9.99</td>
                            <td>学生/教师</td>
                            <td>需要提供教育邮箱验证</td>
                        </tr>
                        <tr>
                            <td>商业许可证</td>
                            <td>$24.99</td>
                            <td>商业使用</td>
                            <td>支持5台设备，包含技术支持</td>
                        </tr>
                    </table>

                    <h4><i class="fas fa-unlock"></i> 激活步骤</h4>
                    <ol>
                        <li>购买许可证后，您会收到包含激活码的邮件</li>
                        <li>打开Typora，点击"Help" → "License"</li>
                        <li>输入您的邮箱地址和激活码</li>
                        <li>点击"Activate"完成激活</li>
                        <li>激活成功后，重启Typora即可使用完整功能</li>
                    </ol>

                    <div class="info-box">
                        <strong><i class="fas fa-gift"></i> 免费试用：</strong>
                        Typora提供15天免费试用期，您可以在试用期内体验所有功能。试用期结束后，如果不购买许可证，部分高级功能将被限制。
                    </div>
                </section>

                <!-- 基础使用部分 -->
                <section id="basic-usage">
                    <h2><span class="step-number">3</span>基础使用</h2>

                    <h3><i class="fas fa-play"></i> 3.1 界面介绍</h3>
                    <p>Typora采用简洁的界面设计，主要由以下几个部分组成：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-window-maximize"></i> 界面区域</th>
                            <th><i class="fas fa-info-circle"></i> 功能说明</th>
                            <th><i class="fas fa-keyboard"></i> 快捷操作</th>
                        </tr>
                        <tr>
                            <td>标题栏</td>
                            <td>显示当前文档名称和保存状态</td>
                            <td>双击可最大化/还原窗口</td>
                        </tr>
                        <tr>
                            <td>菜单栏</td>
                            <td>包含所有功能菜单和设置选项</td>
                            <td>Alt键可快速访问菜单</td>
                        </tr>
                        <tr>
                            <td>编辑区域</td>
                            <td>主要的文档编辑和预览区域</td>
                            <td>支持拖拽、右键菜单等操作</td>
                        </tr>
                        <tr>
                            <td>状态栏</td>
                            <td>显示字数统计、行列信息等</td>
                            <td>点击可切换显示模式</td>
                        </tr>
                        <tr>
                            <td>侧边栏</td>
                            <td>文件树、大纲视图等（可选显示）</td>
                            <td><span class="shortcut-key">Ctrl+Shift+L</span> 切换文件树</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-file-alt"></i> 3.2 创建和打开文档</h3>
                    <p>Typora支持多种方式创建和打开Markdown文档：</p>

                    <h4><i class="fas fa-plus"></i> 创建新文档</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-rocket"></i> 创建文档的多种方式：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• <strong>快捷键：</strong> <span class="shortcut-key">Ctrl+N</span> (Windows/Linux) 或 <span class="shortcut-key">Cmd+N</span> (macOS)</li>
                            <li>• <strong>菜单：</strong> File → New</li>
                            <li>• <strong>拖拽：</strong> 将.md文件拖拽到Typora窗口</li>
                            <li>• <strong>右键：</strong> 在文件夹中右键选择"用Typora打开"</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-folder-open"></i> 打开现有文档</h4>
                    <p>打开已存在的Markdown文档：</p>
                    <ol>
                        <li><strong>通过菜单：</strong> File → Open，然后选择要打开的.md文件</li>
                        <li><strong>使用快捷键：</strong> <span class="shortcut-key">Ctrl+O</span> 快速打开文件对话框</li>
                        <li><strong>拖拽操作：</strong> 直接将文件拖拽到Typora窗口中</li>
                        <li><strong>最近文件：</strong> File → Recent Files 查看最近编辑的文档</li>
                    </ol>

                    <h4><i class="fas fa-save"></i> 保存文档</h4>
                    <p>Typora提供多种保存选项：</p>
                    <pre><code>保存选项：
├── 保存 (Ctrl+S)
│   └── 保存当前文档到指定位置
├── 另存为 (Ctrl+Shift+S)
│   └── 以新名称或新位置保存文档
├── 自动保存
│   ├── 实时保存编辑内容
│   └── 在偏好设置中启用
└── 导出保存
    ├── 保存为PDF、HTML等格式
    └── 通过File → Export菜单</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 保存注意事项：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 建议开启自动保存功能，避免意外丢失内容</li>
                            <li>• 首次保存时选择合适的文件名和位置</li>
                            <li>• 定期备份重要文档到云存储或其他位置</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-edit"></i> 3.3 基本编辑操作</h3>
                    <p>Typora的编辑操作直观简单，支持所见即所得的编辑体验：</p>

                    <h4><i class="fas fa-text-height"></i> 文本编辑</h4>
                    <p>基本的文本编辑操作：</p>
                    <table>
                        <tr>
                            <th><i class="fas fa-edit"></i> 操作</th>
                            <th><i class="fas fa-keyboard"></i> 快捷键</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td>撤销</td>
                            <td><span class="shortcut-key">Ctrl+Z</span></td>
                            <td>撤销上一步操作</td>
                        </tr>
                        <tr>
                            <td>重做</td>
                            <td><span class="shortcut-key">Ctrl+Y</span></td>
                            <td>重做被撤销的操作</td>
                        </tr>
                        <tr>
                            <td>复制</td>
                            <td><span class="shortcut-key">Ctrl+C</span></td>
                            <td>复制选中的内容</td>
                        </tr>
                        <tr>
                            <td>粘贴</td>
                            <td><span class="shortcut-key">Ctrl+V</span></td>
                            <td>粘贴剪贴板内容</td>
                        </tr>
                        <tr>
                            <td>剪切</td>
                            <td><span class="shortcut-key">Ctrl+X</span></td>
                            <td>剪切选中的内容</td>
                        </tr>
                        <tr>
                            <td>全选</td>
                            <td><span class="shortcut-key">Ctrl+A</span></td>
                            <td>选择全部内容</td>
                        </tr>
                        <tr>
                            <td>查找</td>
                            <td><span class="shortcut-key">Ctrl+F</span></td>
                            <td>在文档中查找文本</td>
                        </tr>
                        <tr>
                            <td>替换</td>
                            <td><span class="shortcut-key">Ctrl+H</span></td>
                            <td>查找并替换文本</td>
                        </tr>
                    </table>
                </section>

                <!-- Markdown语法部分 -->
                <section id="markdown-syntax">
                    <h2><span class="step-number">4</span>Markdown语法</h2>

                    <h3><i class="fas fa-code"></i> 4.1 标题语法</h3>
                    <p>Markdown使用#号来表示标题，支持1-6级标题：</p>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 标题语法示例：</strong>
                        <pre><code># 一级标题
## 二级标题
### 三级标题
#### 四级标题
##### 五级标题
###### 六级标题</code></pre>
                    </div>

                    <div class="success-box">
                        <strong><i class="fas fa-lightbulb"></i> 标题使用技巧：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 在Typora中，输入#后按空格会自动转换为标题格式</li>
                            <li>• 使用<span class="shortcut-key">Ctrl+1-6</span>快速设置对应级别标题</li>
                            <li>• 标题会自动生成目录大纲，便于文档导航</li>
                            <li>• 建议保持标题层级的逻辑性，不要跳级使用</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-bold"></i> 4.2 文本格式</h3>
                    <p>Markdown支持多种文本格式化选项：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-font"></i> 格式类型</th>
                            <th><i class="fas fa-code"></i> Markdown语法</th>
                            <th><i class="fas fa-keyboard"></i> 快捷键</th>
                            <th><i class="fas fa-eye"></i> 显示效果</th>
                        </tr>
                        <tr>
                            <td>粗体</td>
                            <td><code>**粗体文本**</code></td>
                            <td><span class="shortcut-key">Ctrl+B</span></td>
                            <td><strong>粗体文本</strong></td>
                        </tr>
                        <tr>
                            <td>斜体</td>
                            <td><code>*斜体文本*</code></td>
                            <td><span class="shortcut-key">Ctrl+I</span></td>
                            <td><em>斜体文本</em></td>
                        </tr>
                        <tr>
                            <td>删除线</td>
                            <td><code>~~删除文本~~</code></td>
                            <td><span class="shortcut-key">Alt+Shift+5</span></td>
                            <td><del>删除文本</del></td>
                        </tr>
                        <tr>
                            <td>下划线</td>
                            <td><code>&lt;u&gt;下划线&lt;/u&gt;</code></td>
                            <td><span class="shortcut-key">Ctrl+U</span></td>
                            <td><u>下划线</u></td>
                        </tr>
                        <tr>
                            <td>高亮</td>
                            <td><code>==高亮文本==</code></td>
                            <td>无</td>
                            <td><mark>高亮文本</mark></td>
                        </tr>
                        <tr>
                            <td>行内代码</td>
                            <td><code>`代码`</code></td>
                            <td><span class="shortcut-key">Ctrl+Shift+`</span></td>
                            <td><code>代码</code></td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-list"></i> 4.3 列表语法</h3>
                    <p>Markdown支持有序列表和无序列表：</p>

                    <h4><i class="fas fa-list-ul"></i> 无序列表</h4>
                    <p>使用-、*或+创建无序列表：</p>
                    <pre><code>- 列表项1
- 列表项2
  - 子列表项1
  - 子列表项2
- 列表项3</code></pre>

                    <h4><i class="fas fa-list-ol"></i> 有序列表</h4>
                    <p>使用数字加点创建有序列表：</p>
                    <pre><code>1. 第一项
2. 第二项
   1. 子项目1
   2. 子项目2
3. 第三项</code></pre>

                    <h4><i class="fas fa-check-square"></i> 任务列表</h4>
                    <p>创建可勾选的任务列表：</p>
                    <pre><code>- [ ] 未完成任务
- [x] 已完成任务
- [ ] 另一个未完成任务</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-magic"></i> 列表编辑技巧：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 在列表项末尾按<span class="shortcut-key">Enter</span>自动创建新列表项</li>
                            <li>• 使用<span class="shortcut-key">Tab</span>增加缩进，<span class="shortcut-key">Shift+Tab</span>减少缩进</li>
                            <li>• 在任务列表中点击复选框可直接切换完成状态</li>
                            <li>• 空列表项按<span class="shortcut-key">Enter</span>两次退出列表模式</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-link"></i> 4.4 链接和图片</h3>
                    <p>Markdown支持多种链接和图片插入方式：</p>

                    <h4><i class="fas fa-external-link-alt"></i> 链接语法</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-link"></i> 链接类型</th>
                            <th><i class="fas fa-code"></i> 语法格式</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td>内联链接</td>
                            <td><code>[链接文本](URL)</code></td>
                            <td>最常用的链接格式</td>
                        </tr>
                        <tr>
                            <td>引用链接</td>
                            <td><code>[链接文本][标识]</code></td>
                            <td>需要在文档末尾定义标识</td>
                        </tr>
                        <tr>
                            <td>自动链接</td>
                            <td><code>&lt;URL&gt;</code></td>
                            <td>直接显示URL地址</td>
                        </tr>
                        <tr>
                            <td>邮箱链接</td>
                            <td><code>&lt;<EMAIL>&gt;</code></td>
                            <td>自动创建邮箱链接</td>
                        </tr>
                    </table>

                    <h4><i class="fas fa-image"></i> 图片语法</h4>
                    <p>插入图片的多种方式：</p>
                    <pre><code># 基本图片语法
![替代文本](图片路径)

# 带标题的图片
![替代文本](图片路径 "图片标题")

# 引用式图片
![替代文本][图片标识]

# HTML方式（支持更多属性）
&lt;img src="图片路径" alt="替代文本" width="300"&gt;</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-camera"></i> 图片插入技巧：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 直接拖拽图片到编辑器会自动插入图片语法</li>
                            <li>• 使用<span class="shortcut-key">Ctrl+Shift+I</span>快速插入图片</li>
                            <li>• 支持网络图片URL和本地图片路径</li>
                            <li>• 可以在偏好设置中配置图片存储位置</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-table"></i> 4.5 表格语法</h3>
                    <p>Markdown表格使用管道符|分隔列：</p>

                    <h4><i class="fas fa-th"></i> 基本表格</h4>
                    <pre><code>| 表头1 | 表头2 | 表头3 |
|-------|-------|-------|
| 内容1 | 内容2 | 内容3 |
| 内容4 | 内容5 | 内容6 |</code></pre>

                    <h4><i class="fas fa-align-left"></i> 对齐方式</h4>
                    <pre><code>| 左对齐 | 居中对齐 | 右对齐 |
|:-------|:-------:|-------:|
| 内容   |   内容   |   内容 |</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-table"></i> 表格编辑技巧：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 在Typora中可以直接点击表格进行可视化编辑</li>
                            <li>• 使用<span class="shortcut-key">Ctrl+T</span>快速插入表格</li>
                            <li>• 右键表格可以插入/删除行列</li>
                            <li>• 支持在表格中使用其他Markdown语法</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-code"></i> 4.6 代码块</h3>
                    <p>Markdown支持行内代码和代码块：</p>

                    <h4><i class="fas fa-terminal"></i> 代码块语法</h4>
                    <pre><code>```语言名称
代码内容
```</code></pre>

                    <h4><i class="fas fa-paint-brush"></i> 语法高亮</h4>
                    <p>Typora支持多种编程语言的语法高亮：</p>
                    <table>
                        <tr>
                            <th><i class="fas fa-code"></i> 语言</th>
                            <th><i class="fas fa-tag"></i> 标识符</th>
                            <th><i class="fas fa-code"></i> 语言</th>
                            <th><i class="fas fa-tag"></i> 标识符</th>
                        </tr>
                        <tr>
                            <td>JavaScript</td>
                            <td>javascript, js</td>
                            <td>Python</td>
                            <td>python, py</td>
                        </tr>
                        <tr>
                            <td>Java</td>
                            <td>java</td>
                            <td>C++</td>
                            <td>cpp, c++</td>
                        </tr>
                        <tr>
                            <td>HTML</td>
                            <td>html</td>
                            <td>CSS</td>
                            <td>css</td>
                        </tr>
                        <tr>
                            <td>SQL</td>
                            <td>sql</td>
                            <td>Bash</td>
                            <td>bash, shell</td>
                        </tr>
                    </table>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 代码块注意事项：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 代码块前后需要空行分隔</li>
                            <li>• 语言标识符不区分大小写</li>
                            <li>• 可以使用<span class="shortcut-key">Ctrl+Shift+K</span>快速插入代码块</li>
                            <li>• 在代码块中按<span class="shortcut-key">Shift+Enter</span>换行</li>
                        </ul>
                    </div>
                </section>

                <!-- 高级功能部分 -->
                <section id="advanced-features">
                    <h2><span class="step-number">5</span>高级功能</h2>

                    <h3><i class="fas fa-magic"></i> 5.1 数学公式</h3>
                    <p>Typora支持LaTeX数学公式渲染，让您可以在文档中插入复杂的数学表达式：</p>

                    <h4><i class="fas fa-calculator"></i> 行内公式</h4>
                    <p>使用单个$符号包围公式：</p>
                    <pre><code>这是一个行内公式：$E = mc^2$</code></pre>

                    <h4><i class="fas fa-square-root-alt"></i> 块级公式</h4>
                    <p>使用双$符号或```math代码块：</p>
                    <pre><code>$$
\frac{-b \pm \sqrt{b^2 - 4ac}}{2a}
$$

或者使用：

```math
\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}
```</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-graduation-cap"></i> 常用数学符号：</strong>
                        <table style="margin-top: 10px;">
                            <tr>
                                <th>符号类型</th>
                                <th>LaTeX代码</th>
                                <th>显示效果</th>
                            </tr>
                            <tr>
                                <td>分数</td>
                                <td>\frac{a}{b}</td>
                                <td>a/b</td>
                            </tr>
                            <tr>
                                <td>根号</td>
                                <td>\sqrt{x}</td>
                                <td>√x</td>
                            </tr>
                            <tr>
                                <td>求和</td>
                                <td>\sum_{i=1}^{n}</td>
                                <td>Σ</td>
                            </tr>
                            <tr>
                                <td>积分</td>
                                <td>\int_{a}^{b}</td>
                                <td>∫</td>
                            </tr>
                        </table>
                    </div>

                    <h3><i class="fas fa-project-diagram"></i> 5.2 图表绘制</h3>
                    <p>Typora支持多种图表类型，使用Mermaid语法绘制：</p>

                    <h4><i class="fas fa-sitemap"></i> 流程图</h4>
                    <pre><code>```mermaid
graph TD
    A[开始] --> B{判断条件}
    B -->|是| C[执行操作1]
    B -->|否| D[执行操作2]
    C --> E[结束]
    D --> E
```</code></pre>

                    <h4><i class="fas fa-chart-line"></i> 序列图</h4>
                    <pre><code>```mermaid
sequenceDiagram
    participant A as 用户
    participant B as 系统
    A->>B: 发送请求
    B-->>A: 返回响应
    A->>B: 确认收到
```</code></pre>

                    <h4><i class="fas fa-chart-pie"></i> 甘特图</h4>
                    <pre><code>```mermaid
gantt
    title 项目进度
    dateFormat  YYYY-MM-DD
    section 设计阶段
    需求分析    :done, des1, 2024-01-01, 2024-01-05
    原型设计    :active, des2, 2024-01-06, 2024-01-10
    section 开发阶段
    前端开发    :des3, after des2, 5d
    后端开发    :des4, after des2, 7d
```</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-chart-bar"></i> 支持的图表类型：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• <strong>流程图：</strong> graph TD/LR/BT/RL</li>
                            <li>• <strong>序列图：</strong> sequenceDiagram</li>
                            <li>• <strong>甘特图：</strong> gantt</li>
                            <li>• <strong>类图：</strong> classDiagram</li>
                            <li>• <strong>状态图：</strong> stateDiagram</li>
                            <li>• <strong>饼图：</strong> pie</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-quote-left"></i> 5.3 引用和注释</h3>
                    <p>Typora支持多种引用和注释格式：</p>

                    <h4><i class="fas fa-quote-right"></i> 块引用</h4>
                    <pre><code>> 这是一个引用
> 可以包含多行内容
>
> > 这是嵌套引用</code></pre>

                    <h4><i class="fas fa-sticky-note"></i> 脚注</h4>
                    <pre><code>这里有一个脚注[^1]

[^1]: 这是脚注的内容</code></pre>

                    <h4><i class="fas fa-comment"></i> HTML注释</h4>
                    <pre><code>&lt;!-- 这是HTML注释，在预览中不会显示 --&gt;</code></pre>

                    <h3><i class="fas fa-palette"></i> 5.4 自定义样式</h3>
                    <p>Typora允许使用HTML和CSS来自定义样式：</p>

                    <h4><i class="fas fa-paint-brush"></i> 内联HTML</h4>
                    <pre><code>&lt;div style="color: red; font-size: 18px;"&gt;
    自定义样式的文本
&lt;/div&gt;

&lt;span style="background-color: yellow;"&gt;高亮文本&lt;/span&gt;</code></pre>

                    <h4><i class="fas fa-code"></i> CSS样式块</h4>
                    <pre><code>&lt;style&gt;
.custom-box {
    border: 2px solid #007acc;
    padding: 10px;
    border-radius: 5px;
    background-color: #f0f8ff;
}
&lt;/style&gt;

&lt;div class="custom-box"&gt;
    这是自定义样式的内容框
&lt;/div&gt;</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 样式使用注意：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• HTML和CSS样式在导出时可能不被所有格式支持</li>
                            <li>• 建议优先使用Markdown原生语法</li>
                            <li>• 复杂样式可以通过自定义主题实现</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-file-alt"></i> 5.5 文档大纲</h3>
                    <p>Typora自动生成文档大纲，帮助您快速导航：</p>

                    <h4><i class="fas fa-list"></i> 大纲视图</h4>
                    <p>启用大纲视图的方法：</p>
                    <ol>
                        <li>点击菜单栏"View" → "Outline"</li>
                        <li>或使用快捷键<span class="shortcut-key">Ctrl+Shift+1</span></li>
                        <li>大纲面板会显示在侧边栏中</li>
                        <li>点击大纲项目可快速跳转到对应位置</li>
                    </ol>

                    <h4><i class="fas fa-anchor"></i> 标题锚点</h4>
                    <p>Typora自动为标题生成锚点，支持内部链接：</p>
                    <pre><code># 章节标题 {#custom-id}

[跳转到章节](#custom-id)</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-navigation"></i> 导航技巧：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 使用<span class="shortcut-key">Ctrl+Home</span>快速跳转到文档开头</li>
                            <li>• 使用<span class="shortcut-key">Ctrl+End</span>快速跳转到文档结尾</li>
                            <li>• 大纲视图支持拖拽调整标题顺序</li>
                            <li>• 可以折叠/展开大纲中的章节</li>
                        </ul>
                    </div>
                </section>

                <!-- 主题与自定义部分 -->
                <section id="themes-customization">
                    <h2><span class="step-number">6</span>主题与自定义</h2>

                    <h3><i class="fas fa-palette"></i> 6.1 内置主题</h3>
                    <p>Typora提供了多种精美的内置主题，满足不同的使用场景和个人喜好：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-paint-brush"></i> 主题名称</th>
                            <th><i class="fas fa-eye"></i> 视觉特点</th>
                            <th><i class="fas fa-users"></i> 适用场景</th>
                            <th><i class="fas fa-star"></i> 推荐指数</th>
                        </tr>
                        <tr>
                            <td>GitHub</td>
                            <td>简洁白色背景，经典GitHub风格</td>
                            <td>技术文档、代码说明</td>
                            <td>⭐⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td>Newsprint</td>
                            <td>报纸风格，衬线字体</td>
                            <td>长文阅读、学术写作</td>
                            <td>⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td>Night</td>
                            <td>深色背景，护眼设计</td>
                            <td>夜间写作、长时间使用</td>
                            <td>⭐⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td>Pixyll</td>
                            <td>现代简约，清爽布局</td>
                            <td>博客写作、个人笔记</td>
                            <td>⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td>Whitey</td>
                            <td>纯白背景，极简设计</td>
                            <td>专注写作、演示文档</td>
                            <td>⭐⭐⭐</td>
                        </tr>
                    </table>

                    <h4><i class="fas fa-exchange-alt"></i> 主题切换</h4>
                    <p>切换主题的方法：</p>
                    <ol>
                        <li>点击菜单栏"Themes"</li>
                        <li>从下拉列表中选择喜欢的主题</li>
                        <li>主题会立即应用，实时预览效果</li>
                        <li>也可以使用快捷键<span class="shortcut-key">Ctrl+Shift+T</span>快速切换</li>
                    </ol>

                    <h3><i class="fas fa-download"></i> 6.2 第三方主题</h3>
                    <p>除了内置主题，您还可以安装社区开发的第三方主题：</p>

                    <h4><i class="fas fa-globe"></i> 热门第三方主题</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-fire"></i> 推荐主题：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• <strong>Vue：</strong> 清新绿色主题，适合前端开发者</li>
                            <li>• <strong>Pie：</strong> 温暖色调，适合日常写作</li>
                            <li>• <strong>Purple：</strong> 紫色主题，个性化选择</li>
                            <li>• <strong>Ursine：</strong> 深色主题变体，更多自定义选项</li>
                            <li>• <strong>Academic：</strong> 学术风格，适合论文写作</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-cog"></i> 主题安装</h4>
                    <p>安装第三方主题的步骤：</p>
                    <ol>
                        <li>从GitHub或主题网站下载主题文件(.css)</li>
                        <li>打开Typora，点击"File" → "Preferences"</li>
                        <li>点击"Appearance"选项卡</li>
                        <li>点击"Open Theme Folder"打开主题文件夹</li>
                        <li>将下载的.css文件复制到主题文件夹</li>
                        <li>重启Typora，新主题会出现在主题列表中</li>
                    </ol>

                    <h3><i class="fas fa-code"></i> 6.3 自定义CSS</h3>
                    <p>对于有CSS基础的用户，可以创建完全自定义的主题：</p>

                    <h4><i class="fas fa-file-code"></i> 创建自定义主题</h4>
                    <pre><code>/* 自定义主题示例 - mytheme.css */

/* 基础样式 */
:root {
    --bg-color: #f8f9fa;
    --text-color: #333;
    --accent-color: #007acc;
}

/* 正文样式 */
#write {
    font-family: 'Microsoft YaHei', sans-serif;
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
    color: var(--accent-color);
    font-weight: 600;
}

/* 代码块样式 */
.md-fences {
    background-color: #2d3748;
    color: #e2e8f0;
    border-radius: 8px;
}

/* 链接样式 */
a {
    color: var(--accent-color);
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}</code></pre>

                    <h4><i class="fas fa-paint-roller"></i> 常用自定义选项</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-cog"></i> 自定义项</th>
                            <th><i class="fas fa-code"></i> CSS选择器</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td>正文字体</td>
                            <td>#write</td>
                            <td>设置编辑区域的字体和样式</td>
                        </tr>
                        <tr>
                            <td>标题样式</td>
                            <td>h1, h2, h3...</td>
                            <td>自定义各级标题的外观</td>
                        </tr>
                        <tr>
                            <td>代码块</td>
                            <td>.md-fences</td>
                            <td>代码块的背景和字体</td>
                        </tr>
                        <tr>
                            <td>表格样式</td>
                            <td>table, th, td</td>
                            <td>表格的边框和背景</td>
                        </tr>
                        <tr>
                            <td>侧边栏</td>
                            <td>.sidebar-tabs</td>
                            <td>文件树和大纲的样式</td>
                        </tr>
                    </table>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 自定义技巧：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 使用浏览器开发者工具检查元素结构</li>
                            <li>• 参考现有主题的CSS代码学习</li>
                            <li>• 使用CSS变量便于统一管理颜色</li>
                            <li>• 测试不同屏幕尺寸下的显示效果</li>
                        </ul>
                    </div>
                </section>

                <!-- 导出与导入部分 -->
                <section id="export-import">
                    <h2><span class="step-number">7</span>导出与导入</h2>

                    <h3><i class="fas fa-file-export"></i> 7.1 导出功能</h3>
                    <p>Typora支持将Markdown文档导出为多种格式，满足不同的使用需求：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-file"></i> 导出格式</th>
                            <th><i class="fas fa-info-circle"></i> 特点</th>
                            <th><i class="fas fa-users"></i> 适用场景</th>
                            <th><i class="fas fa-cog"></i> 配置选项</th>
                        </tr>
                        <tr>
                            <td>PDF</td>
                            <td>保持格式，便于分享</td>
                            <td>正式文档、报告</td>
                            <td>页面设置、样式选择</td>
                        </tr>
                        <tr>
                            <td>HTML</td>
                            <td>网页格式，可嵌入</td>
                            <td>博客发布、网站内容</td>
                            <td>CSS样式、脚本包含</td>
                        </tr>
                        <tr>
                            <td>Word</td>
                            <td>可编辑文档</td>
                            <td>协作编辑、办公文档</td>
                            <td>样式映射、格式保持</td>
                        </tr>
                        <tr>
                            <td>图片</td>
                            <td>PNG/JPEG格式</td>
                            <td>社交分享、演示</td>
                            <td>分辨率、质量设置</td>
                        </tr>
                        <tr>
                            <td>LaTeX</td>
                            <td>学术格式</td>
                            <td>学术论文、期刊投稿</td>
                            <td>模板选择、包管理</td>
                        </tr>
                    </table>

                    <h4><i class="fas fa-file-pdf"></i> PDF导出</h4>
                    <p>PDF是最常用的导出格式，步骤如下：</p>
                    <ol>
                        <li>点击"File" → "Export" → "PDF"</li>
                        <li>在导出对话框中设置页面选项：
                            <ul>
                                <li>页面大小（A4、Letter等）</li>
                                <li>页边距设置</li>
                                <li>页眉页脚</li>
                                <li>背景图形</li>
                            </ul>
                        </li>
                        <li>选择CSS样式（使用当前主题或自定义）</li>
                        <li>点击"Export"保存PDF文件</li>
                    </ol>

                    <div class="success-box">
                        <strong><i class="fas fa-file-pdf"></i> PDF导出优化：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 选择合适的主题以获得最佳打印效果</li>
                            <li>• 调整页边距避免内容被截断</li>
                            <li>• 使用分页符控制页面布局</li>
                            <li>• 检查数学公式和图表的渲染效果</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-code"></i> HTML导出</h4>
                    <p>HTML导出适合网页发布：</p>
                    <pre><code>导出选项：
├── HTML (without styles)
│   └── 纯HTML，不包含CSS样式
├── HTML (with styles)
│   └── 包含内联CSS样式
└── HTML (with outline)
    └── 包含侧边栏大纲导航</code></pre>

                    <h3><i class="fas fa-file-import"></i> 7.2 导入功能</h3>
                    <p>Typora支持从其他格式导入内容：</p>

                    <h4><i class="fas fa-file-word"></i> Word文档导入</h4>
                    <p>将Word文档转换为Markdown：</p>
                    <ol>
                        <li>点击"File" → "Import"</li>
                        <li>选择Word文档(.docx)</li>
                        <li>Typora会自动转换格式</li>
                        <li>检查并调整转换结果</li>
                    </ol>

                    <h4><i class="fas fa-clipboard"></i> 剪贴板导入</h4>
                    <p>从其他应用复制内容到Typora：</p>
                    <div class="info-box">
                        <strong><i class="fas fa-magic"></i> 智能粘贴：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 从网页复制会自动转换为Markdown格式</li>
                            <li>• 表格数据会自动转换为Markdown表格</li>
                            <li>• 图片会自动保存到指定文件夹</li>
                            <li>• 富文本格式会保留基本样式</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-cog"></i> 7.3 导出设置</h3>
                    <p>在偏好设置中可以配置导出选项：</p>

                    <h4><i class="fas fa-image"></i> 图片处理</h4>
                    <pre><code>图片设置：
├── 复制图片到文档文件夹
├── 使用相对路径
├── 压缩图片质量
└── 设置最大宽度</code></pre>

                    <h4><i class="fas fa-palette"></i> 样式设置</h4>
                    <pre><code>样式选项：
├── 使用当前主题
├── 自定义CSS文件
├── 包含语法高亮
└── 数学公式渲染</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 导出注意事项：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 某些高级功能在导出时可能不被支持</li>
                            <li>• 自定义HTML/CSS在某些格式中会丢失</li>
                            <li>• 大文档导出可能需要较长时间</li>
                            <li>• 建议在导出前预览效果</li>
                        </ul>
                    </div>
                </section>

                <!-- 插件与扩展部分 -->
                <section id="plugins-extensions">
                    <h2><span class="step-number">8</span>插件与扩展</h2>

                    <h3><i class="fas fa-puzzle-piece"></i> 8.1 Typora插件生态</h3>
                    <p>虽然Typora本身功能已经非常强大，但通过插件和扩展可以进一步增强其功能。以下是一些实用的插件和扩展工具：</p>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 插件说明：</strong>
                        Typora目前不支持传统意义上的插件系统，但可以通过外部工具、脚本和集成方案来扩展功能。这些"插件"主要通过以下方式实现：
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 外部工具集成</li>
                            <li>• 自定义CSS和JavaScript</li>
                            <li>• 命令行工具配合</li>
                            <li>• 第三方服务API</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-image"></i> 8.2 图床插件</h3>
                    <p>图床插件可以自动上传图片到云存储，解决图片管理问题：</p>

                    <h4><i class="fas fa-cloud-upload-alt"></i> PicGo</h4>
                    <p>PicGo是最受欢迎的图床工具，支持多种云存储服务：</p>
                    <table>
                        <tr>
                            <th><i class="fas fa-cloud"></i> 支持平台</th>
                            <th><i class="fas fa-cog"></i> 配置难度</th>
                            <th><i class="fas fa-dollar-sign"></i> 费用</th>
                            <th><i class="fas fa-info-circle"></i> 特点</th>
                        </tr>
                        <tr>
                            <td>七牛云</td>
                            <td>简单</td>
                            <td>免费额度+付费</td>
                            <td>国内访问速度快</td>
                        </tr>
                        <tr>
                            <td>腾讯云COS</td>
                            <td>中等</td>
                            <td>按量付费</td>
                            <td>稳定性好，CDN加速</td>
                        </tr>
                        <tr>
                            <td>阿里云OSS</td>
                            <td>中等</td>
                            <td>按量付费</td>
                            <td>功能丰富，企业级</td>
                        </tr>
                        <tr>
                            <td>GitHub</td>
                            <td>简单</td>
                            <td>免费</td>
                            <td>程序员友好，版本控制</td>
                        </tr>
                        <tr>
                            <td>Imgur</td>
                            <td>简单</td>
                            <td>免费</td>
                            <td>国外服务，匿名上传</td>
                        </tr>
                    </table>

                    <h4><i class="fas fa-cog"></i> PicGo配置步骤</h4>
                    <ol>
                        <li><strong>下载安装：</strong> 从GitHub下载PicGo客户端</li>
                        <li><strong>选择图床：</strong> 在设置中选择合适的图床服务</li>
                        <li><strong>配置参数：</strong> 填写API密钥、存储桶等信息</li>
                        <li><strong>设置快捷键：</strong> 配置上传快捷键（如Ctrl+Shift+P）</li>
                        <li><strong>Typora集成：</strong> 在Typora偏好设置中配置图片上传</li>
                    </ol>

                    <div class="success-box">
                        <strong><i class="fas fa-lightbulb"></i> PicGo使用技巧：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 支持拖拽上传和剪贴板上传</li>
                            <li>• 可以批量上传多张图片</li>
                            <li>• 支持图片压缩和水印添加</li>
                            <li>• 提供丰富的插件生态</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-code"></i> 8.3 代码增强插件</h3>
                    <p>增强代码编辑和显示功能的工具：</p>

                    <h4><i class="fas fa-paint-brush"></i> Prism.js语法高亮</h4>
                    <p>为导出的HTML添加更丰富的语法高亮：</p>
                    <pre><code>&lt;!-- 在HTML导出中添加Prism.js --&gt;
&lt;link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css" rel="stylesheet" /&gt;
&lt;script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"&gt;&lt;/script&gt;
&lt;script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"&gt;&lt;/script&gt;</code></pre>

                    <h4><i class="fas fa-copy"></i> 代码块复制插件</h4>
                    <p>为代码块添加一键复制功能：</p>
                    <pre><code>// 自定义JavaScript代码
document.querySelectorAll('pre code').forEach(block =&gt; {
    const button = document.createElement('button');
    button.textContent = '复制';
    button.onclick = () =&gt; {
        navigator.clipboard.writeText(block.textContent);
        button.textContent = '已复制';
        setTimeout(() =&gt; button.textContent = '复制', 2000);
    };
    block.parentElement.appendChild(button);
});</code></pre>

                    <h3><i class="fas fa-chart-bar"></i> 8.4 图表增强插件</h3>
                    <p>扩展Typora的图表绘制能力：</p>

                    <h4><i class="fas fa-project-diagram"></i> PlantUML支持</h4>
                    <p>通过外部工具支持PlantUML图表：</p>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> PlantUML集成方案：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• <strong>在线服务：</strong> 使用PlantUML在线服务器生成图片</li>
                            <li>• <strong>本地安装：</strong> 安装Java和PlantUML.jar文件</li>
                            <li>• <strong>VS Code插件：</strong> 在VS Code中编辑，导出到Typora</li>
                            <li>• <strong>脚本自动化：</strong> 编写脚本自动转换PlantUML代码</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-chart-line"></i> Chart.js集成</h4>
                    <p>在HTML导出中添加交互式图表：</p>
                    <pre><code>&lt;!-- Chart.js示例 --&gt;
&lt;canvas id="myChart" width="400" height="200"&gt;&lt;/canvas&gt;
&lt;script src="https://cdn.jsdelivr.net/npm/chart.js"&gt;&lt;/script&gt;
&lt;script&gt;
const ctx = document.getElementById('myChart').getContext('2d');
const myChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: ['Red', 'Blue', 'Yellow', 'Green', 'Purple', 'Orange'],
        datasets: [{
            label: '# of Votes',
            data: [12, 19, 3, 5, 2, 3],
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    }
});
&lt;/script&gt;</code></pre>

                    <h3><i class="fas fa-share-alt"></i> 8.5 发布与分享插件</h3>
                    <p>将Markdown文档发布到各种平台的工具：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-globe"></i> 平台</th>
                            <th><i class="fas fa-tools"></i> 工具</th>
                            <th><i class="fas fa-cog"></i> 集成方式</th>
                            <th><i class="fas fa-info-circle"></i> 特点</th>
                        </tr>
                        <tr>
                            <td>GitHub Pages</td>
                            <td>Jekyll, Hugo</td>
                            <td>Git推送自动部署</td>
                            <td>免费托管，版本控制</td>
                        </tr>
                        <tr>
                            <td>博客平台</td>
                            <td>Hexo, VuePress</td>
                            <td>静态站点生成</td>
                            <td>主题丰富，SEO友好</td>
                        </tr>
                        <tr>
                            <td>知识库</td>
                            <td>GitBook, Notion</td>
                            <td>API同步</td>
                            <td>团队协作，在线编辑</td>
                        </tr>
                        <tr>
                            <td>社交媒体</td>
                            <td>微信公众号助手</td>
                            <td>格式转换工具</td>
                            <td>样式适配，一键发布</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-magic"></i> 8.6 自动化工具</h3>
                    <p>提高工作效率的自动化工具：</p>

                    <h4><i class="fas fa-robot"></i> Typora CLI工具</h4>
                    <p>命令行工具扩展Typora功能：</p>
                    <pre><code># 批量转换工具
typora-export --input ./docs --output ./exports --format pdf

# 自动格式化工具
typora-format --input ./draft.md --output ./formatted.md

# 图片优化工具
typora-image-optimizer --input ./docs --compress 80</code></pre>

                    <h4><i class="fas fa-sync-alt"></i> 文档同步工具</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-cloud"></i> 推荐同步方案：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• <strong>Git + GitHub：</strong> 版本控制 + 云端备份</li>
                            <li>• <strong>Dropbox + Typora：</strong> 实时同步 + 多设备访问</li>
                            <li>• <strong>OneDrive + Office：</strong> 微软生态集成</li>
                            <li>• <strong>iCloud + macOS：</strong> 苹果设备无缝同步</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-download"></i> 8.7 插件安装指南</h3>
                    <p>安装和配置各种扩展工具的通用步骤：</p>

                    <h4><i class="fas fa-step-forward"></i> 通用安装流程</h4>
                    <ol>
                        <li><strong>需求分析：</strong> 确定需要什么功能扩展</li>
                        <li><strong>工具选择：</strong> 研究和比较可用的工具</li>
                        <li><strong>下载安装：</strong> 从官方渠道下载安装包</li>
                        <li><strong>配置设置：</strong> 根据文档配置相关参数</li>
                        <li><strong>集成测试：</strong> 测试与Typora的集成效果</li>
                        <li><strong>工作流优化：</strong> 调整工作流程以充分利用新功能</li>
                    </ol>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 安装注意事项：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 从官方或可信渠道下载工具</li>
                            <li>• 备份重要文档后再安装新工具</li>
                            <li>• 注意工具的系统兼容性要求</li>
                            <li>• 定期更新工具以获得最新功能</li>
                            <li>• 某些工具可能需要付费许可证</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-star"></i> 8.8 推荐插件组合</h3>
                    <p>根据不同使用场景推荐的插件组合：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-user"></i> 用户类型</th>
                            <th><i class="fas fa-puzzle-piece"></i> 推荐插件</th>
                            <th><i class="fas fa-info-circle"></i> 主要功能</th>
                        </tr>
                        <tr>
                            <td>技术博客作者</td>
                            <td>PicGo + Prism.js + Hexo</td>
                            <td>图片管理 + 代码高亮 + 博客发布</td>
                        </tr>
                        <tr>
                            <td>学术研究者</td>
                            <td>Zotero + LaTeX + Pandoc</td>
                            <td>文献管理 + 公式编辑 + 格式转换</td>
                        </tr>
                        <tr>
                            <td>产品经理</td>
                            <td>Mermaid + PlantUML + Notion</td>
                            <td>流程图 + UML图 + 团队协作</td>
                        </tr>
                        <tr>
                            <td>技术文档编写</td>
                            <td>GitBook + GitHub + Chart.js</td>
                            <td>文档发布 + 版本控制 + 数据可视化</td>
                        </tr>
                    </table>
                </section>

                <!-- 快捷键大全部分 -->
                <section id="shortcuts">
                    <h2><span class="step-number">9</span>快捷键大全</h2>

                    <h3><i class="fas fa-keyboard"></i> 9.1 基础编辑快捷键</h3>
                    <p>掌握这些基础快捷键可以大大提高编辑效率：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-edit"></i> 功能</th>
                            <th><i class="fas fa-windows"></i> Windows/Linux</th>
                            <th><i class="fas fa-apple-alt"></i> macOS</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td>新建文档</td>
                            <td><span class="shortcut-key">Ctrl+N</span></td>
                            <td><span class="shortcut-key">Cmd+N</span></td>
                            <td>创建新的Markdown文档</td>
                        </tr>
                        <tr>
                            <td>打开文档</td>
                            <td><span class="shortcut-key">Ctrl+O</span></td>
                            <td><span class="shortcut-key">Cmd+O</span></td>
                            <td>打开现有文档</td>
                        </tr>
                        <tr>
                            <td>保存文档</td>
                            <td><span class="shortcut-key">Ctrl+S</span></td>
                            <td><span class="shortcut-key">Cmd+S</span></td>
                            <td>保存当前文档</td>
                        </tr>
                        <tr>
                            <td>另存为</td>
                            <td><span class="shortcut-key">Ctrl+Shift+S</span></td>
                            <td><span class="shortcut-key">Cmd+Shift+S</span></td>
                            <td>以新名称保存文档</td>
                        </tr>
                        <tr>
                            <td>撤销</td>
                            <td><span class="shortcut-key">Ctrl+Z</span></td>
                            <td><span class="shortcut-key">Cmd+Z</span></td>
                            <td>撤销上一步操作</td>
                        </tr>
                        <tr>
                            <td>重做</td>
                            <td><span class="shortcut-key">Ctrl+Y</span></td>
                            <td><span class="shortcut-key">Cmd+Shift+Z</span></td>
                            <td>重做被撤销的操作</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-font"></i> 9.2 格式化快捷键</h3>
                    <p>快速应用文本格式的快捷键：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-bold"></i> 格式</th>
                            <th><i class="fas fa-windows"></i> Windows/Linux</th>
                            <th><i class="fas fa-apple-alt"></i> macOS</th>
                            <th><i class="fas fa-eye"></i> 效果</th>
                        </tr>
                        <tr>
                            <td>粗体</td>
                            <td><span class="shortcut-key">Ctrl+B</span></td>
                            <td><span class="shortcut-key">Cmd+B</span></td>
                            <td><strong>粗体文本</strong></td>
                        </tr>
                        <tr>
                            <td>斜体</td>
                            <td><span class="shortcut-key">Ctrl+I</span></td>
                            <td><span class="shortcut-key">Cmd+I</span></td>
                            <td><em>斜体文本</em></td>
                        </tr>
                        <tr>
                            <td>下划线</td>
                            <td><span class="shortcut-key">Ctrl+U</span></td>
                            <td><span class="shortcut-key">Cmd+U</span></td>
                            <td><u>下划线文本</u></td>
                        </tr>
                        <tr>
                            <td>删除线</td>
                            <td><span class="shortcut-key">Alt+Shift+5</span></td>
                            <td><span class="shortcut-key">Cmd+Shift+X</span></td>
                            <td><del>删除线文本</del></td>
                        </tr>
                        <tr>
                            <td>行内代码</td>
                            <td><span class="shortcut-key">Ctrl+Shift+`</span></td>
                            <td><span class="shortcut-key">Cmd+Shift+`</span></td>
                            <td><code>代码文本</code></td>
                        </tr>
                        <tr>
                            <td>超链接</td>
                            <td><span class="shortcut-key">Ctrl+K</span></td>
                            <td><span class="shortcut-key">Cmd+K</span></td>
                            <td>插入链接</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-heading"></i> 9.3 标题快捷键</h3>
                    <p>快速设置标题级别：</p>

                    <div class="success-box">
                        <strong><i class="fas fa-list-ol"></i> 标题快捷键：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• <span class="shortcut-key">Ctrl+1</span> - 一级标题 (H1)</li>
                            <li>• <span class="shortcut-key">Ctrl+2</span> - 二级标题 (H2)</li>
                            <li>• <span class="shortcut-key">Ctrl+3</span> - 三级标题 (H3)</li>
                            <li>• <span class="shortcut-key">Ctrl+4</span> - 四级标题 (H4)</li>
                            <li>• <span class="shortcut-key">Ctrl+5</span> - 五级标题 (H5)</li>
                            <li>• <span class="shortcut-key">Ctrl+6</span> - 六级标题 (H6)</li>
                            <li>• <span class="shortcut-key">Ctrl+0</span> - 普通段落</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-plus"></i> 9.4 插入元素快捷键</h3>
                    <p>快速插入各种元素：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-plus-circle"></i> 元素</th>
                            <th><i class="fas fa-keyboard"></i> 快捷键</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td>表格</td>
                            <td><span class="shortcut-key">Ctrl+T</span></td>
                            <td>插入表格</td>
                        </tr>
                        <tr>
                            <td>代码块</td>
                            <td><span class="shortcut-key">Ctrl+Shift+K</span></td>
                            <td>插入代码块</td>
                        </tr>
                        <tr>
                            <td>数学公式</td>
                            <td><span class="shortcut-key">Ctrl+Shift+M</span></td>
                            <td>插入数学公式块</td>
                        </tr>
                        <tr>
                            <td>引用</td>
                            <td><span class="shortcut-key">Ctrl+Shift+Q</span></td>
                            <td>插入引用块</td>
                        </tr>
                        <tr>
                            <td>有序列表</td>
                            <td><span class="shortcut-key">Ctrl+Shift+[</span></td>
                            <td>创建有序列表</td>
                        </tr>
                        <tr>
                            <td>无序列表</td>
                            <td><span class="shortcut-key">Ctrl+Shift+]</span></td>
                            <td>创建无序列表</td>
                        </tr>
                        <tr>
                            <td>分割线</td>
                            <td><span class="shortcut-key">Ctrl+Shift+-</span></td>
                            <td>插入水平分割线</td>
                        </tr>
                        <tr>
                            <td>图片</td>
                            <td><span class="shortcut-key">Ctrl+Shift+I</span></td>
                            <td>插入图片</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-search"></i> 9.5 查找和导航快捷键</h3>
                    <p>文档查找和导航相关快捷键：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-search"></i> 功能</th>
                            <th><i class="fas fa-keyboard"></i> 快捷键</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td>查找</td>
                            <td><span class="shortcut-key">Ctrl+F</span></td>
                            <td>在文档中查找文本</td>
                        </tr>
                        <tr>
                            <td>查找下一个</td>
                            <td><span class="shortcut-key">F3</span></td>
                            <td>查找下一个匹配项</td>
                        </tr>
                        <tr>
                            <td>查找上一个</td>
                            <td><span class="shortcut-key">Shift+F3</span></td>
                            <td>查找上一个匹配项</td>
                        </tr>
                        <tr>
                            <td>替换</td>
                            <td><span class="shortcut-key">Ctrl+H</span></td>
                            <td>查找并替换文本</td>
                        </tr>
                        <tr>
                            <td>跳转到行</td>
                            <td><span class="shortcut-key">Ctrl+G</span></td>
                            <td>跳转到指定行号</td>
                        </tr>
                        <tr>
                            <td>文档开头</td>
                            <td><span class="shortcut-key">Ctrl+Home</span></td>
                            <td>跳转到文档开头</td>
                        </tr>
                        <tr>
                            <td>文档结尾</td>
                            <td><span class="shortcut-key">Ctrl+End</span></td>
                            <td>跳转到文档结尾</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-window-maximize"></i> 9.6 视图和界面快捷键</h3>
                    <p>控制界面显示的快捷键：</p>

                    <div class="info-box">
                        <strong><i class="fas fa-eye"></i> 视图控制：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• <span class="shortcut-key">Ctrl+Shift+L</span> - 切换文件树侧边栏</li>
                            <li>• <span class="shortcut-key">Ctrl+Shift+1</span> - 切换大纲视图</li>
                            <li>• <span class="shortcut-key">F11</span> - 全屏模式</li>
                            <li>• <span class="shortcut-key">F9</span> - 实时预览模式</li>
                            <li>• <span class="shortcut-key">Ctrl+/</span> - 源代码模式</li>
                            <li>• <span class="shortcut-key">Ctrl+Shift+T</span> - 切换主题</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <strong><i class="fas fa-rocket"></i> 效率提升技巧：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 组合使用快捷键可以更快完成复杂操作</li>
                            <li>• 自定义快捷键以适应个人习惯</li>
                            <li>• 练习常用快捷键直到形成肌肉记忆</li>
                            <li>• 使用快捷键比鼠标操作效率更高</li>
                        </ul>
                    </div>
                </section>

                <!-- 技巧与窍门部分 -->
                <section id="tips-tricks">
                    <h2><span class="step-number">10</span>技巧与窍门</h2>

                    <h3><i class="fas fa-lightbulb"></i> 10.1 编辑技巧</h3>
                    <p>这些实用技巧可以让您的Typora使用更加高效：</p>

                    <h4><i class="fas fa-magic"></i> 自动补全功能</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-wand-magic-sparkles"></i> 智能补全：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 输入<code>[]</code>自动创建任务列表</li>
                            <li>• 输入<code>|</code>开始创建表格</li>
                            <li>• 输入<code>```</code>创建代码块</li>
                            <li>• 输入<code>$$</code>创建数学公式块</li>
                            <li>• 输入<code>---</code>创建分割线</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-copy"></i> 复制粘贴技巧</h4>
                    <p>充分利用Typora的智能粘贴功能：</p>
                    <ol>
                        <li><strong>网页内容：</strong> 从网页复制的内容会自动转换为Markdown格式</li>
                        <li><strong>表格数据：</strong> 从Excel或其他应用复制的表格会自动转换</li>
                        <li><strong>图片处理：</strong> 复制的图片会自动保存到指定文件夹</li>
                        <li><strong>代码格式：</strong> 从IDE复制的代码会保持格式和高亮</li>
                    </ol>

                    <h4><i class="fas fa-mouse-pointer"></i> 拖拽操作</h4>
                    <p>利用拖拽功能提高效率：</p>
                    <table>
                        <tr>
                            <th><i class="fas fa-hand-paper"></i> 拖拽对象</th>
                            <th><i class="fas fa-arrow-right"></i> 操作结果</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td>图片文件</td>
                            <td>自动插入图片语法</td>
                            <td>支持多种图片格式</td>
                        </tr>
                        <tr>
                            <td>Markdown文件</td>
                            <td>在新窗口打开</td>
                            <td>可以同时编辑多个文档</td>
                        </tr>
                        <tr>
                            <td>文件夹</td>
                            <td>在侧边栏显示</td>
                            <td>便于管理项目文件</td>
                        </tr>
                        <tr>
                            <td>链接</td>
                            <td>创建超链接</td>
                            <td>自动获取页面标题</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-cogs"></i> 10.2 工作流优化</h3>
                    <p>建立高效的写作工作流：</p>

                    <h4><i class="fas fa-folder-tree"></i> 文件组织</h4>
                    <pre><code>推荐的文件结构：
项目文件夹/
├── docs/                 # 文档目录
│   ├── README.md        # 项目说明
│   ├── chapters/        # 章节文件
│   └── appendix/        # 附录文件
├── assets/              # 资源文件
│   ├── images/          # 图片文件
│   ├── css/             # 样式文件
│   └── templates/       # 模板文件
└── exports/             # 导出文件
    ├── pdf/
    ├── html/
    └── word/</code></pre>

                    <h4><i class="fas fa-clock"></i> 写作习惯</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-pen-fancy"></i> 高效写作建议：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• <strong>先写内容，后调格式：</strong> 专注于内容创作，避免过早纠结格式</li>
                            <li>• <strong>使用大纲视图：</strong> 先建立文档结构，再填充具体内容</li>
                            <li>• <strong>定期保存备份：</strong> 开启自动保存，定期备份重要文档</li>
                            <li>• <strong>版本控制：</strong> 对重要文档使用Git进行版本管理</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-tools"></i> 10.3 高级技巧</h3>
                    <p>掌握这些高级技巧让您成为Typora专家：</p>

                    <h4><i class="fas fa-code"></i> 自定义快捷键</h4>
                    <p>在偏好设置中可以自定义快捷键：</p>
                    <ol>
                        <li>打开"File" → "Preferences" → "General"</li>
                        <li>找到"Custom Key Binding"选项</li>
                        <li>点击"Open Advanced Settings"</li>
                        <li>编辑JSON配置文件添加自定义快捷键</li>
                    </ol>

                    <pre><code>{
  "keyBinding": {
    "Toggle Sidebar": "Ctrl+E",
    "Toggle DevTools": "F12",
    "New Window": "Ctrl+Shift+N"
  }
}</code></pre>

                    <h4><i class="fas fa-plug"></i> 集成外部工具</h4>
                    <p>将Typora与其他工具集成：</p>
                    <table>
                        <tr>
                            <th><i class="fas fa-tools"></i> 工具类型</th>
                            <th><i class="fas fa-box"></i> 推荐工具</th>
                            <th><i class="fas fa-info-circle"></i> 集成方式</th>
                        </tr>
                        <tr>
                            <td>版本控制</td>
                            <td>Git, GitHub Desktop</td>
                            <td>文件夹级别的版本管理</td>
                        </tr>
                        <tr>
                            <td>云同步</td>
                            <td>OneDrive, Dropbox, iCloud</td>
                            <td>将文档文件夹设置在云盘中</td>
                        </tr>
                        <tr>
                            <td>图床服务</td>
                            <td>PicGo, 七牛云, 阿里云</td>
                            <td>通过插件自动上传图片</td>
                        </tr>
                        <tr>
                            <td>发布平台</td>
                            <td>GitHub Pages, 博客平台</td>
                            <td>直接导出或API发布</td>
                        </tr>
                    </table>

                    <h4><i class="fas fa-robot"></i> 自动化工作流</h4>
                    <p>使用脚本自动化常见任务：</p>
                    <pre><code># 批量转换脚本示例 (PowerShell)
# 将文件夹中所有md文件转换为PDF

$sourceFolder = "C:\Documents\Markdown"
$outputFolder = "C:\Documents\PDF"

Get-ChildItem $sourceFolder -Filter "*.md" | ForEach-Object {
    $inputFile = $_.FullName
    $outputFile = Join-Path $outputFolder ($_.BaseName + ".pdf")

    # 使用Typora命令行工具转换
    & "C:\Program Files\Typora\Typora.exe" $inputFile --export pdf --output $outputFile
}</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 高级功能注意：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 自定义配置前建议备份原始设置</li>
                            <li>• 测试自动化脚本确保不会损坏文件</li>
                            <li>• 定期更新集成工具保持兼容性</li>
                            <li>• 复杂配置可能影响软件稳定性</li>
                        </ul>
                    </div>
                </section>

                <!-- 故障排查部分 -->
                <section id="troubleshooting">
                    <h2><span class="step-number">11</span>故障排查</h2>

                    <h3><i class="fas fa-bug"></i> 11.1 常见问题</h3>
                    <p>以下是Typora使用过程中可能遇到的常见问题及解决方案：</p>

                    <h4><i class="fas fa-exclamation-triangle"></i> 启动和运行问题</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-question-circle"></i> 问题描述</th>
                            <th><i class="fas fa-tools"></i> 解决方案</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td>Typora无法启动</td>
                            <td>检查系统要求，重新安装软件</td>
                            <td>可能是安装文件损坏或系统不兼容</td>
                        </tr>
                        <tr>
                            <td>启动速度很慢</td>
                            <td>清理临时文件，关闭不必要的插件</td>
                            <td>大量文件或插件会影响启动速度</td>
                        </tr>
                        <tr>
                            <td>界面显示异常</td>
                            <td>重置主题设置，更新显卡驱动</td>
                            <td>主题冲突或硬件加速问题</td>
                        </tr>
                        <tr>
                            <td>频繁崩溃</td>
                            <td>更新到最新版本，检查内存使用</td>
                            <td>版本bug或系统资源不足</td>
                        </tr>
                    </table>

                    <h4><i class="fas fa-file-alt"></i> 文档编辑问题</h4>
                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 编辑问题解决：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• <strong>文档无法保存：</strong> 检查文件权限，确保有写入权限</li>
                            <li>• <strong>格式显示错误：</strong> 检查Markdown语法，重新加载文档</li>
                            <li>• <strong>图片无法显示：</strong> 确认图片路径正确，检查文件是否存在</li>
                            <li>• <strong>数学公式不渲染：</strong> 检查LaTeX语法，重启Typora</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-download"></i> 导出问题</h4>
                    <p>导出功能常见问题：</p>
                    <ol>
                        <li><strong>PDF导出失败：</strong>
                            <ul>
                                <li>检查是否有足够的磁盘空间</li>
                                <li>尝试更换导出路径</li>
                                <li>简化文档内容，分段导出</li>
                            </ul>
                        </li>
                        <li><strong>导出格式错乱：</strong>
                            <ul>
                                <li>检查主题兼容性</li>
                                <li>使用默认主题重新导出</li>
                                <li>手动调整CSS样式</li>
                            </ul>
                        </li>
                        <li><strong>图片丢失：</strong>
                            <ul>
                                <li>使用相对路径引用图片</li>
                                <li>将图片复制到文档同级目录</li>
                                <li>检查图片文件名是否包含特殊字符</li>
                            </ul>
                        </li>
                    </ol>

                    <h3><i class="fas fa-wrench"></i> 11.2 性能优化</h3>
                    <p>提升Typora运行性能的方法：</p>

                    <h4><i class="fas fa-tachometer-alt"></i> 系统优化</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-rocket"></i> 性能提升技巧：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• <strong>关闭实时预览：</strong> 对于大文档，可以关闭实时预览功能</li>
                            <li>• <strong>限制文件大小：</strong> 将大文档拆分为多个小文件</li>
                            <li>• <strong>清理缓存：</strong> 定期清理Typora的缓存文件</li>
                            <li>• <strong>优化图片：</strong> 压缩图片大小，使用适当的格式</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-memory"></i> 内存管理</h4>
                    <pre><code>内存优化建议：
├── 关闭不必要的文档标签
├── 定期重启Typora释放内存
├── 避免同时打开过多大文件
└── 使用轻量级主题减少渲染负担</code></pre>

                    <h3><i class="fas fa-life-ring"></i> 11.3 获取帮助</h3>
                    <p>当遇到无法解决的问题时，可以通过以下渠道获取帮助：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-question-circle"></i> 帮助渠道</th>
                            <th><i class="fas fa-link"></i> 地址/方式</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td>官方文档</td>
                            <td>https://support.typora.io/</td>
                            <td>最权威的使用指南和FAQ</td>
                        </tr>
                        <tr>
                            <td>GitHub Issues</td>
                            <td>https://github.com/typora/typora-issues</td>
                            <td>报告bug和功能请求</td>
                        </tr>
                        <tr>
                            <td>社区论坛</td>
                            <td>Reddit, Stack Overflow</td>
                            <td>用户交流和问题讨论</td>
                        </tr>
                        <tr>
                            <td>邮件支持</td>
                            <td><EMAIL></td>
                            <td>官方技术支持邮箱</td>
                        </tr>
                    </table>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 求助技巧：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• 详细描述问题现象和复现步骤</li>
                            <li>• 提供系统信息和Typora版本号</li>
                            <li>• 附上相关的错误截图或日志</li>
                            <li>• 说明已经尝试过的解决方法</li>
                        </ul>
                    </div>
                </section>

                <!-- 总结部分 -->
                <section id="summary">
                    <h2><span class="step-number">12</span>总结</h2>

                    <h3><i class="fas fa-flag-checkered"></i> 12.1 学习回顾</h3>
                    <p>通过本教程的学习，您已经掌握了Typora的全面使用技能：</p>

                    <div class="success-box">
                        <strong><i class="fas fa-graduation-cap"></i> 您已经学会了：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> Typora的安装、配置和基本使用</li>
                            <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 完整的Markdown语法和高级功能</li>
                            <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 数学公式、图表绘制等专业功能</li>
                            <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 主题定制和个性化配置</li>
                            <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 多格式导出和文档管理</li>
                            <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 高效的快捷键和工作流技巧</li>
                            <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 常见问题的排查和解决方法</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-road"></i> 12.2 进阶建议</h3>
                    <p>为了进一步提升您的Typora使用技能，建议：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-target"></i> 学习方向</th>
                            <th><i class="fas fa-book"></i> 推荐资源</th>
                            <th><i class="fas fa-clock"></i> 学习时间</th>
                        </tr>
                        <tr>
                            <td>深入学习Markdown</td>
                            <td>CommonMark规范、GitHub Flavored Markdown</td>
                            <td>1-2周</td>
                        </tr>
                        <tr>
                            <td>LaTeX数学公式</td>
                            <td>LaTeX数学符号手册、在线公式编辑器</td>
                            <td>2-3周</td>
                        </tr>
                        <tr>
                            <td>CSS样式定制</td>
                            <td>CSS基础教程、Typora主题开发</td>
                            <td>3-4周</td>
                        </tr>
                        <tr>
                            <td>自动化工作流</td>
                            <td>脚本编程、Git版本控制</td>
                            <td>4-6周</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-heart"></i> 12.3 最佳实践</h3>
                    <p>基于丰富的使用经验，我们推荐以下最佳实践：</p>

                    <div class="info-box">
                        <strong><i class="fas fa-star"></i> 写作最佳实践：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• <strong>内容优先：</strong> 先专注于内容创作，后期再调整格式和样式</li>
                            <li>• <strong>结构清晰：</strong> 使用标题层级建立清晰的文档结构</li>
                            <li>• <strong>定期备份：</strong> 重要文档要有多重备份机制</li>
                            <li>• <strong>版本管理：</strong> 使用Git等工具管理文档版本</li>
                            <li>• <strong>团队协作：</strong> 建立统一的文档规范和工作流程</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-rocket"></i> 12.4 未来展望</h3>
                    <p>Typora作为优秀的Markdown编辑器，将继续发展和完善：</p>

                    <div class="warning-box">
                        <strong><i class="fas fa-crystal-ball"></i> 发展趋势：</strong>
                        <ul style="list-style-type: none; padding-left: 10px; margin-top: 10px;">
                            <li>• <strong>AI集成：</strong> 可能集成AI写作助手和智能校对功能</li>
                            <li>• <strong>云端协作：</strong> 增强多人实时协作编辑能力</li>
                            <li>• <strong>插件生态：</strong> 建立更丰富的插件和扩展生态</li>
                            <li>• <strong>移动端支持：</strong> 可能推出移动端应用</li>
                        </ul>
                    </div>

                    <div class="success-box" style="text-align: center; margin-top: 40px;">
                        <h3><i class="fas fa-trophy"></i> 恭喜您完成学习！</h3>
                        <p style="font-size: 18px; margin-top: 15px;">
                            您现在已经是Typora的熟练用户了！<br>
                            希望这个工具能够帮助您创作出更多优秀的内容。
                        </p>
                        <p style="margin-top: 20px;">
                            <strong>记住：</strong>最好的学习方法就是实践。<br>
                            现在就开始使用Typora创作您的第一篇文档吧！
                        </p>
                    </div>
                </section>

            </div>
        </div>
    </div>

    <!-- 返回顶部按钮 -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // 移动端菜单切换
        document.getElementById('mobileMenuBtn').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('active');
        });

        // 返回顶部按钮
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'flex';
            } else {
                backToTop.style.display = 'none';
            }
        });

        document.getElementById('backToTop').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 侧边栏导航高亮
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('section');
            const navLinks = document.querySelectorAll('.sidebar a');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // 平滑滚动到锚点
        document.querySelectorAll('.sidebar a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }

                // 移动端关闭侧边栏
                if (window.innerWidth <= 768) {
                    document.getElementById('sidebar').classList.remove('active');
                }
            });
        });

        // 代码块复制功能
        document.querySelectorAll('pre code').forEach(block => {
            const button = document.createElement('button');
            button.className = 'copy-button';
            button.textContent = '复制';
            button.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                background: var(--primary-color);
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 12px;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            const pre = block.parentElement;
            pre.style.position = 'relative';
            pre.appendChild(button);

            pre.addEventListener('mouseenter', () => {
                button.style.opacity = '1';
            });

            pre.addEventListener('mouseleave', () => {
                button.style.opacity = '0';
            });

            button.addEventListener('click', () => {
                navigator.clipboard.writeText(block.textContent).then(() => {
                    button.textContent = '已复制';
                    setTimeout(() => {
                        button.textContent = '复制';
                    }, 2000);
                });
            });
        });

        // 表格响应式处理
        document.querySelectorAll('table').forEach(table => {
            const wrapper = document.createElement('div');
            wrapper.style.cssText = 'overflow-x: auto; margin: 20px 0;';
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);
        });

        // 图片点击放大
        document.querySelectorAll('img').forEach(img => {
            img.style.cursor = 'pointer';
            img.addEventListener('click', function() {
                const overlay = document.createElement('div');
                overlay.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                    cursor: pointer;
                `;

                const enlargedImg = document.createElement('img');
                enlargedImg.src = this.src;
                enlargedImg.style.cssText = `
                    max-width: 90%;
                    max-height: 90%;
                    object-fit: contain;
                `;

                overlay.appendChild(enlargedImg);
                document.body.appendChild(overlay);

                overlay.addEventListener('click', () => {
                    document.body.removeChild(overlay);
                });
            });
        });

        // 初始化时设置第一个导航项为活动状态
        document.addEventListener('DOMContentLoaded', function() {
            const firstNavLink = document.querySelector('.sidebar a');
            if (firstNavLink) {
                firstNavLink.classList.add('active');
            }
        });
    </script>
</body>
</html>