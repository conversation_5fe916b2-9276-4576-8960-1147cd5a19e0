#!/bin/bash
set -e

# MySQL 5.7.44 容器启动脚本
# 解决重启问题和权限问题

echo "=== MySQL 5.7.44 容器启动脚本 ==="

# 确保MySQL用户和组存在
echo "检查MySQL用户和组..."
if ! getent group mysql > /dev/null 2>&1; then
    echo "创建mysql组..."
    groupadd mysql
fi

if ! getent passwd mysql > /dev/null 2>&1; then
    echo "创建mysql用户..."
    useradd -r -g mysql mysql
fi

# 确保目录存在
echo "创建必要目录..."
mkdir -p /usr/local/mysql/logs
mkdir -p /var/run/mysqld
mkdir -p /usr/local/mysql/data

# 设置权限
echo "设置目录权限..."
chown -R mysql:mysql /usr/local/mysql
# 尝试设置配置文件权限，如果失败则忽略（可能是只读挂载）
chown mysql:mysql /etc/my.cnf 2>/dev/null || echo "警告: 无法修改 /etc/my.cnf 权限，可能是只读挂载"
chown mysql:mysql /var/run/mysqld
chmod 755 /var/run/mysqld

# 更新库缓存
echo "更新库缓存..."
ldconfig

# 检查数据目录是否已初始化
if [ ! -d "/usr/local/mysql/data/mysql" ]; then
    echo "数据目录未初始化，正在初始化..."
    /usr/local/mysql/bin/mysqld --initialize-insecure --user=mysql --datadir=/usr/local/mysql/data

    echo "数据库初始化完成，配置root用户权限..."
    # 启动MySQL进行初始配置
    /usr/local/mysql/bin/mysqld_safe --defaults-file=/etc/my.cnf --datadir=/usr/local/mysql/data &
    MYSQL_PID=$!

    # 等待MySQL启动
    echo "等待MySQL启动..."
    for i in {1..30}; do
        if /usr/local/mysql/bin/mysqladmin ping --silent; then
            echo "MySQL已启动，配置权限..."
            break
        fi
        echo "等待MySQL启动... ($i/30)"
        sleep 2
    done

    # 配置root用户权限
    /usr/local/mysql/bin/mysql -e "
        USE mysql;
        DELETE FROM user WHERE user='';
        DELETE FROM user WHERE user='root' AND host NOT IN ('localhost', '127.0.0.1', '::1', '%');
        UPDATE user SET host = '%' WHERE user = 'root' AND host = 'localhost';
        GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' IDENTIFIED BY 'root' WITH GRANT OPTION;
        GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' IDENTIFIED BY 'root' WITH GRANT OPTION;
        FLUSH PRIVILEGES;
    "

    echo "权限配置完成，停止MySQL..."
    kill $MYSQL_PID
    wait $MYSQL_PID 2>/dev/null || true

    echo "初始化完成！"
else
    echo "数据目录已存在，跳过初始化..."
fi

# 清理可能存在的socket文件
if [ -f "/usr/local/mysql/mysql.sock" ]; then
    echo "清理旧的socket文件..."
    rm -f /usr/local/mysql/mysql.sock
fi

# 清理可能存在的socket文件
if [ -f "/usr/local/mysql/mysql.sock.lock" ]; then
    echo "清理旧的socket文件..."
    rm -f /usr/local/mysql/mysql.sock.lock
fi

# 清理可能存在的PID文件
if [ -f "/usr/local/mysql/mysql.pid" ]; then
    echo "清理旧的PID文件..."
    rm -f /usr/local/mysql/mysql.pid
fi

if [ -f "/var/run/mysqld/mysqld.pid" ]; then
    echo "清理旧的PID文件..."
    rm -f /var/run/mysqld/mysqld.pid
fi

# 最终权限检查
echo "最终权限检查..."
chown -R mysql:mysql /usr/local/mysql/data
chown -R mysql:mysql /usr/local/mysql/logs

# 启动MySQL
echo "启动MySQL服务..."
echo "使用配置文件: /etc/my.cnf"
echo "数据目录: /usr/local/mysql/data"
echo "Socket文件: /usr/local/mysql/mysql.sock"
echo "PID文件: /usr/local/mysql/mysql.pid"

exec /usr/local/mysql/bin/mysqld_safe \
    --defaults-file=/etc/my.cnf \
    --user=mysql \
    --datadir=/usr/local/mysql/data \
    --socket=/usr/local/mysql/mysql.sock \
    --pid-file=/usr/local/mysql/mysql.pid
