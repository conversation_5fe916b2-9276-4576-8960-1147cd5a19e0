// 临时邮箱桌面应用 Vue.js 主文件

const { createApp, ref, reactive, computed, onMounted, nextTick } = Vue;
const { ElMessage, ElMessageBox, ElNotification } = ElementPlus;

// 创建 Vue 应用
const app = createApp({
    setup() {
        // 响应式数据
        const currentEmail = ref(null);
        const emails = ref([]);
        const selectedEmail = ref(null);
        const availableDomains = ref([]);
        const selectedDomain = ref('mailto.plus');
        const selectedDuration = ref(60);
        const pinCode = ref('tv81***/-+/sb.38');
        const inputPin = ref('tv81***/-+/sb.38');
        const directEmailInput = ref('');
        // 新的邮箱输入方式
        const emailPrefix = ref('svipmail');
        const selectedDomainForDirect = ref('mailto.plus');
        // 自定义邮箱相关
        const showCustomEmailDialog = ref(false);
        const customEmailForm = reactive({
            prefix: '',
            domain: 'mailto.plus',
            duration: 60
        });

        // 加载状态
        const loading = ref(false);
        const refreshing = ref(false);
        const sending = ref(false);

        // 对话框显示状态
        const showComposeDialog = ref(false);
        const showDestroyDialog = ref(false);
        const showPinDialog = ref(false);
        const showEmailDetailDialog = ref(false);
        const verifyingPin = ref(false);
        const pendingEmailAccess = ref(null); // 等待PIN验证的邮箱

        // 复制功能状态
        const contentCopied = ref(false);
        const selectedTextCopied = ref(false);

        // 撰写邮件表单
        const composeForm = reactive({
            to: '',
            subject: '',
            body: ''
        });

        // 计算属性
        const unreadCount = computed(() => {
            return emails.value.filter(email => !email.read).length;
        });

        const customEmailPreview = computed(() => {
            if (customEmailForm.prefix.trim()) {
                return `${customEmailForm.prefix}@${customEmailForm.domain}`;
            }
            return `前缀@${customEmailForm.domain}`;
        });

        // 获取完整邮箱地址的方法
        const getFullEmailAddress = () => {
            if (emailPrefix.value.trim() && selectedDomainForDirect.value) {
                return `${emailPrefix.value.trim()}@${selectedDomainForDirect.value}`;
            }
            return emailPrefix.value.trim() ? `${emailPrefix.value.trim()}@选择域名` : '前缀@选择域名';
        };

        // API 基础 URL - 自动检测可用端口
        const API_BASE = ref('http://127.0.0.1:8001/api');

        // 检测可用的API端口
        const detectAPIPort = async () => {
            const ports = [8001, 8002, 8003, 8004, 8005, 8006, 8007];
            const hosts = ['127.0.0.1', 'localhost'];

            for (const host of hosts) {
                for (const port of ports) {
                    try {
                        const testUrl = `http://${host}:${port}/api/domains`;
                        const response = await axios.get(testUrl, { timeout: 3000 });
                        if (response.status === 200) {
                            API_BASE.value = `http://${host}:${port}/api`;
                            console.log(`✅ 检测到API服务运行在 ${host}:${port}`);
                            return true;
                        }
                    } catch (error) {
                        console.log(`主机 ${host} 端口 ${port} 不可用`);
                    }
                }
            }
            console.error('❌ 无法连接到后端API服务');
            ElMessage.error('无法连接到后端服务，请重启应用');
            return false;
        };

        // 工具函数
        const formatTime = (timeString) => {
            if (!timeString) return '';
            const date = new Date(timeString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        };

        const getEmailPreview = (email) => {
            // 如果邮件有详细内容，优先使用详细内容
            let body = email.detailed_body || email.body;

            if (!body || body === '(无内容)') {
                return '(无内容)';
            }

            // 移除 HTML 标签并限制长度
            const text = body.replace(/<[^>]*>/g, '').trim();
            return text.length > 80 ? text.substring(0, 80) + '...' : text;
        };

        // 获取邮件的实际主题
        const getEmailSubject = (email) => {
            return email.detailed_subject || email.subject || '(无主题)';
        };

        // 批量获取邮件详情（用于改善列表显示）
        const fetchEmailDetails = async (emailList) => {
            if (!emailList || emailList.length === 0) return;

            console.log('[FRONTEND-DEBUG] 开始批量获取邮件详情...');

            // 限制并发数量，避免过多请求
            const batchSize = 3;
            for (let i = 0; i < emailList.length; i += batchSize) {
                const batch = emailList.slice(i, i + batchSize);

                // 并发获取这一批邮件的详情
                const promises = batch.map(async (email) => {
                    try {
                        // 如果已经有详细内容，跳过
                        if (email.detailed_subject && email.detailed_body) {
                            return;
                        }

                        console.log(`[FRONTEND-DEBUG] 正在获取邮件 ${email.id} 的详情...`);
                        const result = await callPyAPI('get_email_detail', email.id);
                        console.log(`[FRONTEND-DEBUG] 邮件 ${email.id} API响应:`, result);

                        if (result && result.success) {
                            // 更新邮件对象的详细内容
                            email.detailed_subject = result.email.subject;
                            email.detailed_body = result.email.html_body || result.email.body;
                            console.log(`[FRONTEND-DEBUG] 已获取邮件 ${email.id} 的详情: 主题="${email.detailed_subject}", 内容长度=${email.detailed_body?.length || 0}`);
                        } else {
                            console.warn(`[FRONTEND-WARN] 邮件 ${email.id} 详情获取失败:`, result?.error || '未知错误');
                        }
                    } catch (error) {
                        console.error(`[FRONTEND-ERROR] 获取邮件 ${email.id} 详情异常:`, error);
                    }
                });

                await Promise.all(promises);

                // 添加小延迟，避免请求过于频繁
                if (i + batchSize < emailList.length) {
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
            }

            console.log('[FRONTEND-DEBUG] 批量获取邮件详情完成');
        };

        const formatEmailBody = (body) => {
            if (!body) return '';
            // 简单的 HTML 格式化
            return body.replace(/\n/g, '<br>');
        };

        // API 调用函数
        const callAPI = async (endpoint, options = {}) => {
            try {
                const config = {
                    url: `${API_BASE.value}${endpoint}`,
                    method: options.method || 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    timeout: 10000  // 10秒超时
                };

                // GET请求使用params，POST请求使用data
                if (config.method.toUpperCase() === 'GET') {
                    config.params = options.data;
                } else {
                    config.data = options.data;
                }

                const response = await axios(config);
                return response.data;
            } catch (error) {
                console.error('API 调用失败:', error);
                if (error.code === 'ECONNREFUSED' || error.code === 'NETWORK_ERROR') {
                    ElMessage.error('网络连接失败，请检查连接');
                } else {
                    ElMessage.error('网络请求失败，请检查连接');
                }
                throw error;
            }
        };

        // pywebview API 调用函数
        const callPyAPI = async (method, ...args) => {
            try {
                console.log(`[API-DEBUG] 调用方法: ${method}, 参数:`, args);
                console.log(`[API-DEBUG] window.pywebview 存在:`, !!window.pywebview);
                console.log(`[API-DEBUG] window.pywebview.api 存在:`, !!(window.pywebview && window.pywebview.api));

                // 控制台调试信息（保留）
                if (method === 'get_email_detail') {
                    console.log(`[DEBUG] 正在获取邮件 ${args[0]} 的详情...`);
                }

                if (window.pywebview && window.pywebview.api) {
                    console.log(`[API-DEBUG] 使用 pywebview API 调用 ${method}`);
                    const result = await window.pywebview.api[method](...args);
                    console.log(`[API-DEBUG] pywebview API 响应:`, result);

                    // 控制台调试信息（保留）
                    if (method === 'get_email_detail') {
                        if (result && result.success) {
                            console.log(`[DEBUG] 邮件详情获取成功`);
                        } else {
                            console.log(`[DEBUG] 邮件详情获取失败 - ${result?.error || '未知错误'}`);
                        }
                    }

                    return result;
                } else {
                    console.log(`[API-DEBUG] pywebview API 不可用，使用 HTTP API`);
                    console.log(`[API-DEBUG] 当前协议:`, window.location.protocol);
                    console.log(`[API-DEBUG] 当前主机:`, window.location.host);
                    // 如果 pywebview API 不可用，使用 HTTP API
                    const endpointMap = {
                        'generate_email': '/generate-email',
                        'get_emails': '/emails',
                        'get_current_email': '/current-email',
                        'delete_email': '/delete-email',
                        'get_email_detail': '/email-detail',
                        'set_pin': '/set-pin',
                        'verify_pin': '/verify-pin',
                        'check_email_pin': '/check-email-pin',
                        'test_pin_access': '/test-pin-access',
                        'send_email': '/send-email',
                        'get_domains': '/domains',
                        'set_current_email': '/set-current-email'
                    };

                    let endpoint = endpointMap[method] || `/${method.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
                    const httpMethod = ['get_emails', 'get_current_email', 'get_domains', 'get_email_detail'].includes(method) ? 'GET' : 'POST';

                    // 特殊处理某些方法的参数格式
                    let requestData = {};
                    if (method === 'get_email_detail' && args.length > 0) {
                        // get_email_detail 方法：将email_id作为URL路径参数
                        endpoint = `/email-detail/${args[0]}`;
                        requestData = {};
                    } else if (method === 'set_current_email' && args.length > 0) {
                        // set_current_email 方法：pywebview 接收字符串，HTTP API 接收对象
                        requestData = { email_address: args[0] };
                    } else if (method === 'set_pin' && args.length > 0) {
                        // set_pin 方法：pywebview 接收字符串，HTTP API 接收对象
                        requestData = { pin: args[0] };
                    } else if (method === 'verify_pin' && args.length > 0) {
                        // verify_pin 方法：pywebview 接收字符串，HTTP API 接收对象
                        requestData = { pin: args[0] };
                    } else {
                        requestData = args.length > 0 ? args[0] : {};
                    }

                    console.log(`[API-DEBUG] HTTP API 调用: ${httpMethod} ${endpoint}`);
                    console.log(`[API-DEBUG] 请求数据:`, requestData);

                    const result = await callAPI(endpoint, {
                        method: httpMethod,
                        data: requestData
                    });

                    console.log(`[API-DEBUG] HTTP API 响应:`, result);
                    return result;
                }
            } catch (error) {
                console.error(`[API-ERROR] ${method} 调用失败:`, error);
                console.error(`[API-ERROR] 错误类型:`, error.constructor.name);
                console.error(`[API-ERROR] 错误消息:`, error.message);
                if (error.response) {
                    console.error(`[API-ERROR] HTTP状态:`, error.response.status);
                    console.error(`[API-ERROR] HTTP响应:`, error.response.data);
                }
                throw error;
            }
        };

        // 生成新邮箱（随机）
        const generateNewEmail = async () => {
            loading.value = true;
            try {
                // 准备请求参数（随机生成，不使用自定义前缀）
                const requestData = {
                    domain: selectedDomain.value,
                    duration: parseInt(selectedDuration.value)
                };

                const result = await callPyAPI('generate_email', requestData);

                console.log('邮箱生成结果:', result);
                if (result.email) {
                    console.log('邮箱地址字段:', result.email.address);
                }

                if (result && result.success) {
                    console.log('✅ 邮箱生成成功');
                    console.log('📮 邮箱数据:', result.email);

                    currentEmail.value = result.email;
                    emails.value = [];
                    selectedEmail.value = null;

                    // 验证数据结构
                    if (result.email && result.email.address) {
                        console.log('✅ 邮箱地址正确:', result.email.address);
                        ElMessage.success(`新邮箱生成成功！地址: ${result.email.address}`);
                    } else {
                        console.error('❌ 邮箱数据结构异常:', result.email);
                        ElMessage.error('邮箱数据格式错误');
                    }

                    // 自动刷新邮件
                    setTimeout(() => {
                        refreshEmails();
                    }, 1000);
                } else {
                    console.error('❌ 生成邮箱失败:', result);
                    ElMessage.error(result?.error || '生成邮箱失败');
                }
            } catch (error) {
                console.error('💥 生成邮箱异常:', error);
                ElMessage.error('生成邮箱失败: ' + error.message);
            } finally {
                loading.value = false;
            }
        };

        // 生成自定义前缀邮箱
        const generateCustomEmail = async () => {
            if (!customEmailForm.prefix.trim()) {
                ElMessage.warning('请先输入自定义前缀');
                return;
            }

            loading.value = true;
            try {
                // 准备请求参数
                const requestData = {
                    domain: customEmailForm.domain,
                    duration: parseInt(customEmailForm.duration),
                    custom_prefix: customEmailForm.prefix.trim()
                };

                console.log('🎯 使用自定义前缀:', requestData.custom_prefix);
                console.log('🌐 使用自定义域名:', requestData.domain);

                const result = await callPyAPI('generate_email', requestData);

                console.log('自定义邮箱生成结果:', result);

                if (result && result.success) {
                    console.log('✅ 自定义邮箱生成成功');
                    console.log('📮 邮箱数据:', result.email);

                    currentEmail.value = result.email;
                    emails.value = [];
                    selectedEmail.value = null;

                    // 关闭对话框
                    showCustomEmailDialog.value = false;

                    // 清空表单
                    customEmailForm.prefix = '';
                    customEmailForm.domain = 'mailto.plus';
                    customEmailForm.duration = 60;

                    // 验证数据结构
                    if (result.email && result.email.address) {
                        console.log('✅ 邮箱地址正确:', result.email.address);
                        ElMessage.success(`自定义邮箱生成成功！地址: ${result.email.address}`);
                    } else {
                        console.error('❌ 邮箱数据结构异常:', result.email);
                        ElMessage.error('邮箱数据格式错误');
                    }

                    // 自动刷新邮件
                    setTimeout(() => {
                        refreshEmails();
                    }, 1000);
                } else {
                    console.error('❌ 生成自定义邮箱失败:', result);
                    ElMessage.error(result?.error || '生成自定义邮箱失败');
                }
            } catch (error) {
                console.error('💥 生成自定义邮箱异常:', error);
                ElMessage.error('生成自定义邮箱失败: ' + error.message);
            } finally {
                loading.value = false;
            }
        };

        // 刷新邮件列表
        const refreshEmails = async (skipPinCheck = false) => {
            if (!currentEmail.value) return;

            // 如果不跳过PIN检查，先检查是否需要PIN码验证
            if (!skipPinCheck) {
                const emailAddress = currentEmail.value.address || currentEmail.value;
                const needsPin = await checkEmailPinProtection(emailAddress);

                if (needsPin && !showPinDialog.value) {
                    // 需要PIN码但还没有验证，显示PIN码对话框
                    pendingEmailAccess.value = emailAddress;
                    inputPin.value = 'tv81***/-+/sb.38'; // 设置默认PIN码
                    showPinDialog.value = true;
                    ElMessage.info('此邮箱已设置PIN码保护，请输入PIN码');
                    return;
                }
            }

            refreshing.value = true;
            try {
                console.log('🔄 开始刷新邮件...');
                const result = await callPyAPI('get_emails');
                console.log('📨 后端响应:', result);

                if (result && result.success) {
                    emails.value = result.emails || [];
                    console.log('✅ 邮件数据设置成功:', emails.value);
                    ElMessage.success(`刷新完成，共 ${emails.value.length} 封邮件`);

                    // 异步获取邮件详情以改善列表显示
                    if (emails.value.length > 0) {
                        fetchEmailDetails(emails.value).catch(error => {
                            console.warn('[FRONTEND-WARN] 批量获取邮件详情失败:', error);
                        });
                    }
                } else {
                    console.error('❌ 后端返回失败:', result);
                    ElMessage.error(result?.error || '刷新邮件失败');
                }
            } catch (error) {
                console.error('💥 刷新邮件异常:', error);
                ElMessage.error('刷新邮件失败: ' + error.message);
            } finally {
                refreshing.value = false;
            }
        };

        // 选择邮件
        const selectEmail = async (email) => {
            try {
                console.log('[FRONTEND-DEBUG] 开始选择邮件');
                console.log('[FRONTEND-DEBUG] 邮件对象:', email);
                console.log('[FRONTEND-DEBUG] 邮件ID:', email.id);
                console.log('[FRONTEND-DEBUG] 邮件ID类型:', typeof email.id);

                // 先设置基本邮件信息，立即显示弹窗
                selectedEmail.value = email;
                showEmailDetailDialog.value = true;
                console.log('[FRONTEND-DEBUG] 邮件详情弹窗已显示，开始获取详细内容...');

                // 使用超时机制获取邮件详情
                let detailResult = null;
                try {
                    // 创建超时Promise
                    const timeoutPromise = new Promise((_, reject) => {
                        setTimeout(() => reject(new Error('获取邮件详情超时')), 15000); // 15秒超时
                    });

                    // 创建API调用Promise
                    console.log('[FRONTEND-DEBUG] 调用API获取邮件详情...');
                    const apiPromise = callPyAPI('get_email_detail', email.id);

                    // 使用Promise.race来实现超时
                    detailResult = await Promise.race([apiPromise, timeoutPromise]);
                    console.log('[FRONTEND-DEBUG] API响应:', detailResult);

                    if (detailResult && detailResult.success && detailResult.email) {
                        console.log('[FRONTEND-DEBUG] 成功获取邮件详情，更新显示内容');
                        selectedEmail.value = detailResult.email;

                        // 更新邮件列表中的数据
                        const emailIndex = emails.value.findIndex(e => e.id === email.id);
                        if (emailIndex !== -1) {
                            emails.value[emailIndex].detailed_subject = detailResult.email.subject;
                            emails.value[emailIndex].detailed_body = detailResult.email.html_body || detailResult.email.body;
                        }
                    } else {
                        console.warn('[FRONTEND-WARN] API返回失败或无数据:', detailResult);
                        // 保持使用原始邮件数据，但添加提示
                        if (selectedEmail.value.body === '(无内容)' || !selectedEmail.value.body) {
                            selectedEmail.value.body = '邮件详情获取失败，请稍后重试';
                        }
                    }
                } catch (timeoutError) {
                    console.warn('[FRONTEND-WARN] 获取邮件详情超时或失败:', timeoutError.message);
                    // 超时时显示友好提示
                    if (selectedEmail.value.body === '(无内容)' || !selectedEmail.value.body) {
                        selectedEmail.value.body = '邮件详情获取超时，请稍后重试或检查网络连接';
                    }
                }

                // 标记为已读
                if (!email.read) {
                    console.log('[FRONTEND-DEBUG] 标记邮件为已读');
                    email.read = true;
                    // 这里可以调用 API 更新已读状态
                }
            } catch (error) {
                console.error('[FRONTEND-EXCEPTION] 选择邮件异常:', error);
                console.error('[FRONTEND-EXCEPTION] 异常类型:', typeof error);
                console.error('[FRONTEND-EXCEPTION] 异常详情:', error);
                console.error('[FRONTEND-EXCEPTION] 异常堆栈:', error.stack);
                // 出错时使用原始邮件数据
                selectedEmail.value = email;
                ElMessage.error('获取邮件详情失败: ' + error.message);

                // 即使出错也显示弹窗
                showEmailDetailDialog.value = true;
            }
        };

        // 删除邮件
        const deleteEmail = async (emailId) => {
            try {
                await ElMessageBox.confirm(
                    '确定要删除这封邮件吗？删除后无法恢复。',
                    '确认删除',
                    {
                        confirmButtonText: '确定删除',
                        cancelButtonText: '取消',
                        type: 'warning',
                        dangerouslyUseHTMLString: false
                    }
                );

                // 显示删除中的提示
                const loadingMessage = ElMessage({
                    message: '正在删除邮件...',
                    type: 'info',
                    duration: 0,
                    showClose: false
                });

                try {
                    console.log(`[FRONTEND-DEBUG] 开始删除邮件 ID: ${emailId}`);
                    const result = await callPyAPI('delete_email', emailId);
                    console.log(`[FRONTEND-DEBUG] 删除邮件响应:`, result);

                    // 关闭加载提示
                    loadingMessage.close();

                    if (result.success) {
                        // 从邮件列表中移除
                        emails.value = emails.value.filter(email => email.id !== emailId);

                        // 如果当前选中的邮件被删除，关闭详情弹窗
                        if (selectedEmail.value && selectedEmail.value.id === emailId) {
                            selectedEmail.value = null;
                            showEmailDetailDialog.value = false;
                        }

                        // 显示成功消息
                        const method = result.method || 'unknown';
                        if (method === 'api_delete') {
                            ElMessage.success('✅ 邮件删除成功（已从服务器删除）');
                        } else if (method === 'local_only') {
                            ElMessage.success('✅ 邮件删除成功（仅本地删除）');
                        } else {
                            ElMessage.success('✅ 邮件删除成功');
                        }

                        console.log(`[FRONTEND-DEBUG] 邮件删除成功，方法: ${method}`);
                    } else {
                        console.error(`[FRONTEND-ERROR] 删除邮件失败:`, result.error);
                        ElMessage.error(`❌ 删除邮件失败: ${result.error || '未知错误'}`);
                    }
                } catch (apiError) {
                    // 关闭加载提示
                    loadingMessage.close();
                    console.error(`[FRONTEND-ERROR] 删除邮件API调用异常:`, apiError);
                    ElMessage.error(`❌ 删除邮件失败: ${apiError.message || '网络错误'}`);
                }
            } catch (error) {
                if (error !== 'cancel') {
                    console.error(`[FRONTEND-ERROR] 删除邮件异常:`, error);
                    ElMessage.error('❌ 删除邮件失败');
                }
            }
        };

        // 从弹窗中删除邮件
        const deleteEmailFromDialog = async () => {
            if (!selectedEmail.value) {
                ElMessage.warning('没有选中的邮件');
                return;
            }

            try {
                console.log(`[FRONTEND-DEBUG] 从弹窗删除邮件:`, selectedEmail.value);
                await deleteEmail(selectedEmail.value.id);
                // 删除成功后，弹窗会在deleteEmail函数中自动关闭
            } catch (error) {
                console.error(`[FRONTEND-ERROR] 从弹窗删除邮件失败:`, error);
                // 错误处理已在deleteEmail函数中完成
            }
        };

        // 复制邮箱地址
        const copyEmail = async () => {
            if (!currentEmail.value || !currentEmail.value.address) {
                ElMessage.error('没有可复制的邮箱地址');
                return;
            }

            // 调试输出
            console.log('复制邮箱:', currentEmail.value);
            console.log('邮箱地址:', currentEmail.value.address);

            // 添加复制动画效果
            const emailElement = document.querySelector('.email-address');
            if (emailElement) {
                emailElement.classList.add('copying');
                setTimeout(() => {
                    emailElement.classList.remove('copying');
                }, 300);
            }

            const emailAddress = currentEmail.value.address;

            try {
                await navigator.clipboard.writeText(emailAddress);
                ElMessage({
                    message: `📋 邮箱地址 ${emailAddress} 已复制到剪贴板`,
                    type: 'success',
                    duration: 2000,
                    showClose: true,
                    customClass: 'copy-success-message'
                });
            } catch (error) {
                // 备用复制方法
                const textArea = document.createElement('textarea');
                textArea.value = emailAddress;
                textArea.style.position = 'fixed';
                textArea.style.opacity = '0';
                textArea.style.left = '-9999px';
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                ElMessage({
                    message: `📋 邮箱地址 ${emailAddress} 已复制到剪贴板`,
                    type: 'success',
                    duration: 2000,
                    showClose: true,
                    customClass: 'copy-success-message'
                });
            }
        };

        // 复制任意文本
        const copyText = async (text) => {
            if (!text || text.trim() === '') {
                ElMessage.warning('没有可复制的内容');
                return;
            }

            try {
                await navigator.clipboard.writeText(text);
                ElMessage({
                    message: `📋 内容已复制到剪贴板`,
                    type: 'success',
                    duration: 1500,
                    showClose: true,
                    customClass: 'copy-success-message'
                });
            } catch (error) {
                // 备用复制方法
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.opacity = '0';
                textArea.style.left = '-9999px';
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                ElMessage({
                    message: `📋 内容已复制到剪贴板`,
                    type: 'success',
                    duration: 1500,
                    showClose: true,
                    customClass: 'copy-success-message'
                });
            }
        };

        // 复制邮件内容（仅复制正文内容）
        const copyEmailContent = async () => {
            if (!selectedEmail.value) {
                ElMessage.warning('没有选中的邮件');
                return;
            }

            // 获取邮件内容（优先HTML，然后纯文本）
            let content = '';
            if (selectedEmail.value.html_body) {
                // 如果有HTML内容，提取纯文本
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = selectedEmail.value.html_body;
                content = tempDiv.textContent || tempDiv.innerText || '';
            } else if (selectedEmail.value.body) {
                content = selectedEmail.value.body;
            }

            if (!content || content.trim() === '' || content === '(无内容)') {
                ElMessage.warning('邮件内容为空，无法复制');
                return;
            }

            // 清理内容（去除多余的换行和空格）
            content = content.trim();

            try {
                await navigator.clipboard.writeText(content);

                // 显示复制成功状态
                contentCopied.value = true;
                setTimeout(() => {
                    contentCopied.value = false;
                }, 2000);

                ElMessage({
                    message: `📋 邮件正文内容已复制到剪贴板`,
                    type: 'success',
                    duration: 2000,
                    showClose: true,
                    customClass: 'copy-success-message'
                });
            } catch (error) {
                // 备用复制方法
                const textArea = document.createElement('textarea');
                textArea.value = content;
                textArea.style.position = 'fixed';
                textArea.style.opacity = '0';
                textArea.style.left = '-9999px';
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                // 显示复制成功状态
                contentCopied.value = true;
                setTimeout(() => {
                    contentCopied.value = false;
                }, 2000);

                ElMessage({
                    message: `📋 邮件正文内容已复制到剪贴板`,
                    type: 'success',
                    duration: 2000,
                    showClose: true,
                    customClass: 'copy-success-message'
                });
            }
        };

        // 存储选中的文本（在点击按钮前保存）
        let storedSelectedText = '';

        // 监听文本选择变化
        const handleSelectionChange = () => {
            const selection = window.getSelection();
            if (selection && selection.toString().trim()) {
                storedSelectedText = selection.toString().trim();
                console.log('🔍 文本选择变化，已保存:', storedSelectedText);
            }
        };

        // 添加选择变化监听器
        document.addEventListener('selectionchange', handleSelectionChange);

        // 复制选中的文本
        const copySelectedText = async (event) => {
            // 阻止默认行为和事件冒泡
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            // 优先使用存储的选中文本
            let selectedText = storedSelectedText;

            // 如果没有存储的文本，尝试实时获取
            if (!selectedText) {
                const selection = window.getSelection();
                if (selection && selection.toString()) {
                    selectedText = selection.toString().trim();
                }
            }

            console.log('🔍 准备复制的文本:', selectedText);
            console.log('🔍 文本长度:', selectedText.length);

            if (!selectedText || selectedText === '') {
                ElMessage.warning('请先用鼠标选中要复制的文本');
                return;
            }

            try {
                await navigator.clipboard.writeText(selectedText);

                // 显示复制成功状态
                selectedTextCopied.value = true;
                setTimeout(() => {
                    selectedTextCopied.value = false;
                }, 2000);

                ElMessage({
                    message: `📋 选中文本已复制到剪贴板: "${selectedText.length > 20 ? selectedText.substring(0, 20) + '...' : selectedText}"`,
                    type: 'success',
                    duration: 2000,
                    showClose: true,
                    customClass: 'copy-success-message'
                });

                // 清空存储的文本
                storedSelectedText = '';
            } catch (error) {
                console.error('复制失败:', error);
                // 备用复制方法
                const textArea = document.createElement('textarea');
                textArea.value = selectedText;
                textArea.style.position = 'fixed';
                textArea.style.opacity = '0';
                textArea.style.left = '-9999px';
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                // 显示复制成功状态
                selectedTextCopied.value = true;
                setTimeout(() => {
                    selectedTextCopied.value = false;
                }, 2000);

                ElMessage({
                    message: `📋 选中文本已复制到剪贴板: "${selectedText.length > 20 ? selectedText.substring(0, 20) + '...' : selectedText}"`,
                    type: 'success',
                    duration: 2000,
                    showClose: true,
                    customClass: 'copy-success-message'
                });

                // 清空存储的文本
                storedSelectedText = '';
            }
        };

        // 设置 PIN 码
        const setPinCode = async () => {
            if (!pinCode.value.trim()) {
                ElMessage.warning('请输入 PIN 码');
                return;
            }

            try {
                const result = await callPyAPI('set_pin', pinCode.value);

                if (result.success) {
                    ElMessage.success('PIN 码设置成功');

                    // 如果返回了epin，可以在控制台显示用于调试
                    if (result.epin) {
                        console.log('🔐 epin已设置:', result.epin);
                    }
                } else {
                    ElMessage.error(result.error || '设置 PIN 码失败');
                }
            } catch (error) {
                ElMessage.error('设置 PIN 码失败');
            }
        };

        // 验证 PIN 码
        const verifyPin = async () => {
            if (!inputPin.value.trim()) {
                ElMessage.warning('请输入 PIN 码');
                return;
            }

            verifyingPin.value = true;
            try {
                const result = await callPyAPI('verify_pin', inputPin.value);

                if (result.success) {
                    showPinDialog.value = false;
                    inputPin.value = 'tv81***/-+/sb.38'; // 重置为默认PIN码
                    ElMessage.success('PIN 码验证成功');

                    // 如果有等待访问的邮箱，现在可以访问了
                    if (pendingEmailAccess.value) {
                        await accessProtectedEmail(pendingEmailAccess.value);
                        pendingEmailAccess.value = null;
                    }
                } else {
                    ElMessage.error('PIN 码错误，请重新输入');
                    inputPin.value = 'tv81***/-+/sb.38'; // 重置为默认PIN码
                }
            } catch (error) {
                ElMessage.error('验证 PIN 码失败');
            } finally {
                verifyingPin.value = false;
            }
        };

        // 取消PIN码验证
        const cancelPinVerification = () => {
            showPinDialog.value = false;
            inputPin.value = 'tv81***/-+/sb.38'; // 重置为默认PIN码
            pendingEmailAccess.value = null;
            ElMessage.info('已取消访问');
        };

        // 检查邮箱是否需要PIN码保护
        const checkEmailPinProtection = async (emailAddress) => {
            try {
                // 调用后端API检查邮箱是否设置了PIN码
                const result = await callPyAPI('check_email_pin', { email_address: emailAddress });
                return result.has_pin || false;
            } catch (error) {
                console.error('检查PIN码保护状态失败:', error);
                return false;
            }
        };

        // 访问受保护的邮箱
        const accessProtectedEmail = async (emailAddress) => {
            try {
                // 访问邮箱时跳过PIN检查，因为已经验证过了
                await refreshEmails(true);
                ElMessage.success('邮箱访问成功');
            } catch (error) {
                ElMessage.error('访问邮箱失败');
            }
        };

        // 尝试访问邮箱（带PIN码检查）
        const tryAccessEmail = async (emailAddress) => {
            const needsPin = await checkEmailPinProtection(emailAddress);

            if (needsPin) {
                // 需要PIN码验证
                pendingEmailAccess.value = emailAddress;
                inputPin.value = 'tv81***/-+/sb.38'; // 设置默认PIN码
                showPinDialog.value = true;
                ElMessage.info('此邮箱已设置PIN码保护，请输入PIN码');
            } else {
                // 直接访问
                await accessProtectedEmail(emailAddress);
            }
        };

        // 测试PIN码保护功能
        const testPinProtection = async () => {
            if (!currentEmail.value) {
                ElMessage.warning('请先生成邮箱地址');
                return;
            }

            const emailAddress = currentEmail.value.address || currentEmail.value;
            await tryAccessEmail(emailAddress);
        };

        // 测试PIN码访问邮件（尝试不同epin格式）
        const testPinAccess = async () => {
            if (!currentEmail.value) {
                ElMessage.warning('请先生成邮箱地址');
                return;
            }

            if (!pinCode.value.trim()) {
                ElMessage.warning('请先设置PIN码');
                return;
            }

            const emailAddress = currentEmail.value.address || currentEmail.value;

            try {
                ElMessage.info('正在测试不同的PIN码格式...');

                const result = await callPyAPI('test_pin_access', {
                    email_address: emailAddress,
                    pin: pinCode.value
                });

                if (result.success) {
                    console.log('🔍 PIN码访问测试结果:', result);

                    // 显示测试结果
                    let message = `测试完成！\n邮箱: ${emailAddress}\nPIN: ${pinCode.value}\n\n`;

                    result.results.forEach(r => {
                        message += `格式${r.format_index}: ${r.epin}\n`;
                        message += `  结果: ${r.success ? `成功获取${r.email_count}封邮件` : '失败'}\n`;
                    });

                    // 找到获取到最多邮件的格式
                    const bestResult = result.results.reduce((best, current) =>
                        current.email_count > best.email_count ? current : best
                    );

                    if (bestResult.email_count > 0) {
                        message += `\n最佳格式: 格式${bestResult.format_index} (${bestResult.email_count}封邮件)`;
                        ElMessage.success(`找到有效的PIN码格式！获取到${bestResult.email_count}封邮件`);
                    } else {
                        ElMessage.warning('所有格式都未获取到邮件');
                    }

                    // 在控制台显示详细结果
                    console.log(message);

                } else {
                    ElMessage.error(result.error || '测试失败');
                }
            } catch (error) {
                console.error('测试PIN码访问失败:', error);
                ElMessage.error('测试失败: ' + error.message);
            }
        };

        // 发送邮件
        const sendEmail = async () => {
            if (!composeForm.to.trim()) {
                ElMessage.warning('请输入收件人邮箱地址');
                return;
            }

            if (!composeForm.subject.trim()) {
                ElMessage.warning('请输入邮件主题');
                return;
            }

            sending.value = true;
            try {
                // 这里应该调用发送邮件的 API
                // 由于 TempMail.Plus 可能不支持发送，这里只是模拟
                await new Promise(resolve => setTimeout(resolve, 2000));

                showComposeDialog.value = false;
                composeForm.to = '';
                composeForm.subject = '';
                composeForm.body = '';

                ElMessage.success('邮件发送成功');
            } catch (error) {
                ElMessage.error('发送邮件失败');
            } finally {
                sending.value = false;
            }
        };

        // 标记所有邮件为已读
        const markAllAsRead = () => {
            emails.value.forEach(email => {
                email.read = true;
            });
            ElMessage.success('所有邮件已标记为已读');
        };

        // 销毁邮箱
        const destroyMailbox = async () => {
            try {
                const result = await callPyAPI('destroy_mailbox');

                if (result.success) {
                    currentEmail.value = null;
                    emails.value = [];
                    selectedEmail.value = null;
                    showDestroyDialog.value = false;
                    ElMessage.success('邮箱已销毁');
                } else {
                    ElMessage.error(result.error || '销毁邮箱失败');
                }
            } catch (error) {
                ElMessage.error('销毁邮箱失败');
            }
        };

        // 清空所有邮件
        const clearAllEmails = async () => {
            try {
                await ElMessageBox.confirm(
                    '确定要清空当前邮箱的所有邮件吗？此操作将同步删除官网上的邮件，无法恢复！',
                    '确认清空邮件',
                    {
                        confirmButtonText: '确定清空',
                        cancelButtonText: '取消',
                        type: 'warning',
                        dangerouslyUseHTMLString: true
                    }
                );

                const loadingMessage = ElMessage({
                    message: '正在清空邮件，请稍候...',
                    type: 'info',
                    duration: 0,
                    showClose: false
                });

                const result = await callPyAPI('clear_all_emails');

                loadingMessage.close();

                if (result.success) {
                    const { deleted_count, failed_count, total_count } = result;

                    // 显示清空结果消息
                    if (failed_count > 0) {
                        ElMessage({
                            message: `邮件清空完成！成功删除 ${deleted_count} 封邮件，${failed_count} 封删除失败`,
                            type: 'warning',
                            duration: 4000,
                            showClose: true
                        });
                    } else {
                        ElMessage({
                            message: `✅ 邮件清空成功！共删除 ${deleted_count} 封邮件`,
                            type: 'success',
                            duration: 3000,
                            showClose: true
                        });
                    }

                    // 重新获取邮件列表以确保与服务器同步
                    console.log('🔄 清空邮件成功，重新获取邮件列表...');
                    selectedEmail.value = null;

                    // 延迟一下再刷新，确保服务器端处理完成
                    setTimeout(async () => {
                        try {
                            await refreshEmails(true); // 跳过PIN检查，因为已经验证过了
                            console.log('✅ 邮件列表已刷新');
                        } catch (error) {
                            console.error('❌ 刷新邮件列表失败:', error);
                            // 如果刷新失败，至少清空本地列表
                            emails.value = [];
                        }
                    }, 1000);

                } else {
                    ElMessage.error(result.error || '清空邮件失败');
                }
            } catch (error) {
                if (error !== 'cancel') {
                    ElMessage.error('清空邮件失败: ' + error.message);
                }
            }
        };

        // 获取可用域名
        const loadDomains = async () => {
            try {
                const result = await callPyAPI('get_domains');

                if (result.success) {
                    availableDomains.value = result.domains || [];
                }
            } catch (error) {
                console.error('加载域名失败:', error);
                // 使用默认域名
                availableDomains.value = [
                    'mailto.plus',
                    'fexpost.com',
                    'fexbox.org',
                    'mailbox.in.ua',
                    'rover.info'
                ];
            }
        };

        // 自动刷新邮件
        const startAutoRefresh = () => {
            setInterval(() => {
                if (currentEmail.value && !refreshing.value) {
                    refreshEmails();
                }
            }, 30000); // 每30秒刷新一次
        };

        // 组件挂载时执行
        onMounted(async () => {
            console.log('[INIT] 应用初始化开始');
            console.log('[INIT] 当前协议:', window.location.protocol);
            console.log('[INIT] pywebview 可用:', !!(window.pywebview && window.pywebview.api));

            // 如果在pywebview环境中，跳过HTTP API检测
            if (window.pywebview && window.pywebview.api) {
                console.log('[INIT] 检测到pywebview环境，跳过HTTP API检测');
            } else {
                console.log('[INIT] 非pywebview环境，检测HTTP API端口');
                const apiConnected = await detectAPIPort();
                if (!apiConnected) {
                    console.error('[INIT] HTTP API连接失败，停止初始化');
                    return; // 如果无法连接API，停止初始化
                }
            }

            console.log('[INIT] 开始加载域名和初始化功能');
            await loadDomains();
            startAutoRefresh();

            // 尝试获取当前邮箱
            try {
                console.log('[INIT] 尝试获取当前邮箱');
                const result = await callPyAPI('get_current_email');
                if (result.success) {
                    currentEmail.value = result.email;
                    refreshEmails();
                    console.log('[INIT] 当前邮箱获取成功');
                } else {
                    console.log('[INIT] 没有当前邮箱');
                }
            } catch (error) {
                console.log('[INIT] 获取当前邮箱失败:', error);
            }

            console.log('[INIT] 应用初始化完成');
        });

        // 使用直接输入的邮箱地址
        const useDirectEmail = async () => {
            // 使用新的输入方式：前缀 + 域名
            const prefix = emailPrefix.value.trim();
            const domain = selectedDomainForDirect.value;

            if (!prefix) {
                ElMessage.error('请输入邮箱前缀');
                return;
            }

            if (!domain) {
                ElMessage.error('请选择域名');
                return;
            }

            // 构建完整邮箱地址
            const emailAddress = `${prefix}@${domain}`;

            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(emailAddress)) {
                ElMessage.error('邮箱地址格式无效');
                return;
            }

            try {
                // 调用后端接口设置当前邮箱，并传递域名和时效信息
                const requestData = {
                    email_address: emailAddress,
                    domain: domain,
                    duration: selectedDuration.value,
                    is_direct_input: true
                };

                console.log('🔧 设置邮箱请求数据:', requestData);

                const result = await callPyAPI('set_current_email_with_info', requestData);

                if (result && result.success) {
                    // 设置前端当前邮箱
                    currentEmail.value = result.email;

                    console.log('✅ 邮箱设置成功:', result.email);

                    // 清空输入框和邮件列表
                    emailPrefix.value = '';
                    emails.value = [];
                    selectedEmail.value = null;

                    ElMessage.success(`已设置邮箱: ${emailAddress}`);
                    ElMessage.info('💡 如果此邮箱在官网设置了PIN码，请在下方设置相同的PIN码后刷新邮件');
                } else {
                    ElMessage.error(result?.error || '设置邮箱失败');
                }
            } catch (error) {
                console.error('设置邮箱失败:', error);
                ElMessage.error('设置邮箱失败: ' + error.message);
            }
        };

        // 返回所有需要在模板中使用的数据和方法
        return {
            // 数据
            currentEmail,
            emails,
            selectedEmail,
            availableDomains,
            selectedDomain,
            selectedDuration,
            pinCode,
            inputPin,
            directEmailInput,
            emailPrefix,
            selectedDomainForDirect,
            showCustomEmailDialog,
            customEmailForm,
            loading,
            refreshing,
            sending,
            showComposeDialog,
            showDestroyDialog,
            showPinDialog,
            showEmailDetailDialog,
            verifyingPin,
            pendingEmailAccess,
            composeForm,
            contentCopied,
            selectedTextCopied,

            // 计算属性
            unreadCount,
            customEmailPreview,

            // 方法
            formatTime,
            getEmailPreview,
            getEmailSubject,
            fetchEmailDetails,
            formatEmailBody,
            getFullEmailAddress,
            generateNewEmail,
            generateCustomEmail,
            useDirectEmail,
            refreshEmails,
            selectEmail,
            deleteEmail,
            deleteEmailFromDialog,
            copyEmail,
            copyText,
            copyEmailContent,
            copySelectedText,
            setPinCode,
            verifyPin,
            cancelPinVerification,
            checkEmailPinProtection,
            tryAccessEmail,
            testPinProtection,
            testPinAccess,
            sendEmail,
            markAllAsRead,
            destroyMailbox,
            clearAllEmails,
            detectAPIPort
        };
    }
});

// 使用 Element Plus
app.use(ElementPlus);

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
}

// 挂载应用
app.mount('#app');
