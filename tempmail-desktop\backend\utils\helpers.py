#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块
提供各种辅助功能
"""

import os
import re
import json
import hashlib
import random
import string
from datetime import datetime, timedelta
from urllib.parse import urlparse

def generate_random_string(length=8, include_digits=True, include_uppercase=False):
    """生成随机字符串"""
    chars = string.ascii_lowercase
    if include_digits:
        chars += string.digits
    if include_uppercase:
        chars += string.ascii_uppercase
    
    return ''.join(random.choices(chars, k=length))

def validate_email(email):
    """验证邮箱地址格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def sanitize_html(html_content):
    """清理 HTML 内容，移除危险标签"""
    if not html_content:
        return ''
    
    # 简单的 HTML 清理，移除脚本和样式标签
    html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
    html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
    
    # 移除危险属性
    html_content = re.sub(r'on\w+="[^"]*"', '', html_content, flags=re.IGNORECASE)
    html_content = re.sub(r"on\w+='[^']*'", '', html_content, flags=re.IGNORECASE)
    
    return html_content

def extract_text_from_html(html_content):
    """从 HTML 中提取纯文本"""
    if not html_content:
        return ''
    
    # 移除 HTML 标签
    text = re.sub(r'<[^>]+>', '', html_content)
    
    # 解码 HTML 实体
    text = text.replace('&amp;', '&')
    text = text.replace('&lt;', '<')
    text = text.replace('&gt;', '>')
    text = text.replace('&quot;', '"')
    text = text.replace('&#39;', "'")
    text = text.replace('&nbsp;', ' ')
    
    # 清理多余的空白字符
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def format_file_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def calculate_md5(text):
    """计算文本的 MD5 哈希值"""
    return hashlib.md5(text.encode('utf-8')).hexdigest()

def is_expired(expire_time):
    """检查是否已过期"""
    if isinstance(expire_time, str):
        expire_time = datetime.fromisoformat(expire_time.replace('Z', '+00:00'))
    
    return datetime.now() > expire_time

def time_until_expiry(expire_time):
    """计算距离过期还有多长时间"""
    if isinstance(expire_time, str):
        expire_time = datetime.fromisoformat(expire_time.replace('Z', '+00:00'))
    
    delta = expire_time - datetime.now()
    
    if delta.total_seconds() <= 0:
        return "已过期"
    
    days = delta.days
    hours, remainder = divmod(delta.seconds, 3600)
    minutes, _ = divmod(remainder, 60)
    
    if days > 0:
        return f"{days}天{hours}小时"
    elif hours > 0:
        return f"{hours}小时{minutes}分钟"
    else:
        return f"{minutes}分钟"

def safe_filename(filename):
    """生成安全的文件名"""
    # 移除或替换不安全的字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # 限制长度
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename

def parse_email_address(email_string):
    """解析邮箱地址字符串"""
    # <AUTHOR> <EMAIL>" 格式
    match = re.match(r'^(.+?)\s*<(.+?)>$', email_string.strip())
    if match:
        name = match.group(1).strip().strip('"\'')
        email = match.group(2).strip()
        return {'name': name, 'email': email}
    else:
        # 纯邮箱地址
        email = email_string.strip()
        return {'name': '', 'email': email}

def format_email_address(name, email):
    """格式化邮箱地址"""
    if name:
        return f'"{name}" <{email}>'
    else:
        return email

def get_domain_from_email(email):
    """从邮箱地址中提取域名"""
    if '@' in email:
        return email.split('@')[1].lower()
    return ''

def is_valid_domain(domain):
    """验证域名格式"""
    pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    return re.match(pattern, domain) is not None

def create_directory_if_not_exists(directory):
    """如果目录不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)

def read_json_file(file_path, default=None):
    """读取 JSON 文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (FileNotFoundError, json.JSONDecodeError):
        return default or {}

def write_json_file(file_path, data):
    """写入 JSON 文件"""
    create_directory_if_not_exists(os.path.dirname(file_path))
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def get_user_agent():
    """获取用户代理字符串"""
    return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'

def log_error(error, context=''):
    """记录错误日志"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_message = f"[{timestamp}] ERROR: {error}"
    if context:
        log_message += f" (Context: {context})"
    
    print(log_message)
    
    # 可以在这里添加写入日志文件的逻辑
    log_dir = 'logs'
    create_directory_if_not_exists(log_dir)
    
    log_file = os.path.join(log_dir, f"error_{datetime.now().strftime('%Y%m%d')}.log")
    try:
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')
    except Exception:
        pass  # 忽略日志写入错误

def truncate_text(text, max_length=100, suffix='...'):
    """截断文本"""
    if not text:
        return ''
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def normalize_whitespace(text):
    """规范化空白字符"""
    if not text:
        return ''
    
    # 将多个空白字符替换为单个空格
    text = re.sub(r'\s+', ' ', text)
    
    # 移除首尾空白
    return text.strip()

def is_url(text):
    """检查文本是否为有效的 URL"""
    try:
        result = urlparse(text)
        return all([result.scheme, result.netloc])
    except Exception:
        return False

def get_file_extension(filename):
    """获取文件扩展名"""
    return os.path.splitext(filename)[1].lower()

def is_image_file(filename):
    """检查是否为图片文件"""
    image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'}
    return get_file_extension(filename) in image_extensions

def generate_unique_id():
    """生成唯一 ID"""
    timestamp = str(int(datetime.now().timestamp() * 1000))
    random_part = generate_random_string(6, include_digits=True)
    return f"{timestamp}_{random_part}"
