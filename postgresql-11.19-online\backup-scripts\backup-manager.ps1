# PostgreSQL 11.19 在线版备份管理脚本
# 版本: 1.0
# 作者: AI Assistant
# 日期: 2025-07-12

param(
    [string]$Action = "menu",
    [string]$BackupPath = "",
    [int]$KeepDays = 7
)

# 全局变量
$BackupDir = ".\backups"

function Show-Menu {
    Clear-Host
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "PostgreSQL 11.19 在线版备份管理器" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "1. 创建完整备份" -ForegroundColor Green
    Write-Host "2. 恢复备份" -ForegroundColor Yellow
    Write-Host "3. 列出所有备份" -ForegroundColor White
    Write-Host "4. 删除旧备份" -ForegroundColor Red
    Write-Host "5. 验证备份" -ForegroundColor Magenta
    Write-Host "6. 修复pgAdmin权限" -ForegroundColor Blue
    Write-Host "7. 退出" -ForegroundColor Gray
    Write-Host ""
    Write-Host "请选择操作 (1-7): " -NoNewline -ForegroundColor Cyan
}

function Get-BackupList {
    if (!(Test-Path $BackupDir)) {
        Write-Host "备份目录不存在: $BackupDir" -ForegroundColor Yellow
        return @()
    }
    
    $backups = Get-ChildItem -Path $BackupDir -Directory | Sort-Object Name -Descending
    return $backups
}

function Show-BackupList {
    $backups = Get-BackupList
    
    if ($backups.Count -eq 0) {
        Write-Host "没有找到备份文件" -ForegroundColor Yellow
        return
    }
    
    Write-Host "`n可用备份:" -ForegroundColor Cyan
    Write-Host "序号`t时间`t`t`t大小`t`t状态" -ForegroundColor White
    Write-Host "----`t----`t`t`t----`t`t----" -ForegroundColor Gray
    
    for ($i = 0; $i -lt $backups.Count; $i++) {
        $backup = $backups[$i]
        $size = (Get-ChildItem -Path $backup.FullName -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
        $sizeStr = "$([math]::Round($size, 2)) MB"
        
        # 检查备份完整性
        $requiredFiles = @("postgres_data_volume.zip", "postgres_logs_volume.zip")
        $isComplete = $true
        foreach ($file in $requiredFiles) {
            if (!(Test-Path (Join-Path $backup.FullName $file))) {
                $isComplete = $false
                break
            }
        }
        
        $status = if ($isComplete) { "完整" } else { "不完整" }
        $statusColor = if ($isComplete) { "Green" } else { "Red" }
        
        Write-Host "$($i+1)`t$($backup.Name)`t$sizeStr`t`t" -NoNewline
        Write-Host $status -ForegroundColor $statusColor
    }
}

function Create-Backup {
    Write-Host "`n开始创建备份..." -ForegroundColor Green
    try {
        & ".\backup-scripts\postgresql-backup-cn.ps1"
        Write-Host "`n备份创建完成!" -ForegroundColor Green
    } catch {
        Write-Host "`n备份创建失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host "`n按任意键继续..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

function Restore-Backup {
    $backups = Get-BackupList
    
    if ($backups.Count -eq 0) {
        Write-Host "`n没有可用的备份文件" -ForegroundColor Yellow
        Write-Host "按任意键继续..." -ForegroundColor Gray
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        return
    }
    
    Show-BackupList
    Write-Host "`n请选择要恢复的备份 (输入序号): " -NoNewline -ForegroundColor Cyan
    $choice = Read-Host
    
    try {
        $index = [int]$choice - 1
        if ($index -ge 0 -and $index -lt $backups.Count) {
            $selectedBackup = $backups[$index]
            Write-Host "`n警告: 恢复操作将完全替换现有数据!" -ForegroundColor Red
            Write-Host "确认恢复备份 '$($selectedBackup.Name)' ? (y/N): " -NoNewline -ForegroundColor Yellow
            $confirm = Read-Host
            
            if ($confirm -eq "y" -or $confirm -eq "Y") {
                Write-Host "`n开始恢复备份..." -ForegroundColor Green
                & ".\backup-scripts\postgresql-restore-cn.ps1" -BackupPath $selectedBackup.FullName
                Write-Host "`n恢复完成!" -ForegroundColor Green
            } else {
                Write-Host "`n恢复操作已取消" -ForegroundColor Yellow
            }
        } else {
            Write-Host "`n无效的选择" -ForegroundColor Red
        }
    } catch {
        Write-Host "`n恢复失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host "`n按任意键继续..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

function Remove-OldBackups {
    Write-Host "`n清理旧备份 (保留最近 $KeepDays 天)..." -ForegroundColor Yellow
    
    $cutoffDate = (Get-Date).AddDays(-$KeepDays)
    $backups = Get-BackupList
    $removedCount = 0
    $totalSize = 0
    
    foreach ($backup in $backups) {
        if ($backup.CreationTime -lt $cutoffDate) {
            $size = (Get-ChildItem -Path $backup.FullName -Recurse | Measure-Object -Property Length -Sum).Sum
            $totalSize += $size
            
            Write-Host "删除备份: $($backup.Name)" -ForegroundColor Red
            Remove-Item -Path $backup.FullName -Recurse -Force
            $removedCount++
        }
    }
    
    if ($removedCount -gt 0) {
        $sizeStr = "$([math]::Round($totalSize / 1MB, 2)) MB"
        Write-Host "`n已删除 $removedCount 个旧备份，释放空间: $sizeStr" -ForegroundColor Green
    } else {
        Write-Host "`n没有需要删除的旧备份" -ForegroundColor Yellow
    }
    
    Write-Host "`n按任意键继续..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

function Verify-Backup {
    $backups = Get-BackupList

    if ($backups.Count -eq 0) {
        Write-Host "`n没有可用的备份文件" -ForegroundColor Yellow
        Write-Host "按任意键继续..." -ForegroundColor Gray
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        return
    }

    Write-Host "`n验证所有备份的完整性..." -ForegroundColor Cyan

    $requiredFiles = @("postgres_data_volume.zip", "postgres_logs_volume.zip")
    $validCount = 0
    $invalidCount = 0

    foreach ($backup in $backups) {
        Write-Host "`n检查备份: $($backup.Name)" -ForegroundColor White
        $isValid = $true
        $missingFiles = @()

        foreach ($file in $requiredFiles) {
            $filePath = Join-Path $backup.FullName $file
            if (Test-Path $filePath) {
                $size = [math]::Round((Get-Item $filePath).Length / 1MB, 2)
                Write-Host "   $file (${size}MB)" -ForegroundColor Green
            } else {
                Write-Host "   $file (缺失)" -ForegroundColor Red
                $missingFiles += $file
                $isValid = $false
            }
        }

        # 检查可选文件
        $optionalFiles = @("postgresql_all_databases.sql.zip", "docker-compose.yml", "postgresql.conf", "pgadmin_data_volume.zip")
        foreach ($file in $optionalFiles) {
            $filePath = Join-Path $backup.FullName $file
            if (Test-Path $filePath) {
                $size = [math]::Round((Get-Item $filePath).Length / 1MB, 2)
                Write-Host "   $file (${size}MB)" -ForegroundColor Cyan
            } else {
                Write-Host "  - $file (可选，缺失)" -ForegroundColor Yellow
            }
        }

        if ($isValid) {
            Write-Host "  状态: 有效" -ForegroundColor Green
            $validCount++
        } else {
            Write-Host "  状态: 无效 (缺失: $($missingFiles -join ', '))" -ForegroundColor Red
            $invalidCount++
        }
    }

    Write-Host "`n验证完成:" -ForegroundColor Cyan
    Write-Host "  有效备份: $validCount" -ForegroundColor Green
    Write-Host "  无效备份: $invalidCount" -ForegroundColor Red

    Write-Host "`n按任意键继续..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

function Fix-PgAdminPermissions {
    Write-Host "`n修复pgAdmin权限..." -ForegroundColor Blue
    try {
        $FixScript = ".\backup-scripts\fix-pgadmin-permissions.ps1"
        if (Test-Path $FixScript) {
            & $FixScript -Restart
            Write-Host "`npgAdmin权限修复完成!" -ForegroundColor Green
        } else {
            Write-Host "`n错误: 找不到权限修复脚本" -ForegroundColor Red
            Write-Host "请确保 $FixScript 文件存在" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "`npgAdmin权限修复失败: $($_.Exception.Message)" -ForegroundColor Red
    }

    Write-Host "`n按任意键继续..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

# 主程序
if ($Action -eq "menu") {
    do {
        Show-Menu
        $choice = Read-Host
        
        switch ($choice) {
            "1" { Create-Backup }
            "2" { Restore-Backup }
            "3" {
                Show-BackupList
                Write-Host "`n按任意键继续..." -ForegroundColor Gray
                $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            }
            "4" { Remove-OldBackups }
            "5" { Verify-Backup }
            "6" { Fix-PgAdminPermissions }
            "7" {
                Write-Host "`n再见!" -ForegroundColor Green
                exit 0
            }
            default {
                Write-Host "`n无效选择，请重试" -ForegroundColor Red
                Start-Sleep -Seconds 1
            }
        }
    } while ($true)
} else {
    # 命令行模式
    switch ($Action.ToLower()) {
        "backup" { 
            & ".\backup-scripts\postgresql-backup-cn.ps1"
        }
        "restore" {
            if ($BackupPath) {
                & ".\backup-scripts\postgresql-restore-cn.ps1" -BackupPath $BackupPath
            } else {
                Write-Host "错误: 恢复操作需要指定 -BackupPath 参数" -ForegroundColor Red
                exit 1
            }
        }
        "list" {
            Show-BackupList
        }
        "cleanup" {
            Remove-OldBackups
        }
        "verify" {
            Verify-Backup
        }
        "fix" {
            Fix-PgAdminPermissions
        }
        default {
            Write-Host "用法:" -ForegroundColor Yellow
            Write-Host "  .\backup-manager.ps1                           # 交互式菜单"
            Write-Host "  .\backup-manager.ps1 -Action backup            # 创建备份"
            Write-Host "  .\backup-manager.ps1 -Action restore -BackupPath path  # 恢复备份"
            Write-Host "  .\backup-manager.ps1 -Action list              # 列出备份"
            Write-Host "  .\backup-manager.ps1 -Action cleanup           # 清理旧备份"
            Write-Host "  .\backup-manager.ps1 -Action verify            # 验证备份"
            Write-Host "  .\backup-manager.ps1 -Action fix               # 修复pgAdmin权限"
        }
    }
}
