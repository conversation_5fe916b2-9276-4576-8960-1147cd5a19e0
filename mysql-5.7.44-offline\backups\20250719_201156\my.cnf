[client]
port = 3306
socket = /usr/local/mysql/mysql.sock
default-character-set = utf8mb4

[mysql]
default-character-set = utf8mb4

[mysqld]
# 基本设置
user = mysql
port = 3306
basedir = /usr/local/mysql
datadir = /usr/local/mysql/data
socket = /usr/local/mysql/mysql.sock
pid-file = /usr/local/mysql/mysql.pid

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# 大小写不敏感设置（重要：表名大小写不敏感）
lower_case_table_names = 1

# 网络设置 - 允许所有IP远程访问
bind-address = 0.0.0.0
skip-name-resolve
back_log = 1024
max_connections = 2000
max_connect_errors = 10000
open_files_limit = 65535
table_open_cache = 4096
table_definition_cache = 2048
max_allowed_packet = 1024M
binlog_cache_size = 4M
max_heap_table_size = 256M
tmp_table_size = 256M

# 查询缓存设置（MySQL 5.7性能优化）
query_cache_size = 64M
query_cache_type = 1
query_cache_limit = 8M

# 排序和分组设置（性能优化）
sort_buffer_size = 32M
join_buffer_size = 32M
thread_cache_size = 64
thread_stack = 256K
read_buffer_size = 8M
read_rnd_buffer_size = 32M

# MyISAM设置（优化）
key_buffer_size = 256M
bulk_insert_buffer_size = 256M

# 连接和超时设置
interactive_timeout = 28800
wait_timeout = 28800
connect_timeout = 60
net_read_timeout = 120
net_write_timeout = 120

# InnoDB设置（高性能优化）
default-storage-engine = INNODB
sql-mode="STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION"

# InnoDB缓冲池设置（重要性能参数）
innodb_buffer_pool_size = 2G
innodb_buffer_pool_instances = 8
innodb_buffer_pool_chunk_size = 128M

# InnoDB日志设置
innodb_log_buffer_size = 64M
innodb_log_file_size = 512M
innodb_log_files_in_group = 2
innodb_flush_log_at_trx_commit = 2

# InnoDB性能优化
innodb_file_per_table = 1
innodb_open_files = 4000
innodb_io_capacity = 2000
innodb_io_capacity_max = 4000
innodb_read_io_threads = 8
innodb_write_io_threads = 8
innodb_purge_threads = 4
innodb_page_cleaners = 4

# InnoDB其他优化
innodb_autoextend_increment = 64
innodb_concurrency_tickets = 5000
innodb_old_blocks_time = 1000
innodb_stats_on_metadata = 0
innodb_adaptive_hash_index = 1
innodb_change_buffering = all
innodb_doublewrite = 1

# NUMA优化（禁用以避免依赖问题）
innodb_numa_interleave = 0

# 日志设置
log-error = /usr/local/mysql/logs/error.log
slow_query_log = 1
slow_query_log_file = /usr/local/mysql/logs/slow.log
long_query_time = 3
log_queries_not_using_indexes = 1

# 二进制日志设置
server-id = 1
log-bin = /usr/local/mysql/logs/mysql-bin
binlog_format = mixed
sync_binlog = 0
expire_logs_days = 10

# 安全设置（禁用SSL，允许远程访问）
skip-external-locking
skip-ssl
skip-grant-tables = 0

# 远程访问设置（允许所有IP访问）
# bind-address已在上面设置为0.0.0.0

# 其他优化设置
transaction_isolation = READ-COMMITTED
concurrent_insert = 2
max_user_connections = 0
skip-name-resolve

# 时区设置
default-time-zone = '+8:00'

# 性能模式优化
performance_schema = ON
performance_schema_max_table_instances = 2000
performance_schema_max_thread_instances = 1000

# 查询优化
optimizer_switch = 'index_merge=on,index_merge_union=on,index_merge_sort_union=on,index_merge_intersection=on'

# 其他性能优化
sync_binlog = 0
innodb_flush_method = O_DIRECT
innodb_lock_wait_timeout = 120

[mysqldump]
quick
max_allowed_packet = 500M

[mysql]
no-auto-rehash

[myisamchk]
key_buffer_size = 8M
sort_buffer_size = 8M
read_buffer = 4M
write_buffer = 4M

[mysqlhotcopy]
interactive-timeout

[mysqld_safe]
log-error = /usr/local/mysql/logs/error.log
pid-file = /usr/local/mysql/mysql.pid
socket = /usr/local/mysql/mysql.sock
