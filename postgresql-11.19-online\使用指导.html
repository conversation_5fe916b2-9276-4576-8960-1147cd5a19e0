<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PostgreSQL 11.19 Docker 使用指导</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .card h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .card h3 {
            color: #34495e;
            margin: 20px 0 10px 0;
            font-size: 1.3em;
        }
        
        .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Consolas', 'Courier New', monospace;
            overflow-x: auto;
            position: relative;
        }
        
        .code-block::before {
            content: "命令行";
            position: absolute;
            top: 5px;
            right: 10px;
            font-size: 0.8em;
            color: #95a5a6;
        }
        
        .sql-block {
            background: #27ae60;
            color: white;
        }
        
        .sql-block::before {
            content: "SQL";
        }
        
        .step {
            background: #ecf0f1;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }
        
        .info-box {
            background: #3498db;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .warning-box {
            background: #f39c12;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .success-box {
            background: #27ae60;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .connection-info {
            background: #f8f9fa;
            border: 2px solid #3498db;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .connection-info h4 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .connection-info ul {
            list-style: none;
        }
        
        .connection-info li {
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .connection-info strong {
            color: #3498db;
            display: inline-block;
            width: 80px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background: #3498db;
            color: white;
        }
        
        tr:nth-child(even) {
            background: #f2f2f2;
        }
        
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐘 PostgreSQL 11.19 Docker</h1>
            <p class="subtitle">基于CentOS 7的PostgreSQL 11.19 Docker镜像使用指导</p>
        </div>

        <div class="card">
            <h2>🚀 快速开始</h2>

            <div class="info-box">
                <strong>💡 两种启动方式：</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>方式一：</strong> 使用Docker Compose（推荐，包含pgAdmin管理界面）</li>
                    <li><strong>方式二：</strong> 直接使用Docker命令</li>
                </ul>
            </div>

            <h3 style="color: #27ae60; margin: 20px 0 15px 0;">🐳 方式一：Docker Compose（推荐）</h3>

            <div class="step">
                <span class="step-number">1</span>
                <strong>一键启动所有服务</strong>
                <div class="code-block">docker-compose up -d</div>
                <div class="success-box" style="margin: 10px 0;">
                    ✅ 自动构建镜像、启动PostgreSQL和pgAdmin、创建数据卷、配置网络
                </div>
            </div>

            <div class="step">
                <span class="step-number">2</span>
                <strong>访问服务</strong>
                <div style="margin: 15px 0;">
                    <div class="connection-info">
                        <h4>PostgreSQL数据库</h4>
                        <ul>
                            <li><strong>主机:</strong> localhost</li>
                            <li><strong>端口:</strong> 3433</li>
                            <li><strong>用户名:</strong> postgres</li>
                            <li><strong>密码:</strong> postgres</li>
                        </ul>
                    </div>
                    <div class="connection-info">
                        <h4>pgAdmin Web管理界面</h4>
                        <ul>
                            <li><strong>地址:</strong> <a href="http://localhost:8080" target="_blank">http://localhost:8080</a></li>
                            <li><strong>邮箱:</strong> <EMAIL></li>
                            <li><strong>密码:</strong> admin123</li>
                        </ul>
                    </div>
                </div>
            </div>

            <h3 style="color: #3498db; margin: 20px 0 15px 0;">⚙️ 方式二：直接Docker命令</h3>

            <div class="step">
                <span class="step-number">1</span>
                <strong>构建Docker镜像</strong>
                <div class="code-block">docker build -t postgresql:11.19-centos7 .</div>
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>启动PostgreSQL容器</strong>

                <h4 style="margin: 15px 0 10px 0; color: #2c3e50;">基本启动（数据不持久化）</h4>
                <div class="code-block">docker run -d --name postgresql-11.19 -p 3433:3433 -e PG_PASSWORD=postgres postgresql:11.19-centos7</div>

                <h4 style="margin: 15px 0 10px 0; color: #2c3e50;">推荐启动（数据持久化 + 完整挂载）</h4>
                <div class="code-block">docker run -d --name postgresql-11.19 \
  -p 3433:3433 \
  -e PG_PASSWORD=postgres \
  -v postgresql-data:/var/lib/postgresql/data \
  -v postgresql-logs:/var/log/postgresql \
  -v ${PWD}/init-scripts:/docker-entrypoint-initdb.d \
  -v ${PWD}/backups:/var/backups/postgresql \
  postgresql:11.19-centos7</div>

                <div class="info-box" style="margin: 15px 0;">
                    <strong>📁 挂载目录说明：</strong>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>/var/lib/postgresql/data</strong> - 数据库数据文件（必须挂载以持久化数据）</li>
                        <li><strong>/var/log/postgresql</strong> - 数据库日志文件</li>
                        <li><strong>/docker-entrypoint-initdb.d</strong> - 初始化脚本目录（.sql文件会自动执行）</li>
                        <li><strong>/var/backups/postgresql</strong> - 备份文件存储目录</li>
                    </ul>
                </div>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>连接数据库</strong>
                <div class="code-block">docker exec -it postgresql-11.19 psql -U postgres -d postgres -p 3433</div>
            </div>
            
            <div class="success-box">
                ✅ 完成！PostgreSQL 11.19 现在运行在端口 3433
            </div>
        </div>

        <div class="card">
            <h2>🔗 连接信息</h2>
            
            <div class="connection-info">
                <h4>数据库连接参数</h4>
                <ul>
                    <li><strong>主机:</strong> localhost (或容器IP)</li>
                    <li><strong>端口:</strong> 3433</li>
                    <li><strong>用户名:</strong> postgres</li>
                    <li><strong>密码:</strong> 通过环境变量PG_PASSWORD设置</li>
                    <li><strong>数据库:</strong> postgres</li>
                </ul>
            </div>
            
            <div class="info-box">
                <strong>💡 提示:</strong> 默认密码为环境变量PG_PASSWORD的值，建议在生产环境中设置强密码
            </div>
        </div>

        <div class="card">
            <h2>🐳 Docker Compose 命令</h2>

            <table>
                <thead>
                    <tr>
                        <th>操作</th>
                        <th>命令</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>启动所有服务</td>
                        <td><code>docker-compose up -d</code></td>
                        <td>后台启动PostgreSQL和pgAdmin</td>
                    </tr>
                    <tr>
                        <td>停止所有服务</td>
                        <td><code>docker-compose down</code></td>
                        <td>停止并删除容器，保留数据卷</td>
                    </tr>
                    <tr>
                        <td>重启服务</td>
                        <td><code>docker-compose restart</code></td>
                        <td>重启所有服务</td>
                    </tr>
                    <tr>
                        <td>查看服务状态</td>
                        <td><code>docker-compose ps</code></td>
                        <td>显示所有服务的运行状态</td>
                    </tr>
                    <tr>
                        <td>查看日志</td>
                        <td><code>docker-compose logs postgresql</code></td>
                        <td>查看PostgreSQL服务日志</td>
                    </tr>
                    <tr>
                        <td>重新构建</td>
                        <td><code>docker-compose build</code></td>
                        <td>重新构建镜像</td>
                    </tr>
                    <tr>
                        <td>完全清理</td>
                        <td><code>docker-compose down -v</code></td>
                        <td>停止服务并删除数据卷（谨慎使用）</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="card">
            <h2>📋 直接Docker命令</h2>
            
            <table>
                <thead>
                    <tr>
                        <th>操作</th>
                        <th>命令</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>构建镜像</td>
                        <td><code>docker build -t postgresql:11.19-centos7 .</code></td>
                        <td>从Dockerfile构建镜像</td>
                    </tr>
                    <tr>
                        <td>启动容器（基本）</td>
                        <td><code>docker run -d --name postgresql-11.19 -p 3433:3433 -e PG_PASSWORD=postgres postgresql:11.19-centos7</code></td>
                        <td>基本启动，数据不持久化</td>
                    </tr>
                    <tr>
                        <td>启动容器（推荐）</td>
                        <td><code>docker run -d --name postgresql-11.19 -p 3433:3433 -e PG_PASSWORD=postgres -v postgresql-data:/var/lib/postgresql/data -v postgresql-logs:/var/log/postgresql -v ${PWD}/init-scripts:/docker-entrypoint-initdb.d -v ${PWD}/backups:/var/backups/postgresql postgresql:11.19-centos7</code></td>
                        <td>完整挂载，数据持久化</td>
                    </tr>
                    <tr>
                        <td>停止容器</td>
                        <td><code>docker stop postgresql-11.19</code></td>
                        <td>停止运行的容器</td>
                    </tr>
                    <tr>
                        <td>启动容器</td>
                        <td><code>docker start postgresql-11.19</code></td>
                        <td>启动已停止的容器</td>
                    </tr>
                    <tr>
                        <td>查看日志</td>
                        <td><code>docker logs postgresql-11.19</code></td>
                        <td>查看容器日志</td>
                    </tr>
                    <tr>
                        <td>连接数据库</td>
                        <td><code>docker exec -it postgresql-11.19 psql -U postgres -p 3433</code></td>
                        <td>进入psql命令行</td>
                    </tr>
                    <tr>
                        <td>删除容器</td>
                        <td><code>docker rm postgresql-11.19</code></td>
                        <td>删除容器（需先停止）</td>
                    </tr>
                    <tr>
                        <td>删除镜像</td>
                        <td><code>docker rmi postgresql:11.19-centos7</code></td>
                        <td>删除镜像</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="card">
            <h2>💾 数据持久化</h2>

            <div class="warning-box">
                <strong>⚠️ 重要提醒：</strong> 不挂载数据卷的话，容器删除后所有数据都会丢失！
            </div>

            <div class="success-box">
                <strong>✅ 系统重启安全：</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>首次启动：</strong> 数据目录为空，执行初始化脚本，创建示例数据</li>
                    <li><strong>重启后：</strong> 数据目录存在，跳过初始化，直接启动，数据完整保留</li>
                    <li><strong>Windows重启：</strong> Docker数据卷会自动保留，无需担心数据丢失</li>
                </ul>
            </div>

            <h3>推荐方式：使用数据卷（完整挂载）</h3>
            <div class="code-block">
# 创建数据卷
docker volume create postgresql-data
docker volume create postgresql-logs

# 完整挂载启动容器
docker run -d --name postgresql-11.19 \
  -p 3433:3433 \
  -e PG_PASSWORD=postgres \
  -v postgresql-data:/var/lib/postgresql/data \
  -v postgresql-logs:/var/log/postgresql \
  -v ${PWD}/init-scripts:/docker-entrypoint-initdb.d \
  -v ${PWD}/backups:/var/backups/postgresql \
  postgresql:11.19-centos7
            </div>

            <h3>基本方式：仅挂载数据目录</h3>
            <div class="code-block">
# 创建数据卷
docker volume create postgres-data

# 使用数据卷启动容器
docker run -d --name postgresql-11.19 \
  -p 3433:3433 \
  -v postgres-data:/var/lib/postgresql/data \
  -e PG_PASSWORD=postgres \
  postgresql:11.19-centos7
            </div>
            
            <h3>使用主机目录（完整挂载）</h3>
            <div class="code-block">
# Windows - 完整挂载（推荐使用相对路径）
docker run -d --name postgresql-11.19 \
  -p 3433:3433 \
  -e PG_PASSWORD=postgres \
  -v postgresql-data:/var/lib/postgresql/data \
  -v postgresql-logs:/var/log/postgresql \
  -v ${PWD}/init-scripts:/docker-entrypoint-initdb.d \
  -v ${PWD}/backups:/var/backups/postgresql \
  postgresql:11.19-centos7

# Windows - 使用绝对路径
docker run -d --name postgresql-11.19 \
  -p 3433:3433 \
  -e PG_PASSWORD=postgres \
  -v D:/postgresql-data:/var/lib/postgresql/data \
  -v D:/postgresql-logs:/var/log/postgresql \
  -v D:/Code/MicrosoftCode/postgresql-11.19-final/init-scripts:/docker-entrypoint-initdb.d \
  -v D:/Code/MicrosoftCode/postgresql-11.19-final/backups:/var/backups/postgresql \
  postgresql:11.19-centos7

# Linux/Mac - 完整挂载
docker run -d --name postgresql-11.19 \
  -p 3433:3433 \
  -e PG_PASSWORD=postgres \
  -v /path/to/postgresql-data:/var/lib/postgresql/data \
  -v /path/to/postgresql-logs:/var/log/postgresql \
  -v ./init-scripts:/docker-entrypoint-initdb.d \
  -v ./backups:/var/backups/postgresql \
  postgresql:11.19-centos7
            </div>

            <div class="info-box">
                <strong>💡 目录说明：</strong>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li><strong>数据目录：</strong> 存储数据库文件，必须挂载</li>
                    <li><strong>日志目录：</strong> 存储PostgreSQL日志文件</li>
                    <li><strong>初始化脚本：</strong> 首次启动时自动执行的SQL脚本</li>
                    <li><strong>备份目录：</strong> 存储数据库备份文件</li>
                </ul>
            </div>
        </div>

        <div class="card">
            <h2>🔧 数据库操作</h2>
            
            <h3>基本SQL操作</h3>
            <div class="code-block sql-block">
-- 查看版本
SELECT version();

-- 列出所有数据库
\l

-- 创建数据库
CREATE DATABASE myapp;

-- 连接到数据库
\c myapp;

-- 创建表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入数据
INSERT INTO users (username, email) VALUES ('admin', '<EMAIL>');

-- 查询数据
SELECT * FROM users;
            </div>
            
            <h3>备份和恢复</h3>
            <div class="code-block">
# 备份单个数据库
docker exec postgresql-11.19 pg_dump -U postgres -d myapp -p 3433 > myapp_backup.sql

# 备份所有数据库
docker exec postgresql-11.19 pg_dumpall -U postgres -p 3433 > all_databases_backup.sql

# 恢复数据库
docker exec -i postgresql-11.19 psql -U postgres -d myapp -p 3433 < myapp_backup.sql
            </div>
        </div>

        <div class="card">
            <h2>🔍 故障排除</h2>

            <div class="info-box">
                <strong>💡 系统重启后的数据安全</strong>
                <p style="margin: 10px 0;">Windows系统重启后，Docker数据卷会自动保留，PostgreSQL会检测到现有数据并跳过初始化，直接启动。您的数据是安全的！</p>
            </div>

            <div class="warning-box">
                <strong>⚠️ 常见问题</strong>
            </div>
            
            <h3>端口被占用</h3>
            <div class="highlight">
                <strong>问题:</strong> 端口3433已被占用<br>
                <strong>解决:</strong> 更换端口或停止占用端口的服务
                <div class="code-block">
# 检查端口占用
netstat -an | findstr 3433

# 使用其他端口
docker run -d --name postgresql-11.19 -p 5432:3433 -e PG_PASSWORD=password postgresql:11.19-centos7
                </div>
            </div>
            
            <h3>容器启动失败</h3>
            <div class="highlight">
                <strong>解决步骤:</strong>
                <div class="code-block">
# 1. 查看容器日志
docker logs postgresql-11.19

# 2. 检查容器状态
docker ps -a

# 3. 重新启动容器
docker restart postgresql-11.19
                </div>
            </div>
            
            <h3>无法连接数据库</h3>
            <div class="highlight">
                <strong>检查清单:</strong>
                <ul>
                    <li>容器是否正在运行: <code>docker ps</code></li>
                    <li>端口是否正确映射</li>
                    <li>密码是否正确</li>
                    <li>防火墙设置</li>
                </ul>
            </div>

            <h3>系统重启后的数据问题</h3>
            <div class="highlight">
                <strong>问题:</strong> 担心Windows重启后数据丢失<br>
                <strong>答案:</strong> 完全不用担心！
                <div class="code-block">
# 检查数据卷是否存在
docker volume ls | findstr postgresql

# 查看启动日志确认跳过初始化
docker-compose logs postgresql

# 验证数据完整性
docker exec postgresql-11.19 psql -U postgres -d sample_db -p 3433 -c "SELECT COUNT(*) FROM users;"
                </div>
                <p><strong>原理:</strong> Docker数据卷独立于容器存在，系统重启不会影响。PostgreSQL启动时会检测数据目录，如果已有数据则跳过初始化。</p>
            </div>
        </div>

        <div class="card">
            <h2>📊 监控和维护</h2>
            
            <h3>查看容器状态</h3>
            <div class="code-block">
# 查看运行中的容器
docker ps

# 查看容器资源使用情况
docker stats postgresql-11.19

# 查看容器详细信息
docker inspect postgresql-11.19
            </div>
            
            <h3>数据库性能监控</h3>
            <div class="code-block sql-block">
-- 查看活动连接
SELECT * FROM pg_stat_activity;

-- 查看数据库大小
SELECT datname, pg_size_pretty(pg_database_size(datname)) 
FROM pg_database;

-- 查看表大小
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) 
FROM pg_tables 
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
            </div>
        </div>

        <div class="card">
            <h2>🎯 生产环境建议</h2>
            
            <div class="info-box">
                <strong>安全建议:</strong>
                <ul>
                    <li>使用强密码</li>
                    <li>限制网络访问</li>
                    <li>定期更新镜像</li>
                    <li>配置SSL连接</li>
                </ul>
            </div>
            
            <div class="info-box">
                <strong>性能建议:</strong>
                <ul>
                    <li>根据需求调整内存限制</li>
                    <li>使用SSD存储</li>
                    <li>定期清理日志</li>
                    <li>监控数据库性能</li>
                </ul>
            </div>
            
            <div class="success-box">
                🎉 恭喜！您已成功部署PostgreSQL 11.19 Docker环境！
            </div>
        </div>
    </div>
</body>
</html>
