<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>kubectl命令使用指导完整教程</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 13px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 机器标识样式 */
        .machine-tag {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            margin: 0 8px 12px 0;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .machine-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .machine-tag:hover::before {
            left: 100%;
        }

        .machine-client {
            background: linear-gradient(135deg, #a55eea 0%, #8e44ad 100%);
            color: white;
        }

        .machine-master {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
        }

        .machine-node {
            background: linear-gradient(135deg, #45b7d1 0%, #96c93d 100%);
            color: white;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: var(--light-surface);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        th,
        td {
            padding: 18px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 命令分类标签 */
        .command-category {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            margin: 0 5px 8px 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .category-basic {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .category-advanced {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
        }

        .category-debug {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }

        .category-config {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
        }

        /* 输出示例样式 */
        .output-example {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 4px solid #48bb78;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.6;
            position: relative;
            overflow-x: auto;
            white-space: pre-line;
            word-break: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
        }

        .output-example::before {
            content: '📤 预期输出';
            position: absolute;
            top: -10px;
            left: 15px;
            background: #48bb78;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }

        .output-example .highlight {
            background: rgba(72, 187, 120, 0.2);
            padding: 2px 4px;
            border-radius: 3px;
        }

        /* 错误示例样式 */
        .error-example {
            background: linear-gradient(135deg, #742a2a 0%, #553c3c 100%);
            color: #fed7d7;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 4px solid #f56565;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 13px;
            line-height: 1.6;
            position: relative;
            overflow-x: auto;
            white-space: pre-line;
            word-break: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
        }

        .error-example::before {
            content: '❌ 错误示例';
            position: absolute;
            top: -10px;
            left: 15px;
            background: #f56565;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 600;
        }

        /* 实践案例样式 */
        .practice-case {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 2px solid var(--primary-color);
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            position: relative;
        }

        .practice-case::before {
            content: '🎯 实践案例';
            position: absolute;
            top: -12px;
            left: 20px;
            background: var(--primary-color);
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        /* 小贴士样式 */
        .tip-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left: 4px solid #ed8936;
            border-radius: 8px;
            padding: 15px 20px;
            margin: 15px 0;
            position: relative;
        }

        .tip-box::before {
            content: '💡';
            position: absolute;
            left: -12px;
            top: 15px;
            background: #ed8936;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .content-wrapper {
                padding: 30px;
            }

            .container {
                padding: 40px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 1001;
                background: var(--primary-color);
                color: white;
                border: none;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                font-size: 20px;
                cursor: pointer;
                box-shadow: var(--shadow-lg);
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
                border-radius: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 16px;
                margin-right: 15px;
            }
        }
    </style>
</head>

<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-terminal"></i> kubectl教程</h2>
            <p>Kubernetes命令行工具完全指南</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#introduction"><i class="fas fa-info-circle"></i>1. kubectl简介</a></li>
                <li><a href="#installation"><i class="fas fa-download"></i>2. 安装配置</a></li>
                <li><a href="#basic-concepts"><i class="fas fa-book"></i>3. 基础概念</a></li>
                <li><a href="#basic-commands"><i class="fas fa-play"></i>4. 基础命令</a></li>
                <li><a href="#resource-management"><i class="fas fa-cubes"></i>5. 资源管理</a></li>
                <li><a href="#pod-operations"><i class="fas fa-box"></i>6. Pod操作</a></li>
                <li><a href="#service-networking"><i class="fas fa-network-wired"></i>7. 服务网络</a></li>
                <li><a href="#config-secrets"><i class="fas fa-key"></i>8. 配置密钥</a></li>
                <li><a href="#monitoring-logs"><i class="fas fa-chart-line"></i>9. 监控日志</a></li>
                <li><a href="#advanced-operations"><i class="fas fa-cogs"></i>10. 高级操作</a></li>
                <li><a href="#troubleshooting"><i class="fas fa-bug"></i>11. 故障排查</a></li>
                <li><a href="#best-practices"><i class="fas fa-star"></i>12. 最佳实践</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-terminal"></i> kubectl命令使用指导完整教程</h1>

                <div class="info-box">
                    <strong><i class="fas fa-info-circle"></i>
                        教程说明：</strong>本教程将从零开始详细介绍kubectl命令的使用方法。kubectl是Kubernetes的命令行工具，是管理Kubernetes集群最重要的工具之一。我们会从最基础的概念开始，逐步深入到高级用法，确保您能够熟练掌握kubectl的各种操作。
                </div>

                <div class="warning-box">
                    <strong><i class="fas fa-exclamation-triangle"></i> 机器标识说明：</strong>
                    <div style="margin-top: 15px;">
                        <span class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</span> -
                        在安装了kubectl的机器上执行<br>
                        <span class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</span> -
                        在Kubernetes Master节点执行<br>
                        <span class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</span> -
                        在Kubernetes Worker节点执行
                    </div>
                </div>

                <section id="introduction">
                    <h2><span class="step-number">1</span>kubectl简介</h2>

                    <h3><i class="fas fa-question-circle"></i> 1.1 什么是kubectl？</h3>
                    <p>kubectl（读作"kube-control"或"kube-c-t-l"）是Kubernetes的官方命令行工具。它是您与Kubernetes集群交互的主要方式，就像是您和Kubernetes集群对话的"翻译官"。
                    </p>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 简单理解kubectl：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>命令行工具：</strong>通过输入命令来操作Kubernetes集群</li>
                            <li><strong>集群管理：</strong>可以创建、查看、修改、删除集群中的各种资源</li>
                            <li><strong>远程操作：</strong>可以在任何安装了kubectl的机器上管理远程集群</li>
                            <li><strong>功能全面：</strong>几乎所有Kubernetes操作都可以通过kubectl完成</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-star"></i> 1.2 kubectl能做什么？</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-cogs"></i> 功能类别</th>
                            <th><i class="fas fa-info-circle"></i> 主要用途</th>
                            <th><i class="fas fa-terminal"></i> 典型命令</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-eye"></i> 查看资源</td>
                            <td>查看集群中的各种资源状态</td>
                            <td><code>kubectl get pods</code></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-plus"></i> 创建资源</td>
                            <td>创建新的应用、服务等</td>
                            <td><code>kubectl create deployment</code></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-edit"></i> 修改资源</td>
                            <td>更新现有资源的配置</td>
                            <td><code>kubectl edit deployment</code></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-trash"></i> 删除资源</td>
                            <td>删除不需要的资源</td>
                            <td><code>kubectl delete pod</code></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-bug"></i> 调试排错</td>
                            <td>查看日志、进入容器调试</td>
                            <td><code>kubectl logs</code></td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-cog"></i> 集群管理</td>
                            <td>管理集群配置和状态</td>
                            <td><code>kubectl cluster-info</code></td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-graduation-cap"></i> 1.3 学习kubectl的重要性</h3>
                    <div class="success-box">
                        <strong><i class="fas fa-trophy"></i> 为什么要学习kubectl？</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>必备技能：</strong>kubectl是Kubernetes运维的基础技能</li>
                            <li><strong>高效管理：</strong>通过命令行可以快速批量操作</li>
                            <li><strong>自动化基础：</strong>脚本化运维的基础工具</li>
                            <li><strong>故障排查：</strong>快速定位和解决问题的利器</li>
                            <li><strong>深入理解：</strong>帮助理解Kubernetes的工作原理</li>
                        </ul>
                    </div>
                </section>

                <section id="installation">
                    <h2><span class="step-number">2</span>安装配置</h2>

                    <h3><i class="fas fa-download"></i> 2.1 安装kubectl</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>
                    <p>首先，我们需要在您的机器上安装kubectl。根据不同的操作系统，安装方法略有不同：</p>

                    <h4><i class="fab fa-linux"></i> 2.1.1 Linux系统安装</h4>
                    <pre><code># 方法1：使用curl下载最新版本
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"

# 设置执行权限
chmod +x kubectl

# 移动到系统路径
sudo mv kubectl /usr/local/bin/

# 方法2：使用包管理器安装（CentOS/RHEL）
cat <<EOF | sudo tee /etc/yum.repos.d/kubernetes.repo
[kubernetes]
name=Kubernetes
baseurl=https://packages.cloud.google.com/yum/repos/kubernetes-el7-\$basearch
enabled=1
gpgcheck=1
repo_gpgcheck=1
gpgkey=https://packages.cloud.google.com/yum/doc/yum-key.gpg https://packages.cloud.google.com/yum/doc/rpm-package-key.gpg
EOF

sudo yum install -y kubectl</code></pre>

                    <h4><i class="fab fa-windows"></i> 2.1.2 Windows系统安装</h4>
                    <pre><code># 使用PowerShell下载
curl.exe -LO "https://dl.k8s.io/release/v1.28.0/bin/windows/amd64/kubectl.exe"

# 或者使用Chocolatey包管理器
choco install kubernetes-cli

# 或者使用Scoop包管理器
scoop install kubectl</code></pre>

                    <h4><i class="fab fa-apple"></i> 2.1.3 macOS系统安装</h4>
                    <pre><code># 使用Homebrew安装（推荐）
brew install kubectl

# 或者使用curl下载
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/darwin/amd64/kubectl"
chmod +x kubectl
sudo mv kubectl /usr/local/bin/</code></pre>

                    <h4><i class="fas fa-check"></i> 2.1.4 验证安装</h4>
                    <pre><code># 检查kubectl版本
kubectl version --client

# 检查kubectl是否在PATH中
which kubectl

# 查看kubectl帮助信息
kubectl --help</code></pre>

                    <div class="output-example">Client Version: version.Info{Major:"1", Minor:"28",
                        GitVersion:"v1.28.0", GitCommit:"855e7c48de7388eb330da0f8d9d2394ee818fb8d",
                        GitTreeState:"clean", BuildDate:"2023-08-15T10:20:30Z", GoVersion:"go1.20.7", Compiler:"gc",
                        Platform:"linux/amd64"}
                        Kustomize Version: v5.0.4-0.20230601165947-6ce0bf390ce3</div>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 安装成功标志：</strong>如果能看到版本信息输出，说明kubectl安装成功！
                    </div>

                    <div class="tip-box">
                        <strong>小贴士：</strong>如果命令找不到，请检查kubectl是否已添加到系统PATH环境变量中。在Linux/macOS中可以使用<code>echo $PATH</code>查看PATH设置。
                    </div>

                    <h3><i class="fas fa-cog"></i> 2.2 配置kubectl</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>
                    <p>安装完kubectl后，需要配置它连接到您的Kubernetes集群。这个配置信息存储在kubeconfig文件中。</p>

                    <h4><i class="fas fa-file-alt"></i> 2.2.1 理解kubeconfig文件</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> kubeconfig文件包含：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>集群信息：</strong>Kubernetes API Server的地址和证书</li>
                            <li><strong>用户信息：</strong>访问集群的用户凭证</li>
                            <li><strong>上下文信息：</strong>将集群和用户信息组合起来</li>
                            <li><strong>当前上下文：</strong>当前使用的集群配置</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-folder"></i> 2.2.2 kubeconfig文件位置</h4>
                    <pre><code># 默认位置
~/.kube/config

# 查看当前使用的kubeconfig文件
echo $KUBECONFIG

# 如果没有设置KUBECONFIG环境变量，kubectl会使用默认位置</code></pre>

                    <h4><i class="fas fa-copy"></i> 2.2.3 获取kubeconfig文件</h4>
                    <p>根据您的集群部署方式，获取kubeconfig的方法不同：</p>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 获取kubeconfig的几种方式：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>自建集群：</strong>从Master节点复制/etc/kubernetes/admin.conf</li>
                            <li><strong>云服务商：</strong>从云控制台下载kubeconfig文件</li>
                            <li><strong>管理员提供：</strong>由集群管理员提供配置文件</li>
                            <li><strong>工具生成：</strong>使用kubeadm等工具生成</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-terminal"></i> 2.2.4 配置kubeconfig</h4>
                    <pre><code># 创建.kube目录
mkdir -p ~/.kube

# 复制kubeconfig文件（从Master节点）
scp root@master-ip:/etc/kubernetes/admin.conf ~/.kube/config

# 设置文件权限
chmod 600 ~/.kube/config

# 测试连接
kubectl cluster-info

# 验证节点连接
kubectl get nodes

# 检查当前上下文
kubectl config current-context</code></pre>

                    <div class="output-example">Kubernetes control plane is running at <span
                            class="highlight">https://*************:6443</span>
                        CoreDNS is running at <span
                            class="highlight">https://*************:6443/api/v1/namespaces/kube-system/services/kube-dns:dns/proxy</span>

                        To further debug and diagnose cluster problems, use 'kubectl cluster-info dump'.</div>

                    <div class="output-example">NAME STATUS ROLES AGE VERSION
                        master-node <span class="highlight">Ready</span> control-plane 5d v1.28.0
                        worker-node-1 <span class="highlight">Ready</span> &lt;none&gt; 5d v1.28.0
                        worker-node-2 <span class="highlight">Ready</span> &lt;none&gt; 5d v1.28.0</div>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i>
                            配置成功标志：</strong>执行<code>kubectl cluster-info</code>能看到集群信息，所有节点状态显示为Ready，说明配置成功！
                    </div>

                    <div class="error-example">Error: The connection to the server localhost:8080 was refused - did you
                        specify the right host or port?</div>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 常见配置错误：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>连接被拒绝：</strong>检查API Server地址和端口是否正确</li>
                            <li><strong>证书错误：</strong>确认证书文件完整且未过期</li>
                            <li><strong>权限不足：</strong>检查kubeconfig文件权限设置</li>
                            <li><strong>网络问题：</strong>确认能够访问集群网络</li>
                        </ul>
                    </div>

                    <div class="practice-case">
                        <h5><i class="fas fa-play-circle"></i> 实践案例：多集群配置</h5>
                        <p>在实际工作中，您可能需要管理多个Kubernetes集群。以下是配置多集群的方法：</p>
                        <pre><code># 查看所有配置的集群
kubectl config get-clusters

# 查看所有上下文
kubectl config get-contexts

# 切换到不同的集群
kubectl config use-context production-cluster

# 为不同环境设置别名
kubectl config set-context dev --cluster=dev-cluster --user=dev-user
kubectl config set-context prod --cluster=prod-cluster --user=prod-user</code></pre>
                    </div>
                </section>

                <section id="basic-concepts">
                    <h2><span class="step-number">3</span>基础概念</h2>

                    <h3><i class="fas fa-book"></i> 3.1 kubectl命令结构</h3>
                    <p>在开始使用kubectl之前，我们需要理解kubectl命令的基本结构。所有kubectl命令都遵循相同的格式：</p>

                    <div class="info-box">
                        <strong><i class="fas fa-code"></i> kubectl命令格式：</strong>
                        <pre
                            style="background: transparent; border: none; padding: 0; margin: 15px 0 0 0; color: #2c5282;"><code style="background: transparent; color: #2c5282;">kubectl [command] [TYPE] [NAME] [flags]</code></pre>
                        <ul style="margin-top: 15px;">
                            <li><strong>command：</strong>要执行的操作（如get、create、delete）</li>
                            <li><strong>TYPE：</strong>资源类型（如pod、service、deployment）</li>
                            <li><strong>NAME：</strong>资源名称（可选，不指定则操作所有）</li>
                            <li><strong>flags：</strong>额外的选项参数（如-o yaml、-n namespace）</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-lightbulb"></i> 3.1.1 命令示例解析</h4>
                    <pre><code># 示例1：查看所有Pod
kubectl get pods
# ↑     ↑   ↑
# 工具  操作 资源类型

# 示例2：查看特定Pod的详细信息
kubectl get pod nginx-pod -o yaml
# ↑     ↑   ↑    ↑        ↑
# 工具  操作 类型  名称     输出格式

# 示例3：在特定命名空间中查看Pod
kubectl get pods -n kube-system
# ↑     ↑   ↑    ↑
# 工具  操作 类型  命名空间参数</code></pre>

                    <h3><i class="fas fa-cubes"></i> 3.2 Kubernetes资源类型</h3>
                    <p>Kubernetes中有很多种资源类型，每种资源都有特定的用途。作为初学者，我们先了解最常用的几种：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 资源类型</th>
                            <th><i class="fas fa-info-circle"></i> 用途说明</th>
                            <th><i class="fas fa-terminal"></i> 简写</th>
                            <th><i class="fas fa-star"></i> 重要程度</th>
                        </tr>
                        <tr>
                            <td>Pod</td>
                            <td>最小的部署单元，包含一个或多个容器</td>
                            <td>po</td>
                            <td>⭐⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td>Deployment</td>
                            <td>管理Pod的部署和更新</td>
                            <td>deploy</td>
                            <td>⭐⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td>Service</td>
                            <td>为Pod提供网络访问</td>
                            <td>svc</td>
                            <td>⭐⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td>Namespace</td>
                            <td>资源的逻辑分组</td>
                            <td>ns</td>
                            <td>⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td>ConfigMap</td>
                            <td>存储配置信息</td>
                            <td>cm</td>
                            <td>⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td>Secret</td>
                            <td>存储敏感信息（密码、密钥等）</td>
                            <td>-</td>
                            <td>⭐⭐⭐</td>
                        </tr>
                    </table>

                    <div class="info-box">
                        <strong><i class="fas fa-graduation-cap"></i> 学习建议：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>从基础开始：</strong>先掌握Pod、Deployment、Service这三个核心资源</li>
                            <li><strong>使用简写：</strong>熟悉常用资源的简写形式，提高操作效率</li>
                            <li><strong>理解关系：</strong>了解不同资源之间的关系和依赖</li>
                            <li><strong>实践为主：</strong>通过实际操作加深理解</li>
                        </ul>
                    </div>
                </section>

                <section id="basic-commands">
                    <h2><span class="step-number">4</span>基础命令</h2>

                    <h3><i class="fas fa-play"></i> 4.1 最常用的kubectl命令</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>
                    <p>作为初学者，我们先学习最常用的几个kubectl命令。这些命令覆盖了日常操作的80%以上场景：</p>

                    <h4><i class="fas fa-eye"></i> 4.1.1 查看命令（get）</h4>
                    <span class="command-category category-basic">基础命令</span>
                    <p><code>kubectl get</code>是最常用的命令，用于查看集群中的资源：</p>

                    <pre><code># 查看所有Pod
kubectl get pods

# 查看所有Pod的详细信息
kubectl get pods -o wide

# 查看特定命名空间的Pod
kubectl get pods -n kube-system

# 查看所有命名空间的Pod
kubectl get pods --all-namespaces

# 查看特定Pod
kubectl get pod nginx-pod

# 实时监控Pod状态变化
kubectl get pods -w</code></pre>

                    <div class="output-example">NAME READY STATUS RESTARTS AGE
                        <span class="highlight">nginx-deployment-7d5c6df8bc-k8s9m</span> 1/1 <span
                            class="highlight">Running</span> 0 2m30s
                        <span class="highlight">nginx-deployment-7d5c6df8bc-x7h2p</span> 1/1 <span
                            class="highlight">Running</span> 0 2m30s
                        web-server-pod 1/1 Running 1 5d
                    </div>

                    <div class="output-example">NAME READY STATUS RESTARTS AGE IP NODE NOMINATED NODE READINESS GATES
                        nginx-deployment-7d5c6df8bc-k8s9m 1/1 Running 0 3m <span class="highlight">***********</span>
                        worker-node-1 &lt;none&gt; &lt;none&gt;
                        nginx-deployment-7d5c6df8bc-x7h2p 1/1 Running 0 3m <span class="highlight">**********</span>
                        worker-node-2 &lt;none&gt; &lt;none&gt;</div>

                    <div class="tip-box">
                        <strong>状态解读：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>READY 1/1：</strong>表示Pod中1个容器已就绪，共1个容器</li>
                            <li><strong>STATUS Running：</strong>Pod正在正常运行</li>
                            <li><strong>RESTARTS 0：</strong>容器重启次数为0</li>
                            <li><strong>AGE：</strong>Pod创建后运行的时间</li>
                        </ul>
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> get命令常用参数：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>-o wide：</strong>显示更多信息（如节点、IP地址）</li>
                            <li><strong>-o yaml：</strong>以YAML格式输出</li>
                            <li><strong>-o json：</strong>以JSON格式输出</li>
                            <li><strong>-n namespace：</strong>指定命名空间</li>
                            <li><strong>--all-namespaces：</strong>查看所有命名空间</li>
                            <li><strong>-w：</strong>监控模式，实时显示变化</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-info-circle"></i> 4.1.2 详细信息命令（describe）</h4>
                    <span class="command-category category-basic">基础命令</span>
                    <p><code>kubectl describe</code>用于查看资源的详细信息，包括事件和状态：</p>

                    <pre><code># 查看Pod的详细信息
kubectl describe pod nginx-pod

# 查看Deployment的详细信息
kubectl describe deployment nginx-deployment

# 查看Service的详细信息
kubectl describe service nginx-service

# 查看节点的详细信息
kubectl describe node worker-node-1</code></pre>

                    <div class="output-example">Name: nginx-deployment-7d5c6df8bc-k8s9m
                        Namespace: default
                        Priority: 0
                        Service Account: default
                        Node: worker-node-1/*************
                        Start Time: Mon, 15 Jan 2024 10:30:00 +0800
                        Labels: app=nginx
                        pod-template-hash=7d5c6df8bc
                        Annotations: &lt;none&gt;
                        Status: <span class="highlight">Running</span>
                        IP: ***********
                        IPs:
                        IP: ***********
                        Controlled By: ReplicaSet/nginx-deployment-7d5c6df8bc
                        Containers:
                        nginx:
                        Container ID: containerd://abc123def456...
                        Image: <span class="highlight">nginx:1.20</span>
                        Image ID: docker.io/library/nginx@sha256:abc123...
                        Port: 80/TCP
                        Host Port: 0/TCP
                        State: <span class="highlight">Running</span>
                        Started: Mon, 15 Jan 2024 10:30:15 +0800
                        Ready: True
                        Restart Count: 0
                        Environment: &lt;none&gt;
                        Mounts:
                        /var/run/secrets/kubernetes.io/serviceaccount from kube-api-access-xyz (ro)
                        Conditions:
                        Type Status
                        Initialized <span class="highlight">True</span>
                        Ready <span class="highlight">True</span>
                        ContainersReady <span class="highlight">True</span>
                        PodScheduled <span class="highlight">True</span>
                        Events:
                        Type Reason Age From Message
                        ---- ------ ---- ---- -------
                        Normal Scheduled 5m default-scheduler Successfully assigned
                        default/nginx-deployment-7d5c6df8bc-k8s9m to worker-node-1
                        Normal Pulling 5m kubelet Pulling image "nginx:1.20"
                        Normal Pulled 4m kubelet Successfully pulled image "nginx:1.20"
                        Normal Created 4m kubelet Created container nginx
                        Normal Started 4m kubelet Started container nginx</div>

                    <div class="tip-box">
                        <strong>describe输出解读：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>基本信息：</strong>名称、命名空间、节点、IP等</li>
                            <li><strong>容器状态：</strong>镜像、端口、运行状态等</li>
                            <li><strong>条件检查：</strong>Pod的各种状态条件</li>
                            <li><strong>事件历史：</strong>Pod生命周期中的重要事件</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> describe命令的价值：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>故障排查：</strong>查看错误事件和状态信息</li>
                            <li><strong>配置检查：</strong>确认资源配置是否正确</li>
                            <li><strong>状态监控：</strong>了解资源的运行状态</li>
                            <li><strong>事件追踪：</strong>查看资源相关的系统事件</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-plus"></i> 4.1.3 创建命令（create）</h4>
                    <span class="command-category category-basic">基础命令</span>
                    <p><code>kubectl create</code>用于创建新的资源：</p>

                    <pre><code># 创建Deployment
kubectl create deployment nginx --image=nginx:1.20

# 创建Service
kubectl create service clusterip nginx --tcp=80:80

# 从文件创建资源
kubectl create -f nginx-deployment.yaml

# 创建命名空间
kubectl create namespace my-app

# 创建ConfigMap
kubectl create configmap app-config --from-literal=key1=value1

# 创建Secret
kubectl create secret generic app-secret --from-literal=password=mypassword</code></pre>

                    <div class="output-example">deployment.apps/nginx created</div>

                    <div class="output-example">service/nginx created</div>

                    <div class="practice-case">
                        <h5><i class="fas fa-play-circle"></i> 实践案例：创建完整的Web应用</h5>
                        <p>让我们创建一个完整的nginx Web应用，包括Deployment、Service和ConfigMap：</p>
                        <pre><code># 1. 创建命名空间
kubectl create namespace web-app

# 2. 创建ConfigMap存储nginx配置
kubectl create configmap nginx-config \
  --from-literal=server_name=example.com \
  --from-literal=listen_port=80 \
  -n web-app

# 3. 创建Deployment
kubectl create deployment nginx-web \
  --image=nginx:1.20 \
  --replicas=3 \
  -n web-app

# 4. 创建Service暴露应用
kubectl create service nodeport nginx-web \
  --tcp=80:80 \
  -n web-app

# 5. 验证创建结果
kubectl get all -n web-app</code></pre>
                    </div>

                    <div class="output-example">NAME READY STATUS RESTARTS AGE
                        pod/nginx-web-7d5c6df8bc-abc12 1/1 Running 0 30s
                        pod/nginx-web-7d5c6df8bc-def34 1/1 Running 0 30s
                        pod/nginx-web-7d5c6df8bc-ghi56 1/1 Running 0 30s

                        NAME TYPE CLUSTER-IP EXTERNAL-IP PORT(S) AGE
                        service/nginx-web NodePort ************ &lt;none&gt; 80:<span class="highlight">32567</span>/TCP
                        25s

                        NAME READY UP-TO-DATE AVAILABLE AGE
                        deployment.apps/nginx-web 3/3 3 3 30s

                        NAME DESIRED CURRENT READY AGE
                        replicaset.apps/nginx-web-7d5c6df8bc 3 3 3 30s</div>

                    <div class="tip-box">
                        <strong>创建成功验证：</strong>通过<code>kubectl get all</code>可以看到所有相关资源都已成功创建，Pod状态为Running，Service分配了NodePort端口32567。
                    </div>

                    <h4><i class="fas fa-trash"></i> 4.1.4 删除命令（delete）</h4>
                    <span class="command-category category-basic">基础命令</span>
                    <p><code>kubectl delete</code>用于删除资源：</p>

                    <pre><code># 删除Pod
kubectl delete pod nginx-pod

# 删除Deployment
kubectl delete deployment nginx

# 删除Service
kubectl delete service nginx

# 从文件删除资源
kubectl delete -f nginx-deployment.yaml

# 删除命名空间（会删除其中所有资源）
kubectl delete namespace my-app

# 强制删除Pod（谨慎使用）
kubectl delete pod nginx-pod --force --grace-period=0

# 批量删除资源
kubectl delete pods --all
kubectl delete deployments -l app=nginx</code></pre>

                    <div class="output-example">pod "nginx-pod" deleted</div>

                    <div class="output-example">deployment.apps "nginx" deleted</div>

                    <div class="error-example">Error from server (NotFound): pods "nginx-pod" not found</div>

                    <div class="practice-case">
                        <h5><i class="fas fa-play-circle"></i> 实践案例：安全删除应用</h5>
                        <p>演示如何安全地删除一个完整的应用，避免数据丢失：</p>
                        <pre><code># 1. 首先备份重要数据（如果有持久化存储）
kubectl get pvc -n web-app

# 2. 停止流量（删除Service）
kubectl delete service nginx-web -n web-app

# 3. 等待现有连接完成，然后缩容
kubectl scale deployment nginx-web --replicas=0 -n web-app

# 4. 确认Pod已停止
kubectl get pods -n web-app

# 5. 删除Deployment
kubectl delete deployment nginx-web -n web-app

# 6. 清理配置资源
kubectl delete configmap nginx-config -n web-app

# 7. 最后删除命名空间
kubectl delete namespace web-app</code></pre>
                    </div>

                    <div class="tip-box">
                        <strong>删除最佳实践：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>先停流量：</strong>删除Service停止新的请求</li>
                            <li><strong>优雅停机：</strong>让现有请求处理完成</li>
                            <li><strong>备份数据：</strong>删除前备份重要数据</li>
                            <li><strong>分步删除：</strong>按依赖关系逐步删除资源</li>
                        </ul>
                    </div>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 删除操作注意事项：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>不可恢复：</strong>删除操作通常不可恢复，请谨慎操作</li>
                            <li><strong>级联删除：</strong>删除Deployment会自动删除其管理的Pod</li>
                            <li><strong>命名空间删除：</strong>删除命名空间会删除其中的所有资源</li>
                            <li><strong>生产环境：</strong>在生产环境中删除前请三思</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-terminal"></i> 4.2 命令组合和技巧</h3>

                    <h4><i class="fas fa-filter"></i> 4.2.1 使用标签选择器</h4>
                    <span class="command-category category-advanced">高级技巧</span>
                    <pre><code># 根据标签查看Pod
kubectl get pods -l app=nginx

# 根据多个标签查看
kubectl get pods -l app=nginx,version=v1

# 查看带有特定标签的所有资源
kubectl get all -l app=nginx</code></pre>

                    <h4><i class="fas fa-search"></i> 4.2.2 字段选择器</h4>
                    <span class="command-category category-advanced">高级技巧</span>
                    <pre><code># 查看运行中的Pod
kubectl get pods --field-selector=status.phase=Running

# 查看特定节点上的Pod
kubectl get pods --field-selector=spec.nodeName=worker-node-1

# 查看失败的Pod
kubectl get pods --field-selector=status.phase=Failed</code></pre>

                    <h4><i class="fas fa-sort"></i> 4.2.3 排序和格式化</h4>
                    <span class="command-category category-advanced">高级技巧</span>
                    <pre><code># 按创建时间排序
kubectl get pods --sort-by=.metadata.creationTimestamp

# 按名称排序
kubectl get pods --sort-by=.metadata.name

# 自定义输出列
kubectl get pods -o custom-columns=NAME:.metadata.name,STATUS:.status.phase,NODE:.spec.nodeName

# 只显示名称
kubectl get pods -o name</code></pre>
                </section>

                <section id="resource-management">
                    <h2><span class="step-number">5</span>资源管理</h2>

                    <h3><i class="fas fa-cubes"></i> 5.1 Deployment管理</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>
                    <p>Deployment是Kubernetes中最重要的工作负载资源，用于管理Pod的部署、更新和扩缩容：</p>

                    <h4><i class="fas fa-rocket"></i> 5.1.1 创建和管理Deployment</h4>
                    <span class="command-category category-basic">基础操作</span>
                    <pre><code># 创建一个nginx Deployment
kubectl create deployment nginx --image=nginx:1.20

# 查看Deployment状态
kubectl get deployments

# 查看Deployment详细信息
kubectl describe deployment nginx

# 查看Deployment的YAML配置
kubectl get deployment nginx -o yaml</code></pre>

                    <h4><i class="fas fa-expand-arrows-alt"></i> 5.1.2 扩缩容操作</h4>
                    <span class="command-category category-basic">基础操作</span>
                    <pre><code># 扩容到3个副本
kubectl scale deployment nginx --replicas=3

# 查看扩容结果
kubectl get pods -l app=nginx

# 缩容到1个副本
kubectl scale deployment nginx --replicas=1

# 自动扩缩容（需要metrics-server）
kubectl autoscale deployment nginx --cpu-percent=50 --min=1 --max=10</code></pre>

                    <h4><i class="fas fa-sync-alt"></i> 5.1.3 更新和回滚</h4>
                    <span class="command-category category-advanced">高级操作</span>
                    <pre><code># 更新镜像版本
kubectl set image deployment/nginx nginx=nginx:1.21

# 查看更新状态
kubectl rollout status deployment/nginx

# 查看更新历史
kubectl rollout history deployment/nginx

# 回滚到上一个版本
kubectl rollout undo deployment/nginx

# 回滚到特定版本
kubectl rollout undo deployment/nginx --to-revision=2

# 暂停更新
kubectl rollout pause deployment/nginx

# 恢复更新
kubectl rollout resume deployment/nginx</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> Deployment更新策略：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>滚动更新：</strong>默认策略，逐步替换旧Pod</li>
                            <li><strong>重建更新：</strong>先删除所有旧Pod，再创建新Pod</li>
                            <li><strong>蓝绿部署：</strong>通过Service切换实现零停机更新</li>
                            <li><strong>金丝雀发布：</strong>先更新部分Pod进行测试</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-network-wired"></i> 5.2 Service管理</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>
                    <p>Service为Pod提供稳定的网络访问入口，是Kubernetes网络的核心概念：</p>

                    <h4><i class="fas fa-plus-circle"></i> 5.2.1 创建Service</h4>
                    <span class="command-category category-basic">基础操作</span>
                    <pre><code># 为Deployment创建ClusterIP Service
kubectl expose deployment nginx --port=80 --target-port=80

# 创建NodePort Service
kubectl expose deployment nginx --port=80 --target-port=80 --type=NodePort

# 创建LoadBalancer Service
kubectl expose deployment nginx --port=80 --target-port=80 --type=LoadBalancer

# 查看Service
kubectl get services

# 查看Service详细信息
kubectl describe service nginx</code></pre>

                    <h4><i class="fas fa-info-circle"></i> 5.2.2 Service类型说明</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> Service类型</th>
                            <th><i class="fas fa-info-circle"></i> 用途说明</th>
                            <th><i class="fas fa-globe"></i> 访问方式</th>
                        </tr>
                        <tr>
                            <td>ClusterIP</td>
                            <td>集群内部访问</td>
                            <td>只能在集群内部访问</td>
                        </tr>
                        <tr>
                            <td>NodePort</td>
                            <td>通过节点端口访问</td>
                            <td>节点IP:端口</td>
                        </tr>
                        <tr>
                            <td>LoadBalancer</td>
                            <td>通过负载均衡器访问</td>
                            <td>负载均衡器IP:端口</td>
                        </tr>
                        <tr>
                            <td>ExternalName</td>
                            <td>映射到外部服务</td>
                            <td>DNS名称映射</td>
                        </tr>
                    </table>

                    <h4><i class="fas fa-link"></i> 5.2.3 测试Service连通性</h4>
                    <span class="command-category category-debug">调试技巧</span>
                    <pre><code># 查看Service的端点
kubectl get endpoints nginx

# 在集群内测试Service
kubectl run test-pod --image=busybox --rm -it --restart=Never -- wget -qO- nginx

# 查看Service的DNS解析
kubectl run test-pod --image=busybox --rm -it --restart=Never -- nslookup nginx

# 端口转发到本地测试
kubectl port-forward service/nginx 8080:80</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> Service测试成功标志：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>端点正常：</strong>kubectl get endpoints显示有可用端点</li>
                            <li><strong>DNS解析：</strong>能够正确解析Service名称</li>
                            <li><strong>连通性：</strong>能够通过Service访问到后端Pod</li>
                            <li><strong>负载均衡：</strong>请求能够分发到不同的Pod</li>
                        </ul>
                    </div>
                </section>

                <section id="pod-operations">
                    <h2><span class="step-number">6</span>Pod操作</h2>

                    <h3><i class="fas fa-box"></i> 6.1 Pod基础操作</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>
                    <p>Pod是Kubernetes中最小的部署单元，理解Pod操作是掌握kubectl的关键：</p>

                    <h4><i class="fas fa-plus"></i> 6.1.1 创建和管理Pod</h4>
                    <span class="command-category category-basic">基础操作</span>
                    <pre><code># 创建一个简单的Pod
kubectl run nginx-pod --image=nginx:1.20

# 创建临时Pod（退出后自动删除）
kubectl run test-pod --image=busybox --rm -it --restart=Never -- /bin/sh

# 查看Pod状态
kubectl get pods

# 查看Pod详细信息
kubectl describe pod nginx-pod

# 查看Pod的YAML配置
kubectl get pod nginx-pod -o yaml</code></pre>

                    <h4><i class="fas fa-terminal"></i> 6.1.2 进入Pod执行命令</h4>
                    <span class="command-category category-debug">调试技巧</span>
                    <pre><code># 进入Pod的shell
kubectl exec -it nginx-pod -- /bin/bash

# 在Pod中执行单个命令
kubectl exec nginx-pod -- ls -la /usr/share/nginx/html

# 在多容器Pod中指定容器
kubectl exec -it nginx-pod -c nginx-container -- /bin/bash

# 在Pod中执行复杂命令
kubectl exec nginx-pod -- sh -c "echo 'Hello World' > /tmp/test.txt && cat /tmp/test.txt"</code></pre>

                    <div class="output-example">root@nginx-pod:/# <span class="highlight">pwd</span>
                        /
                        root@nginx-pod:/# <span class="highlight">ls -la /usr/share/nginx/html</span>
                        total 12
                        drwxr-xr-x 2 <USER> <GROUP> 4096 Jan 15 10:30 .
                        drwxr-xr-x 3 <USER> <GROUP> 4096 Jan 15 10:30 ..
                        -rw-r--r-- 1 <USER> <GROUP> 615 Jan 15 10:30 index.html
                        -rw-r--r-- 1 <USER> <GROUP> 497 Jan 15 10:30 50x.html</div>

                    <div class="practice-case">
                        <h5><i class="fas fa-play-circle"></i> 实践案例：Pod内调试Web应用</h5>
                        <p>演示如何在Pod内部调试nginx Web应用的常见问题：</p>
                        <pre><code># 1. 检查nginx进程状态
kubectl exec nginx-pod -- ps aux | grep nginx

# 2. 检查nginx配置文件
kubectl exec nginx-pod -- cat /etc/nginx/nginx.conf

# 3. 检查网络连接
kubectl exec nginx-pod -- netstat -tlnp

# 4. 测试内部连接
kubectl exec nginx-pod -- curl -I localhost:80

# 5. 查看nginx访问日志
kubectl exec nginx-pod -- tail -f /var/log/nginx/access.log

# 6. 查看nginx错误日志
kubectl exec nginx-pod -- tail -f /var/log/nginx/error.log</code></pre>
                    </div>

                    <div class="output-example">root 1 0.0 0.1 32648 5248 ? Ss 10:30 0:00 <span class="highlight">nginx:
                            master process nginx -g daemon off;</span>
                        nginx 29 0.0 0.0 33108 2048 ? S 10:30 0:00 <span class="highlight">nginx: worker process</span>
                        nginx 30 0.0 0.0 33108 2048 ? S 10:30 0:00 <span class="highlight">nginx: worker process</span>
                    </div>

                    <div class="output-example">HTTP/1.1 <span class="highlight">200 OK</span>
                        Server: nginx/1.20.2
                        Date: Mon, 15 Jan 2024 10:35:00 GMT
                        Content-Type: text/html
                        Content-Length: 615
                        Last-Modified: Mon, 15 Jan 2024 10:30:00 GMT
                        Connection: keep-alive
                        ETag: "659a1234-267"
                        Accept-Ranges: bytes</div>

                    <div class="tip-box">
                        <strong>exec命令技巧：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>-it参数：</strong>提供交互式终端</li>
                            <li><strong>指定容器：</strong>多容器Pod需要用-c指定容器名</li>
                            <li><strong>命令分隔：</strong>使用--分隔kubectl参数和容器命令</li>
                            <li><strong>shell选择：</strong>根据镜像选择合适的shell（bash、sh等）</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-file-alt"></i> 6.1.3 查看Pod日志</h4>
                    <span class="command-category category-debug">调试技巧</span>
                    <pre><code># 查看Pod日志
kubectl logs nginx-pod

# 实时跟踪日志
kubectl logs -f nginx-pod

# 查看最近100行日志
kubectl logs --tail=100 nginx-pod

# 查看过去1小时的日志
kubectl logs --since=1h nginx-pod

# 查看多容器Pod中特定容器的日志
kubectl logs nginx-pod -c nginx-container

# 查看之前重启的Pod日志
kubectl logs nginx-pod --previous

# 查看多个Pod的日志
kubectl logs -l app=nginx --prefix=true</code></pre>

                    <div class="output-example">2024/01/15 10:30:15 [notice] 1#1: using the "epoll" event method
                        2024/01/15 10:30:15 [notice] 1#1: nginx/1.20.2
                        2024/01/15 10:30:15 [notice] 1#1: built by gcc 9.4.0 (Ubuntu 9.4.0-1ubuntu1~20.04.1)
                        2024/01/15 10:30:15 [notice] 1#1: OS: Linux 5.4.0-74-generic
                        2024/01/15 10:30:15 [notice] 1#1: getrlimit(RLIMIT_NOFILE): 1048576:1048576
                        2024/01/15 10:30:15 [notice] 1#1: start worker processes
                        2024/01/15 10:30:15 [notice] 1#1: start worker process 29
                        2024/01/15 10:30:15 [notice] 1#1: start worker process 30
                        <span class="highlight">************* - - [15/Jan/2024:10:35:22 +0000] "GET / HTTP/1.1" 200 615
                            "-" "curl/7.68.0"</span>
                        <span class="highlight">************* - - [15/Jan/2024:10:35:25 +0000] "GET /favicon.ico
                            HTTP/1.1" 404 555 "-" "Mozilla/5.0"</span>
                    </div>

                    <div class="practice-case">
                        <h5><i class="fas fa-play-circle"></i> 实践案例：日志分析和故障排查</h5>
                        <p>演示如何通过日志分析定位和解决常见问题：</p>
                        <pre><code># 1. 查看应用启动日志
kubectl logs nginx-pod --since=10m

# 2. 过滤错误日志
kubectl logs nginx-pod | grep -i error

# 3. 查看访问量统计
kubectl logs nginx-pod | grep "GET /" | wc -l

# 4. 分析访问来源
kubectl logs nginx-pod | awk '{print $1}' | sort | uniq -c | sort -nr

# 5. 监控实时访问
kubectl logs -f nginx-pod | grep --line-buffered "GET"

# 6. 导出日志到文件进行详细分析
kubectl logs nginx-pod --since=24h > nginx-logs-$(date +%Y%m%d).log</code></pre>
                    </div>

                    <div class="error-example">2024/01/15 10:40:15 [error] 29#29: *1 open()
                        "/usr/share/nginx/html/api/users" failed (2: No such file or directory), client: *************,
                        server: localhost, request: "GET /api/users HTTP/1.1", host: "example.com"</div>

                    <div class="tip-box">
                        <strong>日志分析技巧：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>时间过滤：</strong>使用--since参数查看特定时间段的日志</li>
                            <li><strong>关键字搜索：</strong>结合grep命令过滤关键信息</li>
                            <li><strong>实时监控：</strong>使用-f参数实时跟踪日志变化</li>
                            <li><strong>多容器处理：</strong>多容器Pod需要指定容器名</li>
                        </ul>
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> Pod状态说明：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>Pending：</strong>Pod已创建但尚未调度到节点</li>
                            <li><strong>Running：</strong>Pod已调度到节点且至少一个容器正在运行</li>
                            <li><strong>Succeeded：</strong>Pod中所有容器都已成功终止</li>
                            <li><strong>Failed：</strong>Pod中至少一个容器以失败状态终止</li>
                            <li><strong>Unknown：</strong>无法获取Pod状态</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-copy"></i> 6.2 文件传输</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>

                    <h4><i class="fas fa-upload"></i> 6.2.1 文件上传下载</h4>
                    <span class="command-category category-advanced">高级操作</span>
                    <pre><code># 从本地复制文件到Pod
kubectl cp /local/path/file.txt nginx-pod:/container/path/file.txt

# 从Pod复制文件到本地
kubectl cp nginx-pod:/container/path/file.txt /local/path/file.txt

# 复制目录
kubectl cp /local/directory nginx-pod:/container/directory

# 在多容器Pod中指定容器
kubectl cp /local/file.txt nginx-pod:/path/file.txt -c nginx-container</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 文件传输注意事项：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>权限问题：</strong>确保Pod中的用户有读写权限</li>
                            <li><strong>路径存在：</strong>目标路径必须存在</li>
                            <li><strong>容器状态：</strong>容器必须处于运行状态</li>
                            <li><strong>文件大小：</strong>避免传输过大的文件</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-network-wired"></i> 6.3 网络调试</h3>

                    <h4><i class="fas fa-globe"></i> 6.3.1 端口转发</h4>
                    <span class="command-category category-debug">调试技巧</span>
                    <pre><code># 将Pod端口转发到本地
kubectl port-forward nginx-pod 8080:80

# 将Service端口转发到本地
kubectl port-forward service/nginx 8080:80

# 将Deployment端口转发到本地
kubectl port-forward deployment/nginx 8080:80

# 绑定到所有网络接口
kubectl port-forward --address 0.0.0.0 nginx-pod 8080:80</code></pre>

                    <h4><i class="fas fa-search"></i> 6.3.2 网络连通性测试</h4>
                    <span class="command-category category-debug">调试技巧</span>
                    <pre><code># 创建测试Pod进行网络调试
kubectl run netshoot --image=nicolaka/netshoot --rm -it --restart=Never

# 在测试Pod中测试连通性
kubectl exec -it netshoot -- ping google.com
kubectl exec -it netshoot -- nslookup kubernetes.default
kubectl exec -it netshoot -- curl -I http://nginx-service

# 测试Pod间通信
kubectl exec -it netshoot -- telnet nginx-pod-ip 80</code></pre>
                </section>

                <section id="service-networking">
                    <h2><span class="step-number">7</span>服务网络</h2>

                    <h3><i class="fas fa-network-wired"></i> 7.1 网络概念理解</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>
                    <p>Kubernetes网络是一个复杂但重要的概念，理解网络有助于更好地使用kubectl进行网络相关操作：</p>

                    <h4><i class="fas fa-sitemap"></i> 7.1.1 网络层次结构</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-layer-group"></i> Kubernetes网络层次：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>Pod网络：</strong>每个Pod都有独立的IP地址</li>
                            <li><strong>Service网络：</strong>为Pod提供稳定的访问入口</li>
                            <li><strong>Ingress网络：</strong>提供HTTP/HTTPS路由</li>
                            <li><strong>节点网络：</strong>集群节点之间的通信</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-eye"></i> 7.1.2 查看网络信息</h4>
                    <span class="command-category category-basic">基础操作</span>
                    <pre><code># 查看所有Service
kubectl get services --all-namespaces

# 查看Service端点
kubectl get endpoints

# 查看Ingress
kubectl get ingress

# 查看网络策略
kubectl get networkpolicies

# 查看Pod的IP地址
kubectl get pods -o wide</code></pre>

                    <h3><i class="fas fa-route"></i> 7.2 Ingress管理</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>

                    <h4><i class="fas fa-plus-circle"></i> 7.2.1 创建Ingress</h4>
                    <span class="command-category category-advanced">高级操作</span>
                    <pre><code># 查看Ingress控制器
kubectl get pods -n ingress-nginx

# 创建简单的Ingress规则
cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nginx-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: nginx.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: nginx
            port:
              number: 80
EOF

# 查看Ingress状态
kubectl get ingress nginx-ingress

# 查看Ingress详细信息
kubectl describe ingress nginx-ingress</code></pre>

                    <h4><i class="fas fa-certificate"></i> 7.2.2 HTTPS配置</h4>
                    <span class="command-category category-advanced">高级操作</span>
                    <pre><code># 创建TLS Secret
kubectl create secret tls nginx-tls --cert=path/to/cert.crt --key=path/to/cert.key

# 在Ingress中使用TLS
cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nginx-ingress-tls
spec:
  tls:
  - hosts:
    - nginx.example.com
    secretName: nginx-tls
  rules:
  - host: nginx.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: nginx
            port:
              number: 80
EOF</code></pre>

                    <h3><i class="fas fa-shield-alt"></i> 7.3 网络策略</h3>

                    <h4><i class="fas fa-lock"></i> 7.3.1 网络隔离</h4>
                    <span class="command-category category-advanced">高级操作</span>
                    <pre><code># 创建拒绝所有流量的网络策略
cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: deny-all
  namespace: default
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
EOF

# 创建允许特定流量的网络策略
cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-nginx
  namespace: default
spec:
  podSelector:
    matchLabels:
      app: nginx
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: frontend
    ports:
    - protocol: TCP
      port: 80
EOF

# 查看网络策略
kubectl get networkpolicy</code></pre>
                </section>

                <section id="config-secrets">
                    <h2><span class="step-number">8</span>配置密钥</h2>

                    <h3><i class="fas fa-cog"></i> 8.1 ConfigMap管理</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>
                    <p>ConfigMap用于存储非敏感的配置信息，是Kubernetes中管理应用配置的重要方式：</p>

                    <h4><i class="fas fa-plus"></i> 8.1.1 创建ConfigMap</h4>
                    <span class="command-category category-config">配置管理</span>
                    <pre><code># 从字面值创建ConfigMap
kubectl create configmap app-config \
  --from-literal=database_host=mysql.example.com \
  --from-literal=database_port=3306

# 从文件创建ConfigMap
kubectl create configmap nginx-config --from-file=nginx.conf

# 从目录创建ConfigMap
kubectl create configmap app-configs --from-file=config-dir/

# 从环境文件创建ConfigMap
kubectl create configmap env-config --from-env-file=app.env</code></pre>

                    <h4><i class="fas fa-eye"></i> 8.1.2 查看和使用ConfigMap</h4>
                    <span class="command-category category-config">配置管理</span>
                    <pre><code># 查看ConfigMap
kubectl get configmaps

# 查看ConfigMap内容
kubectl describe configmap app-config

# 以YAML格式查看ConfigMap
kubectl get configmap app-config -o yaml

# 在Pod中使用ConfigMap作为环境变量
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: config-test-pod
spec:
  containers:
  - name: test-container
    image: busybox
    command: [ "/bin/sh", "-c", "env" ]
    envFrom:
    - configMapRef:
        name: app-config
  restartPolicy: Never
EOF</code></pre>

                    <h3><i class="fas fa-key"></i> 8.2 Secret管理</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>
                    <p>Secret用于存储敏感信息，如密码、密钥、证书等：</p>

                    <h4><i class="fas fa-plus-circle"></i> 8.2.1 创建Secret</h4>
                    <span class="command-category category-config">配置管理</span>
                    <pre><code># 创建通用Secret
kubectl create secret generic app-secret \
  --from-literal=username=admin \
  --from-literal=password=secretpassword

# 创建Docker镜像拉取Secret
kubectl create secret docker-registry regcred \
  --docker-server=registry.example.com \
  --docker-username=myuser \
  --docker-password=mypassword \
  --docker-email=<EMAIL>

# 创建TLS Secret
kubectl create secret tls tls-secret \
  --cert=path/to/cert.crt \
  --key=path/to/cert.key

# 从文件创建Secret
kubectl create secret generic file-secret --from-file=secret.txt</code></pre>

                    <h4><i class="fas fa-shield-alt"></i> 8.2.2 使用Secret</h4>
                    <span class="command-category category-config">配置管理</span>
                    <pre><code># 查看Secret（内容会被编码）
kubectl get secrets

# 查看Secret详细信息
kubectl describe secret app-secret

# 解码Secret内容
kubectl get secret app-secret -o jsonpath="{.data.password}" | base64 --decode

# 在Pod中使用Secret
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: secret-test-pod
spec:
  containers:
  - name: test-container
    image: busybox
    command: [ "/bin/sh", "-c", "echo \$SECRET_USERNAME && echo \$SECRET_PASSWORD" ]
    env:
    - name: SECRET_USERNAME
      valueFrom:
        secretKeyRef:
          name: app-secret
          key: username
    - name: SECRET_PASSWORD
      valueFrom:
        secretKeyRef:
          name: app-secret
          key: password
  restartPolicy: Never
EOF</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> Secret安全注意事项：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>Base64编码：</strong>Secret内容只是Base64编码，不是加密</li>
                            <li><strong>访问控制：</strong>使用RBAC限制Secret的访问权限</li>
                            <li><strong>传输安全：</strong>确保API Server使用HTTPS</li>
                            <li><strong>存储加密：</strong>在etcd中启用静态数据加密</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-edit"></i> 8.3 配置更新</h3>

                    <h4><i class="fas fa-sync-alt"></i> 8.3.1 更新ConfigMap和Secret</h4>
                    <span class="command-category category-config">配置管理</span>
                    <pre><code># 编辑ConfigMap
kubectl edit configmap app-config

# 替换ConfigMap内容
kubectl create configmap app-config --from-literal=new_key=new_value --dry-run=client -o yaml | kubectl replace -f -

# 删除并重新创建ConfigMap
kubectl delete configmap app-config
kubectl create configmap app-config --from-literal=updated_key=updated_value

# 更新Secret
kubectl create secret generic app-secret \
  --from-literal=username=newuser \
  --from-literal=password=newpassword \
  --dry-run=client -o yaml | kubectl replace -f -</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 配置更新生效：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>环境变量：</strong>需要重启Pod才能生效</li>
                            <li><strong>挂载文件：</strong>会自动更新，但应用需要重新读取</li>
                            <li><strong>滚动更新：</strong>可以通过更新Deployment触发Pod重启</li>
                            <li><strong>配置重载：</strong>某些应用支持配置热重载</li>
                        </ul>
                    </div>
                </section>

                <section id="monitoring-logs">
                    <h2><span class="step-number">9</span>监控日志</h2>

                    <h3><i class="fas fa-chart-line"></i> 9.1 资源监控</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>
                    <p>监控集群和应用的资源使用情况是运维的重要工作，kubectl提供了多种监控命令：</p>

                    <h4><i class="fas fa-tachometer-alt"></i> 9.1.1 查看资源使用情况</h4>
                    <span class="command-category category-debug">监控调试</span>
                    <pre><code># 查看节点资源使用情况（需要metrics-server）
kubectl top nodes

# 查看Pod资源使用情况
kubectl top pods

# 查看特定命名空间的Pod资源使用
kubectl top pods -n kube-system

# 查看所有命名空间的Pod资源使用
kubectl top pods --all-namespaces

# 按CPU使用率排序
kubectl top pods --sort-by=cpu

# 按内存使用率排序
kubectl top pods --sort-by=memory</code></pre>

                    <div class="output-example">NAME CPU(cores) CPU% MEMORY(bytes) MEMORY%
                        <span class="highlight">master-node</span> <span class="highlight">250m</span> 12% <span
                            class="highlight">1024Mi</span> 25%
                        worker-node-1 180m 9% 768Mi 19%
                        worker-node-2 220m 11% 896Mi 22%
                    </div>

                    <div class="output-example">NAME CPU(cores) MEMORY(bytes)
                        <span class="highlight">nginx-deployment-7d5c6df8bc-k8s9m</span> <span
                            class="highlight">5m</span> <span class="highlight">32Mi</span>
                        nginx-deployment-7d5c6df8bc-x7h2p 4m 28Mi
                        coredns-558bd4d5db-abc12 3m 15Mi
                        kube-proxy-def34 2m 20Mi
                    </div>

                    <div class="practice-case">
                        <h5><i class="fas fa-play-circle"></i> 实践案例：资源监控和性能分析</h5>
                        <p>演示如何监控集群资源使用情况并进行性能分析：</p>
                        <pre><code># 1. 检查集群整体资源使用情况
kubectl top nodes

# 2. 找出资源使用最高的Pod
kubectl top pods --all-namespaces --sort-by=memory | head -10

# 3. 监控特定应用的资源使用
watch kubectl top pods -l app=nginx

# 4. 检查节点资源分配情况
kubectl describe nodes | grep -A 5 "Allocated resources"

# 5. 查看Pod资源限制设置
kubectl get pods -o jsonpath='{range .items[*]}{.metadata.name}{"\t"}{.spec.containers[0].resources}{"\n"}{end}'

# 6. 分析资源使用趋势（需要配合监控系统）
kubectl top pods --sort-by=cpu | awk '{print $1, $2}' > cpu-usage-$(date +%H%M).log</code></pre>
                    </div>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 资源监控注意事项：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>metrics-server：</strong>需要安装metrics-server才能使用top命令</li>
                            <li><strong>数据延迟：</strong>资源使用数据可能有1-2分钟的延迟</li>
                            <li><strong>瞬时数据：</strong>显示的是当前瞬时值，不是平均值</li>
                            <li><strong>单位换算：</strong>CPU单位为millicores(m)，内存单位为Mi/Gi</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-server"></i> 9.1.2 集群状态检查</h4>
                    <span class="command-category category-basic">基础操作</span>
                    <pre><code># 查看集群信息
kubectl cluster-info

# 查看集群详细信息
kubectl cluster-info dump

# 查看API版本
kubectl api-versions

# 查看API资源
kubectl api-resources

# 查看组件状态
kubectl get componentstatuses

# 查看节点详细信息
kubectl describe nodes</code></pre>

                    <h3><i class="fas fa-file-alt"></i> 9.2 日志管理</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>

                    <h4><i class="fas fa-search"></i> 9.2.1 高级日志查看</h4>
                    <span class="command-category category-debug">监控调试</span>
                    <pre><code># 查看多个Pod的日志
kubectl logs -l app=nginx

# 查看日志并显示Pod名称
kubectl logs -l app=nginx --prefix=true

# 查看指定时间范围的日志
kubectl logs nginx-pod --since=2023-01-01T10:00:00Z

# 查看最近的日志
kubectl logs nginx-pod --since=10m

# 查看日志的最后N行
kubectl logs nginx-pod --tail=50

# 查看所有容器的日志
kubectl logs nginx-pod --all-containers=true

# 将日志输出到文件
kubectl logs nginx-pod > pod-logs.txt</code></pre>

                    <h4><i class="fas fa-exclamation-triangle"></i> 9.2.2 事件查看</h4>
                    <span class="command-category category-debug">监控调试</span>
                    <pre><code># 查看所有事件
kubectl get events

# 按时间排序查看事件
kubectl get events --sort-by=.metadata.creationTimestamp

# 查看特定命名空间的事件
kubectl get events -n kube-system

# 查看特定资源的事件
kubectl get events --field-selector involvedObject.name=nginx-pod

# 查看警告事件
kubectl get events --field-selector type=Warning

# 实时监控事件
kubectl get events -w</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 日志分析技巧：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>关键字搜索：</strong>使用grep过滤关键信息</li>
                            <li><strong>时间范围：</strong>根据问题发生时间查看相关日志</li>
                            <li><strong>多维度分析：</strong>结合Pod日志和系统事件分析</li>
                            <li><strong>持续监控：</strong>使用-f参数实时跟踪日志变化</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-bell"></i> 9.3 告警和通知</h3>

                    <h4><i class="fas fa-exclamation-circle"></i> 9.3.1 资源配额监控</h4>
                    <span class="command-category category-advanced">高级操作</span>
                    <pre><code># 查看资源配额
kubectl get resourcequotas

# 查看限制范围
kubectl get limitranges

# 查看资源配额详细信息
kubectl describe resourcequota

# 查看命名空间资源使用情况
kubectl describe namespace default</code></pre>
                </section>

                <section id="advanced-operations">
                    <h2><span class="step-number">10</span>高级操作</h2>

                    <h3><i class="fas fa-cogs"></i> 10.1 批量操作</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>

                    <h4><i class="fas fa-layer-group"></i> 10.1.1 标签和选择器</h4>
                    <span class="command-category category-advanced">高级操作</span>
                    <pre><code># 给资源添加标签
kubectl label pods nginx-pod environment=production

# 给多个资源添加标签
kubectl label pods -l app=nginx tier=frontend

# 修改标签
kubectl label pods nginx-pod environment=staging --overwrite

# 删除标签
kubectl label pods nginx-pod environment-

# 根据标签选择资源
kubectl get pods -l environment=production

# 复杂的标签选择
kubectl get pods -l 'environment in (production,staging)'
kubectl get pods -l 'environment!=development'</code></pre>

                    <h4><i class="fas fa-edit"></i> 10.1.2 批量编辑和更新</h4>
                    <span class="command-category category-advanced">高级操作</span>
                    <pre><code># 批量删除Pod
kubectl delete pods -l app=nginx

# 批量更新镜像
kubectl set image deployment/nginx nginx=nginx:1.21

# 批量设置环境变量
kubectl set env deployment/nginx DATABASE_URL=mysql://localhost:3306/mydb

# 批量设置资源限制
kubectl set resources deployment nginx --limits=cpu=200m,memory=512Mi

# 使用patch更新资源
kubectl patch deployment nginx -p '{"spec":{"replicas":5}}'</code></pre>

                    <h3><i class="fas fa-file-code"></i> 10.2 YAML文件操作</h3>

                    <h4><i class="fas fa-download"></i> 10.2.1 导出和备份</h4>
                    <span class="command-category category-advanced">高级操作</span>
                    <pre><code># 导出资源为YAML文件
kubectl get deployment nginx -o yaml > nginx-deployment.yaml

# 导出时去除运行时信息
kubectl get deployment nginx -o yaml --export > nginx-deployment-clean.yaml

# 批量导出资源
kubectl get all -o yaml > all-resources.yaml

# 导出特定命名空间的所有资源
kubectl get all -n my-namespace -o yaml > my-namespace-backup.yaml</code></pre>

                    <h4><i class="fas fa-upload"></i> 10.2.2 应用和验证</h4>
                    <span class="command-category category-advanced">高级操作</span>
                    <pre><code># 应用YAML文件
kubectl apply -f nginx-deployment.yaml

# 批量应用多个文件
kubectl apply -f ./configs/

# 递归应用目录中的所有YAML文件
kubectl apply -f ./configs/ --recursive

# 验证YAML文件语法
kubectl apply -f nginx-deployment.yaml --dry-run=client

# 查看应用后的差异
kubectl diff -f nginx-deployment.yaml

# 强制替换资源
kubectl replace -f nginx-deployment.yaml --force</code></pre>

                    <h3><i class="fas fa-database"></i> 10.3 数据管理</h3>

                    <h4><i class="fas fa-hdd"></i> 10.3.1 持久化卷管理</h4>
                    <span class="command-category category-advanced">高级操作</span>
                    <pre><code># 查看持久化卷
kubectl get pv

# 查看持久化卷声明
kubectl get pvc

# 查看存储类
kubectl get storageclass

# 查看卷快照
kubectl get volumesnapshot

# 创建持久化卷声明
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: my-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
EOF</code></pre>
                </section>

                <section id="troubleshooting">
                    <h2><span class="step-number">11</span>故障排查</h2>

                    <h3><i class="fas fa-bug"></i> 11.1 常见问题诊断</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-terminal"></i> 客户端机器</div>

                    <h4><i class="fas fa-search"></i> 11.1.1 Pod故障排查</h4>
                    <span class="command-category category-debug">故障排查</span>
                    <pre><code># 查看Pod状态和事件
kubectl describe pod problematic-pod

# 查看Pod日志
kubectl logs problematic-pod --previous

# 检查Pod的资源使用
kubectl top pod problematic-pod

# 进入Pod进行调试
kubectl exec -it problematic-pod -- /bin/bash

# 查看Pod的网络连接
kubectl exec problematic-pod -- netstat -tlnp

# 检查Pod的环境变量
kubectl exec problematic-pod -- env</code></pre>

                    <h4><i class="fas fa-network-wired"></i> 11.1.2 网络问题排查</h4>
                    <span class="command-category category-debug">故障排查</span>
                    <pre><code># 检查Service端点
kubectl get endpoints service-name

# 测试Service连通性
kubectl run test-pod --image=busybox --rm -it --restart=Never -- wget -qO- service-name

# 检查DNS解析
kubectl run test-pod --image=busybox --rm -it --restart=Never -- nslookup service-name

# 查看网络策略
kubectl get networkpolicy

# 检查Ingress状态
kubectl describe ingress ingress-name</code></pre>

                    <h3><i class="fas fa-tools"></i> 11.2 调试技巧</h3>

                    <h4><i class="fas fa-microscope"></i> 11.2.1 深度调试</h4>
                    <span class="command-category category-debug">故障排查</span>
                    <pre><code># 创建调试Pod
kubectl run debug-pod --image=busybox --rm -it --restart=Never -- /bin/sh

# 使用特权模式调试
kubectl run debug-pod --image=busybox --rm -it --restart=Never --overrides='{"spec":{"hostNetwork":true,"containers":[{"name":"debug","image":"busybox","stdin":true,"tty":true,"securityContext":{"privileged":true}}]}}'

# 调试网络问题
kubectl run netshoot --image=nicolaka/netshoot --rm -it --restart=Never

# 查看集群DNS配置
kubectl get configmap coredns -n kube-system -o yaml</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 故障排查步骤：</strong>
                        <ol style="margin-top: 15px;">
                            <li><strong>收集信息：</strong>查看Pod状态、日志、事件</li>
                            <li><strong>分析症状：</strong>确定问题的具体表现</li>
                            <li><strong>定位原因：</strong>通过日志和监控找到根本原因</li>
                            <li><strong>验证修复：</strong>应用解决方案并验证效果</li>
                            <li><strong>预防措施：</strong>采取措施防止问题再次发生</li>
                        </ol>
                    </div>

                    <div class="practice-case">
                        <h5><i class="fas fa-play-circle"></i> 综合实践案例：完整的故障排查流程</h5>
                        <p>模拟一个真实的生产环境故障，演示完整的排查和解决过程：</p>

                        <h6><strong>场景：</strong>Web应用无法访问</h6>
                        <p><strong>症状：</strong>用户反馈网站无法访问，返回502错误</p>

                        <pre><code># 第1步：快速检查整体状态
kubectl get pods -l app=web-app
kubectl get services -l app=web-app
kubectl get ingress

# 第2步：检查Pod详细状态
kubectl describe pods -l app=web-app

# 第3步：查看最近的事件
kubectl get events --sort-by=.metadata.creationTimestamp | tail -20

# 第4步：检查应用日志
kubectl logs -l app=web-app --tail=100

# 第5步：检查资源使用情况
kubectl top pods -l app=web-app

# 第6步：网络连通性测试
kubectl run debug-pod --image=busybox --rm -it --restart=Never -- /bin/sh
# 在debug pod中执行：
# wget -qO- http://web-app-service:80
# nslookup web-app-service

# 第7步：检查配置
kubectl get configmap web-app-config -o yaml
kubectl get secret web-app-secret -o yaml</code></pre>

                        <h6><strong>发现问题：</strong></h6>
                        <div class="error-example">Events:
                            Type Reason Age From Message
                            ---- ------ ---- ---- -------
                            Warning Failed 2m (x6 over 8m) kubelet Error: ImagePullBackOff
                            Normal Pulling 2m (x4 over 8m) kubelet Pulling image "web-app:v2.1"
                            Warning Failed 2m (x4 over 8m) kubelet Failed to pull image "web-app:v2.1": rpc error: code
                            = NotFound desc = failed to pull and unpack image</div>

                        <h6><strong>解决方案：</strong></h6>
                        <pre><code># 问题：镜像版本不存在
# 解决：回滚到可用版本
kubectl set image deployment/web-app web-app=web-app:v2.0

# 验证修复
kubectl rollout status deployment/web-app
kubectl get pods -l app=web-app

# 测试应用访问
kubectl port-forward service/web-app 8080:80
curl http://localhost:8080</code></pre>

                        <h6><strong>预防措施：</strong></h6>
                        <ul style="margin-top: 10px;">
                            <li>建立镜像版本管理流程</li>
                            <li>配置健康检查和就绪探针</li>
                            <li>设置资源限制和请求</li>
                            <li>建立监控和告警机制</li>
                        </ul>
                    </div>
                </section>

                <section id="best-practices">
                    <h2><span class="step-number">12</span>最佳实践</h2>

                    <h3><i class="fas fa-star"></i> 12.1 kubectl使用技巧</h3>

                    <h4><i class="fas fa-keyboard"></i> 12.1.1 命令行效率提升</h4>
                    <span class="command-category category-advanced">效率提升</span>
                    <pre><code># 设置kubectl别名
alias k=kubectl

# 设置命令补全
source <(kubectl completion bash)
echo "source <(kubectl completion bash)" >> ~/.bashrc

# 设置默认命名空间
kubectl config set-context --current --namespace=my-namespace

# 使用简写形式
k get po  # 等同于 kubectl get pods
k get svc # 等同于 kubectl get services
k get deploy # 等同于 kubectl get deployments</code></pre>

                    <h4><i class="fas fa-history"></i> 12.1.2 上下文管理</h4>
                    <span class="command-category category-config">配置管理</span>
                    <pre><code># 查看当前上下文
kubectl config current-context

# 查看所有上下文
kubectl config get-contexts

# 切换上下文
kubectl config use-context production-cluster

# 创建新的上下文
kubectl config set-context dev-context --cluster=dev-cluster --user=dev-user --namespace=development

# 删除上下文
kubectl config delete-context old-context</code></pre>

                    <h3><i class="fas fa-shield-alt"></i> 12.2 安全最佳实践</h3>

                    <h4><i class="fas fa-lock"></i> 12.2.1 权限控制</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-user-shield"></i> RBAC最佳实践：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>最小权限原则：</strong>只授予必要的权限</li>
                            <li><strong>角色分离：</strong>为不同用户创建不同的角色</li>
                            <li><strong>定期审计：</strong>定期检查和清理权限</li>
                            <li><strong>命名空间隔离：</strong>使用命名空间进行资源隔离</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-key"></i> 12.2.2 密钥管理</h4>
                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 密钥安全建议：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>避免硬编码：</strong>不要在代码中硬编码密钥</li>
                            <li><strong>使用Secret：</strong>将敏感信息存储在Secret中</li>
                            <li><strong>定期轮换：</strong>定期更换密钥和密码</li>
                            <li><strong>访问控制：</strong>限制对Secret的访问权限</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-graduation-cap"></i> 12.3 学习建议</h3>

                    <h4><i class="fas fa-road"></i> 12.3.1 进阶学习路径</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-trophy"></i> 持续学习建议：</strong>
                        <ul style="margin-top: 15px;">
                            <li><strong>实践为主：</strong>在实际项目中应用所学知识</li>
                            <li><strong>深入理解：</strong>学习Kubernetes的核心概念和原理</li>
                            <li><strong>关注更新：</strong>跟上Kubernetes和kubectl的版本更新</li>
                            <li><strong>社区参与：</strong>参与开源社区，分享经验</li>
                            <li><strong>工具扩展：</strong>学习其他Kubernetes生态工具</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-question-circle"></i> 12.3.2 常见问题解答</h4>
                    <div class="practice-case">
                        <h5><i class="fas fa-lightbulb"></i> FAQ - 常见问题和解决方案</h5>

                        <h6><strong>Q1: kubectl命令执行很慢怎么办？</strong></h6>
                        <pre><code># 检查网络连接
kubectl cluster-info

# 检查kubeconfig配置
kubectl config view

# 使用--request-timeout参数
kubectl get pods --request-timeout=10s

# 检查DNS解析
nslookup your-api-server-domain</code></pre>

                        <h6><strong>Q2: 如何批量操作多个资源？</strong></h6>
                        <pre><code># 使用标签选择器
kubectl delete pods -l app=nginx,version=v1

# 使用文件列表
kubectl apply -f deployment.yaml,service.yaml,configmap.yaml

# 使用目录
kubectl apply -f ./k8s-configs/

# 使用stdin
echo "apiVersion: v1
kind: Pod
metadata:
  name: test-pod
spec:
  containers:
  - name: test
    image: busybox" | kubectl apply -f -</code></pre>

                        <h6><strong>Q3: 如何安全地更新生产环境应用？</strong></h6>
                        <pre><code># 1. 先在测试环境验证
kubectl apply -f new-deployment.yaml --dry-run=client

# 2. 使用滚动更新
kubectl set image deployment/app container=new-image:tag

# 3. 监控更新过程
kubectl rollout status deployment/app

# 4. 如有问题立即回滚
kubectl rollout undo deployment/app

# 5. 验证应用健康状态
kubectl get pods -l app=your-app
kubectl logs -l app=your-app --tail=50</code></pre>

                        <h6><strong>Q4: 如何调试网络连接问题？</strong></h6>
                        <pre><code># 创建网络调试Pod
kubectl run netshoot --image=nicolaka/netshoot --rm -it --restart=Never

# 在调试Pod中测试连接
kubectl exec -it netshoot -- ping google.com
kubectl exec -it netshoot -- nslookup kubernetes.default
kubectl exec -it netshoot -- telnet service-name 80

# 检查Service端点
kubectl get endpoints service-name

# 检查网络策略
kubectl get networkpolicy</code></pre>

                        <h6><strong>Q5: 如何处理资源不足的问题？</strong></h6>
                        <pre><code># 检查节点资源使用
kubectl top nodes
kubectl describe nodes

# 检查Pod资源请求
kubectl get pods -o jsonpath='{range .items[*]}{.metadata.name}{"\t"}{.spec.containers[0].resources.requests}{"\n"}{end}'

# 清理不需要的资源
kubectl delete pods --field-selector=status.phase=Succeeded
kubectl delete pods --field-selector=status.phase=Failed

# 扩容集群或优化资源配置
kubectl scale deployment app --replicas=2</code></pre>
                    </div>

                    <h4><i class="fas fa-book"></i> 12.3.2 推荐资源</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 资源类型</th>
                            <th><i class="fas fa-info-circle"></i> 推荐内容</th>
                            <th><i class="fas fa-link"></i> 获取方式</th>
                        </tr>
                        <tr>
                            <td>官方文档</td>
                            <td>Kubernetes官方文档</td>
                            <td>kubernetes.io</td>
                        </tr>
                        <tr>
                            <td>在线教程</td>
                            <td>Katacoda、Play with Kubernetes</td>
                            <td>免费在线实验环境</td>
                        </tr>
                        <tr>
                            <td>认证考试</td>
                            <td>CKA、CKAD、CKS</td>
                            <td>Linux Foundation</td>
                        </tr>
                        <tr>
                            <td>社区资源</td>
                            <td>GitHub、Stack Overflow</td>
                            <td>开源社区</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-flag-checkered"></i> 12.4 总结</h3>
                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 恭喜您完成kubectl学习！</strong>
                        <p style="margin-top: 15px;">
                            通过本教程的学习，您已经掌握了kubectl的核心功能和使用技巧。kubectl是Kubernetes生态中最重要的工具之一，熟练掌握它将大大提高您的工作效率。</p>

                        <p style="margin-top: 15px;">
                            <strong>记住：</strong>实践是最好的老师。建议您在实际环境中多加练习，逐步提高对kubectl和Kubernetes的理解和应用能力。
                        </p>
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-envelope"></i> 反馈与建议：</strong>
                        <p style="margin-top: 15px;">如果您在学习过程中遇到问题或有改进建议，欢迎通过GitHub Issues或社区群组反馈。您的意见对我们非常宝贵！</p>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <script>
        // 移动端菜单切换
        document.getElementById('mobileMenuBtn').addEventListener('click', function () {
            document.getElementById('sidebar').classList.toggle('active');
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 高亮当前章节
        window.addEventListener('scroll', function () {
            const sections = document.querySelectorAll('section');
            const navLinks = document.querySelectorAll('.sidebar a');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (scrollY >= (sectionTop - 200)) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>

</html>