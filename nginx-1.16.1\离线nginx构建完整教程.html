<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离线nginx Docker构建完整教程</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #2980b9;
        }
        .phase {
            background: #ecf0f1;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 5px solid #3498db;
        }
        .online {
            background: #d5f4e6;
            border-left-color: #27ae60;
        }
        .offline {
            background: #ffeaa7;
            border-left-color: #f39c12;
        }
        .warning {
            background: #fadbd8;
            border-left-color: #e74c3c;
        }
        .code {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Consolas', 'Monaco', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .file-tree {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            border: 1px solid #dee2e6;
        }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            margin-right: 10px;
        }
        .badge-online { background: #27ae60; }
        .badge-offline { background: #f39c12; }
        .badge-important { background: #e74c3c; }
        .highlight {
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ffeaa7;
            margin: 10px 0;
        }
        .checklist {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #c3e6c3;
        }
        .checklist ul {
            margin: 0;
            padding-left: 20px;
        }
        .checklist li {
            margin: 5px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .file-purpose {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐳 离线nginx Docker构建完整教程</h1>
        
        <div class="highlight">
            <strong>📌 重要说明：</strong>本教程专门针对断网环境下的nginx Docker镜像构建，基于CentOS 7镜像，使用RPM包方式安装所有编译依赖，包含SSL、HTTP/2等完整模块。
        </div>

        <h2>📋 方案概述</h2>
        <p>由于现场是断网环境，我们采用完全离线的构建方案：</p>
        <ul>
            <li><span class="badge badge-online">联网阶段</span>下载nginx源码包和所有CentOS 7编译依赖RPM包</li>
            <li><span class="badge badge-offline">断网阶段</span>在现场环境中离线安装RPM包并构建Docker镜像</li>
        </ul>

        <h2>📁 项目文件结构及用途</h2>
        <div class="file-tree">
nginx-offline-build/
├── 📄 Dockerfile                           # Docker镜像构建文件
├── 📄 download-all-packages.sh             # 🌐 联网环境：下载所有依赖包
├── 📄 build-nginx-offline.sh               # 🔌 断网环境：构建nginx镜像
├── 📄 test-nginx.sh                        # 🔌 断网环境：测试nginx容器
├── 📄 离线nginx构建完整教程.html           # 本教程文件
├── 📂 scripts/                             # 构建脚本目录
│   └── build-nginx.sh                      # nginx编译脚本
├── 📂 config/                              # nginx配置文件目录
│   ├── nginx.conf                          # nginx主配置文件
│   └── default.conf                        # 默认站点配置
├── 📂 packages/                            # 🌐 nginx源码包目录
│   ├── nginx-1.24.0.tar.gz                # nginx源码
│   ├── pcre-8.45.tar.gz                   # PCRE库源码
│   ├── zlib-1.2.13.tar.gz                 # zlib库源码
│   └── openssl-1.1.1w.tar.gz              # OpenSSL库源码
└── 📂 centos7-rpms/                        # 🌐 CentOS 7编译依赖RPM包目录
    ├── gcc-4.8.5-44.el7.x86_64.rpm        # C编译器
    ├── gcc-c++-4.8.5-44.el7.x86_64.rpm    # C++编译器
    ├── make-3.82-24.el7.x86_64.rpm        # 构建工具
    ├── pcre-devel-8.32-17.el7.x86_64.rpm  # PCRE开发库
    ├── zlib-devel-1.2.7-18.el7.x86_64.rpm # zlib开发库
    ├── openssl-devel-1.0.2k-25.el7_9.x86_64.rpm # OpenSSL开发库
    ├── GeoIP-devel-1.5.0-14.el7.x86_64.rpm # GeoIP开发库
    └── ... (其他依赖RPM包)
        </div>

        <h2>📝 各文件用途详解</h2>
        
        <div class="file-purpose">
            <h3>🌐 联网环境文件</h3>
            <ul>
                <li><strong>download-all-packages.sh</strong> - 自动下载nginx源码包和所有CentOS 7编译依赖RPM包的脚本</li>
            </ul>
        </div>

        <div class="file-purpose">
            <h3>🔌 断网环境文件</h3>
            <ul>
                <li><strong>Dockerfile</strong> - Docker镜像构建文件，定义如何构建nginx镜像</li>
                <li><strong>build-nginx-offline.sh</strong> - 离线构建脚本，检查环境并构建Docker镜像</li>
                <li><strong>test-nginx.sh</strong> - 测试脚本，验证nginx容器是否正常工作</li>
                <li><strong>scripts/build-nginx.sh</strong> - 在Docker容器内编译nginx的脚本</li>
                <li><strong>config/nginx.conf</strong> - nginx主配置文件</li>
                <li><strong>config/default.conf</strong> - 默认站点配置文件</li>
            </ul>
        </div>

        <h2>🌐 第一阶段：联网环境准备</h2>
        
        <div class="phase online">
            <h3><span class="badge badge-online">联网</span>步骤1：下载项目文件</h3>
            <p>确保你有以下所有项目文件：</p>
            <div class="checklist">
                <ul>
                    <li>✅ Dockerfile</li>
                    <li>✅ download-all-packages.sh</li>
                    <li>✅ build-nginx-offline.sh</li>
                    <li>✅ test-nginx.sh</li>
                    <li>✅ scripts/build-nginx.sh</li>
                    <li>✅ config/nginx.conf</li>
                    <li>✅ config/default.conf</li>
                    <li>✅ 离线nginx构建完整教程.html</li>
                </ul>
            </div>
        </div>

        <div class="phase online">
            <h3><span class="badge badge-online">联网</span>步骤2：运行下载脚本</h3>
            <p>在联网环境中运行一键下载脚本：</p>
            <div class="code">
# 给脚本执行权限
chmod +x download-all-packages.sh

# 执行下载脚本（会自动下载nginx源码包和所有RPM依赖包）
./download-all-packages.sh
            </div>
            <p>脚本会自动下载：</p>
            <ul>
                <li><strong>nginx源码包</strong>：nginx-1.24.0.tar.gz、pcre-8.45.tar.gz、zlib-1.2.13.tar.gz、openssl-1.1.1w.tar.gz</li>
                <li><strong>CentOS 7 RPM包</strong>：gcc、gcc-c++、make、各种-devel包等约20个RPM包</li>
            </ul>
        </div>

        <div class="phase online">
            <h3><span class="badge badge-online">联网</span>步骤3：验证下载结果</h3>
            <div class="code">
# 检查nginx源码包
ls -la packages/
# 应该看到4个.tar.gz文件

# 检查RPM包
ls -la centos7-rpms/
# 应该看到约20个.rpm文件
            </div>
        </div>

        <div class="phase online">
            <h3><span class="badge badge-online">联网</span>步骤4：打包传输</h3>
            <div class="code">
# 打包整个项目目录
tar -czf nginx-offline-build.tar.gz .

# 或者使用zip
zip -r nginx-offline-build.zip .
            </div>
            <div class="warning">
                <strong>⚠️ 重要：</strong>确保packages目录和centos7-rpms目录及其中的所有文件都包含在打包文件中！
            </div>
        </div>

        <h2>🔌 第二阶段：断网环境构建</h2>

        <div class="phase offline">
            <h3><span class="badge badge-offline">断网</span>步骤1：解压项目文件</h3>
            <div class="code">
# 解压项目文件
tar -xzf nginx-offline-build.tar.gz
# 或者
unzip nginx-offline-build.zip

# 进入项目目录
cd nginx-offline-build
            </div>
        </div>

        <div class="phase offline">
            <h3><span class="badge badge-offline">断网</span>步骤2：运行构建脚本</h3>
            <div class="code">
# 给脚本执行权限
chmod +x build-nginx-offline.sh

# 运行离线构建脚本
./build-nginx-offline.sh
            </div>
            <p>构建脚本会自动完成：</p>
            <ul>
                <li>检查Docker环境</li>
                <li>验证所有依赖文件</li>
                <li>构建nginx Docker镜像</li>
                <li>可选择启动nginx容器</li>
            </ul>
        </div>

        <div class="phase offline">
            <h3><span class="badge badge-offline">断网</span>步骤3：测试验证</h3>
            <div class="code">
# 运行测试脚本
chmod +x test-nginx.sh
./test-nginx.sh
            </div>
            <p>测试脚本会验证：</p>
            <ul>
                <li>Docker镜像是否构建成功</li>
                <li>nginx容器是否正常启动</li>
                <li>HTTP连接是否正常</li>
                <li>nginx配置是否正确</li>
                <li>所有必需模块是否启用</li>
            </ul>
        </div>

        <h2>📊 nginx编译模块清单</h2>
        <table>
            <tr>
                <th>模块名称</th>
                <th>功能说明</th>
                <th>编译参数</th>
            </tr>
            <tr>
                <td>SSL/TLS支持</td>
                <td>HTTPS加密传输</td>
                <td>--with-http_ssl_module</td>
            </tr>
            <tr>
                <td>HTTP/2支持</td>
                <td>HTTP/2协议支持</td>
                <td>--with-http_v2_module</td>
            </tr>
            <tr>
                <td>真实IP模块</td>
                <td>获取客户端真实IP</td>
                <td>--with-http_realip_module</td>
            </tr>
            <tr>
                <td>认证请求模块</td>
                <td>外部认证支持</td>
                <td>--with-http_auth_request_module</td>
            </tr>
            <tr>
                <td>安全链接模块</td>
                <td>防盗链功能</td>
                <td>--with-http_secure_link_module</td>
            </tr>
            <tr>
                <td>流SSL模块</td>
                <td>TCP/UDP代理SSL</td>
                <td>--with-stream_ssl_module</td>
            </tr>
            <tr>
                <td>状态模块</td>
                <td>nginx状态监控</td>
                <td>--with-http_stub_status_module</td>
            </tr>
            <tr>
                <td>流模块</td>
                <td>TCP/UDP代理</td>
                <td>--with-stream</td>
            </tr>
            <tr>
                <td>静态gzip模块</td>
                <td>预压缩文件服务</td>
                <td>--with-http_gzip_static_module</td>
            </tr>
            <tr>
                <td>线程支持</td>
                <td>多线程处理</td>
                <td>--with-threads</td>
            </tr>
            <tr>
                <td>文件异步IO</td>
                <td>异步文件操作</td>
                <td>--with-file-aio</td>
            </tr>
        </table>

        <h2>🔧 常用管理命令</h2>
        <div class="phase">
            <h3>容器管理</h3>
            <div class="code">
# 查看容器状态
docker ps

# 查看镜像
docker images nginx-offline

# 启动容器
docker run -d -p 80:80 --name nginx-server nginx-offline:latest

# 停止容器  
docker stop nginx-offline-server

# 重启容器
docker restart nginx-offline-server

# 删除容器
docker rm nginx-offline-server

# 查看容器日志
docker logs nginx-offline-server
            </div>
        </div>

        <div class="phase">
            <h3>nginx管理</h3>
            <div class="code">
# 进入容器
docker exec -it nginx-offline-server /bin/bash

# 测试nginx配置
docker exec nginx-offline-server /usr/local/nginx/sbin/nginx -t

# 重新加载配置
docker exec nginx-offline-server /usr/local/nginx/sbin/nginx -s reload

# 查看nginx进程
docker exec nginx-offline-server ps aux | grep nginx

# 查看nginx版本和模块
docker exec nginx-offline-server /usr/local/nginx/sbin/nginx -V
            </div>
        </div>

        <h2>❗ 故障排除</h2>
        
        <div class="phase warning">
            <h3>常见问题及解决方案</h3>
            
            <h4>1. 下载失败</h4>
            <div class="code">
# 检查网络连接
ping google.com

# 手动下载失败的包
wget -O packages/nginx-1.24.0.tar.gz http://nginx.org/download/nginx-1.24.0.tar.gz
            </div>

            <h4>2. Docker构建失败</h4>
            <div class="code">
# 查看详细构建日志
docker build -t nginx-offline:latest . --no-cache

# 检查依赖文件
ls -la packages/ centos7-rpms/
            </div>

            <h4>3. 容器启动失败</h4>
            <div class="code">
# 查看容器日志
docker logs nginx-offline-server

# 检查端口占用
netstat -tlnp | grep :80

# 使用其他端口
docker run -d -p 8080:80 --name nginx-server nginx-offline:latest
            </div>

            <h4>4. nginx配置错误</h4>
            <div class="code">
# 测试配置文件
docker exec nginx-offline-server /usr/local/nginx/sbin/nginx -t

# 查看错误日志
docker exec nginx-offline-server cat /var/log/nginx/error.log
            </div>
        </div>

        <h2>✅ 验收测试清单</h2>
        <div class="checklist">
            <strong>构建完成后，请执行以下测试确认nginx正常工作：</strong>
            <ul>
                <li>✅ 镜像构建成功：<code>docker images nginx-offline</code></li>
                <li>✅ 容器正常启动：<code>docker ps</code></li>
                <li>✅ HTTP访问正常：<code>curl http://localhost</code></li>
                <li>✅ nginx进程运行：<code>docker exec nginx-offline-server ps aux | grep nginx</code></li>
                <li>✅ 配置文件正确：<code>docker exec nginx-offline-server /usr/local/nginx/sbin/nginx -t</code></li>
                <li>✅ 模块加载完整：<code>docker exec nginx-offline-server /usr/local/nginx/sbin/nginx -V</code></li>
                <li>✅ 日志正常输出：<code>docker logs nginx-offline-server</code></li>
            </ul>
        </div>

        <div class="highlight">
            <strong>🎉 恭喜！</strong>如果以上测试都通过，说明nginx Docker镜像构建成功，可以投入使用了！
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background: #ecf0f1; border-radius: 10px;">
            <h3>📋 快速参考卡片</h3>
            <p><strong>联网环境：</strong>运行 <code>./download-all-packages.sh</code></p>
            <p><strong>断网环境：</strong>运行 <code>./build-nginx-offline.sh</code></p>
            <p><strong>测试验证：</strong>运行 <code>./test-nginx.sh</code></p>
            <p><strong>镜像名称：</strong><code>nginx-offline:latest</code></p>
            <p><strong>容器名称：</strong><code>nginx-offline-server</code></p>
        </div>
    </div>
</body>
</html>
