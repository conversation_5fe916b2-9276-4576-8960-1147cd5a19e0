# PostgreSQL 11.19 在线版 pgAdmin权限修复脚本
# 版本: 1.0
# 作者: AI Assistant
# 日期: 2025-07-12
#
# 用法:
#   .\fix-pgadmin-permissions.ps1
#   .\fix-pgadmin-permissions.ps1 -Restart

param(
    [switch]$Restart
)

# 日志记录函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogContent = "[$Time] [$Level] $Message"

    switch ($Level) {
        "ERROR" { Write-Host $LogContent -ForegroundColor Red }
        "WARN"  { Write-Host $LogContent -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $LogContent -ForegroundColor Green }
        default { Write-Host $LogContent -ForegroundColor White }
    }
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "pgAdmin权限修复脚本 v1.0" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

try {
    # 检查Docker环境
    Write-Log "检查Docker环境..."
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker未运行或未安装"
    }
    Write-Log "Docker版本: $dockerVersion" "SUCCESS"

    # 检查pgAdmin容器状态
    Write-Log "检查pgAdmin容器状态..."
    $PgAdminStatus = docker inspect pgadmin4 --format='{{.State.Status}}' 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Log "pgAdmin容器状态: $PgAdminStatus"
        
        if ($PgAdminStatus -eq "restarting") {
            Write-Log "检测到pgAdmin正在重启，这通常是权限问题" "WARN"
        }
    } else {
        Write-Log "pgAdmin容器不存在" "WARN"
        exit 1
    }

    # 停止pgAdmin容器
    if ($Restart -or $PgAdminStatus -eq "restarting") {
        Write-Log "停止pgAdmin容器..."
        docker stop pgadmin4 2>&1 | Out-Null
        Start-Sleep -Seconds 3
    }

    # 修复pgAdmin数据卷权限
    Write-Log "修复pgAdmin数据卷权限..."
    docker run --rm -v pgadmin_data_online:/data alpine sh -c "
        # 创建必要的目录结构
        mkdir -p /data/storage
        mkdir -p /data/sessions
        mkdir -p /data/azurecredentialcache
        
        # 设置正确的所有者和权限
        chown -R 5050:5050 /data 2>/dev/null || chown -R 5050:0 /data 2>/dev/null || true
        chmod -R 755 /data
        
        # 设置特定文件权限
        find /data -name '*.db' -exec chmod 644 {} \; 2>/dev/null || true
        find /data -name '*.log' -exec chmod 644 {} \; 2>/dev/null || true
        find /data -name '*.json' -exec chmod 644 {} \; 2>/dev/null || true
        find /data -name '*.conf' -exec chmod 644 {} \; 2>/dev/null || true
        
        # 确保关键目录权限
        chmod 755 /data/storage 2>/dev/null || true
        chmod 755 /data/sessions 2>/dev/null || true
        chmod 755 /data/azurecredentialcache 2>/dev/null || true
        
        echo 'pgAdmin权限修复完成'
    " 2>&1 | Out-Null

    if ($LASTEXITCODE -eq 0) {
        Write-Log "pgAdmin数据卷权限修复成功" "SUCCESS"
    } else {
        Write-Log "pgAdmin数据卷权限修复失败" "ERROR"
    }

    # 重启pgAdmin容器
    if ($Restart -or $PgAdminStatus -eq "restarting") {
        Write-Log "启动pgAdmin容器..."
        docker start pgadmin4 2>&1 | Out-Null
        
        # 等待pgAdmin启动
        Write-Log "等待pgAdmin启动..."
        $Count = 0
        $MaxRetries = 10
        $Started = $false

        do {
            Start-Sleep -Seconds 5
            $Count++
            Write-Log "检查pgAdmin状态 (尝试 $Count/$MaxRetries)..."

            $Status = docker inspect pgadmin4 --format='{{.State.Status}}' 2>$null
            if ($Status -eq "running") {
                Write-Log "pgAdmin容器运行正常" "SUCCESS"

                # 检查HTTP响应，减少超时时间
                try {
                    $Response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 3 -UseBasicParsing -ErrorAction Stop
                    if ($Response.StatusCode -eq 200) {
                        Write-Log "pgAdmin启动成功，Web界面可访问" "SUCCESS"
                        $Started = $true
                        break
                    }
                } catch {
                    # 如果容器运行超过30秒，认为启动成功
                    $ContainerStartTime = docker inspect pgadmin4 --format='{{.State.StartedAt}}' 2>$null
                    if ($ContainerStartTime) {
                        try {
                            $StartTime = [DateTime]::Parse($ContainerStartTime.Replace('T', ' ').Replace('Z', ''))
                            $RunningTime = (Get-Date).ToUniversalTime() - $StartTime
                            if ($RunningTime.TotalSeconds -gt 30) {
                                Write-Log "pgAdmin容器已运行超过30秒，认为启动成功" "SUCCESS"
                                $Started = $true
                                break
                            }
                        } catch {
                            # 继续检查
                        }
                    }
                    Write-Log "Web界面暂未响应，继续等待..." "INFO"
                }
            } elseif ($Status -eq "restarting") {
                Write-Log "pgAdmin仍在重启中..." "WARN"
            }
        } while ($Count -lt $MaxRetries)

        if (-not $Started) {
            Write-Log "pgAdmin启动检查超时，但容器可能正常运行" "WARN"
            Write-Log "请手动访问 http://localhost:8080 检查状态" "INFO"
        }
    }

    # 最终状态检查
    Write-Log "最终状态检查..."
    $FinalStatus = docker inspect pgadmin4 --format='{{.State.Status}}' 2>$null
    Write-Log "pgAdmin容器最终状态: $FinalStatus"
    
    if ($FinalStatus -eq "running") {
        Write-Log "pgAdmin权限修复完成，服务正常运行" "SUCCESS"
        Write-Log "pgAdmin访问地址: http://localhost:8080" "SUCCESS"
    } else {
        Write-Log "pgAdmin可能仍有问题，建议查看日志: docker logs pgadmin4" "WARN"
    }

} catch {
    Write-Log "权限修复失败: $($_.Exception.Message)" "ERROR"
    exit 1
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "pgAdmin权限修复脚本执行完成" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
