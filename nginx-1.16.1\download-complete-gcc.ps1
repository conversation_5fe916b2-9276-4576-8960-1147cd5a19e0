# PowerShell脚本：下载完整的CentOS 7 GCC工具链
# 使用阿里云镜像源，更稳定可靠

Write-Host "================================================================================"
Write-Host "                    下载完整的CentOS 7 GCC工具链"
Write-Host "================================================================================"

# 创建下载目录
$downloadDir = "centos7-gcc-complete"
if (!(Test-Path $downloadDir)) {
    New-Item -ItemType Directory -Path $downloadDir
}

# 阿里云CentOS 7镜像源（更稳定）
$baseUrl = "https://mirrors.aliyun.com/centos/7.9.2009/os/x86_64/Packages"

# 完整的GCC工具链包列表
$gccPackages = @(
    # 核心GCC包
    "gcc-4.8.5-44.el7.x86_64.rpm",
    "gcc-c++-4.8.5-44.el7.x86_64.rpm",
    
    # GCC依赖的数学库
    "libmpc-1.0.1-3.el7.x86_64.rpm",
    "mpfr-3.1.1-4.el7.x86_64.rpm", 
    "gmp-6.0.0-15.el7.x86_64.rpm",
    
    # 开发库和头文件
    "libstdc++-4.8.5-44.el7.x86_64.rpm",
    "libstdc++-devel-4.8.5-44.el7.x86_64.rpm",
    "libgcc-4.8.5-44.el7.x86_64.rpm",
    "glibc-devel-2.17-326.el7_9.x86_64.rpm",
    "glibc-headers-2.17-326.el7_9.x86_64.rpm",
    "kernel-headers-3.10.0-1160.102.1.el7.x86_64.rpm",
    
    # 构建工具
    "make-3.82-24.el7.x86_64.rpm",
    "binutils-2.27-44.base.el7.x86_64.rpm",
    
    # SSL/TLS开发库
    "openssl-1.0.2k-25.el7_9.x86_64.rpm",
    "openssl-devel-1.0.2k-25.el7_9.x86_64.rpm",
    "openssl-libs-1.0.2k-25.el7_9.x86_64.rpm",
    "krb5-devel-1.15.1-55.el7_9.x86_64.rpm",
    "keyutils-libs-devel-1.5.8-3.el7.x86_64.rpm",
    "libcom_err-devel-1.42.9-19.el7.x86_64.rpm",
    "libselinux-devel-2.5-15.el7.x86_64.rpm",
    "libsepol-devel-2.5-10.el7.x86_64.rpm",
    "libverto-devel-0.2.5-4.el7.x86_64.rpm",
    
    # PCRE开发库
    "pcre-8.32-17.el7.x86_64.rpm",
    "pcre-devel-8.32-17.el7.x86_64.rpm",
    
    # zlib开发库
    "zlib-1.2.7-18.el7.x86_64.rpm",
    "zlib-devel-1.2.7-18.el7.x86_64.rpm",
    
    # GeoIP库（nginx模块可能需要）
    "GeoIP-1.5.0-14.el7.x86_64.rpm",
    "GeoIP-devel-1.5.0-14.el7.x86_64.rpm"
)

Write-Host "开始下载完整的GCC工具链包..."
Write-Host "使用阿里云镜像源: $baseUrl"
Write-Host ""

$successCount = 0
$failedPackages = @()

foreach ($package in $gccPackages) {
    Write-Host "下载: $package" -ForegroundColor Cyan
    $url = "$baseUrl/$package"
    $outputPath = "$downloadDir\$package"
    
    try {
        # 检查文件是否已存在
        if (Test-Path $outputPath) {
            Write-Host "  ✓ 文件已存在，跳过下载" -ForegroundColor Yellow
            $successCount++
            continue
        }
        
        Invoke-WebRequest -Uri $url -OutFile $outputPath -ErrorAction Stop
        Write-Host "  ✓ 下载成功" -ForegroundColor Green
        $successCount++
    }
    catch {
        Write-Host "  ❌ 下载失败: $($_.Exception.Message)" -ForegroundColor Red
        $failedPackages += $package
        
        # 尝试备用源
        $altUrls = @(
            "https://mirrors.cloud.tencent.com/centos/7.9.2009/os/x86_64/Packages",
            "https://mirrors.huaweicloud.com/centos/7.9.2009/os/x86_64/Packages",
            "http://vault.centos.org/7.9.2009/os/x86_64/Packages"
        )
        
        $downloaded = $false
        foreach ($altUrl in $altUrls) {
            try {
                $altFullUrl = "$altUrl/$package"
                Write-Host "  尝试备用源: $($altUrl -replace 'https?://([^/]+)/.*', '$1')" -ForegroundColor Yellow
                Invoke-WebRequest -Uri $altFullUrl -OutFile $outputPath -ErrorAction Stop
                Write-Host "  ✓ 从备用源下载成功" -ForegroundColor Green
                $downloaded = $true
                $successCount++
                $failedPackages = $failedPackages | Where-Object { $_ -ne $package }
                break
            }
            catch {
                # 静默失败，继续尝试下一个源
            }
        }
        
        if (!$downloaded) {
            Write-Host "  ❌ 所有源都下载失败" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "================================================================================"
Write-Host "                        下载完成统计"
Write-Host "================================================================================"
Write-Host "总包数: $($gccPackages.Count)" -ForegroundColor White
Write-Host "成功下载: $successCount" -ForegroundColor Green
Write-Host "下载失败: $($failedPackages.Count)" -ForegroundColor Red

if ($failedPackages.Count -gt 0) {
    Write-Host ""
    Write-Host "失败的包:" -ForegroundColor Red
    foreach ($pkg in $failedPackages) {
        Write-Host "  - $pkg" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "下载的包保存在: $downloadDir\"
Write-Host ""
Write-Host "接下来的步骤:"
Write-Host "1. 备份当前的centos7-rpms目录:"
Write-Host "   Rename-Item centos7-rpms centos7-rpms-backup"
Write-Host ""
Write-Host "2. 将新下载的完整包复制到centos7-rpms目录:"
Write-Host "   Copy-Item $downloadDir centos7-rpms -Recurse"
Write-Host ""
Write-Host "3. 重新运行Docker构建:"
Write-Host "   docker build -t nginx-offline-build -f Dockerfile ."
Write-Host "================================================================================"
