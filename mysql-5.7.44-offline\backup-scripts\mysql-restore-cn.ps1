# MySQL 5.7.44 离线版完整恢复脚本
# 版本: 2.1
# 作者: AI Assistant
# 日期: 2025-07-12
#
# 用法:
#   .\mysql-restore-cn.ps1 -BackupPath "path\to\backup"
#   .\mysql-restore-cn.ps1 -BackupPath "path\to\backup" -VerboseLog

param(
    [Parameter(Mandatory=$true)]
    [string]$BackupPath,
    [switch]$ForceRestore,
    [switch]$VerboseLog
)

# 全局变量
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$TempDir = Join-Path (Get-Location) "temp_restore_$Timestamp"
$LogFile = Join-Path $BackupPath "restore_log_$Timestamp.txt"

# 日志记录函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogContent = "[$Time] [$Level] $Message"

    switch ($Level) {
        "ERROR" { Write-Host $LogContent -ForegroundColor Red }
        "WARN"  { Write-Host $LogContent -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $LogContent -ForegroundColor Green }
        default { Write-Host $LogContent -ForegroundColor White }
    }

    if (Test-Path $BackupPath) {
        try {
            $LogDir = Split-Path $LogFile -Parent
            if (!(Test-Path $LogDir)) {
                New-Item -ItemType Directory -Path $LogDir -Force | Out-Null
            }
            $LogContent | Out-File -FilePath $LogFile -Append -Encoding UTF8 -Force
        }
        catch {
            Write-Warning "无法写入日志文件: $($_.Exception.Message)"
        }
    }
}

# 进度显示函数
function Show-Progress {
    param([string]$Activity, [string]$Status, [int]$PercentComplete)
    Write-Progress -Activity $Activity -Status $Status -PercentComplete $PercentComplete
}

# 获取容器用户ID函数
function Get-ContainerUserId {
    param([string]$ImageName, [string]$UserName)
    try {
        $UserId = docker run --rm --entrypoint="" $ImageName id -u $UserName 2>$null
        if ($LASTEXITCODE -eq 0) {
            return $UserId.Trim()
        }
    } catch {}
    return $null
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "MySQL 5.7.44 离线版完整恢复脚本 v2.1" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Log "开始MySQL完整恢复"
Write-Log "备份路径: $BackupPath"
Write-Log "临时目录: $TempDir"

# 验证备份路径
if (-not (Test-Path $BackupPath)) {
    Write-Log "错误: 备份路径不存在: $BackupPath" "ERROR"
    exit 1
}

# 创建临时目录
try {
    New-Item -ItemType Directory -Path $TempDir -Force | Out-Null
    Write-Log "临时目录创建成功" "SUCCESS"
} catch {
    Write-Log "创建临时目录失败: $($_.Exception.Message)" "ERROR"
    exit 1
}

try {
    # 步骤1: 检查Docker环境
    Show-Progress "环境检查" "检查Docker环境..." 5
    Write-Log "检查Docker环境..."
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker未运行或未安装"
    }
    Write-Log "Docker版本: $dockerVersion" "SUCCESS"

    # 步骤2: 验证备份文件
    Show-Progress "环境检查" "验证备份文件..." 10
    Write-Log "验证备份文件..."
    $RequiredFiles = @(
        "mysql_data_volume.zip",
        "mysql_logs_volume.zip"
    )

    $MissingFiles = @()
    foreach ($File in $RequiredFiles) {
        $FilePath = Join-Path $BackupPath $File
        if (-not (Test-Path $FilePath)) {
            $MissingFiles += $File
            Write-Log "警告: 缺少备份文件 $File" "WARN"
        } else {
            $Size = [math]::Round((Get-Item $FilePath).Length / 1MB, 2)
            Write-Log "找到备份文件: $File (${Size}MB)" "SUCCESS"
        }
    }

    if ($MissingFiles.Count -gt 0 -and -not $ForceRestore) {
        throw "缺少关键备份文件，使用 -ForceRestore 参数继续"
    }

    # 步骤3: 获取容器用户ID
    Show-Progress "环境检查" "检测容器用户ID..." 15
    Write-Log "检测容器用户ID..."
    $MySQLUserId = Get-ContainerUserId "mysql-offline:5.7.44" "mysql"

    if ($MySQLUserId) {
        Write-Log "MySQL用户ID: $MySQLUserId" "SUCCESS"
    } else {
        Write-Log "无法检测MySQL用户ID，使用默认值999" "WARN"
        $MySQLUserId = "999"
    }

    # 步骤4: 停止现有服务
    Show-Progress "准备恢复" "停止现有服务..." 20
    Write-Log "停止现有服务..."
    docker-compose down 2>&1 | Out-Null
    Start-Sleep -Seconds 5
    Write-Log "现有服务已停止" "SUCCESS"

    # 步骤5: 删除现有数据卷
    Show-Progress "准备恢复" "删除现有数据卷..." 25
    Write-Log "删除现有数据卷..."
    $VolumeList = @("mysql_data_offline", "mysql_logs_offline")
    foreach ($VolumeName in $VolumeList) {
        docker volume rm $VolumeName -f 2>$null | Out-Null
        Write-Log "已删除数据卷: $VolumeName" "SUCCESS"
    }

    # 步骤6: 恢复配置文件
    Show-Progress "恢复配置" "恢复配置文件..." 30
    Write-Log "恢复配置文件..."
    $ConfigFileList = @("docker-compose.yaml", "Dockerfile", "my.cnf", "init-mysql.sql")
    $RestoredFiles = 0
    foreach ($File in $ConfigFileList) {
        $BackupFile = Join-Path $BackupPath $File
        if (Test-Path $BackupFile) {
            Copy-Item $BackupFile -Destination . -Force
            Write-Log "已恢复: $File" "SUCCESS"
            $RestoredFiles++
        }
    }
    Write-Log "配置文件恢复完成，已恢复 $RestoredFiles 个文件" "SUCCESS"

    # 步骤7: 创建新数据卷
    Show-Progress "恢复数据卷" "创建新数据卷..." 35
    Write-Log "创建新数据卷..."
    foreach ($VolumeName in $VolumeList) {
        docker volume create $VolumeName 2>&1 | Out-Null
        Write-Log "已创建数据卷: $VolumeName" "SUCCESS"
    }

    # 步骤8: 恢复MySQL主数据卷
    Show-Progress "恢复数据卷" "恢复MySQL主数据卷..." 45
    Write-Log "恢复MySQL主数据卷..."
    $DataVolumeFile = Join-Path $BackupPath "mysql_data_volume.zip"
    if (Test-Path $DataVolumeFile) {
        try {
            # 创建临时解压目录
            $TempExtractDir = Join-Path $env:TEMP "mysql_restore_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            New-Item -ItemType Directory -Path $TempExtractDir -Force | Out-Null

            # 解压备份文件
            Expand-Archive -Path $DataVolumeFile -DestinationPath $TempExtractDir -Force

            # 使用docker cp方法恢复数据卷
            $RestoreContainer = docker create -v mysql_data_offline:/data alpine 2>&1
            if ($LASTEXITCODE -eq 0) {
                # 检查解压目录结构
                $DataSourcePath = if (Test-Path "${TempExtractDir}/mysql_data") { "${TempExtractDir}/mysql_data/." } else { "${TempExtractDir}/data/." }
                docker cp $DataSourcePath "${RestoreContainer}:/data/" 2>&1 | Out-Null
                docker rm $RestoreContainer 2>&1 | Out-Null

                # 修复数据目录权限
                Write-Log "修复数据目录权限..."
                docker run --rm -v mysql_data_offline:/data alpine sh -c "chown -R ${MySQLUserId}:${MySQLUserId} /data && chmod 755 /data && find /data -type d -exec chmod 755 {} \; && find /data -type f -exec chmod 644 {} \;" 2>&1 | Out-Null
                Write-Log "MySQL主数据卷恢复完成" "SUCCESS"
            } else {
                Write-Log "创建恢复容器失败" "ERROR"
            }

            # 清理临时目录
            Remove-Item $TempExtractDir -Recurse -Force -ErrorAction SilentlyContinue
        } catch {
            Write-Log "MySQL主数据卷恢复失败: $($_.Exception.Message)" "ERROR"
        }
    }

    # 步骤9: 恢复MySQL日志卷
    Show-Progress "恢复数据卷" "恢复MySQL日志卷..." 55
    Write-Log "恢复MySQL日志卷..."
    $LogVolumeFile = Join-Path $BackupPath "mysql_logs_volume.zip"
    if (Test-Path $LogVolumeFile) {
        try {
            # 创建临时解压目录
            $TempExtractDir = Join-Path $env:TEMP "mysql_logs_restore_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            New-Item -ItemType Directory -Path $TempExtractDir -Force | Out-Null

            # 解压备份文件
            Expand-Archive -Path $LogVolumeFile -DestinationPath $TempExtractDir -Force

            # 使用docker cp方法恢复日志卷
            $RestoreContainer = docker create -v mysql_logs_offline:/data alpine 2>&1
            if ($LASTEXITCODE -eq 0) {
                # 检查解压目录结构
                $LogSourcePath = if (Test-Path "${TempExtractDir}/mysql_logs") { "${TempExtractDir}/mysql_logs/." } else { "${TempExtractDir}/data/." }
                docker cp $LogSourcePath "${RestoreContainer}:/data/" 2>&1 | Out-Null
                docker rm $RestoreContainer 2>&1 | Out-Null

                # 修复日志目录权限
                Write-Log "修复日志目录权限..."
                docker run --rm -v mysql_logs_offline:/data alpine sh -c "chown -R ${MySQLUserId}:${MySQLUserId} /data && chmod 755 /data" 2>&1 | Out-Null
                Write-Log "MySQL日志卷恢复完成" "SUCCESS"
            } else {
                Write-Log "创建日志恢复容器失败" "WARN"
            }

            # 清理临时目录
            Remove-Item $TempExtractDir -Recurse -Force -ErrorAction SilentlyContinue
        } catch {
            Write-Log "MySQL日志卷恢复失败: $($_.Exception.Message)" "WARN"
        }
    }

    # 步骤10: 启动服务
    Show-Progress "启动服务" "启动MySQL服务..." 70
    Write-Log "启动MySQL服务..."
    docker-compose up -d 2>&1 | Out-Null

    # 步骤11: 等待MySQL启动
    Show-Progress "启动服务" "等待MySQL启动..." 80
    Write-Log "等待MySQL启动..."
    $Count = 0
    $MaxRetries = 20
    do {
        Start-Sleep -Seconds 5
        $Count++
        Write-Log "检查MySQL状态 (尝试 $Count/$MaxRetries)..."
        $Status = docker exec mysql5.7.44 mysqladmin ping -u root -proot 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Log "MySQL服务启动成功" "SUCCESS"
            break
        }
    } while ($Count -lt $MaxRetries)

    if ($Count -ge $MaxRetries) {
        Write-Log "MySQL服务启动超时，请手动检查" "WARN"
    }

    # 步骤12: 恢复SQL数据（如果可用）
    Show-Progress "恢复SQL数据" "恢复SQL数据..." 85
    Write-Log "恢复SQL数据..."
    $SqlBackupFile = Join-Path $BackupPath "mysql_all_databases.sql.zip"
    if (Test-Path $SqlBackupFile) {
        try {
            # 解压SQL备份
            $SqlTempDir = Join-Path $env:TEMP "mysql_sql_restore_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            if (Test-Path $SqlTempDir) {
                Remove-Item $SqlTempDir -Recurse -Force -ErrorAction SilentlyContinue
            }
            New-Item -ItemType Directory -Path $SqlTempDir -Force | Out-Null
            Expand-Archive -Path $SqlBackupFile -DestinationPath $SqlTempDir -Force

            $SqlFile = Get-ChildItem -Path $SqlTempDir -Filter "*.sql" | Select-Object -First 1
            if ($SqlFile -and (Test-Path $SqlFile.FullName)) {
                Write-Log "从以下文件导入SQL数据: $($SqlFile.Name)"

                # 首先验证SQL文件内容
                $FirstLine = Get-Content $SqlFile.FullName -TotalCount 1 -ErrorAction SilentlyContinue
                if ($FirstLine -and ($FirstLine.StartsWith("--") -or $FirstLine.StartsWith("/*") -or $FirstLine.StartsWith("CREATE") -or $FirstLine.StartsWith("USE"))) {
                    # 导入SQL，改进错误处理
                    try {
                        $ImportCommand = "mysql -u root -proot --default-character-set=utf8mb4 --force"
                        $SqlContent = Get-Content $SqlFile.FullName -Raw -ErrorAction Stop
                        if ($SqlContent) {
                            $ImportResult = $SqlContent | docker exec -i mysql5.7.44 sh -c "$ImportCommand" 2>&1

                            # 通过测试数据库连接检查导入是否成功
                            $TestResult = docker exec mysql5.7.44 mysql -u root -proot -e "SHOW DATABASES;" 2>$null
                            if ($TestResult -and $TestResult.Contains("information_schema")) {
                                Write-Log "SQL数据导入成功完成" "SUCCESS"
                            } else {
                                Write-Log "SQL数据导入完成，但验证失败" "WARN"
                            }
                        } else {
                            Write-Log "SQL文件为空，跳过导入" "WARN"
                        }
                    } catch {
                        Write-Log "SQL导入过程错误: $($_.Exception.Message)" "WARN"
                    }
                } else {
                    Write-Log "SQL文件验证失败或文件为空，跳过导入" "WARN"
                }
            } else {
                Write-Log "备份中未找到有效的SQL文件，跳过SQL数据恢复" "WARN"
            }

            # 清理临时目录
            if (Test-Path $SqlTempDir) {
                Remove-Item $SqlTempDir -Recurse -Force -ErrorAction SilentlyContinue
            }
        } catch {
            Write-Log "SQL数据恢复失败: $($_.Exception.Message)" "WARN"
        }
    } else {
        Write-Log "未找到SQL备份文件，跳过SQL数据恢复" "INFO"
    }

    # 步骤13: 验证恢复
    Show-Progress "验证恢复" "验证恢复结果..." 95
    Write-Log "验证恢复结果..."

    # 检查容器状态
    $ContainerStatus = docker inspect mysql5.7.44 --format='{{.State.Status}}' 2>$null
    if ($ContainerStatus -eq "running") {
        Write-Log "容器状态: 运行中" "SUCCESS"

        # 检查数据库连接
        $DbTest = docker exec mysql5.7.44 mysql -u root -proot -e "SHOW DATABASES;" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Log "数据库连接: 正常" "SUCCESS"
            Write-Log "可用数据库: $($DbTest -join ', ')" "INFO"
        } else {
            Write-Log "数据库连接: 失败" "WARN"
        }
    } else {
        Write-Log "容器状态: $ContainerStatus" "WARN"
    }

} catch {
    Write-Log "恢复失败: $($_.Exception.Message)" "ERROR"
    Write-Log "请检查Docker状态和备份文件完整性" "ERROR"
    exit 1
} finally {
    # 清理临时目录
    if (Test-Path $TempDir) {
        Remove-Item $TempDir -Recurse -Force
        Write-Log "已清理临时目录" "INFO"
    }
    Write-Progress -Activity "恢复完成" -Completed
}

Show-Progress "恢复完成" "恢复已完成!" 100
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "恢复完成!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Log "恢复成功完成" "SUCCESS"
Write-Log "MySQL服务可在以下地址访问: localhost:3306" "SUCCESS"
Write-Log "用户名: root, 密码: root" "SUCCESS"

Write-Host "`n恢复脚本执行完成" -ForegroundColor Green
