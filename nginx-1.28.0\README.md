# 🚀 nginx 1.28.0 离线构建项目

[![nginx](https://img.shields.io/badge/nginx-1.28.0-green.svg)](http://nginx.org/)
[![CentOS](https://img.shields.io/badge/CentOS-7-blue.svg)](https://www.centos.org/)
[![Docker](https://img.shields.io/badge/Docker-支持-blue.svg)](https://www.docker.com/)
[![离线构建](https://img.shields.io/badge/离线构建-✓-success.svg)](#)

一个完整的nginx 1.28.0离线构建解决方案，基于CentOS 7，使用RPM包依赖，支持完全断网环境部署。

## 📋 项目特点

- ✅ **完全离线**：所有依赖预先打包，无需网络连接
- ✅ **基于RPM**：使用系统RPM包而非源码编译依赖
- ✅ **自动修复**：自动处理nginx源码兼容性问题
- ✅ **模块丰富**：包含SSL、HTTP/2等常用模块
- ✅ **生产就绪**：经过测试验证的稳定构建

## 🎯 适用场景

- 企业内网环境部署
- 受限网络环境
- 标准化容器部署
- 离线开发环境
- 安全要求较高的环境

## 📦 包含的nginx模块

| 模块 | 功能 | 状态 |
|------|------|------|
| `--with-http_ssl_module` | SSL/TLS支持 | ✅ 启用 |
| `--with-http_v2_module` | HTTP/2支持 | ✅ 启用 |
| `--with-http_realip_module` | 真实IP模块 | ✅ 启用 |
| `--with-http_auth_request_module` | 认证请求模块 | ✅ 启用 |
| `--with-http_secure_link_module` | 安全链接模块 | ✅ 启用 |
| `--with-http_stub_status_module` | 状态监控模块 | ✅ 启用 |
| `--with-http_gzip_static_module` | 静态gzip模块 | ✅ 启用 |
| `--with-threads` | 线程支持 | ✅ 启用 |
| `--with-file-aio` | 文件异步IO | ✅ 启用 |

## 🚀 快速开始

### 1. 获取项目文件

```bash
# 如果在联网环境，下载依赖包
chmod +x download-packages.sh
./download-packages.sh

# 如果已有完整包，直接解压
tar -xzf nginx-1.28.0-offline-build.tar.gz
cd nginx-1.28.0-offline-build
```

### 2. 构建Docker镜像

```bash
# 使用构建脚本（推荐）
chmod +x build-offline.sh
./build-offline.sh

# 或直接使用Docker命令
docker build -t nginx-1.28.0-offline .
```

### 3. 启动nginx容器

```bash
# 前台运行
docker run --rm -p 80:80 nginx-1.28.0-offline

# 后台运行
docker run -d -p 80:80 --name nginx-server nginx-1.28.0-offline
```

### 4. 验证部署

```bash
# 运行测试脚本
chmod +x test-nginx.sh
./test-nginx.sh

# 或手动测试
curl http://localhost
```

## 📁 项目结构

```
nginx-1.28.0-offline-build/
├── 📄 README.md                  # 项目说明
├── 🌐 指导教程.html              # 详细教程（推荐阅读）
├── 🐳 Dockerfile                 # Docker构建文件
├── 📥 download-packages.sh       # 包下载脚本
├── 🔨 build-offline.sh           # 离线构建脚本
├── 🧪 test-nginx.sh              # 测试脚本
├── 📦 packages/                  # nginx源码包
│   └── nginx-1.28.0.tar.gz
├── 📦 centos7-rpms/              # CentOS 7 RPM依赖包
│   ├── gcc-*.rpm
│   ├── openssl-*.rpm
│   └── ...
├── 🔧 scripts/                   # 构建脚本
│   └── build-nginx.sh
└── ⚙️ config/                    # nginx配置文件
    ├── nginx.conf
    └── default.conf
```

## 🔧 环境要求

- **Docker**: ≥ 18.09
- **系统**: Linux/macOS/Windows（支持Docker）
- **磁盘空间**: ≥ 2GB
- **内存**: ≥ 2GB

## 📚 使用说明

### 基本命令

```bash
# 查看nginx版本
docker run --rm nginx-1.28.0-offline /usr/local/nginx/sbin/nginx -v

# 查看编译配置
docker run --rm nginx-1.28.0-offline /usr/local/nginx/sbin/nginx -V

# 测试配置文件
docker exec nginx-server /usr/local/nginx/sbin/nginx -t

# 重载配置
docker exec nginx-server /usr/local/nginx/sbin/nginx -s reload
```

### 自定义配置

```bash
# 使用自定义配置启动
docker run -d -p 80:80 \
  -v /path/to/nginx.conf:/etc/nginx/nginx.conf \
  -v /path/to/html:/usr/share/nginx/html \
  --name nginx-server nginx-1.28.0-offline
```

### 生产环境部署

```bash
# 生产环境启动（带资源限制）
docker run -d \
  --name nginx-prod \
  --restart=unless-stopped \
  --memory=512m \
  --cpus=1.0 \
  -p 80:80 \
  -p 443:443 \
  -v /data/nginx/html:/usr/share/nginx/html:ro \
  -v /data/nginx/conf:/etc/nginx:ro \
  -v /data/nginx/logs:/var/log/nginx \
  nginx-1.28.0-offline
```

## 🔍 故障排除

### 常见问题

1. **构建失败**
   - 检查Docker磁盘空间
   - 确认所有依赖文件完整
   - 查看构建日志

2. **容器启动失败**
   - 检查端口占用
   - 验证配置文件语法
   - 查看容器日志

3. **模块缺失**
   - 确认编译配置
   - 检查依赖库安装
   - 重新构建镜像

### 调试命令

```bash
# 查看详细构建日志
docker build --no-cache --progress=plain -t nginx-1.28.0-offline .

# 进入容器调试
docker exec -it nginx-server /bin/bash

# 查看nginx错误日志
docker logs nginx-server
```

## 🔒 安全建议

- 定期更新基础镜像
- 配置适当的安全头
- 使用非root用户运行
- 限制容器资源使用
- 启用SSL/TLS加密

## 📈 性能优化

- 调整worker进程数
- 启用gzip压缩
- 配置缓存策略
- 优化连接参数
- 使用HTTP/2

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目采用MIT许可证。nginx本身遵循BSD许可证。

## 📞 支持

- 📖 详细教程：打开 `指导教程.html` 查看完整指南
- 🧪 运行测试：`./test-nginx.sh`
- 📝 查看日志：`docker logs nginx-server`

---

**🎉 享受nginx 1.28.0的强大功能！**

如果这个项目对您有帮助，请给个⭐️支持一下！
