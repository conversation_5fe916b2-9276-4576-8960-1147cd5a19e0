# PostgreSQL 11.19 在线版完整恢复脚本
# 版本: 2.1
# 作者: AI Assistant
# 日期: 2025-07-12
#
# 用法:
#   .\postgresql-restore-cn.ps1 -BackupPath "path\to\backup"
#   .\postgresql-restore-cn.ps1 -BackupPath "path\to\backup" -VerboseLog

param(
    [Parameter(Mandatory=$true)]
    [string]$BackupPath,
    [switch]$ForceRestore,
    [switch]$VerboseLog
)

# 全局变量
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$TempDir = Join-Path (Get-Location) "temp_restore_$Timestamp"
$LogFile = Join-Path $BackupPath "restore_log_$Timestamp.txt"

# 日志记录函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogContent = "[$Time] [$Level] $Message"

    switch ($Level) {
        "ERROR" { Write-Host $LogContent -ForegroundColor Red }
        "WARN"  { Write-Host $LogContent -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $LogContent -ForegroundColor Green }
        default { Write-Host $LogContent -ForegroundColor White }
    }

    if (Test-Path $BackupPath) {
        try {
            $LogDir = Split-Path $LogFile -Parent
            if (!(Test-Path $LogDir)) {
                New-Item -ItemType Directory -Path $LogDir -Force | Out-Null
            }
            $LogContent | Out-File -FilePath $LogFile -Append -Encoding UTF8 -Force
        }
        catch {
            Write-Warning "无法写入日志文件: $($_.Exception.Message)"
        }
    }
}

# 进度显示函数
function Show-Progress {
    param([string]$Activity, [string]$Status, [int]$PercentComplete)
    Write-Progress -Activity $Activity -Status $Status -PercentComplete $PercentComplete
}

# 获取容器用户ID函数
function Get-ContainerUserId {
    param([string]$ImageName, [string]$UserName)
    try {
        $UserId = docker run --rm --entrypoint="" $ImageName id -u $UserName 2>$null
        if ($LASTEXITCODE -eq 0) {
            return $UserId.Trim()
        }
    } catch {}
    return $null
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "PostgreSQL 11.19 在线版完整恢复脚本 v2.1" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Log "开始PostgreSQL完整恢复"
Write-Log "备份路径: $BackupPath"
Write-Log "临时目录: $TempDir"

# 验证备份路径
if (-not (Test-Path $BackupPath)) {
    Write-Log "错误: 备份路径不存在: $BackupPath" "ERROR"
    exit 1
}

# 创建临时目录
try {
    New-Item -ItemType Directory -Path $TempDir -Force | Out-Null
    Write-Log "临时目录创建成功" "SUCCESS"
} catch {
    Write-Log "创建临时目录失败: $($_.Exception.Message)" "ERROR"
    exit 1
}

try {
    # 步骤1: 检查Docker环境
    Show-Progress "环境检查" "检查Docker环境..." 5
    Write-Log "检查Docker环境..."
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker未运行或未安装"
    }
    Write-Log "Docker版本: $dockerVersion" "SUCCESS"

    # 步骤2: 验证备份文件
    Show-Progress "环境检查" "验证备份文件..." 10
    Write-Log "验证备份文件..."
    $RequiredFiles = @(
        "postgres_data_volume.zip",
        "postgres_logs_volume.zip"
    )

    $MissingFiles = @()
    foreach ($File in $RequiredFiles) {
        $FilePath = Join-Path $BackupPath $File
        if (-not (Test-Path $FilePath)) {
            $MissingFiles += $File
            Write-Log "警告: 缺少备份文件 $File" "WARN"
        } else {
            $Size = [math]::Round((Get-Item $FilePath).Length / 1MB, 2)
            Write-Log "找到备份文件: $File (${Size}MB)" "SUCCESS"
        }
    }

    if ($MissingFiles.Count -gt 0 -and -not $ForceRestore) {
        throw "缺少关键备份文件，使用 -ForceRestore 参数继续"
    }

    # 步骤3: 获取容器用户ID
    Show-Progress "环境检查" "检测容器用户ID..." 15
    Write-Log "检测容器用户ID..."
    $PostgresUserId = Get-ContainerUserId "postgresql:11.19" "postgres"

    if ($PostgresUserId) {
        Write-Log "PostgreSQL用户ID: $PostgresUserId" "SUCCESS"
    } else {
        Write-Log "无法检测PostgreSQL用户ID，使用默认值999" "WARN"
        $PostgresUserId = "999"
    }

    # 步骤4: 停止现有服务
    Show-Progress "准备恢复" "停止现有服务..." 20
    Write-Log "停止现有服务..."
    docker-compose down 2>&1 | Out-Null
    Start-Sleep -Seconds 5
    Write-Log "现有服务已停止" "SUCCESS"

    # 步骤5: 删除现有数据卷
    Show-Progress "准备恢复" "删除现有数据卷..." 25
    Write-Log "删除现有数据卷..."
    $VolumeList = @("postgresql_11_19_data_online", "postgresql_11_19_logs_online", "pgadmin_data_online")
    foreach ($VolumeName in $VolumeList) {
        docker volume rm $VolumeName -f 2>$null | Out-Null
        Write-Log "已删除数据卷: $VolumeName" "SUCCESS"
    }

    # 步骤6: 恢复配置文件
    Show-Progress "恢复配置" "恢复配置文件..." 30
    Write-Log "恢复配置文件..."
    $ConfigFileList = @("docker-compose.yml", "Dockerfile", "postgresql.conf", "pg_hba.conf", "docker-entrypoint.sh")
    $RestoredFiles = 0
    foreach ($File in $ConfigFileList) {
        $BackupFile = Join-Path $BackupPath $File
        if (Test-Path $BackupFile) {
            Copy-Item $BackupFile -Destination . -Force
            Write-Log "已恢复: $File" "SUCCESS"
            $RestoredFiles++
        }
    }
    
    # 恢复初始化脚本目录
    $InitScriptsBackup = Join-Path $BackupPath "init-scripts"
    if (Test-Path $InitScriptsBackup) {
        if (Test-Path "init-scripts") {
            Remove-Item "init-scripts" -Recurse -Force
        }
        Copy-Item $InitScriptsBackup -Destination . -Recurse -Force
        Write-Log "已恢复: init-scripts目录" "SUCCESS"
        $RestoredFiles++
    }
    
    Write-Log "配置文件恢复完成，已恢复 $RestoredFiles 个文件/目录" "SUCCESS"

    # 步骤7: 创建新数据卷
    Show-Progress "恢复数据卷" "创建新数据卷..." 35
    Write-Log "创建新数据卷..."
    foreach ($VolumeName in $VolumeList) {
        docker volume create $VolumeName 2>&1 | Out-Null
        Write-Log "已创建数据卷: $VolumeName" "SUCCESS"
    }

    # 步骤8: 恢复PostgreSQL主数据卷
    Show-Progress "恢复数据卷" "恢复PostgreSQL主数据卷..." 45
    Write-Log "恢复PostgreSQL主数据卷..."
    $DataVolumeFile = Join-Path $BackupPath "postgres_data_volume.zip"
    if (Test-Path $DataVolumeFile) {
        try {
            # 创建临时解压目录
            $TempExtractDir = Join-Path $env:TEMP "postgres_restore_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            New-Item -ItemType Directory -Path $TempExtractDir -Force | Out-Null

            # 解压备份文件
            Expand-Archive -Path $DataVolumeFile -DestinationPath $TempExtractDir -Force

            # 使用docker cp方法恢复数据卷
            $RestoreContainer = docker create -v postgresql_11_19_data_online:/data alpine 2>&1
            if ($LASTEXITCODE -eq 0) {
                # 检查解压目录结构
                $DataSourcePath = if (Test-Path "${TempExtractDir}/postgres_data") { "${TempExtractDir}/postgres_data/." } else { "${TempExtractDir}/data/." }
                docker cp $DataSourcePath "${RestoreContainer}:/data/" 2>&1 | Out-Null
                docker rm $RestoreContainer 2>&1 | Out-Null

                # 修复数据目录权限
                Write-Log "修复数据目录权限..."
                docker run --rm -v postgresql_11_19_data_online:/data alpine sh -c "chown -R ${PostgresUserId}:${PostgresUserId} /data && chmod 700 /data && find /data -type d -exec chmod 700 {} \; && find /data -type f -exec chmod 600 {} \;" 2>&1 | Out-Null
                Write-Log "PostgreSQL主数据卷恢复完成" "SUCCESS"
            } else {
                Write-Log "创建恢复容器失败" "ERROR"
            }

            # 清理临时目录
            Remove-Item $TempExtractDir -Recurse -Force -ErrorAction SilentlyContinue
        } catch {
            Write-Log "PostgreSQL主数据卷恢复失败: $($_.Exception.Message)" "ERROR"
        }
    }

    # 步骤9: 恢复PostgreSQL日志卷
    Show-Progress "恢复数据卷" "恢复PostgreSQL日志卷..." 55
    Write-Log "恢复PostgreSQL日志卷..."
    $LogVolumeFile = Join-Path $BackupPath "postgres_logs_volume.zip"
    if (Test-Path $LogVolumeFile) {
        try {
            # 创建临时解压目录
            $TempExtractDir = Join-Path $env:TEMP "postgres_logs_restore_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            New-Item -ItemType Directory -Path $TempExtractDir -Force | Out-Null

            # 解压备份文件
            Expand-Archive -Path $LogVolumeFile -DestinationPath $TempExtractDir -Force

            # 使用docker cp方法恢复日志卷
            $RestoreContainer = docker create -v postgresql_11_19_logs_online:/data alpine 2>&1
            if ($LASTEXITCODE -eq 0) {
                # 检查解压目录结构
                $LogSourcePath = if (Test-Path "${TempExtractDir}/postgres_logs") { "${TempExtractDir}/postgres_logs/." } else { "${TempExtractDir}/data/." }
                docker cp $LogSourcePath "${RestoreContainer}:/data/" 2>&1 | Out-Null
                docker rm $RestoreContainer 2>&1 | Out-Null

                # 修复日志目录权限
                Write-Log "修复日志目录权限..."
                docker run --rm -v postgresql_11_19_logs_online:/data alpine sh -c "chown -R ${PostgresUserId}:${PostgresUserId} /data && chmod 755 /data" 2>&1 | Out-Null
                Write-Log "PostgreSQL日志卷恢复完成" "SUCCESS"
            } else {
                Write-Log "创建日志恢复容器失败" "WARN"
            }

            # 清理临时目录
            Remove-Item $TempExtractDir -Recurse -Force -ErrorAction SilentlyContinue
        } catch {
            Write-Log "PostgreSQL日志卷恢复失败: $($_.Exception.Message)" "WARN"
        }
    }

    # 步骤10: 恢复pgAdmin数据卷
    Show-Progress "恢复数据卷" "恢复pgAdmin数据卷..." 60
    Write-Log "恢复pgAdmin数据卷..."
    $PgAdminVolumeFile = Join-Path $BackupPath "pgadmin_data_volume.zip"
    if (Test-Path $PgAdminVolumeFile) {
        try {
            # 创建临时解压目录
            $TempExtractDir = Join-Path $env:TEMP "pgadmin_restore_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            New-Item -ItemType Directory -Path $TempExtractDir -Force | Out-Null

            # 解压备份文件
            Expand-Archive -Path $PgAdminVolumeFile -DestinationPath $TempExtractDir -Force

            # 使用docker cp方法恢复pgAdmin卷
            $RestoreContainer = docker create -v pgadmin_data_online:/data alpine 2>&1
            if ($LASTEXITCODE -eq 0) {
                # 检查解压目录结构
                $PgAdminSourcePath = if (Test-Path "${TempExtractDir}/pgadmin_data") { "${TempExtractDir}/pgadmin_data/." } else { "${TempExtractDir}/data/." }
                docker cp $PgAdminSourcePath "${RestoreContainer}:/data/" 2>&1 | Out-Null
                docker rm $RestoreContainer 2>&1 | Out-Null

                # 修复pgAdmin数据目录权限 - 这是关键修复
                Write-Log "修复pgAdmin数据目录权限..."
                # pgAdmin容器通常使用用户ID 5050
                docker run --rm -v pgadmin_data_online:/data alpine sh -c "
                    chown -R 5050:5050 /data 2>/dev/null || chown -R 5050:0 /data 2>/dev/null || true
                    chmod -R 755 /data 2>/dev/null || true
                    find /data -name '*.db' -exec chmod 644 {} \; 2>/dev/null || true
                    find /data -name '*.log' -exec chmod 644 {} \; 2>/dev/null || true
                " 2>&1 | Out-Null

                Write-Log "pgAdmin数据卷恢复完成，权限已修复" "SUCCESS"
            } else {
                Write-Log "创建pgAdmin恢复容器失败" "WARN"
            }

            # 清理临时目录
            Remove-Item $TempExtractDir -Recurse -Force -ErrorAction SilentlyContinue
        } catch {
            Write-Log "pgAdmin数据卷恢复失败: $($_.Exception.Message)" "WARN"
        }
    } else {
        Write-Log "未找到pgAdmin备份文件，将使用默认配置" "INFO"
        # 确保pgAdmin数据卷存在并有正确权限
        docker run --rm -v pgadmin_data_online:/data alpine sh -c "
            mkdir -p /data
            chown -R 5050:5050 /data 2>/dev/null || chown -R 5050:0 /data 2>/dev/null || true
            chmod 755 /data
        " 2>&1 | Out-Null
    }

    # 步骤11: 启动服务
    Show-Progress "启动服务" "启动PostgreSQL服务..." 70
    Write-Log "启动PostgreSQL服务..."
    docker-compose up -d 2>&1 | Out-Null

    # 步骤12: 等待PostgreSQL启动
    Show-Progress "启动服务" "等待PostgreSQL启动..." 75
    Write-Log "等待PostgreSQL启动..."
    $Count = 0
    $MaxRetries = 20
    do {
        Start-Sleep -Seconds 5
        $Count++
        Write-Log "检查PostgreSQL状态 (尝试 $Count/$MaxRetries)..."
        $Status = docker exec postgresql-11.19 pg_isready -U postgres -p 3433 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Log "PostgreSQL服务启动成功" "SUCCESS"
            break
        }
    } while ($Count -lt $MaxRetries)

    if ($Count -ge $MaxRetries) {
        Write-Log "PostgreSQL服务启动超时，请手动检查" "WARN"
    }

    # 步骤12.5: 等待pgAdmin启动
    Show-Progress "启动服务" "等待pgAdmin启动..." 78
    Write-Log "等待pgAdmin启动..."
    $PgAdminCount = 0
    $PgAdminMaxRetries = 10
    $PgAdminStarted = $false

    do {
        Start-Sleep -Seconds 5
        $PgAdminCount++
        Write-Log "检查pgAdmin状态 (尝试 $PgAdminCount/$PgAdminMaxRetries)..."

        # 检查pgAdmin容器状态
        $PgAdminStatus = docker inspect pgadmin4 --format='{{.State.Status}}' 2>$null
        if ($PgAdminStatus -eq "running") {
            Write-Log "pgAdmin容器运行正常" "SUCCESS"

            # 简化HTTP检查，减少超时时间
            try {
                $Response = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 3 -UseBasicParsing -ErrorAction Stop
                if ($Response.StatusCode -eq 200) {
                    Write-Log "pgAdmin Web界面响应正常" "SUCCESS"
                    $PgAdminStarted = $true
                    break
                }
            } catch {
                # 如果容器运行超过30秒但HTTP不响应，认为是正常的（可能需要更长时间初始化）
                $ContainerStartTime = docker inspect pgadmin4 --format='{{.State.StartedAt}}' 2>$null
                if ($ContainerStartTime) {
                    try {
                        $StartTime = [DateTime]::Parse($ContainerStartTime.Replace('T', ' ').Replace('Z', ''))
                        $RunningTime = (Get-Date).ToUniversalTime() - $StartTime
                        if ($RunningTime.TotalSeconds -gt 30) {
                            Write-Log "pgAdmin容器已运行超过30秒，认为启动成功（Web界面可能需要更长时间初始化）" "SUCCESS"
                            $PgAdminStarted = $true
                            break
                        }
                    } catch {
                        # 时间解析失败，继续检查
                    }
                }
                Write-Log "pgAdmin Web界面暂未响应，继续等待..." "INFO"
            }
        } elseif ($PgAdminStatus -eq "restarting") {
            Write-Log "pgAdmin正在重启中..." "WARN"
        } else {
            Write-Log "pgAdmin容器状态异常: $PgAdminStatus" "WARN"
        }
    } while ($PgAdminCount -lt $PgAdminMaxRetries)

    if (-not $PgAdminStarted) {
        Write-Log "pgAdmin启动检查超时，但容器可能仍在正常运行" "WARN"
        Write-Log "请手动访问 http://localhost:8080 检查pgAdmin状态" "INFO"
        Write-Log "如有问题，可查看容器日志: docker logs pgadmin4" "INFO"
    }

    # 步骤13: 恢复SQL数据（如果可用）
    Show-Progress "恢复SQL数据" "恢复SQL数据..." 85
    Write-Log "恢复SQL数据..."
    $SqlBackupFile = Join-Path $BackupPath "postgresql_all_databases.sql.zip"
    if (Test-Path $SqlBackupFile) {
        try {
            # 解压SQL备份
            $SqlTempDir = Join-Path $env:TEMP "postgres_sql_restore_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            if (Test-Path $SqlTempDir) {
                Remove-Item $SqlTempDir -Recurse -Force -ErrorAction SilentlyContinue
            }
            New-Item -ItemType Directory -Path $SqlTempDir -Force | Out-Null
            Expand-Archive -Path $SqlBackupFile -DestinationPath $SqlTempDir -Force

            $SqlFile = Get-ChildItem -Path $SqlTempDir -Filter "*.sql" | Select-Object -First 1
            if ($SqlFile -and (Test-Path $SqlFile.FullName)) {
                Write-Log "从以下文件导入SQL数据: $($SqlFile.Name)"

                # 首先验证SQL文件内容
                $FirstLine = Get-Content $SqlFile.FullName -TotalCount 1 -ErrorAction SilentlyContinue
                if ($FirstLine -and ($FirstLine.StartsWith("--") -or $FirstLine.StartsWith("/*") -or $FirstLine.Contains("PostgreSQL"))) {
                    # 导入SQL，改进错误处理
                    try {
                        $ImportCommand = "PGPASSWORD=postgres psql -U postgres -h localhost -p 3433 -d postgres"
                        $SqlContent = Get-Content $SqlFile.FullName -Raw -ErrorAction Stop
                        if ($SqlContent) {
                            $ImportResult = $SqlContent | docker exec -i postgresql-11.19 sh -c "$ImportCommand" 2>&1

                            # 通过测试数据库连接检查导入是否成功
                            $TestResult = docker exec postgresql-11.19 sh -c "PGPASSWORD=postgres psql -U postgres -h localhost -p 3433 -d postgres -c '\l'" 2>$null
                            if ($TestResult -and $TestResult.Contains("postgres")) {
                                Write-Log "SQL数据导入成功完成" "SUCCESS"
                            } else {
                                Write-Log "SQL数据导入完成，但验证失败" "WARN"
                            }
                        } else {
                            Write-Log "SQL文件为空，跳过导入" "WARN"
                        }
                    } catch {
                        Write-Log "SQL导入过程错误: $($_.Exception.Message)" "WARN"
                    }
                } else {
                    Write-Log "SQL文件验证失败或文件为空，跳过导入" "WARN"
                }
            } else {
                Write-Log "备份中未找到有效的SQL文件，跳过SQL数据恢复" "WARN"
            }

            # 清理临时目录
            if (Test-Path $SqlTempDir) {
                Remove-Item $SqlTempDir -Recurse -Force -ErrorAction SilentlyContinue
            }
        } catch {
            Write-Log "SQL数据恢复失败: $($_.Exception.Message)" "WARN"
        }
    } else {
        Write-Log "未找到SQL备份文件，跳过SQL数据恢复" "INFO"
    }

    # 步骤14: 验证恢复
    Show-Progress "验证恢复" "验证恢复结果..." 95
    Write-Log "验证恢复结果..."

    # 检查PostgreSQL容器状态
    $ContainerStatus = docker inspect postgresql-11.19 --format='{{.State.Status}}' 2>$null
    if ($ContainerStatus -eq "running") {
        Write-Log "PostgreSQL容器状态: 运行中" "SUCCESS"

        # 检查数据库连接
        $DbTest = docker exec postgresql-11.19 sh -c "PGPASSWORD=postgres psql -U postgres -h localhost -p 3433 -d postgres -c '\l'" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Log "PostgreSQL数据库连接: 正常" "SUCCESS"
            Write-Log "PostgreSQL服务恢复成功" "SUCCESS"
        } else {
            Write-Log "PostgreSQL数据库连接: 失败" "WARN"
        }
    } else {
        Write-Log "PostgreSQL容器状态: $ContainerStatus" "WARN"
    }

    # 检查pgAdmin容器状态
    $PgAdminContainerStatus = docker inspect pgadmin4 --format='{{.State.Status}}' 2>$null
    if ($PgAdminContainerStatus -eq "running") {
        Write-Log "pgAdmin容器状态: 运行中" "SUCCESS"

        # 检查pgAdmin Web界面
        try {
            $PgAdminResponse = Invoke-WebRequest -Uri "http://localhost:8080" -TimeoutSec 10 -UseBasicParsing -ErrorAction SilentlyContinue
            if ($PgAdminResponse.StatusCode -eq 200) {
                Write-Log "pgAdmin Web界面: 正常访问" "SUCCESS"
                Write-Log "pgAdmin服务恢复成功" "SUCCESS"
            } else {
                Write-Log "pgAdmin Web界面: 响应异常 (状态码: $($PgAdminResponse.StatusCode))" "WARN"
            }
        } catch {
            Write-Log "pgAdmin Web界面: 无法访问 - $($_.Exception.Message)" "WARN"
            Write-Log "建议检查pgAdmin容器日志: docker logs pgadmin4" "INFO"
        }
    } else {
        Write-Log "pgAdmin容器状态: $PgAdminContainerStatus" "WARN"
        if ($PgAdminContainerStatus -eq "restarting") {
            Write-Log "pgAdmin正在重启，这可能是权限问题导致的" "WARN"
            Write-Log "建议检查容器日志: docker logs pgadmin4" "INFO"
        }
    }

} catch {
    Write-Log "恢复失败: $($_.Exception.Message)" "ERROR"
    Write-Log "请检查Docker状态和备份文件完整性" "ERROR"
    exit 1
} finally {
    # 清理临时目录
    if (Test-Path $TempDir) {
        Remove-Item $TempDir -Recurse -Force
        Write-Log "已清理临时目录" "INFO"
    }
    Write-Progress -Activity "恢复完成" -Completed
}

Show-Progress "恢复完成" "恢复已完成!" 100
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "恢复完成!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Log "恢复成功完成" "SUCCESS"
Write-Log "PostgreSQL服务可在以下地址访问: localhost:3433" "SUCCESS"
Write-Log "用户名: postgres, 密码: postgres" "SUCCESS"

# 如果pgAdmin有问题，尝试自动修复
$FinalPgAdminStatus = docker inspect pgadmin4 --format='{{.State.Status}}' 2>$null
if ($FinalPgAdminStatus -eq "restarting" -or $FinalPgAdminStatus -ne "running") {
    Write-Log "检测到pgAdmin可能有问题，尝试自动修复权限..." "WARN"
    try {
        $FixScript = Join-Path (Split-Path $MyInvocation.MyCommand.Path -Parent) "fix-pgadmin-permissions.ps1"
        if (Test-Path $FixScript) {
            & $FixScript -Restart
            Write-Log "pgAdmin权限修复完成" "SUCCESS"
        } else {
            Write-Log "未找到权限修复脚本，请手动运行: .\backup-scripts\fix-pgadmin-permissions.ps1" "WARN"
        }
    } catch {
        Write-Log "自动修复失败: $($_.Exception.Message)" "WARN"
        Write-Log "请手动运行权限修复脚本: .\backup-scripts\fix-pgadmin-permissions.ps1" "WARN"
    }
}

Write-Log "pgAdmin: http://localhost:8080" "SUCCESS"
Write-Host "`n恢复脚本执行完成" -ForegroundColor Green
