[2025-07-19 20:37:11] [SUCCESS] 备份目录创建成功
[2025-07-19 20:37:11] [INFO] 检查Docker环境...
[2025-07-19 20:37:11] [SUCCESS] Docker版本: Docker version 28.0.4, build b8034c0
[2025-07-19 20:37:11] [INFO] 检查MySQL容器状态...
[2025-07-19 20:37:11] [SUCCESS] MySQL容器运行正常
[2025-07-19 20:37:11] [INFO] 备份配置文件...
[2025-07-19 20:37:11] [SUCCESS] 已备份: docker-compose.yaml
[2025-07-19 20:37:11] [SUCCESS] 已备份: Dockerfile
[2025-07-19 20:37:11] [SUCCESS] 已备份: my.cnf
[2025-07-19 20:37:11] [SUCCESS] 已备份: init-mysql.sql
[2025-07-19 20:37:11] [SUCCESS] 配置备份完成，已备份 4 个文件
[2025-07-19 20:37:11] [INFO] 备份数据库SQL数据...
[2025-07-19 20:37:11] [INFO] 执行mysqldump命令...
[2025-07-19 20:37:11] [SUCCESS] SQL备份完成并压缩，大小: 0.19MB
[2025-07-19 20:37:11] [INFO] 停止MySQL容器确保数据一致性...
[2025-07-19 20:37:27] [SUCCESS] 容器已停止
[2025-07-19 20:37:27] [INFO] 备份MySQL数据卷...
[2025-07-19 20:37:27] [INFO] 备份主数据卷: mysql_data_online
[2025-07-19 20:37:28] [SUCCESS] 主数据卷备份完成
[2025-07-19 20:37:28] [INFO] 备份日志卷: mysql_logs_online
[2025-07-19 20:37:29] [SUCCESS] 日志卷备份完成，包含 10 个文件
[2025-07-19 20:37:29] [SUCCESS] 已移动: mysql_data_volume.zip (1.45MB)
[2025-07-19 20:37:29] [SUCCESS] 已移动: mysql_logs_volume.zip (0.01MB)
[2025-07-19 20:37:29] [SUCCESS] 数据卷备份完成
[2025-07-19 20:37:29] [INFO] 重启所有服务...
[2025-07-19 20:37:32] [SUCCESS] MySQL服务重启成功
[2025-07-19 20:37:32] [INFO] 已清理临时目录
[2025-07-19 20:37:32] [INFO] 创建恢复说明文档...
[2025-07-19 20:37:32] [SUCCESS] 备份位置: .\backups\20250719_203711
[2025-07-19 20:37:32] [SUCCESS] 备份大小: 1.66 MB
[2025-07-19 20:37:32] [SUCCESS] 备份内容: 配置文件 + SQL数据 + 数据卷
[2025-07-19 20:37:32] [SUCCESS] 恢复命令: .\backup-scripts\mysql-restore-cn.ps1 -BackupPath ".\backups\20250719_203711"
