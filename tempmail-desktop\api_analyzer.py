#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TempMail.Plus API 分析工具
帮助用户发现和测试真实的 API 端点
"""

import requests
import json
import re
import time
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse

class TempMailAPIAnalyzer:
    """TempMail.Plus API 分析器"""
    
    def __init__(self):
        self.base_url = "https://tempmail.plus"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive'
        })
    
    def analyze_main_page(self):
        """分析主页面"""
        print("🔍 分析主页面...")
        print("-" * 50)
        
        try:
            response = self.session.get(self.base_url)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 1. 查找 JavaScript 文件
            print("1. JavaScript 文件:")
            scripts = soup.find_all('script', src=True)
            js_files = []
            for script in scripts:
                src = script.get('src')
                if src:
                    full_url = urljoin(self.base_url, src)
                    js_files.append(full_url)
                    print(f"   {full_url}")
            
            # 2. 查找内联 JavaScript
            print("\n2. 内联 JavaScript 中的 API 线索:")
            inline_scripts = soup.find_all('script', src=False)
            api_clues = []
            
            for script in inline_scripts:
                if script.string:
                    content = script.string
                    
                    # 查找 API 相关模式
                    patterns = [
                        r'api["\']?\s*:\s*["\']([^"\']+)',
                        r'apiUrl["\']?\s*:\s*["\']([^"\']+)',
                        r'baseUrl["\']?\s*:\s*["\']([^"\']+)',
                        r'endpoint["\']?\s*:\s*["\']([^"\']+)',
                        r'url["\']?\s*:\s*["\']([^"\']*api[^"\']*)',
                        r'fetch\s*\(\s*["\']([^"\']+)',
                        r'\.ajax\s*\(\s*{[^}]*url\s*:\s*["\']([^"\']+)',
                        r'XMLHttpRequest[^;]*\.open\s*\(\s*["\'][^"\']*["\'],\s*["\']([^"\']+)'
                    ]
                    
                    for pattern in patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        for match in matches:
                            if match and match not in api_clues:
                                api_clues.append(match)
                                print(f"   发现: {match}")
            
            # 3. 分析 JavaScript 文件内容
            print("\n3. 分析外部 JavaScript 文件:")
            for js_url in js_files[:3]:  # 只分析前3个文件
                try:
                    js_response = self.session.get(js_url, timeout=10)
                    if js_response.status_code == 200:
                        self._analyze_js_content(js_url, js_response.text)
                except Exception as e:
                    print(f"   无法分析 {js_url}: {e}")
            
            return api_clues
            
        except Exception as e:
            print(f"分析主页面失败: {e}")
            return []
    
    def _analyze_js_content(self, url, content):
        """分析 JavaScript 文件内容"""
        print(f"   分析: {url}")
        
        # 查找函数定义
        function_patterns = [
            r'function\s+(generate\w*|create\w*|get\w*|fetch\w*)\s*\([^)]*\)',
            r'(generate\w*|create\w*|get\w*|fetch\w*)\s*:\s*function\s*\([^)]*\)',
            r'(generate\w*|create\w*|get\w*|fetch\w*)\s*=\s*\([^)]*\)\s*=>'
        ]
        
        for pattern in function_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                func_name = match if isinstance(match, str) else match[0]
                print(f"     函数: {func_name}")
        
        # 查找 API 端点
        api_patterns = [
            r'["\']([^"\']*api[^"\']*)["\']',
            r'["\']([^"\']*generate[^"\']*)["\']',
            r'["\']([^"\']*email[^"\']*)["\']',
            r'["\']([^"\']*inbox[^"\']*)["\']'
        ]
        
        endpoints = set()
        for pattern in api_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if ('/' in match and len(match) > 3 and 
                    not match.startswith('http') and 
                    not match.endswith('.js') and
                    not match.endswith('.css')):
                    endpoints.add(match)
        
        if endpoints:
            print(f"     可能的端点: {list(endpoints)[:5]}")
    
    def test_discovered_endpoints(self, endpoints):
        """测试发现的端点"""
        print("\n🧪 测试发现的端点...")
        print("-" * 50)
        
        working_endpoints = []
        
        for endpoint in endpoints:
            if not endpoint.startswith('/'):
                endpoint = '/' + endpoint
            
            url = urljoin(self.base_url, endpoint)
            
            try:
                # 测试 GET 请求
                response = self.session.get(url, timeout=5)
                status = response.status_code
                
                if status == 200:
                    print(f"   ✅ GET {endpoint}: {status}")
                    working_endpoints.append(('GET', endpoint, status))
                    
                    # 尝试解析响应
                    try:
                        data = response.json()
                        print(f"      JSON 响应: {type(data)} - {list(data.keys()) if isinstance(data, dict) else 'Array'}")
                    except:
                        print(f"      文本响应: {len(response.text)} 字符")
                
                elif status != 404:
                    print(f"   ⚠️  GET {endpoint}: {status}")
                
                # 测试 POST 请求
                response = self.session.post(url, json={}, timeout=5)
                if response.status_code not in [404, 405]:
                    print(f"   ✅ POST {endpoint}: {response.status_code}")
                    working_endpoints.append(('POST', endpoint, response.status_code))
                
            except Exception as e:
                print(f"   ❌ {endpoint}: {type(e).__name__}")
        
        return working_endpoints
    
    def generate_api_template(self, working_endpoints):
        """生成 API 模板代码"""
        print("\n📝 生成 API 模板代码...")
        print("-" * 50)
        
        template = '''
# 基于分析结果的 API 模板
class RealTempMailAPI:
    def __init__(self):
        self.base_url = "https://tempmail.plus"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': 'https://tempmail.plus/'
        })
    
'''
        
        # 为每个工作的端点生成方法
        for method, endpoint, status in working_endpoints:
            method_name = self._endpoint_to_method_name(endpoint)
            
            template += f'''    def {method_name}(self):
        """调用 {endpoint} 端点"""
        url = self.base_url + "{endpoint}"
        response = self.session.{method.lower()}(url)
        
        if response.status_code == 200:
            try:
                return response.json()
            except:
                return {{"text": response.text}}
        else:
            return {{"error": f"HTTP {{response.status_code}}"}}
    
'''
        
        # 保存模板到文件
        with open('generated_api_template.py', 'w', encoding='utf-8') as f:
            f.write(template)
        
        print("   ✅ API 模板已保存到 generated_api_template.py")
        return template
    
    def _endpoint_to_method_name(self, endpoint):
        """将端点转换为方法名"""
        # 移除特殊字符，转换为有效的方法名
        name = re.sub(r'[^a-zA-Z0-9_]', '_', endpoint.strip('/'))
        name = re.sub(r'_+', '_', name).strip('_')
        
        if not name:
            name = 'api_call'
        
        return name
    
    def interactive_analysis(self):
        """交互式分析"""
        print("🎯 TempMail.Plus API 交互式分析工具")
        print("=" * 60)
        
        # 步骤1: 分析主页面
        api_clues = self.analyze_main_page()
        
        # 步骤2: 让用户输入额外的端点
        print("\n📝 请输入您在浏览器开发者工具中发现的 API 端点:")
        print("   (每行一个，输入空行结束)")
        
        user_endpoints = []
        while True:
            endpoint = input("   端点: ").strip()
            if not endpoint:
                break
            user_endpoints.append(endpoint)
        
        # 合并所有端点
        all_endpoints = list(set(api_clues + user_endpoints))
        
        if all_endpoints:
            # 步骤3: 测试端点
            working_endpoints = self.test_discovered_endpoints(all_endpoints)
            
            # 步骤4: 生成模板
            if working_endpoints:
                self.generate_api_template(working_endpoints)
            else:
                print("\n❌ 没有发现可用的 API 端点")
        else:
            print("\n❌ 没有发现任何 API 端点")
        
        # 步骤5: 提供建议
        print("\n💡 建议:")
        print("   1. 使用浏览器开发者工具监控网络请求")
        print("   2. 在网站上执行操作（生成邮箱、查看邮件）")
        print("   3. 记录所有的 AJAX/Fetch 请求")
        print("   4. 将发现的端点添加到应用中")

def main():
    """主函数"""
    analyzer = TempMailAPIAnalyzer()
    analyzer.interactive_analysis()

if __name__ == '__main__':
    main()
