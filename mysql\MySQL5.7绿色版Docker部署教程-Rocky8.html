<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL 8.0绿色版Docker部署完整教程（Rocky8基础镜像）</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 13px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 机器标识样式 */
        .machine-tag {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            margin: 0 8px 12px 0;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .machine-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .machine-tag:hover::before {
            left: 100%;
        }

        .machine-host {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
        }

        .machine-docker {
            background: linear-gradient(135deg, #45b7d1 0%, #96c93d 100%);
            color: white;
        }

        .machine-mysql {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: var(--light-surface);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        th,
        td {
            padding: 18px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 下载链接 */
        .download-link {
            background: linear-gradient(135deg, var(--success-color) 0%, #38a169 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            display: inline-flex;
            align-items: center;
            margin: 8px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            font-size: 14px;
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
        }

        .download-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .download-link:hover::before {
            left: 100%;
        }

        .download-link:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
        }

        .download-link i {
            margin-right: 8px;
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-decoration: none;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            z-index: 999;
            font-size: 20px;
        }

        .back-to-top:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
                border-radius: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 16px;
                margin-right: 15px;
            }
        }
    </style>
</head>

<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-database"></i> MySQL部署教程</h2>
            <p>MySQL 5.7绿色版Docker部署指南（修正版）</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#overview"><i class="fas fa-info-circle"></i>1. 教程概述</a></li>
                <li><a href="#environment-preparation"><i class="fas fa-cog"></i>2. 环境准备</a></li>
                <li><a href="#download-mysql"><i class="fas fa-download"></i>3. 下载MySQL包</a></li>
                <li><a href="#dockerfile-stage1"><i class="fas fa-layer-group"></i>4. 第一阶段：基础镜像</a></li>
                <li><a href="#dockerfile-stage2"><i class="fas fa-tools"></i>5. 第二阶段：MySQL安装</a></li>
                <li><a href="#dockerfile-stage3"><i class="fas fa-cogs"></i>6. 第三阶段：配置优化</a></li>
                <li><a href="#build-image"><i class="fas fa-hammer"></i>7. 构建镜像</a></li>
                <li><a href="#run-container"><i class="fas fa-play"></i>8. 运行容器</a></li>
                <li><a href="#configuration"><i class="fas fa-wrench"></i>9. 配置管理</a></li>
                <li><a href="#data-persistence"><i class="fas fa-save"></i>10. 数据持久化</a></li>
                <li><a href="#security-setup"><i class="fas fa-shield-alt"></i>11. 安全配置</a></li>
                <li><a href="#performance-tuning"><i class="fas fa-tachometer-alt"></i>12. 性能调优</a></li>
                <li><a href="#backup-restore"><i class="fas fa-archive"></i>13. 备份恢复</a></li>
                <li><a href="#monitoring"><i class="fas fa-chart-line"></i>14. 监控管理</a></li>
                <li><a href="#troubleshooting"><i class="fas fa-bug"></i>15. 故障排查</a></li>
                <li><a href="#summary"><i class="fas fa-flag-checkered"></i>16. 总结检查</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-database"></i> MySQL 5.7绿色版Docker部署完整教程（修正版）</h1>

                <div class="danger-box">
                    <strong><i class="fas fa-exclamation-triangle"></i> 重要更新说明（修正版 v2.0）：</strong>
                    <ul style="margin-top: 15px;">
                        <li><strong>基础镜像更新：</strong>从CentOS 7更换为Rocky Linux 8（CentOS 7已EOL）</li>
                        <li><strong>包管理器：</strong>使用dnf替代yum命令</li>
                        <li><strong>安全增强：</strong>添加了详细的密码安全要求和生产环境密码管理</li>
                        <li><strong>错误处理：</strong>增加了常见问题解决方案和详细排查步骤</li>
                        <li><strong>网络检查：</strong>支持多种网络检查命令（ss/netstat/nc）</li>
                        <li><strong>备份优化：</strong>完善了备份命令和目录管理</li>
                        <li><strong>界面增强：</strong>添加了返回顶部按钮和代码复制功能</li>
                        <li><strong>版本提醒：</strong>MySQL 5.7已结束扩展支持，建议迁移到MySQL 8.0</li>
                    </ul>
                </div>

                <div class="info-box">
                    <strong><i class="fas fa-history"></i> 更新日志：</strong>
                    <ul style="margin-top: 15px;">
                        <li><strong>v2.0 (修正版)：</strong>全面技术更新，安全增强，错误处理完善</li>
                        <li><strong>v1.0 (原版)：</strong>基础MySQL 5.7 Docker部署教程</li>
                    </ul>
                </div>

                <div class="info-box">
                    <strong><i class="fas fa-info-circle"></i>
                        教程说明：</strong>本教程详细介绍了如何使用MySQL 5.7绿色tar.gz版本包在Rocky Linux 8基础镜像上构建自定义MySQL
                    Docker镜像的完整过程。采用分阶段构建方式，每个步骤都有详细的注释和说明，适合小白学习。
                </div>

                <div class="warning-box">
                    <strong><i class="fas fa-exclamation-triangle"></i> 执行环境说明：</strong>
                    <div style="margin-top: 15px;">
                        <span class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</span> -
                        在Docker宿主机上执行<br>
                        <span class="machine-tag machine-docker"><i class="fas fa-whale"></i> Docker容器</span> -
                        在Docker容器内执行<br>
                        <span class="machine-tag machine-mysql"><i class="fas fa-database"></i> MySQL容器</span> -
                        在MySQL容器内执行
                    </div>
                </div>

                <section id="overview">
                    <h2><span class="step-number">1</span>教程概述</h2>

                    <h3><i class="fas fa-target"></i> 1.1 部署目标</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-bullseye"></i> 主要目标</h4>
                        <ul>
                            <li><strong><i class="fas fa-box"></i> 自定义镜像：</strong>基于Rocky Linux 8构建MySQL 5.7绿色版Docker镜像
                            </li>
                            <li><strong><i class="fas fa-layer-group"></i> 分阶段构建：</strong>采用多阶段构建方式，便于理解和维护</li>
                            <li><strong><i class="fas fa-cogs"></i> 完整配置：</strong>包含安全配置、性能优化、数据持久化等</li>
                            <li><strong><i class="fas fa-shield-alt"></i> 生产就绪：</strong>满足生产环境的安全和性能要求</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-list-check"></i> 1.2 技术特点</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 特性</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                            <th><i class="fas fa-star"></i> 优势</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-download"></i> 绿色版安装</td>
                            <td>使用官方tar.gz包进行安装</td>
                            <td>无依赖冲突，版本可控</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-layer-group"></i> 分阶段构建</td>
                            <td>将构建过程分为多个阶段</td>
                            <td>便于理解和调试</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-linux"></i> Rocky Linux 8基础</td>
                            <td>使用稳定的Rocky Linux 8作为基础镜像</td>
                            <td>兼容性好，长期支持</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-cog"></i> 自定义配置</td>
                            <td>完全自定义MySQL配置</td>
                            <td>灵活性高，可优化</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-route"></i> 1.3 构建流程</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-list-ol"></i> 构建阶段说明</h4>
                        <ol>
                            <li><strong>第一阶段：</strong>准备基础环境，安装必要的系统依赖</li>
                            <li><strong>第二阶段：</strong>下载并安装MySQL 5.7绿色版</li>
                            <li><strong>第三阶段：</strong>配置MySQL服务和优化设置</li>
                            <li><strong>最终阶段：</strong>构建完整镜像并进行测试验证</li>
                        </ol>
                    </div>
                </section>

                <section id="environment-preparation">
                    <h2><span class="step-number">2</span>环境准备</h2>

                    <h3><i class="fas fa-desktop"></i> 2.1 系统要求</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-list"></i> 项目</th>
                            <th><i class="fas fa-cogs"></i> 要求</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-linux"></i> 操作系统</td>
                            <td>Linux (Rocky/RHEL/Ubuntu)</td>
                            <td>支持Docker运行的Linux发行版</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-whale"></i> Docker版本</td>
                            <td>Docker CE 20.10+</td>
                            <td>支持多阶段构建功能</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-memory"></i> 内存</td>
                            <td>至少4GB可用内存</td>
                            <td>构建过程需要足够内存</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-hdd"></i> 磁盘空间</td>
                            <td>至少10GB可用空间</td>
                            <td>存储镜像和数据文件</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-network-wired"></i> 网络</td>
                            <td>可访问互联网</td>
                            <td>下载MySQL安装包</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-check-circle"></i> 2.2 环境检查</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>在开始构建之前，请先检查Docker环境是否正常：</p>
                    <pre><code># 检查Docker版本
docker --version

# 检查Docker服务状态
systemctl status docker

# 检查Docker信息
docker info

# 测试Docker运行
docker run hello-world

# 检查可用磁盘空间
df -h

# 检查可用内存
free -h</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 验证要点：</strong>
                        <ul style="margin-top: 10px;">
                            <li>Docker版本应为20.10或更高版本</li>
                            <li>Docker服务状态应为active (running)</li>
                            <li>hello-world容器能够正常运行</li>
                            <li>可用磁盘空间大于10GB</li>
                            <li>可用内存大于4GB</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-folder"></i> 2.3 创建工作目录</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建专门的工作目录来存放构建文件：</p>
                    <pre><code># 创建工作目录
mkdir -p /opt/mysql-docker/{dockerfile,config,scripts,data}

# 进入工作目录
cd /opt/mysql-docker

# 查看目录结构
tree /opt/mysql-docker</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 目录说明：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>dockerfile/：</strong>存放Dockerfile文件</li>
                            <li><strong>config/：</strong>存放MySQL配置文件</li>
                            <li><strong>scripts/：</strong>存放初始化脚本</li>
                            <li><strong>data/：</strong>存放数据文件（用于数据持久化）</li>
                        </ul>
                    </div>
                </section>

                <section id="download-mysql">
                    <h2><span class="step-number">3</span>下载MySQL包</h2>

                    <h3><i class="fas fa-download"></i> 3.1 获取MySQL 5.7绿色版</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>下载MySQL 5.7官方绿色版tar.gz包：</p>
                    <pre><code># 进入工作目录
cd /opt/mysql-docker

# 下载MySQL 5.7.44绿色版（推荐版本）
wget https://dev.mysql.com/get/Downloads/MySQL-5.7/mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz

# 验证下载文件
ls -lh mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz

# 检查文件完整性（可选）
md5sum mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz</code></pre>

                    <a href="https://dev.mysql.com/get/Downloads/MySQL-5.7/mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz"
                        class="download-link">
                        <i class="fas fa-download"></i> 下载 MySQL 5.7.44
                    </a>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 版本说明：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>MySQL 5.7.44：</strong>MySQL 5.7系列的稳定版本</li>
                            <li><strong>linux-glibc2.12：</strong>适用于Rocky Linux 8及以上版本</li>
                            <li><strong>x86_64：</strong>64位架构版本</li>
                            <li><strong>文件大小：</strong>约650MB</li>
                        </ul>
                    </div>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 版本选择建议：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>生产环境：</strong>建议使用MySQL 8.0最新版本</li>
                            <li><strong>兼容性：</strong>MySQL 5.7适用于需要兼容旧应用的场景</li>
                            <li><strong>支持周期：</strong>MySQL 5.7已于2023年10月结束扩展支持</li>
                            <li><strong>安全更新：</strong>建议尽快迁移到MySQL 8.0</li>
                            <li><strong>下载地址：</strong>请从MySQL官网获取最新版本</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-archive"></i> 3.2 验证安装包</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>验证下载的MySQL安装包是否完整：</p>
                    <pre><code># 查看文件详细信息
file mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz

# 测试压缩包完整性
tar -tzf mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz > /dev/null && echo "压缩包完整" || echo "压缩包损坏"

# 查看压缩包内容（前10行）
tar -tzf mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz | head -10</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 验证成功标志：</strong>
                        <ul style="margin-top: 10px;">
                            <li>文件类型显示为gzip compressed data</li>
                            <li>完整性检查输出"压缩包完整"</li>
                            <li>能够正常列出压缩包内容</li>
                            <li>包含mysql-5.7.44-linux-glibc2.12-x86_64目录</li>
                        </ul>
                    </div>
                </section>

                <section id="dockerfile-stage1">
                    <h2><span class="step-number">4</span>第一阶段：基础镜像</h2>

                    <h3><i class="fas fa-file-code"></i> 4.1 创建健康检查脚本</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建健康检查脚本，用于监控MySQL服务状态：</p>
                    <pre><code># 创建健康检查脚本
cat > /opt/mysql-docker/scripts/health-check.sh << 'EOF'
#!/bin/bash
# ==========================================
# MySQL 健康检查脚本
# 用于Docker容器健康状态检查
# ==========================================

# 设置错误处理
set -e

# MySQL连接参数
MYSQL_HOST="localhost"
MYSQL_PORT="3306"
MYSQL_USER="root"
MYSQL_PASSWORD="${MYSQL_ROOT_PASSWORD:-}"

# 检查MySQL进程是否运行
check_mysql_process() {
    if pgrep mysqld > /dev/null; then
        echo "✓ MySQL进程正在运行"
        return 0
    else
        echo "✗ MySQL进程未运行"
        return 1
    fi
}

# 检查MySQL端口是否监听
check_mysql_port() {
    # 尝试使用ss命令（推荐）
    if command -v ss > /dev/null; then
        if ss -ln | grep ":${MYSQL_PORT}" > /dev/null; then
            echo "✓ MySQL端口${MYSQL_PORT}正在监听"
            return 0
        fi
    # 备用netstat命令
    elif command -v netstat > /dev/null; then
        if netstat -ln | grep ":${MYSQL_PORT}" > /dev/null; then
            echo "✓ MySQL端口${MYSQL_PORT}正在监听"
            return 0
        fi
    # 使用nc命令测试端口
    elif command -v nc > /dev/null; then
        if nc -z localhost ${MYSQL_PORT} 2>/dev/null; then
            echo "✓ MySQL端口${MYSQL_PORT}正在监听"
            return 0
        fi
    fi

    echo "✗ MySQL端口${MYSQL_PORT}未监听"
    return 1
}

# 检查MySQL连接
check_mysql_connection() {
    if [ -n "$MYSQL_PASSWORD" ]; then
        mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -e "SELECT 1;" > /dev/null 2>&1
    else
        mysql -h"$MYSQL_HOST" -P"$MYSQL_PORT" -u"$MYSQL_USER" -e "SELECT 1;" > /dev/null 2>&1
    fi

    if [ $? -eq 0 ]; then
        echo "✓ MySQL连接正常"
        return 0
    else
        echo "✗ MySQL连接失败"
        return 1
    fi
}

# 主检查函数
main() {
    echo "开始MySQL健康检查..."

    # 检查进程
    if ! check_mysql_process; then
        exit 1
    fi

    # 检查端口
    if ! check_mysql_port; then
        exit 1
    fi

    # 检查连接
    if ! check_mysql_connection; then
        exit 1
    fi

    echo "✓ MySQL健康检查通过"
    exit 0
}

# 执行主函数
main "$@"
EOF

# 设置脚本权限
chmod +x /opt/mysql-docker/scripts/health-check.sh</code></pre>

                    <h3><i class="fas fa-layer-group"></i> 4.2 创建第一阶段Dockerfile</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建第一阶段的Dockerfile，主要用于准备基础环境：</p>
                    <pre><code># 创建第一阶段Dockerfile
cat > /opt/mysql-docker/dockerfile/Dockerfile.stage1 << 'EOF'
# ==========================================
# MySQL 5.7 绿色版 Docker 镜像构建
# 第一阶段：基础环境准备
# 基础镜像：Rocky Linux 8
# ==========================================

FROM rockylinux:8

# 设置维护者信息
LABEL maintainer="MySQL Docker Builder"
LABEL description="MySQL 5.7 Green Version - Stage 1: Base Environment"
LABEL version="1.0"

# 设置环境变量
ENV LANG=en_US.UTF-8
ENV LC_ALL=en_US.UTF-8
ENV TZ=Asia/Shanghai

# 更新系统并安装基础依赖包
RUN dnf update -y && \
    dnf install -y \
        # 基础工具
        wget \
        curl \
        tar \
        gzip \
        which \
        # 编译工具（MySQL可能需要）
        gcc \
        gcc-c++ \
        make \
        cmake \
        # 系统库
        libaio \
        libaio-devel \
        numactl \
        numactl-devel \
        # 网络工具
        net-tools \
        # 进程管理
        procps-ng \
        # 文本处理
        sed \
        grep \
        gawk && \
    # 清理缓存
    dnf clean all && \
    rm -rf /var/cache/dnf/*

# 创建mysql用户和组
RUN groupadd -r mysql && \
    useradd -r -g mysql -s /bin/false mysql

# 创建MySQL相关目录
RUN mkdir -p /usr/local/mysql && \
    mkdir -p /var/lib/mysql && \
    mkdir -p /var/log/mysql && \
    mkdir -p /etc/mysql && \
    mkdir -p /var/run/mysqld && \
    # 设置目录权限
    chown -R mysql:mysql /var/lib/mysql && \
    chown -R mysql:mysql /var/log/mysql && \
    chown -R mysql:mysql /etc/mysql && \
    chown -R mysql:mysql /var/run/mysqld

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo 'Asia/Shanghai' > /etc/timezone

# 创建工作目录
WORKDIR /tmp

# 添加健康检查脚本（第一阶段暂不启用）
COPY scripts/health-check.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/health-check.sh

# 暴露MySQL默认端口（预留）
EXPOSE 3306

# 设置默认命令（第一阶段仅用于测试）
CMD ["/bin/bash"]
EOF</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 第一阶段说明：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>基础镜像：</strong>使用官方Rocky Linux 8镜像</li>
                            <li><strong>系统更新：</strong>使用dnf更新所有系统包到最新版本</li>
                            <li><strong>依赖安装：</strong>安装MySQL运行所需的系统依赖</li>
                            <li><strong>用户创建：</strong>创建专用的mysql用户和组</li>
                            <li><strong>目录准备：</strong>创建MySQL相关的目录结构</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-hammer"></i> 4.3 构建第一阶段镜像</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>构建第一阶段的基础镜像：</p>
                    <pre><code># 进入工作目录
cd /opt/mysql-docker

# 构建第一阶段镜像
docker build -f dockerfile/Dockerfile.stage1 -t mysql57-base:stage1 .

# 查看构建的镜像
docker images | grep mysql57-base

# 测试第一阶段镜像
docker run --rm -it mysql57-base:stage1 /bin/bash -c "
    echo '测试基础环境...'
    echo '用户信息:' && id mysql
    echo 'MySQL目录:' && ls -la /usr/local/mysql /var/lib/mysql
    echo '系统信息:' && cat /etc/redhat-release
    echo '时区信息:' && date
"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 第一阶段验证：</strong>
                        <ul style="margin-top: 10px;">
                            <li>镜像构建成功，无错误信息</li>
                            <li>mysql用户和组创建成功</li>
                            <li>MySQL相关目录创建完成</li>
                            <li>系统时区设置正确</li>
                            <li>基础依赖包安装完成</li>
                        </ul>
                    </div>
                    <pre><code># 创建第一阶段Dockerfile
cat > /opt/mysql-docker/dockerfile/Dockerfile.stage1 << 'EOF'
# ==========================================
# MySQL 5.7 绿色版 Docker 镜像构建
# 第一阶段：基础环境准备
# 基础镜像：Rocky Linux 8
# ==========================================

FROM rockylinux:8

# 设置维护者信息
LABEL maintainer="MySQL Docker Builder"
LABEL description="MySQL 5.7 Green Version - Stage 1: Base Environment"
LABEL version="1.0"

# 设置环境变量
ENV LANG=en_US.UTF-8
ENV LC_ALL=en_US.UTF-8
ENV TZ=Asia/Shanghai

# 更新系统并安装基础依赖包
RUN dnf update -y && \
    dnf install -y \
        # 基础工具
        wget \
        curl \
        tar \
        gzip \
        which \
        # 编译工具（MySQL可能需要）
        gcc \
        gcc-c++ \
        make \
        cmake \
        # 系统库
        libaio \
        libaio-devel \
        numactl \
        numactl-devel \
        # 网络工具
        net-tools \
        # 进程管理
        procps-ng \
        # 文本处理
        sed \
        grep \
        gawk && \
    # 清理缓存
    dnf clean all && \
    rm -rf /var/cache/dnf/*

# 创建mysql用户和组
RUN groupadd -r mysql && \
    useradd -r -g mysql -s /bin/false mysql

# 创建MySQL相关目录
RUN mkdir -p /usr/local/mysql && \
    mkdir -p /var/lib/mysql && \
    mkdir -p /var/log/mysql && \
    mkdir -p /etc/mysql && \
    mkdir -p /var/run/mysqld && \
    # 设置目录权限
    chown -R mysql:mysql /var/lib/mysql && \
    chown -R mysql:mysql /var/log/mysql && \
    chown -R mysql:mysql /etc/mysql && \
    chown -R mysql:mysql /var/run/mysqld

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo 'Asia/Shanghai' > /etc/timezone

# 创建工作目录
WORKDIR /tmp

# 添加健康检查脚本（第一阶段暂不启用）
COPY scripts/health-check.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/health-check.sh

# 暴露MySQL默认端口（预留）
EXPOSE 3306

# 设置默认命令（第一阶段仅用于测试）
CMD ["/bin/bash"]
EOF</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 第一阶段说明：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>基础镜像：</strong>使用官方Rocky Linux 8镜像</li>
                            <li><strong>系统更新：</strong>使用dnf更新所有系统包到最新版本</li>
                            <li><strong>依赖安装：</strong>安装MySQL运行所需的系统依赖</li>
                            <li><strong>用户创建：</strong>创建专用的mysql用户和组</li>
                            <li><strong>目录准备：</strong>创建MySQL相关的目录结构</li>
                        </ul>
                    </div>
                </section>

                <section id="dockerfile-stage2">
                    <h2><span class="step-number">5</span>第二阶段：MySQL安装</h2>

                    <h3><i class="fas fa-tools"></i> 5.1 创建第二阶段Dockerfile</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建第二阶段的Dockerfile，主要用于安装MySQL：</p>
                    <pre><code># 创建第二阶段Dockerfile
cat > /opt/mysql-docker/dockerfile/Dockerfile.stage2 << 'EOF'
# ==========================================
# MySQL 5.7 绿色版 Docker 镜像构建
# 第二阶段：MySQL安装
# 基于第一阶段镜像
# ==========================================

FROM mysql57-base:stage1

# 设置维护者信息
LABEL maintainer="MySQL Docker Builder"
LABEL description="MySQL 5.7 Green Version - Stage 2: MySQL Installation"
LABEL version="2.0"

# 设置MySQL环境变量
ENV MYSQL_VERSION=5.7.44
ENV MYSQL_HOME=/usr/local/mysql
ENV MYSQL_DATADIR=/var/lib/mysql
ENV MYSQL_USER=mysql
ENV PATH=$MYSQL_HOME/bin:$PATH

# 复制MySQL安装包到容器
COPY mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz /tmp/

# 安装MySQL
RUN cd /tmp && \
    # 解压MySQL安装包
    echo "解压MySQL安装包..." && \
    tar -xzf mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz && \
    # 移动到安装目录
    echo "安装MySQL到 $MYSQL_HOME ..." && \
    mv mysql-5.7.44-linux-glibc2.12-x86_64/* $MYSQL_HOME/ && \
    # 设置权限
    echo "设置MySQL目录权限..." && \
    chown -R mysql:mysql $MYSQL_HOME && \
    # 创建符号链接
    echo "创建MySQL命令符号链接..." && \
    ln -sf $MYSQL_HOME/bin/mysql /usr/local/bin/mysql && \
    ln -sf $MYSQL_HOME/bin/mysqld /usr/local/bin/mysqld && \
    ln -sf $MYSQL_HOME/bin/mysqladmin /usr/local/bin/mysqladmin && \
    ln -sf $MYSQL_HOME/bin/mysqldump /usr/local/bin/mysqldump && \
    # 清理临时文件
    echo "清理临时文件..." && \
    rm -rf /tmp/mysql-5.7.44-linux-glibc2.12-x86_64* && \
    # 验证安装
    echo "验证MySQL安装..." && \
    $MYSQL_HOME/bin/mysqld --version

# 创建MySQL配置文件目录
RUN mkdir -p /etc/mysql/conf.d && \
    chown -R mysql:mysql /etc/mysql

# 设置工作目录
WORKDIR $MYSQL_HOME

# 暴露MySQL端口
EXPOSE 3306

# 设置默认命令（第二阶段用于测试MySQL安装）
CMD ["/bin/bash", "-c", "mysqld --version && /bin/bash"]
EOF</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
                        <ul style="margin-top: 10px;">
                            <li>确保MySQL安装包已下载到工作目录</li>
                            <li>第二阶段依赖第一阶段镜像，需先构建第一阶段</li>
                            <li>安装过程会创建必要的符号链接</li>
                            <li>所有MySQL相关目录权限已正确设置</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-hammer"></i> 5.2 构建第二阶段镜像</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>构建第二阶段的MySQL安装镜像：</p>
                    <pre><code># 确保在工作目录
cd /opt/mysql-docker

# 验证MySQL安装包存在
ls -lh mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz

# 构建第二阶段镜像
docker build -f dockerfile/Dockerfile.stage2 -t mysql57-install:stage2 .

# 查看构建的镜像
docker images | grep mysql57

# 测试第二阶段镜像
docker run --rm -it mysql57-install:stage2 /bin/bash -c "
    echo '测试MySQL安装...'
    echo 'MySQL版本:' && mysqld --version
    echo 'MySQL路径:' && which mysql mysqld
    echo 'MySQL目录:' && ls -la /usr/local/mysql/bin/ | head -5
    echo 'MySQL用户:' && id mysql
"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 第二阶段验证：</strong>
                        <ul style="margin-top: 10px;">
                            <li>MySQL 5.7.44安装成功</li>
                            <li>MySQL命令可正常执行</li>
                            <li>符号链接创建正确</li>
                            <li>目录权限设置正确</li>
                            <li>临时文件清理完成</li>
                        </ul>
                    </div>
                </section>

                <section id="dockerfile-stage3">
                    <h2><span class="step-number">6</span>第三阶段：配置优化</h2>

                    <h3><i class="fas fa-cogs"></i> 6.1 创建MySQL配置文件</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建优化的MySQL配置文件：</p>
                    <pre><code># 创建MySQL主配置文件
cat > /opt/mysql-docker/config/my.cnf << 'EOF'
# ==========================================
# MySQL 5.7 配置文件
# 适用于Docker容器环境
# 包含性能优化和安全配置
# ==========================================

[client]
# 客户端配置
port = 3306
socket = /var/run/mysqld/mysqld.sock
default-character-set = utf8mb4

[mysql]
# MySQL客户端配置
default-character-set = utf8mb4
no-auto-rehash

[mysqld]
# ==========================================
# 基础配置
# ==========================================
user = mysql
port = 3306
bind-address = 0.0.0.0
socket = /var/run/mysqld/mysqld.sock
pid-file = /var/run/mysqld/mysqld.pid

# 数据目录
datadir = /var/lib/mysql
tmpdir = /tmp

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# ==========================================
# 连接配置
# ==========================================
max_connections = 200
max_connect_errors = 1000
connect_timeout = 10
wait_timeout = 28800
interactive_timeout = 28800

# ==========================================
# 缓存配置
# ==========================================
# InnoDB缓冲池大小（建议设置为可用内存的70-80%）
innodb_buffer_pool_size = 512M
innodb_buffer_pool_instances = 1

# 查询缓存
query_cache_type = 1
query_cache_size = 64M
query_cache_limit = 2M

# 表缓存
table_open_cache = 2000
table_definition_cache = 1400

# ==========================================
# 日志配置
# ==========================================
# 错误日志
log-error = /var/log/mysql/error.log

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 二进制日志
log-bin = /var/log/mysql/mysql-bin
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# 通用查询日志（生产环境建议关闭）
general_log = 0
general_log_file = /var/log/mysql/general.log

# ==========================================
# InnoDB配置
# ==========================================
innodb_file_per_table = 1
innodb_flush_log_at_trx_commit = 2
innodb_log_buffer_size = 16M
innodb_log_file_size = 256M
innodb_log_files_in_group = 2
innodb_max_dirty_pages_pct = 75
innodb_lock_wait_timeout = 50

# ==========================================
# MyISAM配置
# ==========================================
key_buffer_size = 32M
myisam_sort_buffer_size = 8M
myisam_max_sort_file_size = 10G

# ==========================================
# 其他优化配置
# ==========================================
tmp_table_size = 64M
max_heap_table_size = 64M
sort_buffer_size = 2M
join_buffer_size = 2M
read_buffer_size = 2M
read_rnd_buffer_size = 4M

# 线程配置
thread_cache_size = 8
thread_stack = 256K

# SQL模式
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION

[mysqldump]
# mysqldump配置
quick
quote-names
max_allowed_packet = 64M

[mysql]
# mysql客户端配置
no-auto-rehash

[myisamchk]
# myisamchk配置
key_buffer_size = 128M
sort_buffer_size = 128M
read_buffer = 2M
write_buffer = 2M

[mysqlhotcopy]
# mysqlhotcopy配置
interactive-timeout
EOF</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 配置说明：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>字符集：</strong>使用utf8mb4支持完整的Unicode字符</li>
                            <li><strong>连接数：</strong>最大200个并发连接，适合中小型应用</li>
                            <li><strong>缓存：</strong>InnoDB缓冲池512M，查询缓存64M</li>
                            <li><strong>日志：</strong>启用错误日志、慢查询日志和二进制日志</li>
                            <li><strong>安全：</strong>严格的SQL模式，防止数据不一致</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-play"></i> 6.2 创建MySQL启动脚本</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建MySQL服务启动和初始化脚本：</p>
                    <pre><code># 创建MySQL启动脚本
cat > /opt/mysql-docker/scripts/mysql-entrypoint.sh << 'EOF'
#!/bin/bash
# ==========================================
# MySQL 5.7 Docker 容器启动脚本
# 负责MySQL初始化和启动
# ==========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# MySQL配置变量
MYSQL_HOME=${MYSQL_HOME:-/usr/local/mysql}
MYSQL_DATADIR=${MYSQL_DATADIR:-/var/lib/mysql}
MYSQL_USER=${MYSQL_USER:-mysql}
MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-}
MYSQL_DATABASE=${MYSQL_DATABASE:-}
MYSQL_USER_NAME=${MYSQL_USER_NAME:-}
MYSQL_USER_PASSWORD=${MYSQL_USER_PASSWORD:-}

# 检查MySQL是否已初始化
is_mysql_initialized() {
    if [ -d "$MYSQL_DATADIR/mysql" ]; then
        return 0
    else
        return 1
    fi
}

# 初始化MySQL数据目录
initialize_mysql() {
    log_info "开始初始化MySQL数据目录..."

    # 确保数据目录存在且权限正确
    mkdir -p "$MYSQL_DATADIR"
    chown -R mysql:mysql "$MYSQL_DATADIR"
    chmod 750 "$MYSQL_DATADIR"

    # 初始化MySQL
    log_info "执行MySQL初始化..."
    "$MYSQL_HOME/bin/mysqld" \
        --initialize-insecure \
        --user="$MYSQL_USER" \
        --datadir="$MYSQL_DATADIR" \
        --basedir="$MYSQL_HOME"

    log_success "MySQL数据目录初始化完成"
}

# 启动MySQL服务
start_mysql() {
    log_info "启动MySQL服务..."

    # 启动MySQL（后台运行）
    "$MYSQL_HOME/bin/mysqld_safe" \
        --user="$MYSQL_USER" \
        --datadir="$MYSQL_DATADIR" \
        --socket=/var/run/mysqld/mysqld.sock \
        --pid-file=/var/run/mysqld/mysqld.pid &

    # 等待MySQL启动
    local timeout=30
    local count=0
    while [ $count -lt $timeout ]; do
        if "$MYSQL_HOME/bin/mysqladmin" ping --silent; then
            log_success "MySQL服务启动成功"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done

    log_error "MySQL服务启动超时"
    return 1
}

# 配置MySQL安全设置
configure_mysql_security() {
    log_info "配置MySQL安全设置..."

    # 设置root密码
    if [ -n "$MYSQL_ROOT_PASSWORD" ]; then
        log_info "设置root用户密码..."
        "$MYSQL_HOME/bin/mysql" -u root -e "
            ALTER USER 'root'@'localhost' IDENTIFIED BY '$MYSQL_ROOT_PASSWORD';
            DELETE FROM mysql.user WHERE User='';
            DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');
            DROP DATABASE IF EXISTS test;
            DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';
            FLUSH PRIVILEGES;
        "
        log_success "root密码设置完成"
    else
        log_warning "未设置root密码，建议在生产环境中设置密码"
    fi

    # 创建数据库
    if [ -n "$MYSQL_DATABASE" ]; then
        log_info "创建数据库: $MYSQL_DATABASE"
        if [ -n "$MYSQL_ROOT_PASSWORD" ]; then
            "$MYSQL_HOME/bin/mysql" -u root -p"$MYSQL_ROOT_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS \`$MYSQL_DATABASE\`;"
        else
            "$MYSQL_HOME/bin/mysql" -u root -e "CREATE DATABASE IF NOT EXISTS \`$MYSQL_DATABASE\`;"
        fi
        log_success "数据库创建完成"
    fi

    # 创建用户
    if [ -n "$MYSQL_USER_NAME" ] && [ -n "$MYSQL_USER_PASSWORD" ]; then
        log_info "创建用户: $MYSQL_USER_NAME"
        local grant_db="${MYSQL_DATABASE:-*}"
        if [ -n "$MYSQL_ROOT_PASSWORD" ]; then
            "$MYSQL_HOME/bin/mysql" -u root -p"$MYSQL_ROOT_PASSWORD" -e "
                CREATE USER IF NOT EXISTS '$MYSQL_USER_NAME'@'%' IDENTIFIED BY '$MYSQL_USER_PASSWORD';
                GRANT ALL PRIVILEGES ON \`$grant_db\`.* TO '$MYSQL_USER_NAME'@'%';
                FLUSH PRIVILEGES;
            "
        else
            "$MYSQL_HOME/bin/mysql" -u root -e "
                CREATE USER IF NOT EXISTS '$MYSQL_USER_NAME'@'%' IDENTIFIED BY '$MYSQL_USER_PASSWORD';
                GRANT ALL PRIVILEGES ON \`$grant_db\`.* TO '$MYSQL_USER_NAME'@'%';
                FLUSH PRIVILEGES;
            "
        fi
        log_success "用户创建完成"
    fi
}

# 主函数
main() {
    log_info "MySQL Docker容器启动脚本开始执行..."

    # 检查是否需要初始化
    if ! is_mysql_initialized; then
        log_info "检测到MySQL未初始化，开始初始化过程..."
        initialize_mysql

        # 启动MySQL进行配置
        start_mysql

        # 配置安全设置
        configure_mysql_security

        # 停止MySQL，准备正式启动
        log_info "停止临时MySQL进程..."
        "$MYSQL_HOME/bin/mysqladmin" shutdown
        sleep 2
    else
        log_info "MySQL已初始化，跳过初始化步骤"
    fi

    # 正式启动MySQL
    log_info "正式启动MySQL服务..."
    exec "$MYSQL_HOME/bin/mysqld_safe" \
        --user="$MYSQL_USER" \
        --datadir="$MYSQL_DATADIR" \
        --socket=/var/run/mysqld/mysqld.sock \
        --pid-file=/var/run/mysqld/mysqld.pid
}

# 执行主函数
main "$@"
EOF

# 设置脚本权限
chmod +x /opt/mysql-docker/scripts/mysql-entrypoint.sh</code></pre>

                    <h3><i class="fas fa-layer-group"></i> 6.3 创建第三阶段Dockerfile</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建第三阶段的Dockerfile，整合配置和启动脚本：</p>
                    <pre><code># 创建第三阶段Dockerfile
cat > /opt/mysql-docker/dockerfile/Dockerfile.stage3 << 'EOF'
# ==========================================
# MySQL 5.7 绿色版 Docker 镜像构建
# 第三阶段：配置优化和最终镜像
# 基于第二阶段镜像
# ==========================================

FROM mysql57-install:stage2

# 设置维护者信息
LABEL maintainer="MySQL Docker Builder"
LABEL description="MySQL 5.7 Green Version - Stage 3: Final Configuration"
LABEL version="3.0"

# 设置环境变量
ENV MYSQL_ROOT_PASSWORD=""
ENV MYSQL_DATABASE=""
ENV MYSQL_USER_NAME=""
ENV MYSQL_USER_PASSWORD=""

# 复制配置文件和脚本
COPY config/my.cnf /etc/mysql/my.cnf
COPY scripts/mysql-entrypoint.sh /usr/local/bin/
COPY scripts/health-check.sh /usr/local/bin/

# 设置文件权限
RUN chmod +x /usr/local/bin/mysql-entrypoint.sh && \
    chmod +x /usr/local/bin/health-check.sh && \
    chown mysql:mysql /etc/mysql/my.cnf && \
    chmod 644 /etc/mysql/my.cnf

# 创建必要的目录并设置权限
RUN mkdir -p /var/run/mysqld && \
    chown -R mysql:mysql /var/run/mysqld && \
    chmod 755 /var/run/mysqld && \
    # 确保日志目录存在
    mkdir -p /var/log/mysql && \
    chown -R mysql:mysql /var/log/mysql && \
    chmod 755 /var/log/mysql && \
    # 确保数据目录权限正确
    chown -R mysql:mysql /var/lib/mysql && \
    chmod 750 /var/lib/mysql

# 添加健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/health-check.sh

# 暴露MySQL端口
EXPOSE 3306

# 设置数据卷
VOLUME ["/var/lib/mysql", "/var/log/mysql"]

# 设置工作目录
WORKDIR /usr/local/mysql

# 设置启动命令
ENTRYPOINT ["/usr/local/bin/mysql-entrypoint.sh"]
CMD ["mysqld"]
EOF</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 第三阶段特性：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>完整配置：</strong>包含优化的MySQL配置文件</li>
                            <li><strong>自动初始化：</strong>首次启动自动初始化数据库</li>
                            <li><strong>安全配置：</strong>支持设置root密码和创建用户</li>
                            <li><strong>健康检查：</strong>内置健康检查机制</li>
                            <li><strong>数据持久化：</strong>支持数据和日志卷挂载</li>
                        </ul>
                    </div>
                </section>

                <section id="build-image">
                    <h2><span class="step-number">7</span>构建镜像</h2>

                    <h3><i class="fas fa-hammer"></i> 7.1 构建最终镜像</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>构建完整的MySQL Docker镜像：</p>
                    <pre><code># 确保在工作目录
cd /opt/mysql-docker

# 验证所有文件都已准备好
echo "检查构建文件..."
ls -la dockerfile/
ls -la config/
ls -la scripts/
ls -la mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz

# 构建第三阶段（最终）镜像
echo "开始构建最终MySQL镜像..."
docker build -f dockerfile/Dockerfile.stage3 -t mysql57-green:latest .

# 查看构建的镜像
docker images | grep mysql57</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 构建注意事项：</strong>
                        <ul style="margin-top: 10px;">
                            <li>构建过程可能需要10-15分钟，请耐心等待</li>
                            <li>确保有足够的磁盘空间（至少5GB）</li>
                            <li>如果构建失败，检查错误信息并重试</li>
                            <li>可以保留中间镜像用于调试</li>
                            <li><strong>重要：</strong>确保MySQL安装包已下载到工作目录</li>
                            <li><strong>网络：</strong>构建过程需要稳定的网络连接</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-exclamation-circle"></i> 7.2 常见构建错误解决</h3>
                    <div class="danger-box">
                        <h4><i class="fas fa-bug"></i> 常见错误及解决方案</h4>
                        <table style="margin-top: 15px;">
                            <tr>
                                <th>错误类型</th>
                                <th>错误信息</th>
                                <th>解决方案</th>
                            </tr>
                            <tr>
                                <td>文件不存在</td>
                                <td>COPY failed: file not found</td>
                                <td>检查MySQL安装包是否在工作目录</td>
                            </tr>
                            <tr>
                                <td>网络超时</td>
                                <td>network timeout</td>
                                <td>检查网络连接，重新执行构建</td>
                            </tr>
                            <tr>
                                <td>磁盘空间不足</td>
                                <td>no space left on device</td>
                                <td>清理Docker缓存：docker system prune</td>
                            </tr>
                            <tr>
                                <td>权限错误</td>
                                <td>permission denied</td>
                                <td>检查文件权限，使用sudo执行</td>
                            </tr>
                            <tr>
                                <td>基础镜像拉取失败</td>
                                <td>pull access denied</td>
                                <td>检查Docker Hub连接或使用镜像加速器</td>
                            </tr>
                        </table>
                    </div>

                    <h4><i class="fas fa-tools"></i> 构建失败排查步骤</h4>
                    <pre><code># 1. 检查Docker状态
docker info
docker system df

# 2. 清理Docker缓存
docker system prune -f

# 3. 检查工作目录文件
ls -la /opt/mysql-docker/
ls -la /opt/mysql-docker/mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz

# 4. 分阶段调试构建
# 只构建第一阶段
docker build -f dockerfile/Dockerfile.stage1 -t mysql57-base:stage1 .

# 检查第一阶段镜像
docker run --rm -it mysql57-base:stage1 /bin/bash

# 5. 查看详细构建日志
docker build --no-cache -f dockerfile/Dockerfile.stage3 -t mysql57-green:latest . 2>&1 | tee build.log</code></pre>
                </section>

                <section id="run-container">
                    <h2><span class="step-number">8</span>运行容器</h2>

                    <h3><i class="fas fa-play"></i> 8.1 基本运行方式</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>使用基本参数运行MySQL容器：</p>
                    <pre><code># 基本运行（临时容器，仅用于测试）
docker run --rm -it \
    --name mysql57-test \
    -p 3306:3306 \
    -e MYSQL_ROOT_PASSWORD=MyS3cur3P@ssw0rd2024! \
    mysql57-green:latest

# 后台运行（生产环境推荐）
docker run -d \
    --name mysql57-prod \
    --restart=unless-stopped \
    -p 3306:3306 \
    -e MYSQL_ROOT_PASSWORD=MyS3cur3P@ssw0rd2024! \
    -e MYSQL_DATABASE=myapp \
    -e MYSQL_USER_NAME=appuser \
    -e MYSQL_USER_PASSWORD=AppU$er2024! \
    mysql57-green:latest

# 查看容器状态
docker ps -a | grep mysql57</code></pre>

                    <div class="danger-box">
                        <strong><i class="fas fa-shield-alt"></i> 密码安全要求：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>长度：</strong>至少12个字符</li>
                            <li><strong>复杂度：</strong>包含大小写字母、数字和特殊字符</li>
                            <li><strong>示例强密码：</strong>MyS3cur3P@ssw0rd2024!</li>
                            <li><strong>避免：</strong>简单密码如123456、password等</li>
                            <li><strong>生产环境：</strong>使用密码管理器生成随机密码</li>
                            <li><strong>环境变量：</strong>生产环境中使用Docker secrets或外部密钥管理</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-key"></i> 8.3 生产环境密码管理</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>生产环境中的安全密码管理方式：</p>

                    <h4><i class="fas fa-file-alt"></i> 使用密码文件</h4>
                    <pre><code># 创建密码文件（仅root可读）
echo 'MyS3cur3P@ssw0rd2024!' | sudo tee /etc/mysql-secrets/root-password
sudo chmod 600 /etc/mysql-secrets/root-password
sudo chown root:root /etc/mysql-secrets/root-password

# 使用密码文件运行容器
docker run -d \
    --name mysql57-secure \
    --restart=unless-stopped \
    -p 3306:3306 \
    -v /etc/mysql-secrets/root-password:/run/secrets/mysql-root-password:ro \
    -e MYSQL_ROOT_PASSWORD_FILE=/run/secrets/mysql-root-password \
    mysql57-green:latest</code></pre>

                    <h4><i class="fas fa-lock"></i> 使用Docker Secrets（Docker Swarm）</h4>
                    <pre><code># 创建Docker secret
echo 'MyS3cur3P@ssw0rd2024!' | docker secret create mysql-root-password -

# 在Docker Swarm中使用secret
docker service create \
    --name mysql57-service \
    --secret mysql-root-password \
    -e MYSQL_ROOT_PASSWORD_FILE=/run/secrets/mysql-root-password \
    --publish 3306:3306 \
    mysql57-green:latest</code></pre>

                    <h4><i class="fas fa-cloud"></i> 使用外部密钥管理</h4>
                    <pre><code># 使用HashiCorp Vault示例
# 1. 从Vault获取密码
MYSQL_PASSWORD=$(vault kv get -field=password secret/mysql/root)

# 2. 使用获取的密码运行容器
docker run -d \
    --name mysql57-vault \
    --restart=unless-stopped \
    -p 3306:3306 \
    -e MYSQL_ROOT_PASSWORD="$MYSQL_PASSWORD" \
    mysql57-green:latest

# 清理环境变量
unset MYSQL_PASSWORD</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 密码管理最佳实践：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>密码轮换：</strong>定期更换数据库密码</li>
                            <li><strong>最小权限：</strong>为不同应用创建专用用户</li>
                            <li><strong>审计日志：</strong>启用MySQL审计日志</li>
                            <li><strong>网络隔离：</strong>使用专用网络限制访问</li>
                            <li><strong>加密传输：</strong>启用SSL/TLS连接</li>
                        </ul>
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 环境变量说明：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>MYSQL_ROOT_PASSWORD：</strong>root用户密码（必须设置）</li>
                            <li><strong>MYSQL_DATABASE：</strong>自动创建的数据库名</li>
                            <li><strong>MYSQL_USER_NAME：</strong>自动创建的用户名</li>
                            <li><strong>MYSQL_USER_PASSWORD：</strong>自动创建的用户密码</li>
                        </ul>
                    </div>
                </section>

                <section id="configuration">
                    <h2><span class="step-number">9</span>配置管理</h2>

                    <h3><i class="fas fa-wrench"></i> 9.1 连接测试</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>测试MySQL连接和基本功能：</p>
                    <pre><code># 方法1：交互式连接（推荐，更安全）
docker exec -it mysql57-prod mysql -uroot -p

# 方法2：使用环境变量（适合脚本）
docker exec -it mysql57-prod bash -c 'mysql -uroot -p"$MYSQL_ROOT_PASSWORD"'

# 方法3：直接指定密码（仅用于测试，生产环境不推荐）
docker exec -it mysql57-prod mysql -uroot -pMyS3cur3P@ssw0rd2024!

# 测试SQL命令（交互式）
docker exec -it mysql57-prod mysql -uroot -p -e "
    SHOW DATABASES;
    SELECT VERSION();
    SHOW VARIABLES LIKE 'character_set%';
    SHOW PROCESSLIST;
"

# 测试SQL命令（使用环境变量）
docker exec -it mysql57-prod bash -c 'mysql -uroot -p"$MYSQL_ROOT_PASSWORD" -e "
    SHOW DATABASES;
    SELECT VERSION();
    SHOW VARIABLES LIKE \"character_set%\";
    SHOW PROCESSLIST;
"'</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 安全提醒：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>生产环境：</strong>避免在命令行中直接写密码</li>
                            <li><strong>推荐方式：</strong>使用 -p 参数交互式输入密码</li>
                            <li><strong>脚本使用：</strong>通过环境变量传递密码</li>
                            <li><strong>日志安全：</strong>命令行密码会出现在历史记录中</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-cogs"></i> 9.2 配置文件管理</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>管理和更新MySQL配置：</p>
                    <pre><code># 查看当前配置
docker exec mysql57-prod cat /etc/mysql/my.cnf

# 备份配置文件
docker cp mysql57-prod:/etc/mysql/my.cnf /opt/mysql-docker/config/my.cnf.backup

# 更新配置文件后重启容器
docker restart mysql57-prod

# 查看MySQL状态
docker exec mysql57-prod mysqladmin -uroot -pMyS3cur3P@ssw0rd2024! status</code></pre>
                </section>

                <section id="data-persistence">
                    <h2><span class="step-number">10</span>数据持久化</h2>

                    <h3><i class="fas fa-save"></i> 10.1 数据卷管理</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>管理MySQL数据的持久化存储：</p>

                    <h4><i class="fas fa-database"></i> 默认数据卷创建</h4>
                    <pre><code># 创建默认命名数据卷（Docker管理路径）
docker volume create mysql57-data
docker volume create mysql57-logs
docker volume create mysql57-config

# 查看数据卷详细信息
docker volume inspect mysql57-data

# 查看数据卷在宿主机的默认位置
docker volume inspect mysql57-data | grep Mountpoint
# 默认路径通常为：
# Linux: /var/lib/docker/volumes/mysql57-data/_data
# Windows: C:\ProgramData\Docker\volumes\mysql57-data\_data</code></pre>

                    <h4><i class="fas fa-map-marker-alt"></i> 指定路径的数据卷创建</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 重要说明：</strong>
                        <code>docker volume create</code> 命令本身不支持直接指定路径。要使用自定义路径，需要使用以下几种方法：
                    </div>

                    <h5><i class="fas fa-method"></i> 方法一：使用local驱动器指定路径</h5>
                    <pre><code># Linux系统 - 创建指定路径的数据卷
docker volume create \
    --driver local \
    --opt type=none \
    --opt o=bind \
    --opt device=/opt/mysql-data/data \
    mysql57-data-custom

docker volume create \
    --driver local \
    --opt type=none \
    --opt o=bind \
    --opt device=/opt/mysql-data/logs \
    mysql57-logs-custom

# 确保目录存在并设置权限
mkdir -p /opt/mysql-data/{data,logs,config}
chown -R 999:999 /opt/mysql-data</code></pre>

                    <h5><i class="fas fa-windows"></i> Windows系统指定路径</h5>
                    <pre><code># Windows系统 - 创建指定路径的数据卷
docker volume create \
    --driver local \
    --opt type=none \
    --opt o=bind \
    --opt device=C:/mysql-data/data \
    mysql57-data-win

# PowerShell - 确保目录存在
New-Item -ItemType Directory -Force -Path "C:\mysql-data\data"
New-Item -ItemType Directory -Force -Path "C:\mysql-data\logs"</code></pre>

                    <h3><i class="fas fa-folder"></i> 10.2 目录挂载</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>使用宿主机目录进行数据持久化：</p>

                    <h4><i class="fab fa-linux"></i> Linux系统目录挂载</h4>
                    <pre><code># 创建Linux宿主机目录
mkdir -p /opt/mysql-data/{data,logs,config}
mkdir -p /data/mysql/{data,logs,config}

# 设置目录权限（MySQL容器内用户ID为999）
chown -R 999:999 /opt/mysql-data
chown -R 999:999 /data/mysql

# 使用/opt目录挂载运行容器
docker run -d \
    --name mysql57-opt \
    --restart=unless-stopped \
    -p 3306:3306 \
    -v /opt/mysql-data/data:/var/lib/mysql \
    -v /opt/mysql-data/logs:/var/log/mysql \
    -e MYSQL_ROOT_PASSWORD=MyS3cur3P@ssw0rd2024! \
    mysql57-green:latest</code></pre>

                    <h4><i class="fab fa-windows"></i> Windows系统目录挂载</h4>
                    <pre><code># Windows PowerShell - 创建目录
New-Item -ItemType Directory -Force -Path "C:\mysql-data\data"
New-Item -ItemType Directory -Force -Path "D:\docker\mysql\data"

# 使用C盘目录挂载运行容器
docker run -d \
    --name mysql57-windows-c \
    --restart=unless-stopped \
    -p 3306:3306 \
    -v C:/mysql-data/data:/var/lib/mysql \
    -v C:/mysql-data/logs:/var/log/mysql \
    -e MYSQL_ROOT_PASSWORD=MyS3cur3P@ssw0rd2024! \
    mysql57-green:latest</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 路径挂载注意事项：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>Windows路径：</strong>使用正斜杠(/)而不是反斜杠(\)</li>
                            <li><strong>权限问题：</strong>Linux需要设置正确的用户权限(999:999)</li>
                            <li><strong>路径格式：</strong>Windows绝对路径需要包含盘符(C:/, D:/)</li>
                            <li><strong>性能考虑：</strong>本地磁盘性能优于网络共享</li>
                        </ul>
                    </div>
                </section>

                <section id="security-setup">
                    <h2><span class="step-number">11</span>安全配置</h2>

                    <h3><i class="fas fa-shield-alt"></i> 11.1 用户权限管理</h3>
                    <div class="machine-tag machine-mysql"><i class="fas fa-database"></i> MySQL容器</div>
                    <p>配置MySQL用户和权限：</p>
                    <pre><code># 连接到MySQL
docker exec -it mysql57-prod mysql -uroot -p

# 创建应用用户
CREATE USER 'appuser'@'%' IDENTIFIED BY 'StrongPassword123!';
CREATE USER 'readonly'@'%' IDENTIFIED BY 'ReadOnlyPass123!';

# 授予权限
GRANT SELECT, INSERT, UPDATE, DELETE ON myapp.* TO 'appuser'@'%';
GRANT SELECT ON myapp.* TO 'readonly'@'%';

# 创建管理员用户
CREATE USER 'admin'@'%' IDENTIFIED BY 'AdminPassword123!';
GRANT ALL PRIVILEGES ON *.* TO 'admin'@'%' WITH GRANT OPTION;

# 刷新权限
FLUSH PRIVILEGES;

# 查看用户列表
SELECT User, Host FROM mysql.user;</code></pre>

                    <h3><i class="fas fa-lock"></i> 11.2 网络安全</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>配置网络访问控制：</p>
                    <pre><code># 创建专用网络
docker network create --driver bridge mysql-secure-network

# 限制访问的MySQL容器
docker run -d \
    --name mysql57-secure \
    --network mysql-secure-network \
    --restart=unless-stopped \
    -v mysql57-data:/var/lib/mysql \
    -e MYSQL_ROOT_PASSWORD=VerySecurePassword123! \
    mysql57-green:latest</code></pre>
                </section>

                <section id="performance-tuning">
                    <h2><span class="step-number">12</span>性能调优</h2>

                    <h3><i class="fas fa-tachometer-alt"></i> 12.1 内存优化</h3>
                    <div class="machine-tag machine-mysql"><i class="fas fa-database"></i> MySQL容器</div>
                    <p>根据可用内存调整MySQL配置：</p>
                    <pre><code># 查看当前内存配置
docker exec mysql57-prod mysql -uroot -p -e "
    SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
    SHOW VARIABLES LIKE 'query_cache_size';
    SHOW VARIABLES LIKE 'key_buffer_size';
"</code></pre>

                    <h3><i class="fas fa-database"></i> 12.2 InnoDB优化</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建高性能InnoDB配置：</p>
                    <pre><code># 创建性能优化配置文件
cat > /opt/mysql-docker/config/my-performance.cnf << 'EOF'
[mysqld]
# InnoDB性能优化配置
innodb_buffer_pool_size = 1G
innodb_buffer_pool_instances = 4
innodb_log_file_size = 512M
innodb_log_buffer_size = 32M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
innodb_io_capacity = 2000

# 查询缓存优化
query_cache_type = 1
query_cache_size = 128M
query_cache_limit = 4M

# 连接优化
max_connections = 500
thread_cache_size = 16
table_open_cache = 4000
EOF</code></pre>
                </section>

                <section id="backup-restore">
                    <h2><span class="step-number">13</span>备份恢复</h2>

                    <h3><i class="fas fa-archive"></i> 13.1 逻辑备份</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>使用mysqldump进行逻辑备份：</p>
                    <pre><code># 创建备份目录
mkdir -p /backup/mysql

# 备份单个数据库（交互式输入密码）
docker exec mysql57-prod mysqldump -uroot -p myapp > /backup/mysql/myapp-$(date +%Y%m%d).sql

# 备份单个数据库（使用密码，生产环境不推荐）
docker exec mysql57-prod mysqldump -uroot -pMyS3cur3P@ssw0rd2024! myapp > /backup/mysql/myapp-$(date +%Y%m%d).sql

# 备份所有数据库
docker exec mysql57-prod mysqldump -uroot -p --all-databases > /backup/mysql/all-databases-$(date +%Y%m%d).sql

# 压缩备份（推荐）
docker exec mysql57-prod mysqldump -uroot -p --all-databases --single-transaction --routines --triggers | gzip > /backup/mysql/all-databases-$(date +%Y%m%d).sql.gz

# 备份到容器内再复制出来（更安全）
docker exec mysql57-prod mysqldump -uroot -p --all-databases > /tmp/backup.sql
docker cp mysql57-prod:/tmp/backup.sql /backup/mysql/all-databases-$(date +%Y%m%d).sql
docker exec mysql57-prod rm /tmp/backup.sql</code></pre>

                    <h3><i class="fas fa-undo"></i> 13.2 数据恢复</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>从备份文件恢复数据：</p>
                    <pre><code># 恢复单个数据库
docker exec -i mysql57-prod mysql -uroot -p myapp < /backup/myapp-20231201.sql

# 恢复所有数据库
docker exec -i mysql57-prod mysql -uroot -p < /backup/all-databases-20231201.sql</code></pre>
                </section>

                <section id="monitoring">
                    <h2><span class="step-number">14</span>监控管理</h2>

                    <h3><i class="fas fa-chart-line"></i> 14.1 容器监控</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>监控MySQL容器的资源使用情况：</p>
                    <pre><code># 查看容器资源使用
docker stats mysql57-prod

# 查看容器详细信息
docker inspect mysql57-prod

# 查看容器日志
docker logs mysql57-prod --tail 100 -f</code></pre>

                    <h3><i class="fas fa-heartbeat"></i> 14.2 健康检查</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>配置和监控容器健康状态：</p>
                    <pre><code># 查看健康检查状态
docker inspect mysql57-prod | grep -A 10 "Health"

# 手动执行健康检查
docker exec mysql57-prod /usr/local/bin/health-check.sh</code></pre>
                </section>

                <section id="troubleshooting">
                    <h2><span class="step-number">15</span>故障排查</h2>

                    <h3><i class="fas fa-bug"></i> 15.1 常见问题诊断</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>诊断和解决常见的MySQL容器问题：</p>
                    <pre><code># 容器无法启动
docker logs mysql57-prod

# 检查端口占用（多种方法）
# 方法1：使用ss命令（推荐）
ss -tulpn | grep 3306

# 方法2：使用netstat命令（传统方法）
netstat -tulpn | grep 3306

# 方法3：使用lsof命令
lsof -i :3306

# 方法4：使用nc命令测试连接
nc -zv localhost 3306

# 检查磁盘空间
df -h
docker system df</code></pre>

                    <h3><i class="fas fa-exclamation-triangle"></i> 15.2 连接问题排查</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>解决MySQL连接相关问题：</p>
                    <pre><code># 测试网络连接
telnet localhost 3306
nc -zv localhost 3306

# 检查MySQL用户权限
docker exec mysql57-prod mysql -uroot -p -e "
    SELECT User, Host FROM mysql.user;
    SHOW GRANTS FOR 'root'@'%';
"</code></pre>

                    <h3><i class="fas fa-tools"></i> 15.3 性能问题排查</h3>
                    <div class="machine-tag machine-mysql"><i class="fas fa-database"></i> MySQL容器</div>
                    <p>诊断MySQL性能问题：</p>
                    <pre><code># 查看当前运行的查询
SHOW PROCESSLIST;

# 查看慢查询日志
SHOW VARIABLES LIKE 'slow_query_log%';
SHOW STATUS LIKE 'Slow_queries';

# 查看锁等待情况
SHOW ENGINE INNODB STATUS\G</code></pre>

                    <div class="danger-box">
                        <strong><i class="fas fa-exclamation-circle"></i> 紧急故障处理：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>数据损坏：</strong>立即停止写入，从最新备份恢复</li>
                            <li><strong>磁盘满：</strong>清理日志文件，扩展存储空间</li>
                            <li><strong>内存不足：</strong>重启容器，调整内存配置</li>
                            <li><strong>连接超限：</strong>杀死长时间运行的查询</li>
                        </ul>
                    </div>
                </section>

                <section id="summary">
                    <h2><span class="step-number">16</span>总结检查</h2>

                    <h3><i class="fas fa-list-check"></i> 16.1 部署检查清单</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-check-circle"></i> 完成项目检查</h4>
                        <ul>
                            <li>✅ Docker环境准备完成</li>
                            <li>✅ MySQL 5.7.44绿色版下载完成</li>
                            <li>✅ 分阶段Dockerfile创建完成（基于Rocky Linux 8）</li>
                            <li>✅ MySQL配置文件优化完成</li>
                            <li>✅ 启动脚本和健康检查脚本创建完成</li>
                            <li>✅ MySQL镜像构建成功</li>
                            <li>✅ 容器运行和数据持久化配置完成</li>
                            <li>✅ 安全配置和性能优化完成</li>
                            <li>✅ 错误处理和故障排查方案完善</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-rocket"></i> 16.2 生产环境建议</h3>
                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 生产环境注意事项：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>版本升级：</strong>考虑迁移到MySQL 8.0获得更好的支持</li>
                            <li><strong>安全性：</strong>使用强密码，限制网络访问，定期更新</li>
                            <li><strong>备份：</strong>建立定期备份策略，测试恢复流程</li>
                            <li><strong>监控：</strong>部署监控系统，设置告警机制</li>
                            <li><strong>资源：</strong>根据负载调整内存和CPU配置</li>
                            <li><strong>网络：</strong>使用防火墙和VPN保护数据传输</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-graduation-cap"></i> 16.3 学习总结</h3>
                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 通过本教程您学到了：</strong>
                        <ul style="margin-top: 10px;">
                            <li>如何使用绿色版MySQL构建Docker镜像</li>
                            <li>分阶段构建的优势和实现方法</li>
                            <li>MySQL配置优化和安全设置</li>
                            <li>Docker数据持久化和网络配置</li>
                            <li>容器健康检查和监控方法</li>
                            <li>生产环境部署的最佳实践</li>
                            <li>常见问题的排查和解决方法</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-trophy"></i> 恭喜完成！</h4>
                        <p>您已经成功完成了MySQL 5.7绿色版Docker镜像的构建和部署。这个自定义镜像具有良好的性能、安全性和可维护性，可以用于开发、测试和生产环境。建议在生产环境中考虑升级到MySQL
                            8.0以获得更好的长期支持。</p>
                    </div>
                </section>

            </div>
        </div>
    </div>

    <!-- 返回顶部按钮 -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // 移动端菜单切换
        document.getElementById('mobileMenuBtn').addEventListener('click', function () {
            document.getElementById('sidebar').classList.toggle('active');
        });

        // 返回顶部功能
        window.addEventListener('scroll', function () {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'flex';
            } else {
                backToTop.style.display = 'none';
            }
        });

        document.getElementById('backToTop').addEventListener('click', function (e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 侧边栏导航高亮
        window.addEventListener('scroll', function () {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.sidebar a[href^="#"]');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // 平滑滚动
        document.querySelectorAll('.sidebar a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }

                // 移动端关闭菜单
                if (window.innerWidth <= 768) {
                    document.getElementById('sidebar').classList.remove('active');
                }
            });
        });

        // 代码复制功能
        document.querySelectorAll('pre code').forEach(block => {
            const button = document.createElement('button');
            button.className = 'copy-button';
            button.textContent = '复制';
            button.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                background: var(--primary-color);
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 12px;
                opacity: 0;
                transition: opacity 0.3s;
            `;

            const pre = block.parentElement;
            pre.style.position = 'relative';
            pre.appendChild(button);

            pre.addEventListener('mouseenter', () => {
                button.style.opacity = '1';
            });

            pre.addEventListener('mouseleave', () => {
                button.style.opacity = '0';
            });

            button.addEventListener('click', () => {
                navigator.clipboard.writeText(block.textContent).then(() => {
                    button.textContent = '已复制';
                    setTimeout(() => {
                        button.textContent = '复制';
                    }, 2000);
                });
            });
        });
    </script>
</body>

</html>