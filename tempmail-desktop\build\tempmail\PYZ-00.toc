('D:\\Code\\MicrosoftCode\\tempmail-desktop\\build\\tempmail\\PYZ-00.pyz',
 [('PyQt5',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_colorize',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_colorize.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_ios_support',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_ios_support.py',
   'PYMODULE'),
  ('_markupbase',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\DevelopmentTools\\Python3.13\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyrepl',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE'),
  ('_pyrepl._minimal_curses',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\_minimal_curses.py',
   'PYMODULE'),
  ('_pyrepl._threading_handler',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\_threading_handler.py',
   'PYMODULE'),
  ('_pyrepl.commands',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\commands.py',
   'PYMODULE'),
  ('_pyrepl.completing_reader',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\completing_reader.py',
   'PYMODULE'),
  ('_pyrepl.console',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\console.py',
   'PYMODULE'),
  ('_pyrepl.curses',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\curses.py',
   'PYMODULE'),
  ('_pyrepl.fancy_termios',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\fancy_termios.py',
   'PYMODULE'),
  ('_pyrepl.historical_reader',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\historical_reader.py',
   'PYMODULE'),
  ('_pyrepl.input',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\input.py',
   'PYMODULE'),
  ('_pyrepl.keymap',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\keymap.py',
   'PYMODULE'),
  ('_pyrepl.main',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\main.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\pager.py',
   'PYMODULE'),
  ('_pyrepl.reader',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\reader.py',
   'PYMODULE'),
  ('_pyrepl.readline',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\readline.py',
   'PYMODULE'),
  ('_pyrepl.simple_interact',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\simple_interact.py',
   'PYMODULE'),
  ('_pyrepl.trace',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\trace.py',
   'PYMODULE'),
  ('_pyrepl.types',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\types.py',
   'PYMODULE'),
  ('_pyrepl.unix_console',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\unix_console.py',
   'PYMODULE'),
  ('_pyrepl.unix_eventqueue',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\unix_eventqueue.py',
   'PYMODULE'),
  ('_pyrepl.utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\utils.py',
   'PYMODULE'),
  ('_pyrepl.windows_console',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_pyrepl\\windows_console.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast', 'D:\\DevelopmentTools\\Python3.13\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backend', '-', 'PYMODULE'),
  ('backend.api',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\api\\__init__.py',
   'PYMODULE'),
  ('backend.api.advanced_tempmail_api',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\api\\advanced_tempmail_api.py',
   'PYMODULE'),
  ('backend.api.real_tempmail_api',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\api\\real_tempmail_api.py',
   'PYMODULE'),
  ('backend.api.tempmail_api',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\api\\tempmail_api.py',
   'PYMODULE'),
  ('backend.app',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\app.py',
   'PYMODULE'),
  ('backend.database',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\database\\__init__.py',
   'PYMODULE'),
  ('backend.database.db_manager',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\database\\db_manager.py',
   'PYMODULE'),
  ('backend.utils',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\utils\\__init__.py',
   'PYMODULE'),
  ('backend.utils.helpers',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\utils\\helpers.py',
   'PYMODULE'),
  ('backports',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64', 'D:\\DevelopmentTools\\Python3.13\\Lib\\base64.py', 'PYMODULE'),
  ('bcrypt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bdb', 'D:\\DevelopmentTools\\Python3.13\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\DevelopmentTools\\Python3.13\\Lib\\bisect.py', 'PYMODULE'),
  ('blinker',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\blinker\\__init__.py',
   'PYMODULE'),
  ('blinker._utilities',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\blinker\\_utilities.py',
   'PYMODULE'),
  ('blinker.base',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\blinker\\base.py',
   'PYMODULE'),
  ('bottle',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bottle.py',
   'PYMODULE'),
  ('bs4',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4._deprecation',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bs4\\_deprecation.py',
   'PYMODULE'),
  ('bs4._typing',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bs4\\_typing.py',
   'PYMODULE'),
  ('bs4._warnings',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bs4\\_warnings.py',
   'PYMODULE'),
  ('bs4.builder',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('bs4.dammit',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.exceptions',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bs4\\exceptions.py',
   'PYMODULE'),
  ('bs4.filter',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bs4\\filter.py',
   'PYMODULE'),
  ('bs4.formatter',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'D:\\DevelopmentTools\\Python3.13\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('click',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.testing',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click\\testing.py',
   'PYMODULE'),
  ('click.types',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('clr',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr.py',
   'PYMODULE'),
  ('clr_loader',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\__init__.py',
   'PYMODULE'),
  ('clr_loader.ffi',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\ffi\\__init__.py',
   'PYMODULE'),
  ('clr_loader.ffi.hostfxr',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\ffi\\hostfxr.py',
   'PYMODULE'),
  ('clr_loader.ffi.mono',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\ffi\\mono.py',
   'PYMODULE'),
  ('clr_loader.ffi.netfx',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\ffi\\netfx.py',
   'PYMODULE'),
  ('clr_loader.hostfxr',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\hostfxr.py',
   'PYMODULE'),
  ('clr_loader.mono',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\mono.py',
   'PYMODULE'),
  ('clr_loader.netfx',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\netfx.py',
   'PYMODULE'),
  ('clr_loader.types',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\types.py',
   'PYMODULE'),
  ('clr_loader.util',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\util\\__init__.py',
   'PYMODULE'),
  ('clr_loader.util.clr_error',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\util\\clr_error.py',
   'PYMODULE'),
  ('clr_loader.util.coreclr_errors',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\util\\coreclr_errors.py',
   'PYMODULE'),
  ('clr_loader.util.find',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\util\\find.py',
   'PYMODULE'),
  ('clr_loader.util.hostfxr_errors',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\util\\hostfxr_errors.py',
   'PYMODULE'),
  ('clr_loader.util.runtime_spec',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\util\\runtime_spec.py',
   'PYMODULE'),
  ('cmd', 'D:\\DevelopmentTools\\Python3.13\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\DevelopmentTools\\Python3.13\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\DevelopmentTools\\Python3.13\\Lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\DevelopmentTools\\Python3.13\\Lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.verification',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE'),
  ('csv', 'D:\\DevelopmentTools\\Python3.13\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\curses\\has_key.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal', 'D:\\DevelopmentTools\\Python3.13\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\DevelopmentTools\\Python3.13\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\DevelopmentTools\\Python3.13\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\DevelopmentTools\\Python3.13\\Lib\\doctest.py', 'PYMODULE'),
  ('email',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('flask',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\__init__.py',
   'PYMODULE'),
  ('flask.app',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\app.py',
   'PYMODULE'),
  ('flask.blueprints',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\blueprints.py',
   'PYMODULE'),
  ('flask.cli',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\cli.py',
   'PYMODULE'),
  ('flask.config',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\config.py',
   'PYMODULE'),
  ('flask.ctx',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\ctx.py',
   'PYMODULE'),
  ('flask.debughelpers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\debughelpers.py',
   'PYMODULE'),
  ('flask.globals',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\globals.py',
   'PYMODULE'),
  ('flask.helpers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\helpers.py',
   'PYMODULE'),
  ('flask.json',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\json\\__init__.py',
   'PYMODULE'),
  ('flask.json.provider',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\json\\provider.py',
   'PYMODULE'),
  ('flask.json.tag',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\json\\tag.py',
   'PYMODULE'),
  ('flask.logging',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\logging.py',
   'PYMODULE'),
  ('flask.sansio', '-', 'PYMODULE'),
  ('flask.sansio.app',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\sansio\\app.py',
   'PYMODULE'),
  ('flask.sansio.blueprints',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\sansio\\blueprints.py',
   'PYMODULE'),
  ('flask.sansio.scaffold',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\sansio\\scaffold.py',
   'PYMODULE'),
  ('flask.sessions',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\sessions.py',
   'PYMODULE'),
  ('flask.signals',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\signals.py',
   'PYMODULE'),
  ('flask.templating',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\templating.py',
   'PYMODULE'),
  ('flask.testing',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\testing.py',
   'PYMODULE'),
  ('flask.typing',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\typing.py',
   'PYMODULE'),
  ('flask.wrappers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask\\wrappers.py',
   'PYMODULE'),
  ('flask_cors',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask_cors\\__init__.py',
   'PYMODULE'),
  ('flask_cors.core',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask_cors\\core.py',
   'PYMODULE'),
  ('flask_cors.decorator',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask_cors\\decorator.py',
   'PYMODULE'),
  ('flask_cors.extension',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask_cors\\extension.py',
   'PYMODULE'),
  ('flask_cors.version',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask_cors\\version.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\DevelopmentTools\\Python3.13\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib', 'D:\\DevelopmentTools\\Python3.13\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\DevelopmentTools\\Python3.13\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\DevelopmentTools\\Python3.13\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\DevelopmentTools\\Python3.13\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\DevelopmentTools\\Python3.13\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\DevelopmentTools\\Python3.13\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\DevelopmentTools\\Python3.13\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\DevelopmentTools\\Python3.13\\Lib\\hmac.py', 'PYMODULE'),
  ('html',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('http',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\DevelopmentTools\\Python3.13\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('itsdangerous',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\itsdangerous\\__init__.py',
   'PYMODULE'),
  ('itsdangerous._json',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\itsdangerous\\_json.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\itsdangerous\\encoding.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\itsdangerous\\exc.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\itsdangerous\\serializer.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\itsdangerous\\signer.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\itsdangerous\\timed.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\itsdangerous\\url_safe.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jinja2',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lxml',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'D:\\DevelopmentTools\\Python3.13\\Lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\DevelopmentTools\\Python3.13\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers', 'D:\\DevelopmentTools\\Python3.13\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\DevelopmentTools\\Python3.13\\Lib\\opcode.py', 'PYMODULE'),
  ('optparse',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\optparse.py',
   'PYMODULE'),
  ('packaging',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('pdb', 'D:\\DevelopmentTools\\Python3.13\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\DevelopmentTools\\Python3.13\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\DevelopmentTools\\Python3.13\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\platform.py',
   'PYMODULE'),
  ('pprint', 'D:\\DevelopmentTools\\Python3.13\\Lib\\pprint.py', 'PYMODULE'),
  ('proxy_tools',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\proxy_tools\\__init__.py',
   'PYMODULE'),
  ('py_compile',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\py_compile.py',
   'PYMODULE'),
  ('pycparser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'D:\\DevelopmentTools\\Python3.13\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pythonnet',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\pythonnet\\__init__.py',
   'PYMODULE'),
  ('queue', 'D:\\DevelopmentTools\\Python3.13\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\DevelopmentTools\\Python3.13\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\DevelopmentTools\\Python3.13\\Lib\\random.py', 'PYMODULE'),
  ('requests',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'D:\\DevelopmentTools\\Python3.13\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\DevelopmentTools\\Python3.13\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._discovery',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_discovery.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._shutil',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE'),
  ('setuptools._static',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\DevelopmentTools\\Python3.13\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\DevelopmentTools\\Python3.13\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\DevelopmentTools\\Python3.13\\Lib\\signal.py', 'PYMODULE'),
  ('site', 'D:\\DevelopmentTools\\Python3.13\\Lib\\site.py', 'PYMODULE'),
  ('socket', 'D:\\DevelopmentTools\\Python3.13\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\socketserver.py',
   'PYMODULE'),
  ('soupsieve',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.__main__',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\sqlite3\\__main__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'D:\\DevelopmentTools\\Python3.13\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'D:\\DevelopmentTools\\Python3.13\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\sysconfig\\__init__.py',
   'PYMODULE'),
  ('tarfile', 'D:\\DevelopmentTools\\Python3.13\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\threading.py',
   'PYMODULE'),
  ('token', 'D:\\DevelopmentTools\\Python3.13\\Lib\\token.py', 'PYMODULE'),
  ('tokenize',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tomllib',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._re',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('tomllib._types',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\DevelopmentTools\\Python3.13\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\DevelopmentTools\\Python3.13\\Lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uuid', 'D:\\DevelopmentTools\\Python3.13\\Lib\\uuid.py', 'PYMODULE'),
  ('webbrowser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('webview',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\__init__.py',
   'PYMODULE'),
  ('webview.dom',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\dom\\__init__.py',
   'PYMODULE'),
  ('webview.dom.classlist',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\dom\\classlist.py',
   'PYMODULE'),
  ('webview.dom.dom',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\dom\\dom.py',
   'PYMODULE'),
  ('webview.dom.element',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\dom\\element.py',
   'PYMODULE'),
  ('webview.dom.event',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\dom\\event.py',
   'PYMODULE'),
  ('webview.dom.propsdict',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\dom\\propsdict.py',
   'PYMODULE'),
  ('webview.errors',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\errors.py',
   'PYMODULE'),
  ('webview.event',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\event.py',
   'PYMODULE'),
  ('webview.guilib',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\guilib.py',
   'PYMODULE'),
  ('webview.http',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\http.py',
   'PYMODULE'),
  ('webview.localization',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\localization.py',
   'PYMODULE'),
  ('webview.menu',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\menu.py',
   'PYMODULE'),
  ('webview.platforms',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\platforms\\__init__.py',
   'PYMODULE'),
  ('webview.platforms.android',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\platforms\\android.py',
   'PYMODULE'),
  ('webview.platforms.cef',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\platforms\\cef.py',
   'PYMODULE'),
  ('webview.platforms.cocoa',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\platforms\\cocoa.py',
   'PYMODULE'),
  ('webview.platforms.edgechromium',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\platforms\\edgechromium.py',
   'PYMODULE'),
  ('webview.platforms.gtk',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\platforms\\gtk.py',
   'PYMODULE'),
  ('webview.platforms.mshtml',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\platforms\\mshtml.py',
   'PYMODULE'),
  ('webview.platforms.qt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\platforms\\qt.py',
   'PYMODULE'),
  ('webview.platforms.winforms',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\platforms\\winforms.py',
   'PYMODULE'),
  ('webview.screen',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\screen.py',
   'PYMODULE'),
  ('webview.util',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\util.py',
   'PYMODULE'),
  ('webview.window',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\window.py',
   'PYMODULE'),
  ('werkzeug',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\__init__.py',
   'PYMODULE'),
  ('werkzeug._internal',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\_internal.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\_reloader.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\datastructures\\__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\datastructures\\accept.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\datastructures\\auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\datastructures\\cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\datastructures\\csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\datastructures\\etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\datastructures\\file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\datastructures\\headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\datastructures\\mixins.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\datastructures\\range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\datastructures\\structures.py',
   'PYMODULE'),
  ('werkzeug.debug',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\debug\\__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\debug\\console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\debug\\repr.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\debug\\tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\formparser.py',
   'PYMODULE'),
  ('werkzeug.http',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\http.py',
   'PYMODULE'),
  ('werkzeug.local',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\local.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\middleware\\__init__.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\middleware\\shared_data.py',
   'PYMODULE'),
  ('werkzeug.routing',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\routing\\__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\routing\\converters.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\routing\\exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\routing\\map.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\routing\\matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\routing\\rules.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\sansio\\__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\sansio\\http.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\sansio\\multipart.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\sansio\\request.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\sansio\\response.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\sansio\\utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\security.py',
   'PYMODULE'),
  ('werkzeug.serving',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\serving.py',
   'PYMODULE'),
  ('werkzeug.test',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\test.py',
   'PYMODULE'),
  ('werkzeug.urls',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\urls.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\user_agent.py',
   'PYMODULE'),
  ('werkzeug.utils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\utils.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\wrappers\\__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\wrappers\\request.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\wrappers\\response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug\\wsgi.py',
   'PYMODULE'),
  ('wsgiref',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\wsgiref\\__init__.py',
   'PYMODULE'),
  ('wsgiref.handlers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\wsgiref\\handlers.py',
   'PYMODULE'),
  ('wsgiref.headers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\wsgiref\\headers.py',
   'PYMODULE'),
  ('wsgiref.simple_server',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\wsgiref\\simple_server.py',
   'PYMODULE'),
  ('wsgiref.types',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\wsgiref\\types.py',
   'PYMODULE'),
  ('wsgiref.util',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\wsgiref\\util.py',
   'PYMODULE'),
  ('xml',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\zipimport.py',
   'PYMODULE')])
