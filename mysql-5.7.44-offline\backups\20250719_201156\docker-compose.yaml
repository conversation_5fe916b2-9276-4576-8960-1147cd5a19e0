services:
  mysql:
    build:
      context: .
      dockerfile: Dockerfile
    image: mysql-offline:5.7.44
    container_name: mysql5.7.44
    restart: always

    # 日志配置 - 增加详细日志输出
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

    # 环境变量
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_USER=root
      - MYSQL_PASSWORD=root
      - MYSQL_DATABASE=mysql
      - TZ=Asia/Shanghai

    # 端口映射
    ports:
      - "3306:3306"

    # 数据卷挂载
    volumes:
      # MySQL数据目录持久化
      - mysql_data:/usr/local/mysql/data
      # MySQL日志目录持久化
      - mysql_logs:/usr/local/mysql/logs
      # 配置文件挂载（移除只读限制以允许权限设置）
      - ./my.cnf:/etc/my.cnf
      # 可选：挂载备份目录
      - ./backups:/backups
      # 可选：挂载初始化SQL脚本
      # - ./init-scripts:/docker-entrypoint-initdb.d:ro


    # 网络设置
    networks:
      - mysql_network

    # 健康检查 - 使用自定义健康检查脚本
    healthcheck:
      test: ["CMD", "/usr/local/bin/healthcheck.sh"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 120s

    # 资源限制（根据MySQL性能优化调整）
    deploy:
      resources:
        limits:
          memory: 6G
          cpus: '4.0'
        reservations:
          memory: 1G
          cpus: '1.0'

    # 安全选项
    security_opt:
      - no-new-privileges:true

    # 让容器以root身份启动，MySQL进程会自动切换到mysql用户

# 数据卷定义
volumes:
  mysql_data:
    driver: local
    name: mysql_data_offline
  mysql_logs:
    driver: local
    name: mysql_logs_offline

# 网络定义
networks:
  mysql_network:
    driver: bridge
    name: mysql_network
