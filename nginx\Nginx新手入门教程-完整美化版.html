<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nginx新手入门教程 - 从零开始学Nginx</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 12px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 欢迎框样式 */
        .welcome-box {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .welcome-box::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 8s ease-in-out infinite;
        }

        .welcome-box h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .welcome-box p {
            font-size: 16px;
            line-height: 1.8;
            color: var(--text-primary);
            position: relative;
            z-index: 1;
        }

        /* 学习路径样式 */
        .learning-path {
            margin-top: 30px;
            position: relative;
            z-index: 1;
        }

        .learning-path h4 {
            color: var(--secondary-color);
            margin-bottom: 25px;
        }

        .path-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .path-step {
            background: var(--light-surface);
            border-radius: 15px;
            padding: 25px;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .path-step:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .step-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 20px;
            margin-bottom: 15px;
            box-shadow: var(--shadow-md);
        }

        .step-content h5 {
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .step-content p {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.6;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box,
        .beginner-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover,
        .beginner-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        .beginner-box {
            background: linear-gradient(135deg, rgba(128, 90, 213, 0.1) 0%, rgba(128, 90, 213, 0.05) 100%);
            border-left-color: #805ad5;
            color: #553c9a;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: var(--light-surface);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        th,
        td {
            padding: 18px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-decoration: none;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            z-index: 999;
            font-size: 20px;
        }

        .back-to-top:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .content-wrapper {
                padding: 30px;
            }

            .container {
                padding: 40px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
                border-radius: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 16px;
                margin-right: 15px;
            }

            .path-steps {
                grid-template-columns: 1fr;
            }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
        }
    </style>
</head>

<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-server"></i> Nginx新手入门</h2>
            <p>从零开始学习Nginx</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#what-is-nginx"><i class="fas fa-question-circle"></i>1. 什么是Nginx</a></li>
                <li><a href="#why-use-nginx"><i class="fas fa-lightbulb"></i>2. 为什么要用Nginx</a></li>
                <li><a href="#installation"><i class="fas fa-download"></i>3. 安装Nginx</a></li>
                <li><a href="#basic-config"><i class="fas fa-cog"></i>4. 基础配置</a></li>
                <li><a href="#static-files"><i class="fas fa-file"></i>5. 静态文件服务</a></li>
                <li><a href="#reverse-proxy"><i class="fas fa-exchange-alt"></i>6. 反向代理</a></li>
                <li><a href="#load-balancing"><i class="fas fa-balance-scale"></i>7. 负载均衡</a></li>
                <li><a href="#ssl-https"><i class="fas fa-lock"></i>8. SSL/HTTPS配置</a></li>
                <li><a href="#performance"><i class="fas fa-tachometer-alt"></i>9. 性能优化</a></li>
                <li><a href="#security"><i class="fas fa-shield-alt"></i>10. 安全配置</a></li>
                <li><a href="#monitoring"><i class="fas fa-chart-line"></i>11. 监控与日志</a></li>
                <li><a href="#troubleshooting"><i class="fas fa-tools"></i>12. 故障排除</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-server"></i> Nginx新手入门教程</h1>

                <div class="welcome-box">
                    <h3><i class="fas fa-heart"></i> 欢迎来到Nginx的世界！</h3>
                    <p>这是一份专门为初学者准备的Nginx入门教程。我们会用最通俗易懂的语言，带你从零开始学习Nginx。不需要任何基础，只要你有一颗学习的心！</p>

                    <div class="learning-path">
                        <h4><i class="fas fa-map"></i> 学习路径</h4>
                        <div class="path-steps">
                            <div class="path-step">
                                <div class="step-icon">1</div>
                                <div class="step-content">
                                    <h5>理解概念</h5>
                                    <p>先了解什么是Nginx，为什么要用它</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">2</div>
                                <div class="step-content">
                                    <h5>安装配置</h5>
                                    <p>学会安装Nginx并进行基础配置</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">3</div>
                                <div class="step-content">
                                    <h5>实战应用</h5>
                                    <p>掌握静态文件、反向代理等功能</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">4</div>
                                <div class="step-content">
                                    <h5>高级功能</h5>
                                    <p>学习负载均衡、SSL、性能优化</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <section id="what-is-nginx">
                    <h2><span class="step-number">1</span>什么是Nginx？</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-baby"></i> 小白友好解释</h3>
                        <p>想象一下，你开了一家餐厅，Nginx就像是一个超级能干的服务员，它能够：</p>
                        <ul>
                            <li><i class="fas fa-door-open"></i> <strong>迎接客人</strong> - 接收用户的网络请求</li>
                            <li><i class="fas fa-utensils"></i> <strong>端菜上桌</strong> - 把网页文件送给用户</li>
                            <li><i class="fas fa-route"></i> <strong>指路引导</strong> - 把请求转发给后台服务器</li>
                            <li><i class="fas fa-users"></i> <strong>分流管理</strong> - 合理分配客人到不同的桌子</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-book"></i> 正式定义</h3>
                    <p>Nginx（发音：engine-x）是一个高性能的<strong>HTTP和反向代理服务器</strong>，也是一个<strong>IMAP/POP3/SMTP服务器</strong>。它以其高性能、稳定性、丰富的功能集、简单的配置文件和低系统资源消耗而闻名。
                    </p>

                    <div class="info-box">
                        <h4><i class="fas fa-lightbulb"></i> 为什么叫Nginx？</h4>
                        <p>Nginx的名字来源于"engine x"，意思是"引擎X"。它的创始人Igor
                            Sysoev在2004年开发了这个项目，最初是为了解决俄罗斯访问量第二的Rambler.ru网站的性能问题。</p>
                    </div>

                    <h3><i class="fas fa-history"></i> Nginx的发展历程</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-calendar"></i> 时间</th>
                            <th><i class="fas fa-landmark"></i> 事件</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td>2002</td>
                            <td>项目启动</td>
                            <td>Igor Sysoev开始开发Nginx</td>
                        </tr>
                        <tr>
                            <td>2004</td>
                            <td>首次发布</td>
                            <td>Nginx 0.1.0版本发布</td>
                        </tr>
                        <tr>
                            <td>2011</td>
                            <td>公司成立</td>
                            <td>Nginx Inc.公司成立</td>
                        </tr>
                        <tr>
                            <td>2019</td>
                            <td>被收购</td>
                            <td>F5 Networks收购Nginx Inc.</td>
                        </tr>
                        <tr>
                            <td>现在</td>
                            <td>市场领导者</td>
                            <td>全球最受欢迎的Web服务器之一</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-star"></i> Nginx的核心特点</h3>
                    <div class="path-steps">
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-tachometer-alt"></i></div>
                            <div class="step-content">
                                <h5>高性能</h5>
                                <p>采用事件驱动架构，能处理大量并发连接</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-memory"></i></div>
                            <div class="step-content">
                                <h5>低内存消耗</h5>
                                <p>相比Apache，内存占用更少，效率更高</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-cog"></i></div>
                            <div class="step-content">
                                <h5>配置简单</h5>
                                <p>配置文件语法简洁，易于理解和维护</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-shield-alt"></i></div>
                            <div class="step-content">
                                <h5>稳定可靠</h5>
                                <p>经过大量生产环境验证，稳定性极高</p>
                            </div>
                        </div>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-check-circle"></i> 小结</h4>
                        <p>Nginx就是一个超级高效的"网络服务员"，它能帮你处理网站访问、文件传输、请求转发等各种任务。有了它，你的网站就能更快、更稳定地为用户提供服务！</p>
                    </div>
                </section>

                <section id="why-use-nginx">
                    <h2><span class="step-number">2</span>为什么要使用Nginx？</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-question"></i> 没有Nginx的痛苦</h3>
                        <p>想象一下，你的网站没有Nginx的情况：</p>
                        <ul>
                            <li><i class="fas fa-times"></i> <strong>访问慢</strong> - 用户打开网页要等很久</li>
                            <li><i class="fas fa-times"></i> <strong>服务器压力大</strong> - 稍微多点用户就扛不住</li>
                            <li><i class="fas fa-times"></i> <strong>静态文件效率低</strong> - 图片、CSS、JS加载缓慢</li>
                            <li><i class="fas fa-times"></i> <strong>无法负载均衡</strong> - 所有请求都压在一台服务器上</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-star"></i> Nginx的优势</h3>

                    <h4><i class="fas fa-rocket"></i> 1. 高并发处理能力</h4>
                    <div class="info-box">
                        <p><strong>传统服务器：</strong>每个用户连接需要一个线程，1000个用户就需要1000个线程</p>
                        <p><strong>Nginx：</strong>使用事件驱动模型，一个进程就能处理数万个连接</p>

                        <pre><code># 传统Apache的处理方式（简化说明）
用户1 → 线程1 → 处理请求
用户2 → 线程2 → 处理请求
用户3 → 线程3 → 处理请求
...（每个用户一个线程，资源消耗大）

# Nginx的处理方式
用户1、用户2、用户3... → 事件循环 → 高效处理所有请求</code></pre>
                    </div>

                    <h4><i class="fas fa-file"></i> 2. 静态文件服务优化</h4>
                    <div class="success-box">
                        <p>Nginx在处理静态文件（图片、CSS、JS等）方面表现卓越：</p>
                        <ul>
                            <li><strong>零拷贝技术：</strong>直接从磁盘发送文件到网络，不经过应用程序内存</li>
                            <li><strong>文件缓存：</strong>智能缓存常用文件，减少磁盘读取</li>
                            <li><strong>压缩传输：</strong>自动压缩文件，减少传输时间</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-exchange-alt"></i> 3. 反向代理功能</h4>
                    <div class="warning-box">
                        <p><strong>场景：</strong>你的网站后台有多个服务（用户服务、订单服务、支付服务）</p>
                        <p><strong>Nginx的作用：</strong></p>
                        <ol>
                            <li>用户访问 www.yoursite.com</li>
                            <li>Nginx接收请求，根据URL路径判断要访问哪个服务</li>
                            <li>将请求转发给对应的后台服务器</li>
                            <li>将后台服务器的响应返回给用户</li>
                        </ol>
                        <p>用户感觉访问的是一个网站，实际上背后可能有很多台服务器在工作！</p>
                    </div>

                    <h4><i class="fas fa-balance-scale"></i> 4. 负载均衡</h4>
                    <div class="info-box">
                        <p>当你的网站用户很多时，一台服务器可能扛不住，这时候可以用多台服务器：</p>
                        <pre><code># 用户请求分配示例
用户A → Nginx → 服务器1 (处理用户A的请求)
用户B → Nginx → 服务器2 (处理用户B的请求)
用户C → Nginx → 服务器3 (处理用户C的请求)
用户D → Nginx → 服务器1 (服务器1空闲了，继续处理)

# 这样就把压力分散到了多台服务器上！</code></pre>
                    </div>
                </section>

                <section id="installation">
                    <h2><span class="step-number">3</span>安装Nginx</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-info-circle"></i> 安装前须知</h3>
                        <p>安装Nginx有多种方式，我们会介绍最常用的几种方法。选择适合你操作系统的方式即可！</p>
                    </div>

                    <h3><i class="fab fa-ubuntu"></i> Ubuntu/Debian系统安装</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-terminal"></i> 使用包管理器安装（推荐新手）</h4>
                        <p>这是最简单的安装方式，适合初学者：</p>
                        <pre><code># 1. 更新软件包列表
sudo apt update

# 2. 安装Nginx
sudo apt install nginx

# 3. 启动Nginx服务
sudo systemctl start nginx

# 4. 设置开机自启动
sudo systemctl enable nginx

# 5. 检查Nginx状态
sudo systemctl status nginx</code></pre>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-check"></i> 验证安装</h4>
                        <p>安装完成后，打开浏览器访问 <code>http://localhost</code> 或 <code>http://你的服务器IP</code>，如果看到"Welcome to
                            nginx!"页面，说明安装成功！</p>
                    </div>

                    <h3><i class="fab fa-centos"></i> CentOS/RHEL系统安装</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-terminal"></i> 使用yum/dnf安装</h4>
                        <pre><code># CentOS 7及以下版本
sudo yum install epel-release
sudo yum install nginx

# CentOS 8及以上版本
sudo dnf install nginx

# 启动并设置自启动
sudo systemctl start nginx
sudo systemctl enable nginx

# 开放防火墙端口（如果启用了防火墙）
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload</code></pre>
                    </div>

                    <h3><i class="fab fa-windows"></i> Windows系统安装</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-download"></i> 下载安装</h4>
                        <ol>
                            <li>访问Nginx官网：<code>http://nginx.org/en/download.html</code></li>
                            <li>下载Windows版本的zip文件</li>
                            <li>解压到你想要的目录，比如 <code>C:\nginx</code></li>
                            <li>打开命令提示符，进入nginx目录</li>
                            <li>运行 <code>nginx.exe</code> 启动服务</li>
                        </ol>
                        <pre><code># Windows命令行操作
cd C:\nginx
nginx.exe

# 停止nginx
nginx.exe -s stop

# 重新加载配置
nginx.exe -s reload</code></pre>
                    </div>

                    <h3><i class="fab fa-apple"></i> macOS系统安装</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-beer"></i> 使用Homebrew安装（推荐）</h4>
                        <pre><code># 1. 安装Homebrew（如果还没有安装）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 2. 安装Nginx
brew install nginx

# 3. 启动Nginx
brew services start nginx

# 4. 停止Nginx
brew services stop nginx</code></pre>
                    </div>

                    <h3><i class="fas fa-hammer"></i> 从源码编译安装（高级用户）</h3>
                    <div class="danger-box">
                        <h4><i class="fas fa-exclamation-triangle"></i> 注意</h4>
                        <p>源码编译适合有经验的用户，可以自定义编译选项，但过程较复杂。新手建议使用包管理器安装。</p>
                        <pre><code># 下载源码
wget http://nginx.org/download/nginx-1.24.0.tar.gz
tar -zxvf nginx-1.24.0.tar.gz
cd nginx-1.24.0

# 配置编译选项
./configure --prefix=/etc/nginx \
            --sbin-path=/usr/sbin/nginx \
            --conf-path=/etc/nginx/nginx.conf \
            --with-http_ssl_module \
            --with-http_v2_module

# 编译并安装
make && sudo make install</code></pre>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-lightbulb"></i> 安装小贴士</h4>
                        <ul>
                            <li><strong>新手推荐：</strong>使用系统包管理器安装，简单快捷</li>
                            <li><strong>生产环境：</strong>考虑从官方仓库安装最新稳定版</li>
                            <li><strong>特殊需求：</strong>需要特定模块时才考虑源码编译</li>
                            <li><strong>Docker用户：</strong>可以使用官方nginx镜像</li>
                        </ul>
                    </div>
                </section>

                <section id="basic-config">
                    <h2><span class="step-number">4</span>基础配置</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-map"></i> 配置文件位置</h3>
                        <p>Nginx的配置文件通常位于以下位置：</p>
                        <ul>
                            <li><strong>Ubuntu/Debian：</strong><code>/etc/nginx/nginx.conf</code></li>
                            <li><strong>CentOS/RHEL：</strong><code>/etc/nginx/nginx.conf</code></li>
                            <li><strong>Windows：</strong><code>nginx安装目录/conf/nginx.conf</code></li>
                            <li><strong>macOS (Homebrew)：</strong><code>/usr/local/etc/nginx/nginx.conf</code></li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-file-code"></i> 配置文件结构</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-tree"></i> 基本结构解析</h4>
                        <p>Nginx配置文件采用块状结构，就像俄罗斯套娃一样，一层套一层：</p>
                        <pre><code># 全局配置
user nginx;
worker_processes auto;

# 事件配置块
events {
    worker_connections 1024;
}

# HTTP配置块
http {
    # HTTP全局配置
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 服务器配置块
    server {
        listen 80;
        server_name example.com;

        # 位置配置块
        location / {
            root /var/www/html;
            index index.html;
        }
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-cogs"></i> 常用配置指令详解</h3>

                    <h4><i class="fas fa-user"></i> 1. 用户和进程配置</h4>
                    <div class="warning-box">
                        <pre><code># 指定nginx运行用户（安全考虑）
user nginx;

# 工作进程数量（通常设置为CPU核心数）
worker_processes auto;  # auto表示自动检测

# 错误日志位置和级别
error_log /var/log/nginx/error.log warn;</code></pre>
                        <p><strong>解释：</strong>这些配置决定了Nginx以什么身份运行，用多少个进程处理请求。</p>
                    </div>

                    <h4><i class="fas fa-network-wired"></i> 2. 事件配置</h4>
                    <div class="info-box">
                        <pre><code>events {
    # 每个工作进程的最大连接数
    worker_connections 1024;

    # 使用epoll事件模型（Linux推荐）
    use epoll;

    # 允许一个进程同时接受多个连接
    multi_accept on;
}</code></pre>
                        <p><strong>解释：</strong>这里配置Nginx如何处理网络连接，影响并发性能。</p>
                    </div>

                    <h4><i class="fas fa-globe"></i> 3. HTTP全局配置</h4>
                    <div class="success-box">
                        <pre><code>http {
    # 包含MIME类型定义
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式定义
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    # 访问日志
    access_log /var/log/nginx/access.log main;

    # 开启高效文件传输
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;

    # 连接超时时间
    keepalive_timeout 65;

    # 开启gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript;
}</code></pre>
                    </div>

                    <h3><i class="fas fa-server"></i> 服务器块配置</h3>
                    <div class="beginner-box">
                        <h4><i class="fas fa-lightbulb"></i> 什么是server块？</h4>
                        <p>server块就像是一个虚拟主机，可以配置不同的域名、端口，处理不同的网站。一个Nginx可以配置多个server块。</p>
                    </div>

                    <div class="info-box">
                        <h4><i class="fas fa-code"></i> 基本server块配置</h4>
                        <pre><code>server {
    # 监听端口
    listen 80;

    # 服务器名称（域名）
    server_name example.com www.example.com;

    # 网站根目录
    root /var/www/example.com;

    # 默认首页文件
    index index.html index.htm index.php;

    # 访问日志（可选，会覆盖全局设置）
    access_log /var/log/nginx/example.com.access.log;
    error_log /var/log/nginx/example.com.error.log;

    # 位置配置
    location / {
        try_files $uri $uri/ =404;
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-tools"></i> 常用管理命令</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-terminal"></i> 必须掌握的命令</h4>
                        <pre><code># 检查配置文件语法
sudo nginx -t

# 重新加载配置（不停止服务）
sudo nginx -s reload

# 停止nginx
sudo nginx -s stop

# 优雅停止（等待当前请求处理完）
sudo nginx -s quit

# 重启nginx服务
sudo systemctl restart nginx

# 查看nginx状态
sudo systemctl status nginx

# 查看nginx进程
ps aux | grep nginx</code></pre>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-check-circle"></i> 配置修改流程</h4>
                        <ol>
                            <li><strong>备份配置：</strong><code>sudo cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup</code>
                            </li>
                            <li><strong>修改配置：</strong>使用编辑器修改配置文件</li>
                            <li><strong>检查语法：</strong><code>sudo nginx -t</code></li>
                            <li><strong>重新加载：</strong><code>sudo nginx -s reload</code></li>
                            <li><strong>验证效果：</strong>访问网站检查是否生效</li>
                        </ol>
                    </div>
                </section>

                <section id="static-files">
                    <h2><span class="step-number">5</span>静态文件服务</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-question"></i> 什么是静态文件？</h3>
                        <p>静态文件就是不需要服务器处理的文件，比如：</p>
                        <ul>
                            <li><i class="fas fa-image"></i> <strong>图片：</strong>jpg, png, gif, svg等</li>
                            <li><i class="fas fa-file-code"></i> <strong>样式表：</strong>CSS文件</li>
                            <li><i class="fas fa-file-code"></i> <strong>脚本：</strong>JavaScript文件</li>
                            <li><i class="fas fa-file-alt"></i> <strong>文档：</strong>PDF, Word等</li>
                            <li><i class="fas fa-video"></i> <strong>媒体：</strong>视频、音频文件</li>
                        </ul>
                        <p>Nginx在处理这些文件时非常高效，这是它的强项之一！</p>
                    </div>

                    <h3><i class="fas fa-folder"></i> 基本静态文件配置</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-home"></i> 简单的静态网站配置</h4>
                        <pre><code>server {
    listen 80;
    server_name mywebsite.com;

    # 网站根目录
    root /var/www/mywebsite;

    # 默认首页
    index index.html index.htm;

    # 处理所有请求
    location / {
        # 尝试找文件，找不到返回404
        try_files $uri $uri/ =404;
    }

    # 静态资源缓存配置
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;  # 缓存1年
        add_header Cache-Control "public, immutable";
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-rocket"></i> 性能优化配置</h3>

                    <h4><i class="fas fa-compress"></i> 1. 开启Gzip压缩</h4>
                    <div class="success-box">
                        <p>Gzip压缩可以大幅减少传输的数据量，提高网站加载速度：</p>
                        <pre><code>http {
    # 开启gzip压缩
    gzip on;

    # 压缩级别 (1-9，9最高但最耗CPU)
    gzip_comp_level 6;

    # 最小压缩文件大小
    gzip_min_length 1000;

    # 压缩的文件类型
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # 为代理请求也启用压缩
    gzip_proxied any;

    # 添加Vary头，告诉缓存服务器根据Accept-Encoding区分缓存
    gzip_vary on;
}</code></pre>
                    </div>

                    <h4><i class="fas fa-clock"></i> 2. 浏览器缓存配置</h4>
                    <div class="warning-box">
                        <p>合理的缓存策略可以减少服务器负载，提高用户体验：</p>
                        <pre><code>server {
    # 图片文件缓存1年
    location ~* \.(jpg|jpeg|png|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;  # 不记录访问日志
    }

    # CSS和JS文件缓存1个月
    location ~* \.(css|js)$ {
        expires 1M;
        add_header Cache-Control "public";
    }

    # HTML文件不缓存（经常更新）
    location ~* \.(html|htm)$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # 字体文件缓存1年
    location ~* \.(woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        # 允许跨域访问字体
        add_header Access-Control-Allow-Origin "*";
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-shield-alt"></i> 安全配置</h3>
                    <div class="danger-box">
                        <h4><i class="fas fa-exclamation-triangle"></i> 隐藏敏感文件</h4>
                        <p>防止用户访问不应该公开的文件：</p>
                        <pre><code>server {
    # 禁止访问隐藏文件（以.开头的文件）
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 禁止访问备份文件
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 禁止访问配置文件
    location ~* \.(conf|config|ini)$ {
        deny all;
    }

    # 禁止访问日志文件
    location ~* \.(log)$ {
        deny all;
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-folder-open"></i> 目录浏览配置</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-list"></i> 启用目录列表</h4>
                        <p>有时候你可能需要让用户浏览文件夹内容（比如文件下载站）：</p>
                        <pre><code>server {
    listen 80;
    server_name files.example.com;
    root /var/www/files;

    location / {
        # 开启目录浏览
        autoindex on;

        # 显示文件大小
        autoindex_exact_size off;  # off显示KB/MB，on显示字节

        # 显示本地时间
        autoindex_localtime on;

        # 自定义目录页面样式
        autoindex_format html;  # 可选：html, xml, json, jsonp
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-download"></i> 文件下载配置</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-file-download"></i> 强制下载特定文件</h4>
                        <pre><code>server {
    # 强制下载PDF文件而不是在浏览器中打开
    location ~* \.pdf$ {
        add_header Content-Disposition "attachment";
    }

    # 限制下载速度（防止带宽被占满）
    location /downloads/ {
        limit_rate 1m;  # 限制每个连接1MB/s
    }

    # 大文件下载优化
    location /bigfiles/ {
        # 开启sendfile
        sendfile on;
        # 开启TCP_NOPUSH
        tcp_nopush on;
        # 设置较大的输出缓冲区
        output_buffers 1 2m;
    }
}</code></pre>
                    </div>

                    <div class="beginner-box">
                        <h4><i class="fas fa-lightbulb"></i> 实战练习</h4>
                        <p>试着创建一个简单的静态网站：</p>
                        <ol>
                            <li>创建网站目录：<code>sudo mkdir -p /var/www/mysite</code></li>
                            <li>创建HTML文件：<code>sudo nano /var/www/mysite/index.html</code></li>
                            <li>配置Nginx虚拟主机</li>
                            <li>测试访问效果</li>
                        </ol>
                    </div>
                </section>

                <section id="reverse-proxy">
                    <h2><span class="step-number">6</span>反向代理</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-question"></i> 什么是反向代理？</h3>
                        <p>想象一下，你去餐厅吃饭：</p>
                        <ul>
                            <li><i class="fas fa-user"></i> <strong>你（客户端）</strong> - 想要点菜</li>
                            <li><i class="fas fa-user-tie"></i> <strong>服务员（Nginx）</strong> - 接收你的点菜需求</li>
                            <li><i class="fas fa-utensils"></i> <strong>厨师（后端服务器）</strong> - 实际做菜的人</li>
                        </ul>
                        <p>你不直接和厨师交流，而是通过服务员。服务员把你的需求转达给厨师，再把做好的菜端给你。这就是反向代理！</p>
                    </div>

                    <h3><i class="fas fa-cogs"></i> 基本反向代理配置</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-arrow-right"></i> 简单的代理配置</h4>
                        <pre><code>server {
    listen 80;
    server_name api.example.com;

    location / {
        # 代理到后端服务器
        proxy_pass http://127.0.0.1:3000;

        # 传递客户端真实IP
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}</code></pre>
                        <p><strong>解释：</strong>所有访问 api.example.com 的请求都会被转发到本地的3000端口。</p>
                    </div>

                    <h3><i class="fas fa-route"></i> 路径代理配置</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-map-signs"></i> 根据路径分发请求</h4>
                        <p>可以根据不同的URL路径，将请求转发到不同的后端服务：</p>
                        <pre><code>server {
    listen 80;
    server_name myapp.com;

    # 首页和静态文件
    location / {
        root /var/www/myapp;
        try_files $uri $uri/ @backend;
    }

    # API请求转发到Node.js应用
    location /api/ {
        proxy_pass http://127.0.0.1:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # 用户服务
    location /user/ {
        proxy_pass http://127.0.0.1:3001/;
    }

    # 订单服务
    location /order/ {
        proxy_pass http://127.0.0.1:3002/;
    }

    # 如果静态文件不存在，转发到后端
    location @backend {
        proxy_pass http://127.0.0.1:3000;
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-cog"></i> 高级代理配置</h3>

                    <h4><i class="fas fa-clock"></i> 1. 超时和缓冲设置</h4>
                    <div class="info-box">
                        <pre><code>location /api/ {
    proxy_pass http://backend;

    # 连接超时时间
    proxy_connect_timeout 30s;

    # 发送请求超时时间
    proxy_send_timeout 30s;

    # 接收响应超时时间
    proxy_read_timeout 30s;

    # 缓冲设置
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
    proxy_busy_buffers_size 8k;

    # 临时文件设置
    proxy_temp_file_write_size 8k;
    proxy_max_temp_file_size 1024m;
}</code></pre>
                    </div>

                    <h4><i class="fas fa-shield-alt"></i> 2. 错误处理</h4>
                    <div class="danger-box">
                        <pre><code>location /api/ {
    proxy_pass http://backend;

    # 当后端返回这些错误时，尝试下一个服务器
    proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;

    # 最多尝试次数
    proxy_next_upstream_tries 3;

    # 尝试超时时间
    proxy_next_upstream_timeout 30s;

    # 自定义错误页面
    error_page 502 503 504 /50x.html;

    location = /50x.html {
        root /var/www/error;
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-globe"></i> WebSocket代理</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-plug"></i> 支持WebSocket连接</h4>
                        <p>现代Web应用经常使用WebSocket进行实时通信：</p>
                        <pre><code>location /websocket/ {
    proxy_pass http://websocket_backend;

    # WebSocket必需的头部
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";

    # 传递客户端信息
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    # 禁用缓冲（实时性要求）
    proxy_buffering off;

    # 增加超时时间
    proxy_read_timeout 86400s;
    proxy_send_timeout 86400s;
}</code></pre>
                    </div>

                    <div class="beginner-box">
                        <h4><i class="fas fa-lightbulb"></i> 实战场景</h4>
                        <p>反向代理的常见应用场景：</p>
                        <ul>
                            <li><strong>微服务架构：</strong>将不同的API路径转发到不同的服务</li>
                            <li><strong>前后端分离：</strong>静态文件由Nginx提供，API请求转发到后端</li>
                            <li><strong>负载均衡：</strong>将请求分发到多台后端服务器</li>
                            <li><strong>SSL终止：</strong>Nginx处理HTTPS，后端使用HTTP</li>
                        </ul>
                    </div>
                </section>

                <section id="load-balancing">
                    <h2><span class="step-number">7</span>负载均衡</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-balance-scale"></i> 什么是负载均衡？</h3>
                        <p>想象一个银行有多个窗口：</p>
                        <ul>
                            <li><i class="fas fa-users"></i> <strong>客户（用户请求）</strong> - 需要办理业务</li>
                            <li><i class="fas fa-user-tie"></i> <strong>大堂经理（Nginx）</strong> - 指引客户到合适的窗口</li>
                            <li><i class="fas fa-desktop"></i> <strong>窗口（服务器）</strong> - 处理具体业务</li>
                        </ul>
                        <p>大堂经理会把客户分配到不同的窗口，避免某个窗口排队太长，这就是负载均衡！</p>
                    </div>

                    <h3><i class="fas fa-server"></i> 定义服务器组</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-list"></i> upstream配置</h4>
                        <p>首先需要定义一组后端服务器：</p>
                        <pre><code>http {
    # 定义后端服务器组
    upstream backend {
        server ************:8080;  # 服务器1
        server ************:8080;  # 服务器2
        server ************:8080;  # 服务器3
    }

    server {
        listen 80;
        server_name myapp.com;

        location / {
            proxy_pass http://backend;  # 使用服务器组
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-random"></i> 负载均衡算法</h3>

                    <h4><i class="fas fa-arrow-right"></i> 1. 轮询（默认）</h4>
                    <div class="success-box">
                        <p>请求按顺序分配给每台服务器：</p>
                        <pre><code>upstream backend {
    server ************:8080;  # 第1个请求
    server ************:8080;  # 第2个请求
    server ************:8080;  # 第3个请求
    # 第4个请求又回到第1台服务器
}</code></pre>
                        <p><strong>特点：</strong>简单公平，适合服务器性能相近的情况。</p>
                    </div>

                    <h4><i class="fas fa-weight"></i> 2. 加权轮询</h4>
                    <div class="warning-box">
                        <p>根据服务器性能分配不同的权重：</p>
                        <pre><code>upstream backend {
    server ************:8080 weight=3;  # 高性能服务器，权重3
    server ************:8080 weight=2;  # 中等性能，权重2
    server ************:8080 weight=1;  # 低性能服务器，权重1
}</code></pre>
                        <p><strong>说明：</strong>权重越高，分配到的请求越多。上面的配置中，第1台服务器会处理50%的请求。</p>
                    </div>

                    <h4><i class="fas fa-tachometer-alt"></i> 3. 最少连接</h4>
                    <div class="info-box">
                        <p>将请求分配给当前连接数最少的服务器：</p>
                        <pre><code>upstream backend {
    least_conn;  # 启用最少连接算法
    server ************:8080;
    server ************:8080;
    server ************:8080;
}</code></pre>
                        <p><strong>适用场景：</strong>请求处理时间差异较大的应用。</p>
                    </div>

                    <h4><i class="fas fa-fingerprint"></i> 4. IP哈希</h4>
                    <div class="danger-box">
                        <p>根据客户端IP进行哈希，确保同一IP总是访问同一台服务器：</p>
                        <pre><code>upstream backend {
    ip_hash;  # 启用IP哈希
    server ************:8080;
    server ************:8080;
    server ************:8080;
}</code></pre>
                        <p><strong>用途：</strong>需要会话保持的应用（如购物车、登录状态）。</p>
                    </div>

                    <h3><i class="fas fa-heartbeat"></i> 健康检查</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-stethoscope"></i> 服务器状态管理</h4>
                        <p>可以标记服务器的不同状态：</p>
                        <pre><code>upstream backend {
    server ************:8080;                    # 正常服务器
    server ************:8080 weight=2;           # 高权重服务器
    server ************:8080 down;               # 临时下线
    server ************:8080 backup;             # 备用服务器
    server ************:8080 max_fails=3 fail_timeout=30s;  # 故障检测
}</code></pre>
                        <p><strong>参数说明：</strong></p>
                        <ul>
                            <li><strong>down：</strong>标记服务器为永久不可用</li>
                            <li><strong>backup：</strong>备用服务器，只有其他服务器都不可用时才使用</li>
                            <li><strong>max_fails：</strong>最大失败次数</li>
                            <li><strong>fail_timeout：</strong>失败超时时间</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-lightbulb"></i> 负载均衡最佳实践</h4>
                        <ul>
                            <li><strong>监控服务器状态：</strong>定期检查后端服务器健康状况</li>
                            <li><strong>合理设置权重：</strong>根据服务器实际性能调整权重</li>
                            <li><strong>准备备用服务器：</strong>设置backup服务器应对突发情况</li>
                            <li><strong>记录访问日志：</strong>便于分析负载分布和故障排查</li>
                        </ul>
                    </div>
                </section>

                <section id="ssl-https">
                    <h2><span class="step-number">8</span>SSL/HTTPS配置</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-lock"></i> 为什么需要HTTPS？</h3>
                        <p>HTTPS就像给你的网站加了一把锁：</p>
                        <ul>
                            <li><i class="fas fa-shield-alt"></i> <strong>数据加密：</strong>防止信息被窃听</li>
                            <li><i class="fas fa-certificate"></i> <strong>身份验证：</strong>确认网站是真实的</li>
                            <li><i class="fas fa-check-circle"></i> <strong>数据完整性：</strong>防止信息被篡改</li>
                            <li><i class="fas fa-search"></i> <strong>SEO友好：</strong>搜索引擎更喜欢HTTPS网站</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-certificate"></i> 获取SSL证书</h3>

                    <h4><i class="fas fa-gift"></i> 1. 免费证书（Let's Encrypt）</h4>
                    <div class="success-box">
                        <p>Let's Encrypt提供免费的SSL证书，推荐新手使用：</p>
                        <pre><code># Ubuntu/Debian安装certbot
sudo apt update
sudo apt install certbot python3-certbot-nginx

# CentOS/RHEL安装certbot
sudo yum install epel-release
sudo yum install certbot python3-certbot-nginx

# 自动获取并配置证书
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# 测试自动续期
sudo certbot renew --dry-run</code></pre>
                    </div>

                    <h4><i class="fas fa-cog"></i> 2. 手动SSL配置</h4>
                    <div class="info-box">
                        <p>如果你已经有SSL证书文件：</p>
                        <pre><code>server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL证书文件路径
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;

    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # 网站内容
    root /var/www/yourdomain;
    index index.html;

    location / {
        try_files $uri $uri/ =404;
    }
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}</code></pre>
                    </div>

                    <h3><i class="fas fa-shield-alt"></i> 安全配置优化</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-lock"></i> 增强安全性</h4>
                        <pre><code>server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    # SSL基本配置
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    # 安全协议和加密套件
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256;
    ssl_prefer_server_ciphers off;

    # HSTS (强制HTTPS)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # 防止点击劫持
    add_header X-Frame-Options DENY always;

    # 防止MIME类型嗅探
    add_header X-Content-Type-Options nosniff always;

    # XSS保护
    add_header X-XSS-Protection "1; mode=block" always;

    # 引用策略
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
}</code></pre>
                    </div>

                    <div class="danger-box">
                        <h4><i class="fas fa-exclamation-triangle"></i> 常见问题</h4>
                        <ul>
                            <li><strong>证书过期：</strong>设置自动续期，避免网站无法访问</li>
                            <li><strong>混合内容：</strong>确保所有资源都使用HTTPS</li>
                            <li><strong>性能影响：</strong>启用HTTP/2和会话复用减少开销</li>
                            <li><strong>证书链：</strong>确保包含完整的证书链</li>
                        </ul>
                    </div>
                </section>

                <section id="performance">
                    <h2><span class="step-number">9</span>性能优化</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-rocket"></i> 为什么要优化性能？</h3>
                        <p>网站性能直接影响用户体验和业务成果：</p>
                        <ul>
                            <li><i class="fas fa-clock"></i> <strong>加载速度：</strong>用户等待时间越短越好</li>
                            <li><i class="fas fa-users"></i> <strong>并发处理：</strong>同时服务更多用户</li>
                            <li><i class="fas fa-server"></i> <strong>资源利用：</strong>充分利用服务器性能</li>
                            <li><i class="fas fa-money-bill"></i> <strong>成本控制：</strong>减少服务器数量和带宽费用</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-cogs"></i> 基础性能配置</h3>

                    <h4><i class="fas fa-microchip"></i> 1. 工作进程优化</h4>
                    <div class="info-box">
                        <pre><code># 全局配置
worker_processes auto;  # 自动设置为CPU核心数
worker_cpu_affinity auto;  # 自动绑定CPU核心

# 事件配置
events {
    worker_connections 4096;  # 增加连接数
    use epoll;  # Linux下使用epoll
    multi_accept on;  # 允许同时接受多个连接
}</code></pre>
                    </div>

                    <h4><i class="fas fa-memory"></i> 2. 缓冲区优化</h4>
                    <div class="success-box">
                        <pre><code>http {
    # 客户端缓冲区
    client_body_buffer_size 128k;
    client_max_body_size 10m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;

    # 输出缓冲区
    output_buffers 1 32k;
    postpone_output 1460;

    # 代理缓冲区
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
}</code></pre>
                    </div>

                    <h3><i class="fas fa-compress"></i> 压缩优化</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-file-archive"></i> 高级Gzip配置</h4>
                        <pre><code>http {
    # 基础gzip设置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;

    # 压缩文件类型
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        application/xml
        image/svg+xml;

    # 代理请求压缩
    gzip_proxied any;

    # 禁用对IE6的压缩
    gzip_disable "msie6";
}</code></pre>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-chart-line"></i> 性能监控建议</h4>
                        <ul>
                            <li><strong>监控指标：</strong>响应时间、并发连接数、错误率</li>
                            <li><strong>日志分析：</strong>定期分析访问日志，找出性能瓶颈</li>
                            <li><strong>压力测试：</strong>使用工具如ab、wrk测试服务器性能</li>
                            <li><strong>持续优化：</strong>根据实际使用情况调整配置</li>
                        </ul>
                    </div>
                </section>

                <section id="security">
                    <h2><span class="step-number">10</span>安全配置</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-shield-alt"></i> 为什么要重视安全？</h3>
                        <p>网站安全就像给你的房子装防盗门：</p>
                        <ul>
                            <li><i class="fas fa-user-secret"></i> <strong>防止黑客攻击</strong> - 阻止恶意访问和攻击</li>
                            <li><i class="fas fa-database"></i> <strong>保护数据安全</strong> - 防止敏感信息泄露</li>
                            <li><i class="fas fa-business-time"></i> <strong>维护业务稳定</strong> - 避免服务中断和损失</li>
                            <li><i class="fas fa-gavel"></i> <strong>合规要求</strong> - 满足法律法规要求</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-ban"></i> 访问控制</h3>

                    <h4><i class="fas fa-globe"></i> 1. IP地址限制</h4>
                    <div class="warning-box">
                        <p>限制特定IP或IP段的访问：</p>
                        <pre><code>server {
    listen 80;
    server_name example.com;

    # 允许特定IP访问
    allow *************;
    allow ***********/24;  # 允许整个网段

    # 拒绝其他所有IP
    deny all;

    # 也可以针对特定目录设置
    location /admin/ {
        allow *************;  # 只允许管理员IP
        deny all;
    }
}</code></pre>
                    </div>

                    <h4><i class="fas fa-key"></i> 2. 基础认证</h4>
                    <div class="info-box">
                        <p>为敏感目录添加用户名密码验证：</p>
                        <pre><code># 1. 创建密码文件
sudo htpasswd -c /etc/nginx/.htpasswd admin
# 输入密码后会创建用户admin

# 2. 配置Nginx
server {
    listen 80;
    server_name example.com;

    location /admin/ {
        auth_basic "管理员区域";
        auth_basic_user_file /etc/nginx/.htpasswd;
    }

    location /api/private/ {
        auth_basic "私有API";
        auth_basic_user_file /etc/nginx/.htpasswd;
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-eye-slash"></i> 隐藏敏感信息</h3>
                    <div class="danger-box">
                        <h4><i class="fas fa-mask"></i> 隐藏Nginx版本和服务器信息</h4>
                        <pre><code>http {
    # 隐藏Nginx版本号
    server_tokens off;

    # 自定义Server头部（可选）
    more_set_headers "Server: MyWebServer";
}

server {
    # 隐藏PHP版本信息
    location ~ \.php$ {
        fastcgi_hide_header X-Powered-By;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 禁止访问备份文件
    location ~ ~$ {
        deny all;
    }

    # 禁止访问配置文件
    location ~* \.(conf|config|ini|sql)$ {
        deny all;
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-shield-virus"></i> 防护攻击</h3>

                    <h4><i class="fas fa-tachometer-alt"></i> 1. 限制请求频率</h4>
                    <div class="warning-box">
                        <p>防止DDoS攻击和恶意爬虫：</p>
                        <pre><code>http {
    # 定义限制区域
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

    server {
        # 登录页面限制（每秒1次请求）
        location /login {
            limit_req zone=login burst=3 nodelay;
        }

        # API接口限制（每秒10次请求）
        location /api/ {
            limit_req zone=api burst=20 nodelay;
        }

        # 限制并发连接数
        location /download/ {
            limit_conn_zone $binary_remote_addr zone=downloads:10m;
            limit_conn downloads 1;  # 每个IP只能1个并发下载
        }
    }
}</code></pre>
                    </div>

                    <h4><i class="fas fa-bug"></i> 2. 防止常见攻击</h4>
                    <div class="danger-box">
                        <pre><code>server {
    # 防止SQL注入和XSS攻击
    location / {
        # 检查恶意请求
        if ($args ~* "union.*select|insert.*into|delete.*from") {
            return 403;
        }

        # 防止XSS
        if ($args ~* "<script|javascript:|vbscript:") {
            return 403;
        }
    }

    # 限制请求方法
    if ($request_method !~ ^(GET|HEAD|POST)$ ) {
        return 405;
    }

    # 防止目录遍历
    location ~ \.\./  {
        deny all;
    }

    # 限制文件上传大小
    client_max_body_size 10M;

    # 防止缓冲区溢出
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
}</code></pre >
                    </div >

                    <h3><i class="fas fa-lock"></i> 安全头部</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-helmet-safety"></i> 添加安全响应头</h4>
                        <pre><code>server {
    # 防止点击劫持
    add_header X-Frame-Options "SAMEORIGIN" always;

    # 防止MIME类型嗅探
    add_header X-Content-Type-Options "nosniff" always;

    # XSS保护
    add_header X-XSS-Protection "1; mode=block" always;

    # 强制HTTPS（如果使用SSL）
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # 内容安全策略
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'" always;

    # 引用策略
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 权限策略
    add_header Permissions-Policy "camera=(), microphone=(), geolocation=()" always;
}</code></pre>
                    </div>

                    <div class="info-box">
                        <h4><i class="fas fa-lightbulb"></i> 安全配置最佳实践</h4>
                        <ul>
                            <li><strong>定期更新：</strong>保持Nginx和系统版本最新</li>
                            <li><strong>最小权限：</strong>Nginx进程使用非特权用户运行</li>
                            <li><strong>日志监控：</strong>定期检查访问日志，发现异常</li>
                            <li><strong>备份配置：</strong>定期备份配置文件</li>
                            <li><strong>测试验证：</strong>配置修改后要充分测试</li>
                        </ul>
                    </div>
                </section >

                <section id="monitoring">
                    <h2><span class="step-number">11</span>监控与日志</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-chart-line"></i> 为什么要监控？</h3>
                        <p>监控就像给网站装上"健康检查仪"：</p>
                        <ul>
                            <li><i class="fas fa-heartbeat"></i> <strong>实时了解状态</strong> - 知道网站是否正常运行</li>
                            <li><i class="fas fa-exclamation-triangle"></i> <strong>及时发现问题</strong> - 在用户发现之前解决问题</li>
                            <li><i class="fas fa-chart-bar"></i> <strong>性能分析</strong> - 了解访问量、响应时间等</li>
                            <li><i class="fas fa-search"></i> <strong>故障排查</strong> - 通过日志快速定位问题</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-file-alt"></i> 日志配置</h3>

                    <h4><i class="fas fa-eye"></i> 1. 访问日志</h4>
                    <div class="info-box">
                        <p>记录所有访问请求的详细信息：</p>
                        <pre><code>http {
    # 定义日志格式
                    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

                    # 详细日志格式
                    log_format detailed '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '$request_time $upstream_response_time';

                    # 全局访问日志
                    access_log /var/log/nginx/access.log main;

                    server {
        # 特定网站的日志
                    access_log /var/log/nginx/example.com.access.log detailed;

                    # 静态文件不记录日志（减少日志量）
                    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
                        access_log off;
        }

                    # API接口使用详细日志
                    location /api/ {
                        access_log /var/log/nginx/api.access.log detailed;
        }
    }
}</code></pre>
                    </div>

                    <h4><i class="fas fa-exclamation-circle"></i> 2. 错误日志</h4>
                    <div class="danger-box">
                        <p>记录服务器错误和异常情况：</p>
                        <pre><code>http {
    # 全局错误日志
    error_log /var/log/nginx/error.log warn;

    server {
        # 特定网站的错误日志
        error_log /var/log/nginx/example.com.error.log;

        # 不同级别的日志
        # debug, info, notice, warn, error, crit, alert, emerg
        error_log /var/log/nginx/debug.log debug;
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-chart-bar"></i> 日志分析</h3>

                    <h4><i class="fas fa-search"></i> 1. 常用日志分析命令</h4>
                    <div class="info-box">
                        <p>使用命令行工具分析日志：</p>
                        <pre><code># 查看访问量最高的IP
awk '{print $1}' /var/log/nginx/access.log | sort | uniq -c | sort -nr | head -10

# 查看最受欢迎的页面
awk '{print $7}' /var/log/nginx/access.log | sort | uniq -c | sort -nr | head -10

# 查看状态码分布
awk '{print $9}' /var/log/nginx/access.log | sort | uniq -c | sort -nr

# 查看错误请求（4xx, 5xx）
awk '$9 >= 400 {print $0}' /var/log/nginx/access.log

# 查看响应时间较长的请求
awk '$NF > 1.0 {print $0}' /var/log/nginx/access.log

# 实时监控访问日志
tail -f /var/log/nginx/access.log</code></pre>
                    </div>

                    <h4><i class="fas fa-tools"></i> 2. 日志轮转</h4>
                    <div class="warning-box">
                        <p>防止日志文件过大，定期轮转：</p>
                        <pre><code># 创建logrotate配置文件
sudo nano /etc/logrotate.d/nginx

# 配置内容：
/var/log/nginx/*.log {
    daily                    # 每天轮转
    missingok               # 文件不存在不报错
    rotate 52               # 保留52个文件（一年）
    compress                # 压缩旧文件
    delaycompress          # 延迟压缩
    notifempty             # 空文件不轮转
    create 644 nginx nginx  # 创建新文件的权限
    postrotate
        if [ -f /var/run/nginx.pid ]; then
            kill -USR1 `cat /var/run/nginx.pid`
        fi
    endscript
}</code></pre>
                    </div>

                    <h3><i class="fas fa-heartbeat"></i> 状态监控</h3>

                    <h4><i class="fas fa-info-circle"></i> 1. Nginx状态模块</h4>
                    <div class="success-box">
                        <p>启用状态页面监控Nginx运行状态：</p>
                        <pre><code>server {
    listen 80;
    server_name localhost;

    # 状态页面（仅允许本地访问）
    location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        allow ***********/24;  # 允许内网访问
        deny all;
    }

    # 查看状态信息
    location /server-info {
        return 200 "Server: $hostname\nTime: $time_local\nUptime: $upstream_response_time\n";
        add_header Content-Type text/plain;
    }
}</code></pre>
                        <p>访问 <code>http://localhost/nginx_status</code> 可以看到：</p>
                        <ul>
                            <li><strong>Active connections:</strong> 当前活跃连接数</li>
                            <li><strong>Accepts:</strong> 总接受连接数</li>
                            <li><strong>Handled:</strong> 总处理连接数</li>
                            <li><strong>Requests:</strong> 总请求数</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-bell"></i> 2. 简单监控脚本</h4>
                    <div class="info-box">
                        <p>创建简单的监控脚本：</p>
                        <pre><code>#!/bin/bash
# nginx_monitor.sh

# 检查Nginx是否运行
if ! pgrep nginx > /dev/null; then
    echo "$(date): Nginx is not running!" | tee -a /var/log/nginx_monitor.log
    # 发送邮件或短信通知
    # systemctl start nginx  # 自动重启（谨慎使用）
fi

# 检查磁盘空间
DISK_USAGE=$(df /var/log | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): Disk usage is ${DISK_USAGE}%" | tee -a /var/log/nginx_monitor.log
fi

# 检查错误日志中的异常
ERROR_COUNT=$(tail -100 /var/log/nginx/error.log | grep -c "error")
if [ $ERROR_COUNT -gt 10 ]; then
    echo "$(date): Too many errors: $ERROR_COUNT" | tee -a /var/log/nginx_monitor.log
fi</code></pre>
                        <p>设置定时任务：</p>
                        <pre><code># 编辑crontab
crontab -e

# 每5分钟检查一次
*/5 * * * * /path/to/nginx_monitor.sh</code></pre>
                    </div>

                    <div class="beginner-box">
                        <h4><i class="fas fa-lightbulb"></i> 监控工具推荐</h4>
                        <p>对于生产环境，建议使用专业监控工具：</p>
                        <ul>
                            <li><strong>Prometheus + Grafana:</strong> 开源监控解决方案</li>
                            <li><strong>ELK Stack:</strong> 日志收集和分析</li>
                            <li><strong>Zabbix:</strong> 企业级监控系统</li>
                            <li><strong>Nagios:</strong> 传统监控工具</li>
                            <li><strong>云监控:</strong> 阿里云、腾讯云等提供的监控服务</li>
                        </ul>
                    </div>
                </section>

                <section id="troubleshooting">
                    <h2><span class="step-number">12</span>故障排除</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-tools"></i> 故障排除思路</h3>
                        <p>遇到问题不要慌，按步骤排查：</p>
                        <ol>
                            <li><i class="fas fa-search"></i> <strong>确认现象</strong> - 具体是什么问题？</li>
                            <li><i class="fas fa-file-alt"></i> <strong>查看日志</strong> - 错误日志里有什么信息？</li>
                            <li><i class="fas fa-cog"></i> <strong>检查配置</strong> - 配置文件是否正确？</li>
                            <li><i class="fas fa-heartbeat"></i> <strong>检查服务</strong> - Nginx是否正常运行？</li>
                            <li><i class="fas fa-network-wired"></i> <strong>检查网络</strong> - 端口是否开放？</li>
                        </ol>
                    </div>

                    <h3><i class="fas fa-exclamation-triangle"></i> 常见问题及解决方案</h3>

                    <h4><i class="fas fa-times-circle"></i> 1. Nginx无法启动</h4>
                    <div class="danger-box">
                        <p><strong>现象：</strong>执行启动命令后Nginx没有运行</p>
                        <p><strong>排查步骤：</strong></p>
                        <pre><code># 1. 检查配置文件语法
sudo nginx -t

# 2. 查看错误日志
sudo tail -f /var/log/nginx/error.log

# 3. 检查端口占用
sudo netstat -tlnp | grep :80
sudo lsof -i :80

# 4. 检查权限
ls -la /etc/nginx/nginx.conf
ps aux | grep nginx

# 5. 手动启动查看错误
sudo nginx -g "daemon off;"</code></pre>
                        <p><strong>常见原因：</strong></p>
                        <ul>
                            <li>配置文件语法错误</li>
                            <li>端口被其他程序占用</li>
                            <li>权限不足</li>
                            <li>SSL证书文件不存在或权限问题</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-ban"></i> 2. 403 Forbidden错误</h4>
                    <div class="warning-box">
                        <p><strong>现象：</strong>访问网站显示403错误</p>
                        <p><strong>解决方案：</strong></p>
                        <pre><code># 1. 检查文件权限
ls -la /var/www/html/
sudo chmod 644 /var/www/html/index.html
sudo chmod 755 /var/www/html/

# 2. 检查Nginx用户权限
ps aux | grep nginx
sudo chown -R nginx:nginx /var/www/html/

# 3. 检查目录索引配置
location / {
    root /var/www/html;
    index index.html index.htm;
    autoindex on;  # 如果需要目录浏览
}

# 4. 检查SELinux（CentOS/RHEL）
getenforce
sudo setsebool -P httpd_can_network_connect 1</code></pre>
                    </div>

                    <h4><i class="fas fa-clock"></i> 3. 502 Bad Gateway错误</h4>
                    <div class="danger-box">
                        <p><strong>现象：</strong>反向代理时出现502错误</p>
                        <p><strong>排查步骤：</strong></p>
                        <pre><code># 1. 检查后端服务是否运行
curl http://127.0.0.1:3000/
netstat -tlnp | grep :3000

# 2. 检查代理配置
location / {
    proxy_pass http://127.0.0.1:3000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}

# 3. 增加超时时间
proxy_connect_timeout 60s;
proxy_send_timeout 60s;
proxy_read_timeout 60s;

# 4. 检查防火墙
sudo iptables -L
sudo firewall-cmd --list-all</code></pre>
                    </div>

                    <h4><i class="fas fa-server"></i> 4. 504 Gateway Timeout错误</h4>
                    <div class="info-box">
                        <p><strong>现象：</strong>请求超时</p>
                        <p><strong>解决方案：</strong></p>
                        <pre><code># 增加超时配置
location / {
    proxy_pass http://backend;
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;

    # 增加缓冲区
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
}</code></pre>
                    </div>

                    <h3><i class="fas fa-tools"></i> 调试技巧</h3>

                    <h4><i class="fas fa-bug"></i> 1. 启用调试日志</h4>
                    <div class="info-box">
                        <pre><code># 临时启用调试日志
error_log /var/log/nginx/debug.log debug;

# 只对特定请求启用调试
location /api/ {
    error_log /var/log/nginx/api_debug.log debug;
}

# 查看调试日志
sudo tail -f /var/log/nginx/debug.log</code></pre>
                    </div>

                    <h4><i class="fas fa-search"></i> 2. 使用curl测试</h4>
                    <div class="success-box">
                        <pre><code># 测试基本连接
curl -I http://localhost/

# 测试HTTPS
curl -I https://localhost/

# 查看详细信息
curl -v http://localhost/

# 测试特定头部
curl -H "Host: example.com" http://localhost/

# 测试POST请求
curl -X POST -d "data=test" http://localhost/api/</code></pre>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-graduation-cap"></i> 故障排除最佳实践</h4>
                        <ul>
                            <li><strong>保持冷静：</strong>不要急于修改配置，先分析问题</li>
                            <li><strong>备份配置：</strong>修改前先备份工作的配置</li>
                            <li><strong>逐步测试：</strong>一次只改一个配置项</li>
                            <li><strong>记录过程：</strong>记录问题现象和解决步骤</li>
                            <li><strong>学习积累：</strong>总结常见问题的解决方案</li>
                        </ul>
                    </div>
                </section>

                <div class="success-box">
                    <h3><i class="fas fa-graduation-cap"></i> 恭喜你完成了Nginx完整入门学习！</h3>
                    <p>通过这个详细的教程，你已经掌握了Nginx从基础到进阶的核心知识。现在你可以：</p>
                    <ul>
                        <li><i class="fas fa-check"></i> 独立安装和配置Nginx</li>
                        <li><i class="fas fa-check"></i> 搭建静态网站和反向代理</li>
                        <li><i class="fas fa-check"></i> 配置负载均衡和SSL证书</li>
                        <li><i class="fas fa-check"></i> 进行安全加固和性能优化</li>
                        <li><i class="fas fa-check"></i> 监控运行状态和排查故障</li>
                    </ul>

                    <h4><i class="fas fa-rocket"></i> 进阶学习建议</h4>
                    <div class="path-steps">
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-code"></i></div>
                            <div class="step-content">
                                <h5>模块开发</h5>
                                <p>学习编写自定义Nginx模块，扩展功能</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-cloud"></i></div>
                            <div class="step-content">
                                <h5>容器化部署</h5>
                                <p>学习Docker和Kubernetes中的Nginx应用</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-chart-line"></i></div>
                            <div class="step-content">
                                <h5>高级运维</h5>
                                <p>深入学习监控、日志分析和自动化运维</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-shield-alt"></i></div>
                            <div class="step-content">
                                <h5>安全专家</h5>
                                <p>学习WAF、DDoS防护等高级安全技术</p>
                            </div>
                        </div>
                    </div>

                    <h4><i class="fas fa-book"></i> 推荐学习资源</h4>
                    <ul>
                        <li><strong>官方文档：</strong><a href="http://nginx.org/en/docs/"
                                target="_blank">http://nginx.org/en/docs/</a></li>
                        <li><strong>Nginx Plus：</strong>商业版本的高级功能</li>
                        <li><strong>OpenResty：</strong>基于Nginx的Web平台</li>
                        <li><strong>社区论坛：</strong>参与技术讨论和问题解答</li>
                        <li><strong>实战项目：</strong>在真实项目中应用所学知识</li>
                    </ul>

                    <p><strong>记住：</strong>实践是最好的老师！多动手配置，多解决实际问题，你会越来越熟练。祝你在Nginx的学习道路上越走越远！</p>
                </div>

            </div >
        </div >
    </div >

    <!-- 返回顶部按钮 -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // 移动端菜单切换
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const sidebar = document.getElementById('sidebar');

        mobileMenuBtn.addEventListener('click', () => {
            sidebar.classList.toggle('active');
        });

        // 返回顶部功能
        const backToTop = document.getElementById('backToTop');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'flex';
            } else {
                backToTop.style.display = 'none';
            }
        });

        backToTop.addEventListener('click', (e) => {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 侧边栏导航高亮
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.sidebar a[href^="#"]');

        window.addEventListener('scroll', () => {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // 平滑滚动
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                const targetSection = document.querySelector(targetId);
                if (targetSection) {
                    targetSection.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
                // 移动端关闭菜单
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                }
            });
        });
</script>

</body>

</html>