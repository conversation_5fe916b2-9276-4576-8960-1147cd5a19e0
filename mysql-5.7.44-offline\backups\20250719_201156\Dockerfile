# MySQL 5.7.44 离线优化版 - 体积小且稳定
FROM centos:centos7.9.2009 AS builder

# 复制离线依赖包和MySQL安装包
COPY rpm-packages/ /tmp/rpm-packages/
COPY mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz /usr/local/
COPY my.cnf /etc/

# 只安装MySQL运行必需的核心RPM包，移除开发包和非必需包
RUN cd /tmp/rpm-packages && \
    # 只安装核心运行时包，跳过开发包和文档包
    rpm -ivh --force --nodeps \
    libaio-0.3.109-13.el7.x86_64.rpm \
    numactl-libs-2.0.12-5.el7.x86_64.rpm \
    ncurses-libs-5.9-14.20130511.el7_4.i686.rpm \
    psmisc-22.20-17.el7.x86_64.rpm \
    tar-1.26-35.el7.x86_64.rpm \
    libgcc-4.8.5-44.el7.x86_64.rpm \
    libstdc++-4.8.5-44.el7.x86_64.rpm \
    glibc-2.17-326.el7_9.3.x86_64.rpm \
    && \
    cd /usr/local/ && \
    tar -zxf mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz && \
    mv mysql-5.7.44-linux-glibc2.12-x86_64 mysql && \
    rm -f /usr/local/mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz && \
    rm -rf /tmp/rpm-packages && \
    # 彻底清理缓存和临时文件
    rm -rf /var/cache/yum/* && \
    rm -rf /tmp/* && \
    rm -rf /var/tmp/* && \
    rm -rf /usr/share/doc/* && \
    rm -rf /usr/share/man/* && \
    rm -rf /usr/share/info/* && \
    rm -rf /usr/share/locale/* && \
    rm -rf /usr/share/i18n/* && \
    rm -rf /var/log/* && \
    # 清理MySQL中不必要的文件
    rm -rf /usr/local/mysql/mysql-test && \
    rm -rf /usr/local/mysql/docs && \
    rm -rf /usr/local/mysql/man && \
    rm -rf /usr/local/mysql/share/mysql/charsets/README

# 第二阶段：最终运行镜像
FROM centos:centos7.9.2009

# 只复制必要的文件和库
COPY --from=builder /usr/local/mysql /usr/local/mysql
COPY --from=builder /etc/my.cnf /etc/my.cnf
COPY --from=builder /usr/lib64/libaio.so.* /usr/lib64/
COPY --from=builder /usr/lib64/libnuma.so.* /usr/lib64/
COPY --from=builder /usr/lib64/libncurses.so.* /usr/lib64/
COPY --from=builder /usr/lib64/libtinfo.so.* /usr/lib64/
COPY --from=builder /usr/lib64/libgcc_s.so.* /usr/lib64/
COPY --from=builder /usr/lib64/libstdc++.so.* /usr/lib64/
COPY --from=builder /usr/bin/pkill /usr/bin/pkill
COPY --from=builder /usr/bin/killall /usr/bin/killall

# 复制启动脚本和健康检查脚本
COPY mysql-entrypoint.sh /usr/local/bin/
COPY healthcheck.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/mysql-entrypoint.sh && \
    chmod +x /usr/local/bin/healthcheck.sh

# 设置环境变量
ENV PATH="/usr/local/mysql/bin:${PATH}"
WORKDIR /usr/local/mysql

# 暴露端口
EXPOSE 3306

# 添加健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

# 使用启动脚本作为入口点
ENTRYPOINT ["/usr/local/bin/mysql-entrypoint.sh"]