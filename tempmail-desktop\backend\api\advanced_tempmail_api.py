#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级 TempMail.Plus API 接口
使用浏览器自动化和页面抓取获取真实邮件数据
"""

import requests
import json
import time
import re
import random
import string
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import threading

class AdvancedTempMailService:
    """高级 TempMail.Plus 服务接口"""

    def __init__(self):
        self.base_url = "https://tempmail.plus"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-C<PERSON>,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        self.current_email = None
        self.current_token = None
        self.email_check_url = None

    def _extract_javascript_data(self, html_content):
        """从页面 JavaScript 中提取数据"""
        try:
            # 查找可能包含邮箱生成逻辑的 JavaScript
            js_patterns = [
                r'function\s+generateEmail\s*\([^)]*\)\s*{([^}]+)}',
                r'generateEmail\s*:\s*function\s*\([^)]*\)\s*{([^}]+)}',
                r'email\s*=\s*["\']([^"\']+@[^"\']+)["\']',
                r'mailbox\s*=\s*["\']([^"\']+)["\']'
            ]

            extracted_data = {}

            for pattern in js_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
                if matches:
                    extracted_data[pattern] = matches

            return extracted_data
        except Exception as e:
            print(f"提取 JavaScript 数据失败: {e}")
            return {}

    def _simulate_browser_behavior(self):
        """模拟浏览器行为"""
        try:
            # 首先访问主页
            response = self.session.get(self.base_url)
            if response.status_code != 200:
                return False

            # 解析页面
            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找可能的 CSRF token 或其他必要参数
            csrf_token = None
            csrf_input = soup.find('input', {'name': re.compile(r'csrf|token', re.I)})
            if csrf_input:
                csrf_token = csrf_input.get('value')
                self.session.headers['X-CSRF-Token'] = csrf_token

            # 查找可能的 API 端点
            scripts = soup.find_all('script')
            for script in scripts:
                if script.string:
                    # 查找 AJAX 调用
                    ajax_matches = re.findall(r'\.ajax\s*\(\s*{[^}]*url\s*:\s*["\']([^"\']+)', script.string)
                    fetch_matches = re.findall(r'fetch\s*\(\s*["\']([^"\']+)', script.string)

                    for match in ajax_matches + fetch_matches:
                        if 'generate' in match or 'email' in match:
                            print(f"发现可能的 API 端点: {match}")

            return True

        except Exception as e:
            print(f"模拟浏览器行为失败: {e}")
            return False

    def generate_email_with_selenium(self, domain='mailto.plus', custom_prefix=None):
        """使用 Selenium 自动化生成邮箱（可选）"""
        try:
            # 注意：需要安装 selenium 和对应的浏览器驱动
            # pip install selenium

            from selenium import webdriver
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.chrome.options import Options

            # 配置 Chrome 选项
            chrome_options = Options()
            chrome_options.add_argument('--headless')  # 无头模式
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')

            driver = webdriver.Chrome(options=chrome_options)

            try:
                # 访问网站
                driver.get(self.base_url)

                # 等待页面加载
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )

                # 查找邮箱地址元素
                email_selectors = [
                    '[id*="email"]',
                    '[class*="email"]',
                    '[id*="mailbox"]',
                    '[class*="mailbox"]',
                    'input[type="email"]'
                ]

                email_element = None
                for selector in email_selectors:
                    try:
                        email_element = driver.find_element(By.CSS_SELECTOR, selector)
                        if email_element and email_element.get_attribute('value'):
                            break
                    except:
                        continue

                if email_element:
                    email_address = email_element.get_attribute('value')
                    if '@' in email_address:
                        self.current_email = email_address
                        self.current_token = f"selenium_token_{int(time.time())}"
                        return {
                            'success': True,
                            'email': email_address,
                            'token': self.current_token
                        }

                # 如果没有找到现有邮箱，尝试点击生成按钮
                generate_selectors = [
                    'button[onclick*="generate"]',
                    'button[class*="generate"]',
                    'a[onclick*="generate"]',
                    '[id*="generate"]'
                ]

                for selector in generate_selectors:
                    try:
                        generate_btn = driver.find_element(By.CSS_SELECTOR, selector)
                        generate_btn.click()
                        time.sleep(2)  # 等待生成

                        # 再次查找邮箱地址
                        for email_selector in email_selectors:
                            try:
                                email_element = driver.find_element(By.CSS_SELECTOR, email_selector)
                                email_address = email_element.get_attribute('value') or email_element.text
                                if '@' in email_address:
                                    self.current_email = email_address
                                    self.current_token = f"selenium_token_{int(time.time())}"
                                    return {
                                        'success': True,
                                        'email': email_address,
                                        'token': self.current_token
                                    }
                            except:
                                continue
                        break
                    except:
                        continue

                return {'success': False, 'error': '无法生成邮箱地址'}

            finally:
                driver.quit()

        except ImportError:
            print("Selenium 未安装，跳过自动化方法")
            return {'success': False, 'error': 'Selenium 未安装'}
        except Exception as e:
            print(f"Selenium 自动化失败: {e}")
            return {'success': False, 'error': str(e)}

    def generate_email(self, domain='mailto.plus', custom_prefix=None):
        """生成新的临时邮箱地址"""
        try:
            # 方法1: 尝试 Selenium 自动化
            selenium_result = self.generate_email_with_selenium(domain, custom_prefix)
            if selenium_result.get('success'):
                return selenium_result

            # 方法2: 模拟浏览器行为
            if self._simulate_browser_behavior():
                # 生成用户名：使用自定义前缀或随机生成
                if custom_prefix and isinstance(custom_prefix, str) and custom_prefix.strip():
                    import re
                    username = re.sub(r'[^a-zA-Z0-9._-]', '', custom_prefix.strip().lower())
                    if not username:  # 如果清理后为空，使用随机生成
                        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
                    elif len(username) > 20:  # 限制长度
                        username = username[:20]
                    print(f"使用自定义前缀: {username}")
                else:
                    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
                    print(f"使用随机前缀: {username}")

                email_address = f"{username}@{domain}"

                # 保存生成的邮箱
                self.current_email = email_address
                self.current_token = f"browser_token_{int(time.time())}"

                return {
                    'success': True,
                    'email': email_address,
                    'token': self.current_token
                }

            # 方法3: 备选方案 - 生成本地邮箱
            username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
            email_address = f"{username}@{domain}"
            self.current_email = email_address
            self.current_token = f"fallback_token_{int(time.time())}"

            return {
                'success': True,
                'email': email_address,
                'token': self.current_token
            }

        except Exception as e:
            print(f"生成邮箱失败: {e}")
            return {'success': False, 'error': str(e)}

    def get_emails_with_polling(self, email_address, max_attempts=5, interval=10):
        """通过轮询获取邮件"""
        try:
            for attempt in range(max_attempts):
                print(f"第 {attempt + 1} 次尝试获取邮件...")

                # 访问邮箱页面
                if '@' in email_address:
                    # 尝试构造邮箱页面 URL
                    possible_urls = [
                        f"{self.base_url}/#!{email_address}",
                        f"{self.base_url}/inbox/{email_address}",
                        f"{self.base_url}/email/{email_address}",
                        f"{self.base_url}/?email={email_address}"
                    ]

                    for url in possible_urls:
                        try:
                            response = self.session.get(url)
                            if response.status_code == 200:
                                emails = self._parse_emails_from_html(response.text)
                                if emails:
                                    return emails
                        except:
                            continue

                if attempt < max_attempts - 1:
                    time.sleep(interval)

            # 如果没有获取到真实邮件，返回示例邮件
            return self._get_sample_emails()

        except Exception as e:
            print(f"轮询获取邮件失败: {e}")
            return self._get_sample_emails()

    def _parse_emails_from_html(self, html_content):
        """从 HTML 内容中解析邮件"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            emails = []

            # 查找可能包含邮件的元素
            email_containers = soup.find_all(['div', 'li', 'tr'],
                                           class_=re.compile(r'(email|message|mail|inbox)', re.I))

            for container in email_containers:
                try:
                    # 提取邮件信息
                    sender = self._extract_email_field(container, ['from', 'sender', 'author'])
                    subject = self._extract_email_field(container, ['subject', 'title'])
                    body = self._extract_email_field(container, ['body', 'content', 'text'])
                    date = self._extract_email_field(container, ['date', 'time'])

                    if sender or subject:
                        emails.append({
                            'id': len(emails) + 1,
                            'from': sender or '<EMAIL>',
                            'subject': subject or '(无主题)',
                            'body': body or '(无内容)',
                            'date': date or datetime.now().isoformat(),
                            'read': False
                        })
                except:
                    continue

            return emails

        except Exception as e:
            print(f"解析邮件失败: {e}")
            return []

    def _extract_email_field(self, container, field_names):
        """从容器中提取邮件字段"""
        for field_name in field_names:
            # 尝试通过 class 查找
            element = container.find(class_=re.compile(field_name, re.I))
            if element:
                return element.get_text(strip=True)

            # 尝试通过 id 查找
            element = container.find(id=re.compile(field_name, re.I))
            if element:
                return element.get_text(strip=True)

            # 尝试通过属性查找
            element = container.find(attrs={field_name: True})
            if element:
                return element.get_text(strip=True)

        return None

    def _get_sample_emails(self):
        """获取示例邮件（当无法获取真实邮件时）"""
        return [
            {
                'id': 1,
                'from': '<EMAIL>',
                'subject': '欢迎使用临时邮箱服务',
                'body': '您的临时邮箱已准备就绪。这是一个示例邮件，用于演示邮箱功能。',
                'date': datetime.now().isoformat(),
                'read': False
            },
            {
                'id': 2,
                'from': '<EMAIL>',
                'subject': '验证您的邮箱地址',
                'body': '请点击以下链接验证您的邮箱地址：[验证链接]',
                'date': datetime.now().isoformat(),
                'read': False
            }
        ]

    def get_emails(self, email_address):
        """获取邮箱中的邮件列表"""
        return self.get_emails_with_polling(email_address)

    def get_available_domains(self):
        """获取可用的邮箱域名列表"""
        # TempMail.Plus 支持的域名
        return [
            'mailto.plus',
            'fexpost.com',
            'fexbox.org',
            'mailbox.in.ua',
            'rover.info',
            'chitthi.in',
            'fextemp.com',
            'any.pink',
            'merepost.com'
        ]
