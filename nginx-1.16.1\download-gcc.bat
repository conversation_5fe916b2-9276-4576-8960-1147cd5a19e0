@echo off
echo ================================================================================
echo                     下载完整的CentOS 7 GCC工具链
echo ================================================================================

mkdir centos7-gcc-complete 2>nul

set BASE_URL=https://mirrors.aliyun.com/centos/7.9.2009/os/x86_64/Packages

echo 开始下载GCC工具链包...

curl -L -o centos7-gcc-complete/gcc-4.8.5-44.el7.x86_64.rpm %BASE_URL%/gcc-4.8.5-44.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/gcc-c++-4.8.5-44.el7.x86_64.rpm %BASE_URL%/gcc-c++-4.8.5-44.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/libmpc-1.0.1-3.el7.x86_64.rpm %BASE_URL%/libmpc-1.0.1-3.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/mpfr-3.1.1-4.el7.x86_64.rpm %BASE_URL%/mpfr-3.1.1-4.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/gmp-6.0.0-15.el7.x86_64.rpm %BASE_URL%/gmp-6.0.0-15.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/libstdc++-4.8.5-44.el7.x86_64.rpm %BASE_URL%/libstdc++-4.8.5-44.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/libstdc++-devel-4.8.5-44.el7.x86_64.rpm %BASE_URL%/libstdc++-devel-4.8.5-44.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/libgcc-4.8.5-44.el7.x86_64.rpm %BASE_URL%/libgcc-4.8.5-44.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/glibc-devel-2.17-326.el7_9.x86_64.rpm %BASE_URL%/glibc-devel-2.17-326.el7_9.x86_64.rpm
curl -L -o centos7-gcc-complete/glibc-headers-2.17-326.el7_9.x86_64.rpm %BASE_URL%/glibc-headers-2.17-326.el7_9.x86_64.rpm
curl -L -o centos7-gcc-complete/kernel-headers-3.10.0-1160.102.1.el7.x86_64.rpm %BASE_URL%/kernel-headers-3.10.0-1160.102.1.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/make-3.82-24.el7.x86_64.rpm %BASE_URL%/make-3.82-24.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/binutils-2.27-44.base.el7.x86_64.rpm %BASE_URL%/binutils-2.27-44.base.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/openssl-1.0.2k-25.el7_9.x86_64.rpm %BASE_URL%/openssl-1.0.2k-25.el7_9.x86_64.rpm
curl -L -o centos7-gcc-complete/openssl-devel-1.0.2k-25.el7_9.x86_64.rpm %BASE_URL%/openssl-devel-1.0.2k-25.el7_9.x86_64.rpm
curl -L -o centos7-gcc-complete/openssl-libs-1.0.2k-25.el7_9.x86_64.rpm %BASE_URL%/openssl-libs-1.0.2k-25.el7_9.x86_64.rpm
curl -L -o centos7-gcc-complete/krb5-devel-1.15.1-55.el7_9.x86_64.rpm %BASE_URL%/krb5-devel-1.15.1-55.el7_9.x86_64.rpm
curl -L -o centos7-gcc-complete/keyutils-libs-devel-1.5.8-3.el7.x86_64.rpm %BASE_URL%/keyutils-libs-devel-1.5.8-3.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/libcom_err-devel-1.42.9-19.el7.x86_64.rpm %BASE_URL%/libcom_err-devel-1.42.9-19.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/libselinux-devel-2.5-15.el7.x86_64.rpm %BASE_URL%/libselinux-devel-2.5-15.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/libsepol-devel-2.5-10.el7.x86_64.rpm %BASE_URL%/libsepol-devel-2.5-10.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/libverto-devel-0.2.5-4.el7.x86_64.rpm %BASE_URL%/libverto-devel-0.2.5-4.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/pcre-8.32-17.el7.x86_64.rpm %BASE_URL%/pcre-8.32-17.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/pcre-devel-8.32-17.el7.x86_64.rpm %BASE_URL%/pcre-devel-8.32-17.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/zlib-1.2.7-18.el7.x86_64.rpm %BASE_URL%/zlib-1.2.7-18.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/zlib-devel-1.2.7-18.el7.x86_64.rpm %BASE_URL%/zlib-devel-1.2.7-18.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/GeoIP-1.5.0-14.el7.x86_64.rpm %BASE_URL%/GeoIP-1.5.0-14.el7.x86_64.rpm
curl -L -o centos7-gcc-complete/GeoIP-devel-1.5.0-14.el7.x86_64.rpm %BASE_URL%/GeoIP-devel-1.5.0-14.el7.x86_64.rpm

echo.
echo ================================================================================
echo                           下载完成
echo ================================================================================
echo 下载的包保存在: centos7-gcc-complete\
echo.
echo 接下来的步骤:
echo 1. 备份当前的centos7-rpms目录:
echo    ren centos7-rpms centos7-rpms-backup
echo.
echo 2. 将新下载的完整包复制到centos7-rpms目录:
echo    xcopy centos7-gcc-complete centos7-rpms\ /E /I
echo.
echo 3. 重新运行Docker构建:
echo    docker build -t nginx-offline-build -f Dockerfile .
echo ================================================================================
pause
