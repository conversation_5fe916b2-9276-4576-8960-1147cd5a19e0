#!/bin/bash

# PostgreSQL 11.19 离线RPM包安装脚本

set -e

echo "开始安装PostgreSQL 11.19编译所需的RPM包..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用root用户运行此脚本"
    exit 1
fi

# 安装所有RPM包
echo "安装RPM包..."
rpm -Uvh --force --nodeps *.rpm

echo "RPM包安装完成！"
echo "现在可以编译PostgreSQL 11.19了"

# 验证关键包是否安装成功
echo "验证关键依赖包..."
packages=(
    "gcc"
    "gcc-c++"
    "make"
    "readline-devel"
    "zlib-devel"
    "openssl-devel"
)

for pkg in "${packages[@]}"; do
    if rpm -q "$pkg" >/dev/null 2>&1; then
        echo "✓ $pkg 已安装"
    else
        echo "✗ $pkg 未安装"
    fi
done

echo "依赖检查完成！"
