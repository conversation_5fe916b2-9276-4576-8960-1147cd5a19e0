# PostgreSQL 11.19 Offline Complete Backup Script
# Version: 2.0
# Author: AI Assistant
# Date: 2025-07-12

param(
    [string]$BackupDir = ".\backups",
    [switch]$ForceBackup,
    [switch]$VerboseLog
)

# Global variables
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$BackupPath = Join-Path $BackupDir $Timestamp
$TempDir = Join-Path (Get-Location) "temp_backup_$Timestamp"
$LogFile = Join-Path $BackupPath "backup_log.txt"

# Logging function
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogContent = "[$Time] [$Level] $Message"

    switch ($Level) {
        "ERROR" { Write-Host $LogContent -ForegroundColor Red }
        "WARN"  { Write-Host $LogContent -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $LogContent -ForegroundColor Green }
        default { Write-Host $LogContent -ForegroundColor White }
    }

    if (Test-Path $BackupPath) {
        try {
            $LogContent | Out-File -FilePath $LogFile -Append -Encoding UTF8 -Force
        } catch {
            # If log writing fails, silently ignore
        }
    }
}

# Progress display function
function Show-Progress {
    param([string]$Activity, [string]$Status, [int]$PercentComplete)
    Write-Progress -Activity $Activity -Status $Status -PercentComplete $PercentComplete
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "PostgreSQL 11.19 Offline Complete Backup Script v2.0" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Log "Starting PostgreSQL complete backup"
Write-Log "Backup directory: $BackupPath"
Write-Log "Temporary directory: $TempDir"

# Create directories
try {
    New-Item -ItemType Directory -Path $BackupPath -Force | Out-Null
    New-Item -ItemType Directory -Path $TempDir -Force | Out-Null
    Write-Log "Backup directories created successfully" "SUCCESS"
} catch {
    Write-Log "Failed to create backup directories: $($_.Exception.Message)" "ERROR"
    exit 1
}

try {
    # Step 1: Check Docker environment
    Show-Progress "Environment Check" "Checking Docker environment..." 10
    Write-Log "Checking Docker environment..."
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker is not running or not installed"
    }
    Write-Log "Docker version: $dockerVersion" "SUCCESS"

    # Step 2: Check container status
    Show-Progress "Environment Check" "Checking PostgreSQL container status..." 20
    Write-Log "Checking PostgreSQL container status..."
    $ContainerStatus = docker inspect postgresql-11.19 --format='{{.State.Status}}' 2>$null
    if ($ContainerStatus -ne "running") {
        Write-Log "Warning: PostgreSQL container is not running, status: $ContainerStatus" "WARN"
        if (-not $ForceBackup) {
            throw "Container is not running, use -ForceBackup parameter to force backup"
        }
    } else {
        Write-Log "PostgreSQL container is running normally" "SUCCESS"
    }

    # Step 3: Backup configuration files
    Show-Progress "Backup Configuration" "Backing up configuration files..." 30
    Write-Log "Backing up configuration files..."
    $ConfigFiles = @("docker-compose.yml", "Dockerfile", "postgresql.conf", "pg_hba.conf", "docker-entrypoint.sh")
    $BackedUpFiles = 0
    foreach ($File in $ConfigFiles) {
        if (Test-Path $File) {
            Copy-Item $File -Destination $BackupPath -Force
            Write-Log "Backed up: $File" "SUCCESS"
            $BackedUpFiles++
        } else {
            Write-Log "File not found: $File" "WARN"
        }
    }
    
    # Backup initialization scripts directory
    if (Test-Path "init-scripts") {
        Copy-Item "init-scripts" -Destination $BackupPath -Recurse -Force
        Write-Log "Backed up: init-scripts directory" "SUCCESS"
        $BackedUpFiles++
    }
    
    Write-Log "Configuration backup completed, backed up $BackedUpFiles files/directories" "SUCCESS"

    # Step 4: Backup SQL data (if container is running)
    if ($ContainerStatus -eq "running") {
        Show-Progress "Backup Data" "Backing up database SQL..." 40
        Write-Log "Backing up database SQL data..."
        $SqlBackupFile = Join-Path $TempDir "postgresql_all_databases.sql"
        
        try {
            Write-Log "Executing pg_dumpall command..."
            $DumpCommand = "pg_dumpall -U postgres -h localhost -p 3433"

            $BackupOutput = docker exec postgresql-11.19 sh -c "PGPASSWORD=postgres $DumpCommand" 2>$null

            if ($LASTEXITCODE -eq 0 -and $BackupOutput -and $BackupOutput.Length -gt 0) {
                $FirstLine = ($BackupOutput | Select-Object -First 1).Trim()
                if ($FirstLine.StartsWith("--") -or $FirstLine.StartsWith("/*") -or $FirstLine.Contains("PostgreSQL")) {
                    $BackupOutput | Out-File -FilePath $SqlBackupFile -Encoding UTF8

                    # Compress SQL backup
                    $CompressedFile = Join-Path $BackupPath "postgresql_all_databases.sql.zip"
                    Compress-Archive -Path $SqlBackupFile -DestinationPath $CompressedFile -Force

                    $FileSize = [math]::Round((Get-Item $CompressedFile).Length / 1MB, 2)
                    Write-Log "SQL backup completed and compressed, size: ${FileSize}MB" "SUCCESS"
                } else {
                    Write-Log "SQL backup output validation failed - invalid format" "WARN"
                }
            } else {
                Write-Log "SQL backup failed or data is empty (exit code: $LASTEXITCODE)" "WARN"
            }
        } catch {
            Write-Log "SQL backup process error: $($_.Exception.Message)" "ERROR"
        }
    }

    # Step 5: Stop container to ensure data consistency
    if ($ContainerStatus -eq "running") {
        Show-Progress "Prepare Data Volumes" "Stopping container to ensure data consistency..." 50
        Write-Log "Stopping PostgreSQL container to ensure data consistency..."
        docker-compose stop postgresql 2>&1 | Out-Null
        Start-Sleep -Seconds 5
        Write-Log "Container stopped" "SUCCESS"
    }

    # Step 6: Backup data volumes
    Show-Progress "Backup Data Volumes" "Backing up PostgreSQL data volumes..." 60
    Write-Log "Backing up PostgreSQL data volumes..."
    
    # Backup main data volume
    Write-Log "Backing up main data volume: postgresql_11_19_data_offline"
    try {
        $DataBackupContainer = docker create -v postgresql_11_19_data_offline:/data alpine 2>&1
        if ($LASTEXITCODE -eq 0) {
            docker cp "${DataBackupContainer}:/data" "${TempDir}/postgres_data" 2>&1 | Out-Null
            docker rm $DataBackupContainer 2>&1 | Out-Null

            if (Test-Path "${TempDir}/postgres_data") {
                Compress-Archive -Path "${TempDir}/postgres_data" -DestinationPath "${TempDir}/postgres_data_volume.zip" -Force
                Write-Log "Main data volume backup completed" "SUCCESS"
            } else {
                Write-Log "Main data volume data copy failed" "ERROR"
            }
        } else {
            Write-Log "Failed to create backup container" "ERROR"
        }
    } catch {
        Write-Log "Main data volume backup exception: $($_.Exception.Message)" "ERROR"
    }
    
    # Backup log volume
    Show-Progress "Backup Data Volumes" "Backing up PostgreSQL log volume..." 70
    Write-Log "Backing up log volume: postgresql_11_19_logs_offline"
    try {
        $LogBackupContainer = docker create -v postgresql_11_19_logs_offline:/data alpine 2>&1
        if ($LASTEXITCODE -eq 0) {
            docker cp "${LogBackupContainer}:/data" "${TempDir}/postgres_logs" 2>&1 | Out-Null
            docker rm $LogBackupContainer 2>&1 | Out-Null

            if (Test-Path "${TempDir}/postgres_logs") {
                $LogFiles = Get-ChildItem "${TempDir}/postgres_logs" -Recurse -File
                if ($LogFiles.Count -gt 0) {
                    Compress-Archive -Path "${TempDir}/postgres_logs" -DestinationPath "${TempDir}/postgres_logs_volume.zip" -Force
                    Write-Log "Log volume backup completed, contains $($LogFiles.Count) files" "SUCCESS"
                } else {
                    New-Item -ItemType Directory -Path "${TempDir}/empty_logs" -Force | Out-Null
                    New-Item -ItemType File -Path "${TempDir}/empty_logs/README.txt" -Force | Out-Null
                    "This log volume was empty at backup time. PostgreSQL logs are configured to write to /var/log/postgresql directory." | Out-File -FilePath "${TempDir}/empty_logs/README.txt" -Encoding UTF8
                    Compress-Archive -Path "${TempDir}/empty_logs" -DestinationPath "${TempDir}/postgres_logs_volume.zip" -Force
                    Write-Log "Log volume backup completed (volume was empty)" "SUCCESS"
                    Remove-Item "${TempDir}/empty_logs" -Recurse -Force
                }
            } else {
                Write-Log "Log volume data copy failed" "WARN"
            }
        } else {
            Write-Log "Failed to create log backup container" "WARN"
        }
    } catch {
        Write-Log "Log volume backup exception: $($_.Exception.Message)" "WARN"
    }

    # Backup pgAdmin data volume (if exists)
    Show-Progress "Backup Data Volumes" "Backing up pgAdmin data volume..." 75
    Write-Log "Backing up pgAdmin data volume: pgadmin_data_offline"
    try {
        $PgAdminBackupContainer = docker create -v pgadmin_data_offline:/data alpine 2>&1
        if ($LASTEXITCODE -eq 0) {
            docker cp "${PgAdminBackupContainer}:/data" "${TempDir}/pgadmin_data" 2>&1 | Out-Null
            docker rm $PgAdminBackupContainer 2>&1 | Out-Null

            if (Test-Path "${TempDir}/pgadmin_data") {
                Compress-Archive -Path "${TempDir}/pgadmin_data" -DestinationPath "${TempDir}/pgadmin_data_volume.zip" -Force
                Write-Log "pgAdmin data volume backup completed" "SUCCESS"
            } else {
                Write-Log "pgAdmin data volume data copy failed" "WARN"
            }
        } else {
            Write-Log "pgAdmin data volume does not exist or failed to create container" "WARN"
        }
    } catch {
        Write-Log "pgAdmin data volume backup exception: $($_.Exception.Message)" "WARN"
    }

    # Move backup files to backup directory
    $DataFile = Join-Path $TempDir "postgres_data_volume.zip"
    $LogFileCompressed = Join-Path $TempDir "postgres_logs_volume.zip"
    $PgAdminFile = Join-Path $TempDir "pgadmin_data_volume.zip"

    foreach ($File in @($DataFile, $LogFileCompressed, $PgAdminFile)) {
        if (Test-Path $File) {
            $TargetFile = Join-Path $BackupPath (Split-Path $File -Leaf)
            Move-Item $File $TargetFile -Force
            $Size = [math]::Round((Get-Item $TargetFile).Length / 1MB, 2)
            Write-Log "Moved: $(Split-Path $File -Leaf) (${Size}MB)" "SUCCESS"
        }
    }

    Write-Log "Data volume backup completed" "SUCCESS"

    # Step 7: Restart all services
    if ($ContainerStatus -eq "running") {
        Show-Progress "Restore Services" "Restarting all services..." 90
        Write-Log "Restarting all services..."
        docker-compose up -d 2>&1 | Out-Null
        
        # Wait for PostgreSQL to start
        $Count = 0
        do {
            Start-Sleep -Seconds 3
            $Count++
            $Status = docker exec postgresql-11.19 pg_isready -U postgres -p 3433 2>$null
            if ($LASTEXITCODE -eq 0) {
                break
            }
        } while ($Count -lt 10)
        
        if ($Count -lt 10) {
            Write-Log "PostgreSQL service restarted successfully" "SUCCESS"
        } else {
            Write-Log "PostgreSQL service restart may have issues" "WARN"
        }
    }

} catch {
    Write-Log "Backup failed: $($_.Exception.Message)" "ERROR"
    exit 1
} finally {
    # Clean up temporary directory
    if (Test-Path $TempDir) {
        Remove-Item $TempDir -Recurse -Force
        Write-Log "Cleaned up temporary directory" "INFO"
    }
    Write-Progress -Activity "Backup Complete" -Completed
}

# Step 8: Create restore instructions
Show-Progress "Generate Documentation" "Creating restore instructions..." 95
Write-Log "Creating restore instructions document..."

$RestoreInstructions = @"
# PostgreSQL 11.19 Offline Complete Backup Restore Instructions
Backup Time: $Timestamp
Backup Type: Complete backup (configuration files + SQL data + data volumes)

## Backup Contents
1. Configuration files: docker-compose.yml, Dockerfile, postgresql.conf, pg_hba.conf, docker-entrypoint.sh
2. Initialization scripts: init-scripts directory
3. SQL data: postgresql_all_databases.sql.zip
4. Data volumes:
   - postgres_data_volume.zip (main data)
   - postgres_logs_volume.zip (logs)
   - pgadmin_data_volume.zip (pgAdmin data)

## Restore Command
.\backup-scripts\postgresql-restore-en.ps1 -BackupPath ".\backups\$Timestamp"

## Important Notes
- Restore will completely replace existing data
- Ensure Docker Desktop is running
- Existing services will be stopped during restore
- Services will automatically start after restore completion

## Access Information
- PostgreSQL: localhost:3433
- Username: postgres
- Password: postgres
- Database: postgres
- pgAdmin: http://localhost:8080 (<EMAIL> / <EMAIL>)
"@

$RestoreInstructions | Out-File -FilePath (Join-Path $BackupPath "restore_instructions.txt") -Encoding UTF8

# Calculate backup size
$BackupSize = (Get-ChildItem $BackupPath -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB

Show-Progress "Backup Complete" "Backup completed!" 100
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "Backup Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Log "Backup location: $BackupPath" "SUCCESS"
Write-Log "Backup size: $([math]::Round($BackupSize, 2)) MB" "SUCCESS"
Write-Log "Backup contents: Configuration files + SQL data + Data volumes" "SUCCESS"
Write-Log "Restore command: .\backup-scripts\postgresql-restore-en.ps1 -BackupPath `".\backups\$Timestamp`"" "SUCCESS"

Write-Host "`nBackup script execution completed" -ForegroundColor Green
