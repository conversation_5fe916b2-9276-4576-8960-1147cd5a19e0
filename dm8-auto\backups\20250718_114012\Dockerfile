# 达梦数据库 DM8 Docker 镜像 - 多阶段构建优化版本
# 基于银河麒麟操作系统

# ================================
# 第一阶段：构建阶段 (Builder Stage)
# ================================
FROM hxsoong/kylin:v10-sp3 AS builder

# 设置构建时环境变量
ENV LANG=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8 \
    TZ=Asia/Shanghai

# 设置工作目录
WORKDIR /tmp

# 安装构建时必需的软件包
RUN echo "安装构建依赖..." && \
    yum update -y && \
    yum install -y \
        libaio \
        libaio-devel \
        unzip \
        which \
        file \
        openssl \
        glibc-langpack-zh \
        shadow-utils \
        sudo && \
    echo "配置时区..." && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "清理构建缓存..." && \
    yum clean all && \
    rm -rf /var/cache/yum/*

# 创建dmdba用户和组（构建阶段）
RUN groupadd -g 1001 dmdba && \
    useradd -u 1001 -g dmdba -m -s /bin/bash dmdba && \
    echo "dmdba:$(openssl passwd -1 dmdba)" | chpasswd -e && \
    echo "dmdba ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# 复制达梦数据库安装文件
COPY dm8_20250506_x86_rh7_64/ /tmp/dm8_setup/
COPY dm8_20250506_x86_rh7_64/auto.xml /tmp/auto.xml

# 执行静默安装
RUN echo "开始静默安装达梦数据库..." && \
    chmod 755 /tmp/dm8_setup/DMInstall.bin && \
    cd /tmp/dm8_setup && \
    ./DMInstall.bin -q /tmp/auto.xml && \
    echo "安装完成，设置权限..." && \
    chown -R dmdba:dmdba /home/<USER>/dmdbms && \
    echo "清理不必要的文件..." && \
    rm -rf /home/<USER>/dmdbms/doc \
           /home/<USER>/dmdbms/samples \
           /home/<USER>/dmdbms/uninstall \
           /home/<USER>/dmdbms/web \
           /home/<USER>/dmdbms/tool/dmhs \
           /home/<USER>/dmdbms/tool/dts && \
    echo "清理安装文件..." && \
    rm -rf /tmp/dm8_setup /tmp/auto.xml

# ================================
# 第二阶段：运行阶段 (Runtime Stage)
# ================================
FROM hxsoong/kylin:v10-sp3 AS runtime

# 设置运行时环境变量
ENV LANG=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8 \
    TZ=Asia/Shanghai \
    DM_HOME=/home/<USER>/dmdbms \
    DM_DATA=/home/<USER>/dmdbms/data \
    PATH=/home/<USER>/dmdbms/bin:$PATH

# 只安装运行时必需的软件包
RUN echo "安装运行时依赖..." && \
    yum update -y && \
    yum install -y \
        libaio \
        net-tools \
        procps-ng \
        glibc-langpack-zh \
        tzdata \
        which \
        shadow-utils \
        sudo \
        openssl && \
    echo "配置时区..." && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "清理运行时缓存..." && \
    yum clean all && \
    rm -rf /var/cache/yum/* \
           /tmp/* \
           /var/tmp/*

# 创建dmdba用户和组（运行阶段）
RUN groupadd -g 1001 dmdba && \
    useradd -u 1001 -g dmdba -m -s /bin/bash dmdba && \
    echo "dmdba:$(openssl passwd -1 dmdba)" | chpasswd -e && \
    echo "dmdba ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# 从构建阶段复制已安装的达梦数据库
COPY --from=builder --chown=dmdba:dmdba /home/<USER>/dmdbms /home/<USER>/dmdbms

# 复制脚本文件
COPY docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh
COPY healthcheck.sh /usr/local/bin/healthcheck.sh

# 设置脚本权限
RUN chmod +x /usr/local/bin/docker-entrypoint.sh && \
    chmod +x /usr/local/bin/healthcheck.sh

# 创建数据目录并设置权限
RUN mkdir -p /home/<USER>/dmdbms/data && \
    chown -R dmdba:dmdba /home/<USER>/dmdbms/data

# 暴露端口
EXPOSE 5236

# 设置数据卷
VOLUME ["/home/<USER>/dmdbms/data"]

# 添加健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

# 切换到dmdba用户
USER dmdba

# 设置启动命令
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
CMD ["dmserver"]
