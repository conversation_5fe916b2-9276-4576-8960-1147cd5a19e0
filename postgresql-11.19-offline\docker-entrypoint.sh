#!/bin/bash
set -e

# 初始化函数
init_database() {
    echo "初始化PostgreSQL数据库..."
    
    # 初始化数据库集群
    /usr/local/pgsql/bin/initdb -D "$PGDATA" --auth-local=trust --auth-host=md5 --username="$PG_USER"
    
    # 复制配置文件
    cp /etc/postgresql/postgresql.conf "$PGDATA/postgresql.conf"
    cp /etc/postgresql/pg_hba.conf "$PGDATA/pg_hba.conf"
    
    echo "数据库初始化完成"
}

# 启动临时PostgreSQL服务进行初始配置
start_temp_server() {
    echo "启动临时PostgreSQL服务..."
    /usr/local/pgsql/bin/pg_ctl -D "$PGDATA" -o "-c listen_addresses='' -p $PG_PORT" -w start
}

# 停止临时PostgreSQL服务
stop_temp_server() {
    echo "停止临时PostgreSQL服务..."
    /usr/local/pgsql/bin/pg_ctl -D "$PGDATA" -m fast -w stop
}

# 配置数据库
configure_database() {
    echo "配置数据库..."
    
    # 设置postgres用户密码
    /usr/local/pgsql/bin/psql -v ON_ERROR_STOP=1 --username "$PG_USER" --dbname postgres --port "$PG_PORT" <<-EOSQL
        ALTER USER $PG_USER PASSWORD '$PG_PASSWORD';

        -- 显示PostgreSQL版本
        SELECT version();
EOSQL

    echo "数据库配置完成"
}

# 执行初始化SQL脚本
run_init_scripts() {
    if [ -d /docker-entrypoint-initdb.d ]; then
        echo "执行初始化脚本..."
        for f in /docker-entrypoint-initdb.d/*; do
            case "$f" in
                *.sh)
                    if [ -x "$f" ]; then
                        echo "执行脚本: $f"
                        "$f"
                    fi
                    ;;
                *.sql)
                    echo "执行SQL文件: $f"
                    /usr/local/pgsql/bin/psql -v ON_ERROR_STOP=1 --username "$PG_USER" --dbname "$PG_DB" --port "$PG_PORT" -f "$f"
                    ;;
                *.sql.gz)
                    echo "执行压缩SQL文件: $f"
                    gunzip -c "$f" | /usr/local/pgsql/bin/psql -v ON_ERROR_STOP=1 --username "$PG_USER" --dbname "$PG_DB" --port "$PG_PORT"
                    ;;
                *)
                    echo "忽略文件: $f"
                    ;;
            esac
        done
    fi
}

# 主函数
main() {
    # 检查数据目录是否为空
    if [ ! -s "$PGDATA/PG_VERSION" ]; then
        echo "数据目录为空，开始初始化..."
        
        # 初始化数据库
        init_database
        
        # 启动临时服务
        start_temp_server
        
        # 配置数据库
        configure_database
        
        # 执行初始化脚本
        run_init_scripts
        
        # 停止临时服务
        stop_temp_server
        
        echo "PostgreSQL初始化完成"
    else
        echo "数据目录已存在，跳过初始化"
    fi
    
    # 启动PostgreSQL服务
    echo "启动PostgreSQL服务..."
    exec /usr/local/pgsql/bin/postgres -D "$PGDATA" -c config_file="$PGDATA/postgresql.conf"
}

# 如果第一个参数是postgres，则执行主函数
if [ "$1" = 'postgres' ]; then
    main
else
    # 否则执行传入的命令
    exec "$@"
fi
