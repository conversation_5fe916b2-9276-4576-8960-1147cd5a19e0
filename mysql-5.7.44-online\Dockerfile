# 最终优化版本 - 体积小且稳定
FROM centos:centos7.9.2009 AS builder

RUN rm -rf /etc/yum.repos.d/*
COPY CentOS-Base.repo /etc/yum.repos.d/
COPY mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz /usr/local/
COPY my.cnf /etc/

# 安装最小依赖集（移除libaio-devel减小体积）
RUN yum clean all && \
    yum makecache && \
    yum install -y --skip-broken \
    tar \
    libaio \
    numactl \
    ncurses-compat-libs \
    psmisc && \
    yum clean all && \
    cd /usr/local/ && \
    tar -zxf mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz && \
    mv mysql-5.7.44-linux-glibc2.12-x86_64 mysql && \
    rm -f /usr/local/mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz && \
    rm -rf /var/cache/yum/* && \
    rm -rf /tmp/* && \
    rm -rf /var/tmp/* && \
    rm -rf /usr/share/doc/* && \
    rm -rf /usr/share/man/* && \
    rm -rf /usr/share/info/* && \
    rm -rf /usr/share/locale/*

# 第二阶段：最终运行镜像（移除初始化阶段减少层数）
FROM centos:centos7.9.2009

# 只复制必要的文件和库
COPY --from=builder /usr/local/mysql /usr/local/mysql
COPY --from=builder /etc/my.cnf /etc/my.cnf
COPY --from=builder /usr/lib64/libaio.so.* /usr/lib64/
COPY --from=builder /usr/lib64/libnuma.so.* /usr/lib64/
COPY --from=builder /usr/lib64/libncurses.so.* /usr/lib64/
COPY --from=builder /usr/lib64/libtinfo.so.* /usr/lib64/
COPY --from=builder /usr/bin/pkill /usr/bin/pkill
COPY --from=builder /usr/bin/killall /usr/bin/killall

# 复制启动脚本和健康检查脚本
COPY mysql-entrypoint.sh /usr/local/bin/
COPY healthcheck.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/mysql-entrypoint.sh && \
    chmod +x /usr/local/bin/healthcheck.sh

# 设置环境变量
ENV PATH="/usr/local/mysql/bin:${PATH}"
WORKDIR /usr/local/mysql

# 暴露端口
EXPOSE 3306

# 添加健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/healthcheck.sh

# 使用启动脚本作为入口点
ENTRYPOINT ["/usr/local/bin/mysql-entrypoint.sh"]
