-- PostgreSQL 11.19 初始化脚本
-- 此脚本在数据库首次启动时自动执行

-- 创建示例数据库
CREATE DATABASE sample_db WITH TEMPLATE template0 ENCODING 'UTF8';

-- 连接到示例数据库
\c sample_db;

-- 创建示例表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_created_at ON users(created_at);

-- 插入示例数据
INSERT INTO users (username, email, password_hash) VALUES
('admin', '<EMAIL>', 'hashed_password_1'),
('user1', '<EMAIL>', 'hashed_password_2'),
('user2', '<EMAIL>', 'hashed_password_3');

-- 创建产品表
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    stock_quantity INTEGER DEFAULT 0,
    category VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入示例产品数据
INSERT INTO products (name, description, price, stock_quantity, category) VALUES
('笔记本电脑', '高性能办公笔记本', 5999.99, 50, '电子产品'),
('无线鼠标', '蓝牙无线鼠标', 99.99, 200, '电子产品'),
('办公椅', '人体工学办公椅', 899.99, 30, '办公用品');

-- 创建订单表
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建订单详情表
CREATE TABLE order_items (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id),
    product_id INTEGER REFERENCES products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL
);

-- 创建视图
CREATE VIEW user_order_summary AS
SELECT 
    u.username,
    u.email,
    COUNT(o.id) as total_orders,
    COALESCE(SUM(o.total_amount), 0) as total_spent
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
GROUP BY u.id, u.username, u.email;

-- 创建函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 显示创建的表
\dt

-- 显示数据库信息
SELECT 
    'PostgreSQL 11.19 初始化完成' as message,
    current_database() as database_name,
    current_user as current_user,
    version() as postgresql_version;
