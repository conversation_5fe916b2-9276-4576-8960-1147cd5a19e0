<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Linux文件内容操作命令新手入门教程</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 13px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: var(--light-surface);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        th,
        td {
            padding: 18px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 命令类型标签 */
        .command-tag {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            margin: 0 8px 12px 0;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .command-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .command-tag:hover::before {
            left: 100%;
        }

        .cmd-view {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
        }

        .cmd-edit {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .cmd-search {
            background: linear-gradient(135deg, #45b7d1 0%, #96c93d 100%);
            color: white;
        }

        .cmd-create {
            background: linear-gradient(135deg, #f9ca24 0%, #f0932b 100%);
            color: white;
        }

        .cmd-advanced {
            background: linear-gradient(135deg, #a55eea 0%, #8e44ad 100%);
            color: white;
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-decoration: none;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            z-index: 999;
            font-size: 20px;
        }

        .back-to-top:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .content-wrapper {
                padding: 30px;
            }

            .container {
                padding: 40px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
                border-radius: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 16px;
                margin-right: 15px;
            }
        }

        /* 打印样式 */
        @media print {

            .sidebar,
            .back-to-top,
            .mobile-menu-btn {
                display: none;
            }

            .main-content {
                margin-left: 0;
            }

            body {
                background: white;
                font-size: 12px;
            }

            .container {
                box-shadow: none;
                padding: 0;
            }

            pre {
                background: #f8f9fa;
                color: #333;
                border: 1px solid #ddd;
            }

            .info-box,
            .warning-box,
            .success-box,
            .danger-box {
                border: 1px solid #ddd;
                background: #f8f9fa;
            }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
        }
    </style>
</head>

<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-file-alt"></i> Linux文件操作</h2>
            <p>文件内容操作命令新手指南</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#introduction"><i class="fas fa-home"></i>1. 教程介绍</a></li>
                <li><a href="#basic-concepts"><i class="fas fa-lightbulb"></i>2. 基础概念</a></li>
                <li><a href="#view-commands"><i class="fas fa-eye"></i>3. 查看文件内容</a></li>
                <li><a href="#create-commands"><i class="fas fa-plus"></i>4. 创建和写入文件</a></li>
                <li><a href="#edit-commands"><i class="fas fa-edit"></i>5. 编辑文件内容</a></li>
                <li><a href="#search-commands"><i class="fas fa-search"></i>6. 搜索文件内容</a></li>
                <li><a href="#compare-commands"><i class="fas fa-balance-scale"></i>7. 比较文件内容</a></li>
                <li><a href="#sort-commands"><i class="fas fa-sort"></i>8. 排序和去重</a></li>
                <li><a href="#text-processing"><i class="fas fa-cogs"></i>9. 文本处理</a></li>
                <li><a href="#advanced-commands"><i class="fas fa-magic"></i>10. 高级操作</a></li>
                <li><a href="#practical-examples"><i class="fas fa-hands-helping"></i>11. 实战案例</a></li>
                <li><a href="#faq"><i class="fas fa-question-circle"></i>12. 常见问题</a></li>
                <li><a href="#summary"><i class="fas fa-flag-checkered"></i>13. 总结</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-terminal"></i> Linux文件内容操作命令新手入门教程</h1>

                <div class="info-box">
                    <strong><i class="fas fa-info-circle"></i> 教程说明：</strong>
                    本教程专为Linux新手小白设计，详细介绍Linux系统中文件内容的添加、修改、删除和查看操作。每个命令都有详细的解释、参数说明和实际案例，让您轻松掌握Linux文件操作技能。
                </div>

                <div class="warning-box">
                    <strong><i class="fas fa-exclamation-triangle"></i> 命令类型说明：</strong>
                    <div style="margin-top: 15px;">
                        <span class="command-tag cmd-view"><i class="fas fa-eye"></i> 查看命令</span> - 用于查看文件内容，不会修改文件<br>
                        <span class="command-tag cmd-edit"><i class="fas fa-edit"></i> 编辑命令</span> - 用于修改文件内容<br>
                        <span class="command-tag cmd-search"><i class="fas fa-search"></i> 搜索命令</span> -
                        用于在文件中搜索特定内容<br>
                        <span class="command-tag cmd-create"><i class="fas fa-plus"></i> 创建命令</span> - 用于创建新文件或添加内容<br>
                        <span class="command-tag cmd-advanced"><i class="fas fa-magic"></i> 高级命令</span> - 复杂的文本处理操作
                    </div>
                </div>

                <section id="introduction">
                    <h2><span class="step-number">1</span>教程介绍</h2>

                    <h3><i class="fas fa-target"></i> 1.1 学习目标</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-check-circle"></i> 通过本教程，您将学会：</h4>
                        <ul>
                            <li><strong>查看文件内容：</strong>使用cat、less、more、head、tail等命令查看文件</li>
                            <li><strong>创建和编辑文件：</strong>使用touch、echo、vi/vim、nano等创建和编辑文件</li>
                            <li><strong>搜索文件内容：</strong>使用grep、find等命令搜索文件中的特定内容</li>
                            <li><strong>文本处理：</strong>使用sed、awk、sort、uniq等进行文本处理</li>
                            <li><strong>文件比较：</strong>使用diff、cmp等命令比较文件差异</li>
                            <li><strong>实际应用：</strong>在真实场景中灵活运用这些命令</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-users"></i> 1.2 适用人群</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-user"></i> 人群</th>
                            <th><i class="fas fa-star"></i> 适用程度</th>
                            <th><i class="fas fa-comment"></i> 说明</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-baby"></i> Linux新手</td>
                            <td>⭐⭐⭐⭐⭐</td>
                            <td>完全零基础，从最基本的概念开始学习</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-graduation-cap"></i> 计算机专业学生</td>
                            <td>⭐⭐⭐⭐</td>
                            <td>系统学习Linux文件操作命令</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-laptop-code"></i> 开发人员</td>
                            <td>⭐⭐⭐</td>
                            <td>快速掌握常用的文件操作技能</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-server"></i> 系统管理员</td>
                            <td>⭐⭐</td>
                            <td>复习和巩固基础命令</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-clock"></i> 1.3 学习时间安排</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-calendar-alt"></i> 建议学习计划：</h4>
                        <ul>
                            <li><strong>第1天：</strong>基础概念 + 查看文件内容命令（1-3章）</li>
                            <li><strong>第2天：</strong>创建和编辑文件命令（4-5章）</li>
                            <li><strong>第3天：</strong>搜索和比较命令（6-7章）</li>
                            <li><strong>第4天：</strong>文本处理和高级操作（8-10章）</li>
                            <li><strong>第5天：</strong>实战案例和总结（11-12章）</li>
                        </ul>
                        <p><strong>总学习时间：</strong>约5天，每天1-2小时</p>
                    </div>
                </section>

                <section id="basic-concepts">
                    <h2><span class="step-number">2</span>基础概念</h2>

                    <h3><i class="fas fa-file"></i> 2.1 什么是文件内容操作</h3>
                    <p>文件内容操作是指对文件内部数据进行的各种处理，包括：</p>
                    <ul>
                        <li><strong>查看：</strong>读取并显示文件内容</li>
                        <li><strong>创建：</strong>新建文件并写入内容</li>
                        <li><strong>修改：</strong>更改文件中的现有内容</li>
                        <li><strong>删除：</strong>移除文件中的特定内容</li>
                        <li><strong>搜索：</strong>在文件中查找特定的文本或模式</li>
                        <li><strong>处理：</strong>对文件内容进行排序、去重、替换等操作</li>
                    </ul>

                    <h3><i class="fas fa-folder-open"></i> 2.2 Linux文件系统基础</h3>
                    <div class="warning-box">
                        <strong><i class="fas fa-lightbulb"></i> 重要概念：</strong>
                        <ul>
                            <li><strong>路径：</strong>文件在系统中的位置，如 /home/<USER>/document.txt</li>
                            <li><strong>绝对路径：</strong>从根目录(/)开始的完整路径</li>
                            <li><strong>相对路径：</strong>相对于当前目录的路径</li>
                            <li><strong>当前目录：</strong>用 . 表示</li>
                            <li><strong>上级目录：</strong>用 .. 表示</li>
                            <li><strong>主目录：</strong>用 ~ 表示用户的家目录</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-terminal"></i> 2.3 命令行基础</h3>
                    <p>在学习文件操作命令之前，需要了解命令行的基本结构：</p>
                    <pre><code># 命令的基本格式
命令名 [选项] [参数]

# 示例
ls -l /home/<USER>
│  │  └─ 参数（要操作的目录）
│  └─ 选项（-l表示详细列表）
└─ 命令名（ls用于列出文件）</code></pre>

                    <div class="info-box">
                        <h4><i class="fas fa-keyboard"></i> 常用快捷键：</h4>
                        <ul>
                            <li><strong>Tab键：</strong>自动补全命令或文件名</li>
                            <li><strong>Ctrl+C：</strong>中断当前命令</li>
                            <li><strong>Ctrl+D：</strong>退出当前会话</li>
                            <li><strong>↑↓方向键：</strong>查看命令历史</li>
                            <li><strong>Ctrl+L：</strong>清屏</li>
                        </ul>
                    </div>
                </section>

                <section id="view-commands">
                    <h2><span class="step-number">3</span>查看文件内容</h2>
                    <span class="command-tag cmd-view"><i class="fas fa-eye"></i> 查看命令</span>

                    <h3><i class="fas fa-cat"></i> 3.1 cat命令 - 显示文件全部内容</h3>
                    <p><code>cat</code>命令是最常用的查看文件内容的命令，它会将整个文件内容一次性显示在屏幕上。</p>

                    <h4>基本语法</h4>
                    <pre><code># 基本用法
cat 文件名

# 查看多个文件
cat 文件1 文件2 文件3

# 显示行号
cat -n 文件名

# 显示非空行的行号
cat -b 文件名</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 创建一个测试文件
echo -e "第一行内容\n第二行内容\n\n第四行内容" > test.txt

# 查看文件内容
cat test.txt
# 输出：
# 第一行内容
# 第二行内容
#
# 第四行内容

# 显示行号
cat -n test.txt
# 输出：
#      1	第一行内容
#      2	第二行内容
#      3
#      4	第四行内容

# 只显示非空行的行号
cat -b test.txt
# 输出：
#      1	第一行内容
#      2	第二行内容
#      3	第四行内容</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 使用技巧：</strong>
                        <ul>
                            <li><code>cat</code>适合查看小文件，大文件建议使用<code>less</code>或<code>more</code></li>
                            <li>可以使用<code>cat > 文件名</code>创建新文件并输入内容</li>
                            <li><code>cat -A</code>可以显示所有不可见字符</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-file-alt"></i> 3.2 less命令 - 分页查看文件</h3>
                    <p><code>less</code>命令可以分页查看文件内容，特别适合查看大文件。</p>

                    <h4>基本语法</h4>
                    <pre><code># 基本用法
less 文件名

# 在less中的常用操作：
# 空格键或f：向下翻页
# b：向上翻页
# j或↓：向下一行
# k或↑：向上一行
# g：跳到文件开头
# G：跳到文件结尾
# /搜索词：向下搜索
# ?搜索词：向上搜索
# n：下一个搜索结果
# N：上一个搜索结果
# q：退出</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 查看系统日志文件
less /var/log/messages

# 查看文件并显示行号
less -N /etc/passwd

# 查看文件时忽略大小写搜索
less -i /etc/hosts</code></pre>

                    <h3><i class="fas fa-arrow-up"></i> 3.3 head命令 - 查看文件开头</h3>
                    <p><code>head</code>命令用于查看文件的开头部分，默认显示前10行。</p>

                    <h4>基本语法</h4>
                    <pre><code># 查看前10行（默认）
head 文件名

# 查看前n行
head -n 数字 文件名
head -数字 文件名

# 查看前n个字符
head -c 数字 文件名</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 查看文件前10行
head /etc/passwd

# 查看文件前5行
head -n 5 /etc/passwd
head -5 /etc/passwd

# 查看文件前100个字符
head -c 100 /etc/passwd

# 同时查看多个文件的开头
head -n 3 file1.txt file2.txt</code></pre>

                    <h3><i class="fas fa-arrow-down"></i> 3.4 tail命令 - 查看文件结尾</h3>
                    <p><code>tail</code>命令用于查看文件的结尾部分，默认显示最后10行。</p>

                    <h4>基本语法</h4>
                    <pre><code># 查看最后10行（默认）
tail 文件名

# 查看最后n行
tail -n 数字 文件名
tail -数字 文件名

# 实时监控文件变化
tail -f 文件名

# 从第n行开始显示到文件结尾
tail -n +数字 文件名</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 查看日志文件最后10行
tail /var/log/messages

# 查看最后20行
tail -n 20 /var/log/messages
tail -20 /var/log/messages

# 实时监控日志文件（常用于调试）
tail -f /var/log/messages

# 从第50行开始显示到文件结尾
tail -n +50 /etc/passwd</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-star"></i> 实用技巧：</strong>
                        <ul>
                            <li><code>tail -f</code>是监控日志文件的神器，按Ctrl+C退出监控</li>
                            <li>可以组合使用：<code>head -n 20 file.txt | tail -n 5</code>显示第16-20行</li>
                            <li><code>tail -f</code>可以同时监控多个文件</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-book-open"></i> 3.5 more命令 - 分页查看文件</h3>
                    <p><code>more</code>命令类似于<code>less</code>，但功能相对简单，只能向前翻页。</p>

                    <h4>基本语法</h4>
                    <pre><code># 基本用法
more 文件名

# 在more中的操作：
# 空格键：向下翻页
# Enter：向下一行
# q：退出
# /搜索词：搜索内容</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 分页查看文件
more /etc/passwd

# 从第10行开始查看
more +10 /etc/passwd</code></pre>
                </section>

                <section id="create-commands">
                    <h2><span class="step-number">4</span>创建和写入文件</h2>
                    <span class="command-tag cmd-create"><i class="fas fa-plus"></i> 创建命令</span>

                    <h3><i class="fas fa-hand-pointer"></i> 4.1 touch命令 - 创建空文件</h3>
                    <p><code>touch</code>命令主要用于创建空文件或更新文件的时间戳。</p>

                    <h4>基本语法</h4>
                    <pre><code># 创建空文件
touch 文件名

# 创建多个文件
touch 文件1 文件2 文件3

# 更新文件时间戳
touch 已存在的文件名</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 创建一个空文件
touch newfile.txt

# 创建多个空文件
touch file1.txt file2.txt file3.txt

# 创建带路径的文件
touch /tmp/testfile.txt

# 验证文件是否创建成功
ls -l newfile.txt</code></pre>

                    <h3><i class="fas fa-echo"></i> 4.2 echo命令 - 输出文本到文件</h3>
                    <p><code>echo</code>命令可以将文本输出到屏幕或重定向到文件中。</p>

                    <h4>基本语法</h4>
                    <pre><code># 输出到屏幕
echo "文本内容"

# 输出到文件（覆盖）
echo "文本内容" > 文件名

# 追加到文件
echo "文本内容" >> 文件名

# 输出变量
echo $变量名</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 创建文件并写入内容
echo "Hello, World!" > hello.txt

# 追加内容到文件
echo "这是第二行" >> hello.txt
echo "这是第三行" >> hello.txt

# 查看文件内容
cat hello.txt
# 输出：
# Hello, World!
# 这是第二行
# 这是第三行

# 输出多行内容
echo -e "第一行\n第二行\n第三行" > multiline.txt

# 输出当前用户名
echo "当前用户：$USER" > userinfo.txt</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
                        <ul>
                            <li><code>></code> 会覆盖文件原有内容</li>
                            <li><code>>></code> 会在文件末尾追加内容</li>
                            <li>使用<code>-e</code>选项可以解释转义字符如<code>\n</code>（换行）</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-keyboard"></i> 4.3 cat命令创建文件</h3>
                    <p>除了查看文件，<code>cat</code>命令也可以用来创建文件。</p>

                    <h4>基本语法</h4>
                    <pre><code># 创建文件并输入内容
cat > 文件名
# 然后输入内容，按Ctrl+D结束

# 追加内容到文件
cat >> 文件名
# 然后输入内容，按Ctrl+D结束</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 创建文件并输入多行内容
cat > myfile.txt
这是第一行
这是第二行
这是第三行
# 按Ctrl+D结束输入

# 追加内容
cat >> myfile.txt
这是追加的第四行
这是追加的第五行
# 按Ctrl+D结束输入

# 查看文件内容
cat myfile.txt</code></pre>

                    <h3><i class="fas fa-copy"></i> 4.4 重定向操作符详解</h3>
                    <p>重定向是Linux中非常重要的概念，用于控制命令的输入和输出。</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-arrow-right"></i> 操作符</th>
                            <th><i class="fas fa-info"></i> 功能</th>
                            <th><i class="fas fa-example"></i> 示例</th>
                        </tr>
                        <tr>
                            <td><code>></code></td>
                            <td>重定向输出（覆盖）</td>
                            <td><code>echo "hello" > file.txt</code></td>
                        </tr>
                        <tr>
                            <td><code>>></code></td>
                            <td>重定向输出（追加）</td>
                            <td><code>echo "world" >> file.txt</code></td>
                        </tr>
                        <tr>
                            <td><code><</code></td>
                            <td>重定向输入</td>
                            <td><code>sort < file.txt</code></td>
                        </tr>
                        <tr>
                            <td><code>2></code></td>
                            <td>重定向错误输出</td>
                            <td><code>ls /nonexist 2> error.log</code></td>
                        </tr>
                        <tr>
                            <td><code>&></code></td>
                            <td>重定向所有输出</td>
                            <td><code>ls /nonexist &> all.log</code></td>
                        </tr>
                    </table>

                    <h4>实际案例</h4>
                    <pre><code># 将命令输出保存到文件
ls -l > filelist.txt

# 将错误信息保存到文件
ls /nonexistent 2> error.log

# 将正确和错误输出都保存到文件
ls /home /nonexistent &> output.log

# 将输出追加到现有文件
date >> log.txt

# 从文件读取输入
sort < names.txt > sorted_names.txt</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-lightbulb"></i> 实用技巧：</strong>
                        <ul>
                            <li>使用<code>/dev/null</code>丢弃不需要的输出：<code>command > /dev/null</code></li>
                            <li>同时显示和保存输出：<code>command | tee file.txt</code></li>
                            <li>管道符<code>|</code>可以将一个命令的输出作为另一个命令的输入</li>
                        </ul>
                    </div>
                </section>

                <section id="edit-commands">
                    <h2><span class="step-number">5</span>编辑文件内容</h2>
                    <span class="command-tag cmd-edit"><i class="fas fa-edit"></i> 编辑命令</span>

                    <h3><i class="fas fa-vim"></i> 5.1 vi/vim编辑器 - 强大的文本编辑器</h3>
                    <p><code>vi</code>（或增强版<code>vim</code>）是Linux系统中最强大的文本编辑器，虽然学习曲线较陡，但功能非常强大。</p>

                    <h4>基本概念</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> vi的三种模式：</strong>
                        <ul>
                            <li><strong>命令模式（Normal Mode）：</strong>默认模式，用于移动光标和执行命令</li>
                            <li><strong>插入模式（Insert Mode）：</strong>用于输入和编辑文本</li>
                            <li><strong>命令行模式（Command Mode）：</strong>用于保存、退出等操作</li>
                        </ul>
                    </div>

                    <h4>基本操作</h4>
                    <pre><code># 打开文件
vi 文件名
vim 文件名

# 模式切换
i          # 进入插入模式（在光标前插入）
a          # 进入插入模式（在光标后插入）
o          # 进入插入模式（在下一行插入）
Esc        # 返回命令模式
:          # 进入命令行模式

# 保存和退出
:w         # 保存文件
:q         # 退出
:wq        # 保存并退出
:q!        # 强制退出不保存
:x         # 保存并退出（等同于:wq）</code></pre>

                    <h4>常用命令</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-keyboard"></i> 命令</th>
                            <th><i class="fas fa-info"></i> 功能</th>
                            <th><i class="fas fa-comment"></i> 说明</th>
                        </tr>
                        <tr>
                            <td><code>h j k l</code></td>
                            <td>光标移动</td>
                            <td>左 下 上 右</td>
                        </tr>
                        <tr>
                            <td><code>dd</code></td>
                            <td>删除当前行</td>
                            <td>删除光标所在行</td>
                        </tr>
                        <tr>
                            <td><code>yy</code></td>
                            <td>复制当前行</td>
                            <td>复制光标所在行</td>
                        </tr>
                        <tr>
                            <td><code>p</code></td>
                            <td>粘贴</td>
                            <td>在光标后粘贴</td>
                        </tr>
                        <tr>
                            <td><code>u</code></td>
                            <td>撤销</td>
                            <td>撤销上一步操作</td>
                        </tr>
                        <tr>
                            <td><code>/搜索词</code></td>
                            <td>搜索</td>
                            <td>向下搜索</td>
                        </tr>
                        <tr>
                            <td><code>n</code></td>
                            <td>下一个</td>
                            <td>下一个搜索结果</td>
                        </tr>
                    </table>

                    <h4>实际案例</h4>
                    <pre><code># 创建并编辑新文件
vim newfile.txt

# 在vim中的操作步骤：
# 1. 按 i 进入插入模式
# 2. 输入文本内容
# 3. 按 Esc 返回命令模式
# 4. 输入 :wq 保存并退出

# 编辑现有文件
vim /etc/hosts

# 快速编辑技巧
vim +10 file.txt        # 打开文件并跳到第10行
vim +/pattern file.txt  # 打开文件并搜索pattern</code></pre>

                    <h3><i class="fas fa-feather"></i> 5.2 nano编辑器 - 简单易用的编辑器</h3>
                    <p><code>nano</code>是一个简单易用的文本编辑器，特别适合Linux新手使用。</p>

                    <h4>基本操作</h4>
                    <pre><code># 打开文件
nano 文件名

# 常用快捷键（在nano中显示在底部）
Ctrl+O    # 保存文件
Ctrl+X    # 退出编辑器
Ctrl+K    # 剪切当前行
Ctrl+U    # 粘贴
Ctrl+W    # 搜索
Ctrl+\    # 替换
Ctrl+G    # 显示帮助</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 创建并编辑文件
nano myfile.txt

# 编辑系统配置文件
sudo nano /etc/hosts

# 以只读模式打开文件
nano -v file.txt</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-star"></i> 编辑器选择建议：</strong>
                        <ul>
                            <li><strong>新手推荐：</strong>nano - 简单直观，快捷键显示在屏幕底部</li>
                            <li><strong>进阶用户：</strong>vim - 功能强大，效率极高</li>
                            <li><strong>图形界面：</strong>gedit、kate等图形化编辑器</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-stream"></i> 5.3 sed命令 - 流编辑器</h3>
                    <p><code>sed</code>是一个强大的流编辑器，可以对文件进行批量修改而不需要打开编辑器。</p>

                    <h4>基本语法</h4>
                    <pre><code># 基本格式
sed '操作' 文件名

# 常用操作
s/旧内容/新内容/    # 替换
d                  # 删除
p                  # 打印
a                  # 追加
i                  # 插入</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 创建测试文件
echo -e "apple\nbanana\napple\norange" > fruits.txt

# 替换第一个匹配项
sed 's/apple/grape/' fruits.txt
# 输出：grape, banana, apple, orange

# 替换所有匹配项
sed 's/apple/grape/g' fruits.txt
# 输出：grape, banana, grape, orange

# 删除包含特定内容的行
sed '/banana/d' fruits.txt
# 输出：apple, apple, orange

# 在第2行后插入新行
sed '2a\strawberry' fruits.txt

# 直接修改文件（谨慎使用）
sed -i 's/apple/grape/g' fruits.txt</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 注意事项：</strong>
                        <ul>
                            <li><code>sed</code>默认不修改原文件，只输出结果</li>
                            <li>使用<code>-i</code>选项会直接修改原文件，请谨慎使用</li>
                            <li>建议先测试命令效果，确认无误后再使用<code>-i</code>选项</li>
                        </ul>
                    </div>
                </section>

                <section id="search-commands">
                    <h2><span class="step-number">6</span>搜索文件内容</h2>
                    <span class="command-tag cmd-search"><i class="fas fa-search"></i> 搜索命令</span>

                    <h3><i class="fas fa-filter"></i> 6.1 grep命令 - 文本搜索神器</h3>
                    <p><code>grep</code>是Linux中最重要的文本搜索工具，可以在文件中搜索指定的模式。</p>

                    <h4>基本语法</h4>
                    <pre><code># 基本用法
grep "搜索内容" 文件名

# 常用选项
-i    # 忽略大小写
-n    # 显示行号
-v    # 显示不匹配的行
-r    # 递归搜索目录
-l    # 只显示包含匹配内容的文件名
-c    # 只显示匹配行的数量</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 创建测试文件
cat > sample.txt << EOF
Apple is red
Banana is yellow
apple is sweet
Orange is orange
APPLE is delicious
EOF

# 基本搜索
grep "apple" sample.txt
# 输出：apple is sweet

# 忽略大小写搜索
grep -i "apple" sample.txt
# 输出：
# Apple is red
# apple is sweet
# APPLE is delicious

# 显示行号
grep -n -i "apple" sample.txt
# 输出：
# 1:Apple is red
# 3:apple is sweet
# 5:APPLE is delicious

# 搜索不包含指定内容的行
grep -v "apple" sample.txt
# 输出：
# Apple is red
# Banana is yellow
# Orange is orange
# APPLE is delicious

# 统计匹配行数
grep -c -i "apple" sample.txt
# 输出：3</code></pre>

                    <h3><i class="fas fa-search-plus"></i> 6.2 grep高级用法</h3>
                    <p>grep支持正则表达式，可以进行更复杂的搜索。</p>

                    <h4>正则表达式基础</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-code"></i> 符号</th>
                            <th><i class="fas fa-info"></i> 含义</th>
                            <th><i class="fas fa-example"></i> 示例</th>
                        </tr>
                        <tr>
                            <td><code>^</code></td>
                            <td>行首</td>
                            <td><code>grep "^Apple" file.txt</code></td>
                        </tr>
                        <tr>
                            <td><code>$</code></td>
                            <td>行尾</td>
                            <td><code>grep "red$" file.txt</code></td>
                        </tr>
                        <tr>
                            <td><code>.</code></td>
                            <td>任意单个字符</td>
                            <td><code>grep "a.e" file.txt</code></td>
                        </tr>
                        <tr>
                            <td><code>*</code></td>
                            <td>前面字符0次或多次</td>
                            <td><code>grep "ap*le" file.txt</code></td>
                        </tr>
                        <tr>
                            <td><code>[]</code></td>
                            <td>字符集合</td>
                            <td><code>grep "[Aa]pple" file.txt</code></td>
                        </tr>
                    </table>

                    <h4>高级搜索案例</h4>
                    <pre><code># 搜索以"A"开头的行
grep "^A" sample.txt

# 搜索以"e"结尾的行
grep "e$" sample.txt

# 搜索包含数字的行
grep "[0-9]" /etc/passwd

# 搜索空行
grep "^$" file.txt

# 搜索多个模式
grep -E "apple|orange" sample.txt

# 在多个文件中搜索
grep -r "error" /var/log/

# 搜索并显示前后几行
grep -A 2 -B 2 "apple" sample.txt  # 显示匹配行及前后2行</code></pre>

                    <h3><i class="fas fa-search-location"></i> 6.3 find命令结合grep</h3>
                    <p>结合<code>find</code>和<code>grep</code>可以在整个目录树中搜索文件内容。</p>

                    <h4>实际案例</h4>
                    <pre><code># 在当前目录及子目录中搜索包含"error"的文件
find . -type f -exec grep -l "error" {} \;

# 搜索所有.txt文件中包含"apple"的内容
find . -name "*.txt" -exec grep -H "apple" {} \;

# 搜索最近7天修改的文件中包含"config"的内容
find . -type f -mtime -7 -exec grep -l "config" {} \;

# 更简单的递归搜索方法
grep -r "error" /var/log/
grep -r --include="*.txt" "apple" .</code></pre>

                    <h3><i class="fas fa-eye-slash"></i> 6.4 其他搜索工具</h3>

                    <h4>egrep - 扩展正则表达式</h4>
                    <pre><code># egrep等同于grep -E
egrep "apple|orange" sample.txt

# 搜索多个模式
egrep "(apple|orange)" sample.txt

# 搜索重复字符
egrep "ap+le" sample.txt</code></pre>

                    <h4>fgrep - 固定字符串搜索</h4>
                    <pre><code># fgrep等同于grep -F，不解释正则表达式
fgrep "a.e" sample.txt  # 搜索字面意思的"a.e"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-lightbulb"></i> 搜索技巧：</strong>
                        <ul>
                            <li>使用<code>grep -r</code>可以递归搜索整个目录</li>
                            <li>结合<code>|</code>管道可以进行多级过滤</li>
                            <li>使用<code>grep -v</code>可以排除不需要的结果</li>
                            <li><code>grep --color=auto</code>可以高亮显示匹配内容</li>
                        </ul>
                    </div>
                </section>

                <section id="compare-commands">
                    <h2><span class="step-number">7</span>比较文件内容</h2>
                    <span class="command-tag cmd-view"><i class="fas fa-balance-scale"></i> 比较命令</span>

                    <h3><i class="fas fa-not-equal"></i> 7.1 diff命令 - 比较文件差异</h3>
                    <p><code>diff</code>命令用于比较两个文件的差异，显示不同之处。</p>

                    <h4>基本语法</h4>
                    <pre><code># 基本用法
diff 文件1 文件2

# 常用选项
-u    # 统一格式输出
-i    # 忽略大小写
-w    # 忽略空白字符
-r    # 递归比较目录</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 创建两个测试文件
echo -e "apple\nbanana\ncherry" > file1.txt
echo -e "apple\norange\ncherry\ndate" > file2.txt

# 比较文件差异
diff file1.txt file2.txt
# 输出：
# 2c2
# < banana
# ---
# > orange
# 3a4
# > date

# 使用统一格式
diff -u file1.txt file2.txt
# 输出更易读的格式

# 忽略大小写比较
diff -i file1.txt file2.txt</code></pre>

                    <h3><i class="fas fa-equals"></i> 7.2 cmp命令 - 字节级比较</h3>
                    <p><code>cmp</code>命令逐字节比较两个文件，找出第一个不同的位置。</p>

                    <h4>实际案例</h4>
                    <pre><code># 比较两个文件
cmp file1.txt file2.txt

# 详细显示差异
cmp -l file1.txt file2.txt

# 静默模式（只返回退出状态）
cmp -s file1.txt file2.txt
echo $?  # 0表示相同，1表示不同</code></pre>

                    <h3><i class="fas fa-code-branch"></i> 7.3 comm命令 - 逐行比较</h3>
                    <p><code>comm</code>命令比较两个已排序的文件，显示共同行和独有行。</p>

                    <h4>实际案例</h4>
                    <pre><code># 创建两个排序的文件
echo -e "apple\nbanana\ncherry" | sort > sorted1.txt
echo -e "banana\ncherry\ndate" | sort > sorted2.txt

# 比较文件
comm sorted1.txt sorted2.txt
# 输出三列：
# 第1列：只在文件1中的行
# 第2列：只在文件2中的行
# 第3列：两个文件共有的行

# 只显示共同行
comm -12 sorted1.txt sorted2.txt</code></pre>
                </section>

                <section id="sort-commands">
                    <h2><span class="step-number">8</span>排序和去重</h2>
                    <span class="command-tag cmd-advanced"><i class="fas fa-sort"></i> 排序命令</span>

                    <h3><i class="fas fa-sort-alpha-down"></i> 8.1 sort命令 - 文件排序</h3>
                    <p><code>sort</code>命令用于对文件内容进行排序。</p>

                    <h4>基本语法</h4>
                    <pre><code># 基本用法
sort 文件名

# 常用选项
-r    # 逆序排序
-n    # 按数值排序
-k    # 指定排序字段
-u    # 去除重复行
-t    # 指定分隔符</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 创建测试文件
cat > numbers.txt << EOF
10
2
30
1
20
2
EOF

# 默认排序（按字符）
sort numbers.txt
# 输出：1, 10, 2, 20, 2, 30

# 按数值排序
sort -n numbers.txt
# 输出：1, 2, 2, 10, 20, 30

# 逆序排序
sort -nr numbers.txt
# 输出：30, 20, 10, 2, 2, 1

# 排序并去重
sort -nu numbers.txt
# 输出：1, 2, 10, 20, 30

# 按指定字段排序
echo -e "张三:25:北京\n李四:30:上海\n王五:20:广州" > people.txt
sort -t: -k2 -n people.txt  # 按年龄排序</code></pre>

                    <h3><i class="fas fa-layer-group"></i> 8.2 uniq命令 - 去除重复行</h3>
                    <p><code>uniq</code>命令用于去除文件中的重复行（需要先排序）。</p>

                    <h4>基本语法</h4>
                    <pre><code># 基本用法
uniq 文件名

# 常用选项
-c    # 显示重复次数
-d    # 只显示重复的行
-u    # 只显示不重复的行</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 创建包含重复行的文件
cat > duplicates.txt << EOF
apple
banana
apple
cherry
banana
apple
EOF

# 先排序再去重
sort duplicates.txt | uniq
# 输出：apple, banana, cherry

# 显示重复次数
sort duplicates.txt | uniq -c
# 输出：
#    3 apple
#    2 banana
#    1 cherry

# 只显示重复的行
sort duplicates.txt | uniq -d
# 输出：apple, banana

# 只显示不重复的行
sort duplicates.txt | uniq -u
# 输出：cherry</code></pre>
                </section>

                <section id="text-processing">
                    <h2><span class="step-number">9</span>文本处理</h2>
                    <span class="command-tag cmd-advanced"><i class="fas fa-cogs"></i> 文本处理</span>

                    <h3><i class="fas fa-cut"></i> 9.1 cut命令 - 提取列</h3>
                    <p><code>cut</code>命令用于从文件中提取指定的列或字段。</p>

                    <h4>基本语法</h4>
                    <pre><code># 按字符位置提取
cut -c 位置 文件名

# 按分隔符提取字段
cut -d '分隔符' -f 字段号 文件名</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 创建测试文件
echo -e "张三,25,北京\n李四,30,上海\n王五,20,广州" > data.csv

# 提取第1个字段（姓名）
cut -d ',' -f 1 data.csv
# 输出：张三, 李四, 王五

# 提取第2和第3个字段
cut -d ',' -f 2,3 data.csv
# 输出：25,北京  30,上海  20,广州

# 提取字符位置1-3
cut -c 1-3 data.csv</code></pre>

                    <h3><i class="fas fa-magic"></i> 9.2 awk命令 - 强大的文本处理工具</h3>
                    <p><code>awk</code>是一个强大的文本处理工具，可以进行复杂的数据处理和计算。</p>

                    <h4>基本语法</h4>
                    <pre><code># 基本格式
awk '模式 {动作}' 文件名

# 常用内置变量
$0    # 整行内容
$1    # 第一个字段
$2    # 第二个字段
NF    # 字段数量
NR    # 行号</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 使用之前的data.csv文件
# 打印第一列
awk -F ',' '{print $1}' data.csv

# 打印行号和第一列
awk -F ',' '{print NR, $1}' data.csv

# 计算第二列的平均值
awk -F ',' '{sum+=$2} END {print "平均年龄:", sum/NR}' data.csv

# 筛选年龄大于25的记录
awk -F ',' '$2 > 25 {print $0}' data.csv

# 格式化输出
awk -F ',' '{printf "姓名: %s, 年龄: %s岁\n", $1, $2}' data.csv</code></pre>

                    <h3><i class="fas fa-exchange-alt"></i> 9.3 tr命令 - 字符转换</h3>
                    <p><code>tr</code>命令用于转换或删除字符。</p>

                    <h4>实际案例</h4>
                    <pre><code># 转换大小写
echo "Hello World" | tr 'a-z' 'A-Z'
# 输出：HELLO WORLD

# 删除指定字符
echo "Hello World" | tr -d 'l'
# 输出：Heo Word

# 替换字符
echo "Hello World" | tr 'o' '0'
# 输出：Hell0 W0rld

# 压缩重复字符
echo "aabbcc" | tr -s 'a-c'
# 输出：abc</code></pre>

                    <h3><i class="fas fa-calculator"></i> 9.4 wc命令 - 统计文件信息</h3>
                    <p><code>wc</code>命令用于统计文件的行数、单词数和字符数。</p>

                    <h4>基本语法</h4>
                    <pre><code># 基本用法
wc 文件名

# 常用选项
-l    # 只显示行数
-w    # 只显示单词数
-c    # 只显示字符数
-m    # 只显示字符数（多字节字符）</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 创建测试文件
echo -e "Hello World\nThis is a test\nLinux commands" > count.txt

# 统计所有信息
wc count.txt
# 输出：3 7 35 count.txt
# 解释：3行 7个单词 35个字符

# 只统计行数
wc -l count.txt
# 输出：3 count.txt

# 只统计单词数
wc -w count.txt
# 输出：7 count.txt

# 只统计字符数
wc -c count.txt
# 输出：35 count.txt

# 统计多个文件
wc *.txt

# 从管道输入统计
ls -la | wc -l  # 统计当前目录文件数量</code></pre>

                    <h3><i class="fas fa-list-ol"></i> 9.5 nl命令 - 添加行号</h3>
                    <p><code>nl</code>命令用于给文件内容添加行号，比cat -n更灵活。</p>

                    <h4>基本语法</h4>
                    <pre><code># 基本用法
nl 文件名

# 常用选项
-b a    # 给所有行编号（包括空行）
-b t    # 只给非空行编号（默认）
-n ln   # 行号左对齐
-n rn   # 行号右对齐
-w 数字  # 设置行号宽度</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 给文件添加行号
nl count.txt
# 输出：
#      1	Hello World
#      2	This is a test
#      3	Linux commands

# 给所有行（包括空行）编号
nl -b a count.txt

# 设置行号格式
nl -n ln -w 3 count.txt  # 左对齐，宽度为3</code></pre>

                    <h3><i class="fas fa-columns"></i> 9.6 paste命令 - 合并文件列</h3>
                    <p><code>paste</code>命令用于将多个文件的内容按列合并。</p>

                    <h4>基本语法</h4>
                    <pre><code># 基本用法
paste 文件1 文件2

# 常用选项
-d '分隔符'  # 指定分隔符
-s          # 串行模式，将一个文件的所有行合并为一行</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 创建测试文件
echo -e "张三\n李四\n王五" > names.txt
echo -e "25\n30\n28" > ages.txt
echo -e "北京\n上海\n广州" > cities.txt

# 合并文件
paste names.txt ages.txt cities.txt
# 输出：
# 张三	25	北京
# 李四	30	上海
# 王五	28	广州

# 使用逗号作为分隔符
paste -d ',' names.txt ages.txt cities.txt
# 输出：
# 张三,25,北京
# 李四,30,上海
# 王五,28,广州

# 串行模式
paste -s names.txt
# 输出：张三	李四	王五</code></pre>

                    <h3><i class="fas fa-cut"></i> 9.7 split命令 - 分割文件</h3>
                    <p><code>split</code>命令用于将大文件分割成多个小文件。</p>

                    <h4>基本语法</h4>
                    <pre><code># 按行数分割
split -l 行数 文件名 前缀

# 按大小分割
split -b 大小 文件名 前缀

# 按数字后缀
split -d -l 行数 文件名 前缀</code></pre>

                    <h4>实际案例</h4>
                    <pre><code># 创建大文件
seq 1 100 > numbers.txt

# 每10行分割一个文件
split -l 10 numbers.txt part_
# 生成：part_aa, part_ab, part_ac...

# 使用数字后缀
split -d -l 10 numbers.txt part_
# 生成：part_00, part_01, part_02...

# 按大小分割（每1KB一个文件）
split -b 1k largefile.txt chunk_

# 查看分割结果
ls part_*
wc -l part_*</code></pre>
                </section>

                <section id="advanced-commands">
                    <h2><span class="step-number">10</span>高级操作</h2>
                    <span class="command-tag cmd-advanced"><i class="fas fa-magic"></i> 高级命令</span>

                    <h3><i class="fas fa-link"></i> 10.1 管道和重定向组合</h3>
                    <p>通过组合多个命令，可以实现复杂的文本处理任务。</p>

                    <h4>实际案例</h4>
                    <pre><code># 统计文件中单词出现频率
cat file.txt | tr ' ' '\n' | sort | uniq -c | sort -nr

# 查找最大的10个文件
ls -la | sort -k5 -nr | head -10

# 提取日志中的IP地址并统计
grep "GET" access.log | awk '{print $1}' | sort | uniq -c | sort -nr

# 查找包含错误的日志行并保存
grep -i "error" /var/log/*.log | head -20 > errors.txt</code></pre>

                    <h3><i class="fas fa-tee"></i> 10.2 tee命令 - 同时输出到屏幕和文件</h3>
                    <p><code>tee</code>命令可以将输出同时显示在屏幕上并保存到文件中。</p>

                    <h4>实际案例</h4>
                    <pre><code># 同时显示和保存命令输出
ls -la | tee filelist.txt

# 追加到文件
echo "新内容" | tee -a log.txt

# 输出到多个文件
echo "重要信息" | tee file1.txt file2.txt file3.txt</code></pre>

                    <h3><i class="fas fa-compress"></i> 10.3 文件压缩和查看</h3>
                    <p>处理压缩文件中的文本内容。</p>

                    <h4>实际案例</h4>
                    <pre><code># 查看压缩文件内容
zcat file.txt.gz | head -10
zgrep "error" logfile.gz
zless bigfile.txt.gz</code></pre>

                    <h3><i class="fas fa-link"></i> 10.4 join命令 - 关联文件</h3>
                    <p><code>join</code>命令用于根据共同字段关联两个已排序的文件。</p>

                    <h4>实际案例</h4>
                    <pre><code># 创建两个关联文件
echo -e "1 张三\n2 李四\n3 王五" | sort > users.txt
echo -e "1 北京\n2 上海\n3 广州" | sort > locations.txt

# 关联文件
join users.txt locations.txt
# 输出：
# 1 张三 北京
# 2 李四 上海
# 3 王五 广州

# 指定分隔符
join -t ',' file1.csv file2.csv</code></pre>

                    <h3><i class="fas fa-random"></i> 10.5 shuf命令 - 随机排序</h3>
                    <p><code>shuf</code>命令用于随机打乱文件行的顺序。</p>

                    <h4>实际案例</h4>
                    <pre><code># 随机打乱文件行
shuf names.txt

# 随机选择n行
shuf -n 3 names.txt

# 生成随机数
shuf -i 1-100 -n 10  # 从1-100中随机选择10个数</code></pre>

                    <h3><i class="fas fa-eye"></i> 10.6 watch命令 - 实时监控</h3>
                    <p><code>watch</code>命令用于定期执行命令并显示结果。</p>

                    <h4>实际案例</h4>
                    <pre><code># 每2秒监控文件大小变化
watch -n 2 'ls -lh bigfile.txt'

# 监控日志文件最后几行
watch 'tail -n 5 /var/log/messages'

# 监控系统负载
watch 'uptime'</code></pre>

                    <h3><i class="fas fa-code"></i> 10.7 xargs命令 - 参数传递</h3>
                    <p><code>xargs</code>命令用于将标准输入转换为命令行参数。</p>

                    <h4>实际案例</h4>
                    <pre><code># 删除find找到的文件
find . -name "*.tmp" | xargs rm

# 统计多个文件的行数
echo "file1.txt file2.txt file3.txt" | xargs wc -l

# 批量处理文件
ls *.txt | xargs -I {} cp {} backup/

# 并行执行
echo -e "file1\nfile2\nfile3" | xargs -P 3 -I {} process_file {}</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-rocket"></i> 高级技巧组合：</strong>
                        <ul>
                            <li><strong>日志分析：</strong><code>tail -f /var/log/access.log | grep "ERROR" | tee error.log</code>
                            </li>
                            <li><strong>文件备份：</strong><code>find /home -name "*.conf" | xargs tar -czf config_backup.tar.gz</code>
                            </li>
                            <li><strong>批量重命名：</strong><code>ls *.txt | sed 's/\.txt$//' | xargs -I {} mv {}.txt {}.bak</code>
                            </li>
                            <li><strong>统计代码行数：</strong><code>find . -name "*.py" | xargs wc -l | tail -1</code></li>
                        </ul>
                    </div>
                </section>

                <section id="practical-examples">
                    <h2><span class="step-number">11</span>实战案例</h2>
                    <span class="command-tag cmd-advanced"><i class="fas fa-hands-helping"></i> 实战案例</span>

                    <h3><i class="fas fa-chart-line"></i> 11.1 日志分析案例</h3>
                    <p>分析Web服务器访问日志，提取有用信息。</p>

                    <h4>案例场景</h4>
                    <p>假设我们有一个Apache访问日志文件，需要分析访问情况。</p>

                    <pre><code># 创建模拟日志文件
cat > access.log << EOF
************* - - [10/Oct/2023:13:55:36 +0800] "GET /index.html HTTP/1.1" 200 2326
************* - - [10/Oct/2023:13:55:37 +0800] "GET /about.html HTTP/1.1" 200 1234
************* - - [10/Oct/2023:13:55:38 +0800] "POST /login HTTP/1.1" 404 512
************* - - [10/Oct/2023:13:55:39 +0800] "GET /index.html HTTP/1.1" 200 2326
EOF

# 统计访问最多的IP地址
awk '{print $1}' access.log | sort | uniq -c | sort -nr
# 输出：
#    2 *************
#    1 *************
#    1 *************

# 统计不同HTTP状态码的数量
awk '{print $9}' access.log | sort | uniq -c
# 输出：
#    3 200
#    1 404

# 提取404错误的请求
awk '$9 == 404 {print $0}' access.log

# 统计访问的页面
awk '{print $7}' access.log | sort | uniq -c | sort -nr</code></pre>

                    <h3><i class="fas fa-database"></i> 11.2 数据处理案例</h3>
                    <p>处理CSV格式的员工数据文件。</p>

                    <h4>案例场景</h4>
                    <pre><code># 创建员工数据文件
cat > employees.csv << EOF
姓名,部门,工资,入职日期
张三,技术部,8000,2020-01-15
李四,销售部,6000,2019-03-20
王五,技术部,9000,2021-06-10
赵六,人事部,5500,2018-12-01
钱七,技术部,7500,2020-08-25
EOF

# 计算技术部平均工资
grep "技术部" employees.csv | awk -F ',' '{sum+=$3; count++} END {print "技术部平均工资:", sum/count}'

# 按工资排序
sort -t ',' -k3 -nr employees.csv

# 统计各部门人数
awk -F ',' 'NR>1 {dept[$2]++} END {for(d in dept) print d, dept[d]}' employees.csv

# 查找工资超过7000的员工
awk -F ',' 'NR>1 && $3>7000 {print $1, $3}' employees.csv</code></pre>

                    <h3><i class="fas fa-cog"></i> 11.3 系统配置文件处理</h3>
                    <p>处理系统配置文件的常见任务。</p>

                    <h4>案例场景</h4>
                    <pre><code># 备份配置文件
cp /etc/hosts /etc/hosts.backup

# 添加新的主机记录
echo "************* newserver.local" >> /etc/hosts

# 查看非注释行
grep -v "^#" /etc/ssh/sshd_config | grep -v "^$"

# 统计用户shell类型
awk -F ':' '{print $7}' /etc/passwd | sort | uniq -c

# 查找特定用户信息
grep "^username:" /etc/passwd | cut -d ':' -f 1,3,6</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-lightbulb"></i> 实战技巧总结：</strong>
                        <ul>
                            <li><strong>组合使用：</strong>多个命令通过管道组合可以实现复杂功能</li>
                            <li><strong>备份重要：</strong>修改重要文件前一定要备份</li>
                            <li><strong>测试先行：</strong>在生产环境使用前先在测试环境验证</li>
                            <li><strong>脚本化：</strong>重复的操作可以写成脚本自动化执行</li>
                        </ul>
                    </div>
                </section>

                <section id="faq">
                    <h2><span class="step-number">12</span>常见问题解答</h2>

                    <h3><i class="fas fa-question"></i> 12.1 命令使用问题</h3>

                    <div class="info-box">
                        <h4><i class="fas fa-question-circle"></i> Q1: 为什么cat命令显示的中文乱码？</h4>
                        <p><strong>A:</strong> 这通常是字符编码问题。可以尝试：</p>
                        <pre><code># 检查文件编码
file -i filename.txt

# 转换编码
iconv -f gbk -t utf-8 filename.txt > newfile.txt

# 设置终端编码
export LANG=zh_CN.UTF-8</code></pre>
                    </div>

                    <div class="warning-box">
                        <h4><i class="fas fa-question-circle"></i> Q2: 使用vi编辑器时如何退出？</h4>
                        <p><strong>A:</strong> 这是新手最常遇到的问题：</p>
                        <ul>
                            <li>按<code>Esc</code>键确保在命令模式</li>
                            <li>输入<code>:q</code>退出（文件未修改）</li>
                            <li>输入<code>:q!</code>强制退出不保存</li>
                            <li>输入<code>:wq</code>保存并退出</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-question-circle"></i> Q3: grep搜索时如何忽略大小写？</h4>
                        <p><strong>A:</strong> 使用<code>-i</code>选项：</p>
                        <pre><code># 忽略大小写搜索
grep -i "apple" fruit.txt

# 组合其他选项
grep -in "apple" fruit.txt  # 忽略大小写并显示行号</code></pre>
                    </div>

                    <div class="danger-box">
                        <h4><i class="fas fa-question-circle"></i> Q4: 误删文件内容怎么办？</h4>
                        <p><strong>A:</strong> 预防和恢复措施：</p>
                        <ul>
                            <li><strong>预防：</strong>重要操作前先备份<code>cp file.txt file.txt.bak</code></li>
                            <li><strong>恢复：</strong>如果有备份，直接恢复<code>cp file.txt.bak file.txt</code></li>
                            <li><strong>部分恢复：</strong>使用<code>history</code>查看历史命令，重新执行</li>
                            <li><strong>系统恢复：</strong>使用系统快照或专业恢复工具</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-tools"></i> 12.2 性能和效率问题</h3>

                    <div class="info-box">
                        <h4><i class="fas fa-question-circle"></i> Q5: 处理大文件时命令很慢怎么办？</h4>
                        <p><strong>A:</strong> 针对大文件的优化策略：</p>
                        <ul>
                            <li>使用<code>less</code>而不是<code>cat</code>查看大文件</li>
                            <li>使用<code>head</code>或<code>tail</code>只查看部分内容</li>
                            <li>使用<code>grep</code>先过滤再处理</li>
                            <li>考虑使用<code>split</code>分割大文件</li>
                        </ul>
                        <pre><code># 示例：处理大日志文件
tail -n 1000 huge.log | grep "ERROR" | head -20</code></pre>
                    </div>

                    <div class="warning-box">
                        <h4><i class="fas fa-question-circle"></i> Q6: 如何安全地批量修改文件？</h4>
                        <p><strong>A:</strong> 安全的批量操作流程：</p>
                        <ol>
                            <li><strong>测试：</strong>先在小范围测试命令</li>
                            <li><strong>备份：</strong>批量操作前备份重要文件</li>
                            <li><strong>预览：</strong>使用不带<code>-i</code>的sed先预览结果</li>
                            <li><strong>执行：</strong>确认无误后再执行实际修改</li>
                        </ol>
                        <pre><code># 安全的批量替换流程
# 1. 先预览
sed 's/old/new/g' file.txt

# 2. 备份原文件
cp file.txt file.txt.backup

# 3. 执行修改
sed -i 's/old/new/g' file.txt</code></pre>
                    </div>

                    <h3><i class="fas fa-lightbulb"></i> 12.3 实用技巧问题</h3>

                    <div class="success-box">
                        <h4><i class="fas fa-question-circle"></i> Q7: 如何快速查找包含特定内容的文件？</h4>
                        <p><strong>A:</strong> 组合使用find和grep：</p>
                        <pre><code># 在当前目录及子目录中搜索
grep -r "关键词" .

# 只搜索特定类型文件
find . -name "*.txt" -exec grep -l "关键词" {} \;

# 更高效的方法
grep -r --include="*.txt" "关键词" .</code></pre>
                    </div>

                    <div class="info-box">
                        <h4><i class="fas fa-question-circle"></i> Q8: 如何统计代码项目的总行数？</h4>
                        <p><strong>A:</strong> 多种统计方法：</p>
                        <pre><code># 统计所有.py文件的行数
find . -name "*.py" | xargs wc -l

# 统计多种文件类型
find . \( -name "*.py" -o -name "*.js" -o -name "*.html" \) | xargs wc -l

# 排除空行和注释行（Python示例）
find . -name "*.py" | xargs grep -v "^$\|^#" | wc -l</code></pre>
                    </div>

                    <div class="warning-box">
                        <h4><i class="fas fa-question-circle"></i> Q9: 命令执行出错时如何调试？</h4>
                        <p><strong>A:</strong> 调试技巧：</p>
                        <ul>
                            <li><strong>查看错误信息：</strong>仔细阅读错误提示</li>
                            <li><strong>检查语法：</strong>确认命令语法正确</li>
                            <li><strong>分步执行：</strong>将复杂命令分解为简单步骤</li>
                            <li><strong>使用echo：</strong>先用echo测试命令构造</li>
                            <li><strong>查看帮助：</strong>使用<code>man 命令名</code>查看手册</li>
                        </ul>
                        <pre><code># 调试示例
# 先用echo测试
echo "sed 's/old/new/g' file.txt"

# 分步执行
cat file.txt | grep "pattern"
cat file.txt | grep "pattern" | sed 's/old/new/g'</code></pre>
                    </div>
                </section>

                <section id="summary">
                    <h2><span class="step-number">13</span>总结</h2>

                    <h3><i class="fas fa-bookmark"></i> 13.1 快速参考卡片</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-flash"></i> 最常用的10个命令（新手必会）：</h4>
                        <table style="margin: 15px 0;">
                            <tr>
                                <th>命令</th>
                                <th>功能</th>
                                <th>示例</th>
                            </tr>
                            <tr>
                                <td><code>cat</code></td>
                                <td>查看文件</td>
                                <td><code>cat file.txt</code></td>
                            </tr>
                            <tr>
                                <td><code>grep</code></td>
                                <td>搜索内容</td>
                                <td><code>grep "word" file.txt</code></td>
                            </tr>
                            <tr>
                                <td><code>head</code></td>
                                <td>查看开头</td>
                                <td><code>head -10 file.txt</code></td>
                            </tr>
                            <tr>
                                <td><code>tail</code></td>
                                <td>查看结尾</td>
                                <td><code>tail -f log.txt</code></td>
                            </tr>
                            <tr>
                                <td><code>wc</code></td>
                                <td>统计信息</td>
                                <td><code>wc -l file.txt</code></td>
                            </tr>
                            <tr>
                                <td><code>sort</code></td>
                                <td>排序</td>
                                <td><code>sort file.txt</code></td>
                            </tr>
                            <tr>
                                <td><code>uniq</code></td>
                                <td>去重</td>
                                <td><code>sort file.txt | uniq</code></td>
                            </tr>
                            <tr>
                                <td><code>sed</code></td>
                                <td>替换</td>
                                <td><code>sed 's/old/new/g' file.txt</code></td>
                            </tr>
                            <tr>
                                <td><code>awk</code></td>
                                <td>文本处理</td>
                                <td><code>awk '{print $1}' file.txt</code></td>
                            </tr>
                            <tr>
                                <td><code>vim</code></td>
                                <td>编辑文件</td>
                                <td><code>vim file.txt</code></td>
                            </tr>
                        </table>
                    </div>

                    <h3><i class="fas fa-graduation-cap"></i> 13.2 完整命令总结表</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-terminal"></i> 命令</th>
                            <th><i class="fas fa-tag"></i> 类型</th>
                            <th><i class="fas fa-info"></i> 主要功能</th>
                            <th><i class="fas fa-star"></i> 重要程度</th>
                        </tr>
                        <tr>
                            <td><code>cat</code></td>
                            <td>查看</td>
                            <td>显示文件全部内容</td>
                            <td>⭐⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td><code>less/more</code></td>
                            <td>查看</td>
                            <td>分页查看文件</td>
                            <td>⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td><code>head/tail</code></td>
                            <td>查看</td>
                            <td>查看文件开头/结尾</td>
                            <td>⭐⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td><code>grep</code></td>
                            <td>搜索</td>
                            <td>搜索文件内容</td>
                            <td>⭐⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td><code>vi/vim</code></td>
                            <td>编辑</td>
                            <td>强大的文本编辑器</td>
                            <td>⭐⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td><code>nano</code></td>
                            <td>编辑</td>
                            <td>简单的文本编辑器</td>
                            <td>⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td><code>sed</code></td>
                            <td>编辑</td>
                            <td>流编辑器，批量修改</td>
                            <td>⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td><code>awk</code></td>
                            <td>处理</td>
                            <td>强大的文本处理工具</td>
                            <td>⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td><code>sort</code></td>
                            <td>处理</td>
                            <td>排序文件内容</td>
                            <td>⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td><code>uniq</code></td>
                            <td>处理</td>
                            <td>去除重复行</td>
                            <td>⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td><code>wc</code></td>
                            <td>统计</td>
                            <td>统计行数、单词数、字符数</td>
                            <td>⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td><code>nl</code></td>
                            <td>处理</td>
                            <td>添加行号</td>
                            <td>⭐⭐</td>
                        </tr>
                        <tr>
                            <td><code>paste</code></td>
                            <td>处理</td>
                            <td>合并文件列</td>
                            <td>⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td><code>split</code></td>
                            <td>处理</td>
                            <td>分割大文件</td>
                            <td>⭐⭐</td>
                        </tr>
                        <tr>
                            <td><code>join</code></td>
                            <td>处理</td>
                            <td>关联文件</td>
                            <td>⭐⭐</td>
                        </tr>
                        <tr>
                            <td><code>xargs</code></td>
                            <td>高级</td>
                            <td>参数传递和批处理</td>
                            <td>⭐⭐⭐⭐</td>
                        </tr>
                        <tr>
                            <td><code>tee</code></td>
                            <td>高级</td>
                            <td>同时输出到屏幕和文件</td>
                            <td>⭐⭐⭐</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-route"></i> 13.3 学习路径建议</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-map"></i> 进阶学习建议：</h4>
                        <ol>
                            <li><strong>掌握基础：</strong>熟练使用cat、grep、vi等基本命令</li>
                            <li><strong>学习组合：</strong>掌握管道和重定向的使用</li>
                            <li><strong>正则表达式：</strong>深入学习正则表达式语法</li>
                            <li><strong>脚本编程：</strong>学习Shell脚本编程</li>
                            <li><strong>高级工具：</strong>学习awk、sed等高级文本处理工具</li>
                            <li><strong>实际应用：</strong>在实际工作中多练习和应用</li>
                        </ol>
                    </div>

                    <h3><i class="fas fa-book"></i> 13.4 推荐资源</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-external-link-alt"></i> 进一步学习资源：</h4>
                        <ul>
                            <li><strong>官方文档：</strong>使用<code>man 命令名</code>查看详细手册</li>
                            <li><strong>在线练习：</strong>Linux命令在线练习网站</li>
                            <li><strong>书籍推荐：</strong>《Linux命令行与shell脚本编程大全》</li>
                            <li><strong>视频教程：</strong>各大在线教育平台的Linux课程</li>
                            <li><strong>实践环境：</strong>搭建虚拟机或使用云服务器练习</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-heart"></i> 13.5 结语</h3>
                    <p>恭喜您完成了Linux文件内容操作命令的学习！这些命令是Linux系统管理和开发工作的基础，熟练掌握它们将大大提高您的工作效率。</p>

                    <div class="warning-box">
                        <strong><i class="fas fa-lightbulb"></i> 最后的建议：</strong>
                        <ul>
                            <li><strong>多练习：</strong>理论学习后一定要多动手练习</li>
                            <li><strong>做笔记：</strong>记录常用的命令组合和技巧</li>
                            <li><strong>善用帮助：</strong>遇到问题时使用man命令查看帮助</li>
                            <li><strong>安全第一：</strong>在生产环境操作时要格外小心</li>
                            <li><strong>持续学习：</strong>Linux命令博大精深，需要持续学习和实践</li>
                        </ul>
                    </div>

                    <div
                        style="text-align: center; margin-top: 50px; padding: 30px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px; color: white;">
                        <h3><i class="fas fa-trophy"></i> 学习完成！</h3>
                        <p>您已经掌握了Linux文件内容操作的核心技能</p>
                        <p>现在可以开始您的Linux实践之旅了！</p>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <!-- 返回顶部按钮 -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // 移动端菜单切换
        document.getElementById('mobileMenuBtn').addEventListener('click', function () {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('active');
        });

        // 返回顶部功能
        const backToTop = document.getElementById('backToTop');

        window.addEventListener('scroll', function () {
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'flex';
            } else {
                backToTop.style.display = 'none';
            }
        });

        backToTop.addEventListener('click', function (e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 侧边栏导航高亮
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.sidebar a[href^="#"]');

        window.addEventListener('scroll', function () {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // 平滑滚动
        navLinks.forEach(link => {
            link.addEventListener('click', function (e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetSection = document.getElementById(targetId);
                if (targetSection) {
                    targetSection.scrollIntoView({
                        behavior: 'smooth'
                    });
                }

                // 移动端关闭菜单
                if (window.innerWidth <= 768) {
                    document.getElementById('sidebar').classList.remove('active');
                }
            });
        });

        // 点击页面其他地方关闭移动端菜单
        document.addEventListener('click', function (e) {
            const sidebar = document.getElementById('sidebar');
            const menuBtn = document.getElementById('mobileMenuBtn');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(e.target) &&
                !menuBtn.contains(e.target)) {
                sidebar.classList.remove('active');
            }
        });
    </script>
</body>

</html>