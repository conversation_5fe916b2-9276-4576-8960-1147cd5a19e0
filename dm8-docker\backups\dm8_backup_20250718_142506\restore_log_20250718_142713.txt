[2025-07-18 14:27:13] [INFO] 开始DM8 Docker完整恢复
[2025-07-18 14:27:13] [INFO] 备份路径: D:\Code\MicrosoftCode\dm8-docker\backups\dm8_backup_20250718_142506
[2025-07-18 14:27:13] [INFO] 恢复模式: 完整恢复 (项目文件 + Docker配置 + 数据卷)
[2025-07-18 14:27:13] [INFO] 临时目录: D:\Code\MicrosoftCode\dm8-docker\temp_restore_20250718_142713
[2025-07-18 14:27:13] [SUCCESS] 临时目录创建成功
[2025-07-18 14:27:13] [INFO] 检查Docker环境...
[2025-07-18 14:27:13] [SUCCESS] Docker版本: Docker version 28.0.4, build b8034c0
[2025-07-18 14:27:13] [INFO] 验证备份文件...
[2025-07-18 14:27:13] [SUCCESS] 找到数据卷备份: dm8_data_volume/ (8660.1MB)
[2025-07-18 14:27:13] [SUCCESS] 找到日志卷备份: dm8_logs_volume.zip (0.03MB)
[2025-07-18 14:27:13] [SUCCESS] 找到备份目录: project_files/ (901.88MB)
[2025-07-18 14:27:13] [INFO] 停止现有服务...
[2025-07-18 14:27:25] [SUCCESS] 现有服务已停止
[2025-07-18 14:27:25] [INFO] 删除现有数据卷...
[2025-07-18 14:27:25] [SUCCESS] 已删除数据卷: dm8_data_volumes
[2025-07-18 14:27:25] [SUCCESS] 已删除数据卷: dm8_logs_volumes
[2025-07-18 14:27:25] [INFO] 恢复项目文件...
[2025-07-18 14:27:25] [SUCCESS] 已恢复: docker-compose.yml
[2025-07-18 14:27:25] [SUCCESS] 已恢复: Dockerfile
[2025-07-18 14:27:25] [SUCCESS] 已恢复: docker-entrypoint.sh
[2025-07-18 14:27:25] [SUCCESS] 已恢复: install_dm8.exp
[2025-07-18 14:27:25] [SUCCESS] 已恢复: test-connection.py
[2025-07-18 14:27:25] [SUCCESS] 已恢复: dm8_20250506_x86_rh7_64 目录
[2025-07-18 14:27:25] [SUCCESS] 项目文件恢复完成，已恢复 6 个文件/目录
[2025-07-18 14:27:25] [INFO] 创建新数据卷...
[2025-07-18 14:27:25] [SUCCESS] 已创建数据卷: dm8_data_volumes
[2025-07-18 14:27:25] [SUCCESS] 已创建数据卷: dm8_logs_volumes
[2025-07-18 14:27:25] [INFO] 恢复DM8主数据卷...
[2025-07-18 14:27:25] [INFO] 使用未压缩的数据卷备份...
[2025-07-18 14:30:03] [INFO] 修复数据目录权限...
[2025-07-18 14:30:04] [SUCCESS] DM8主数据卷恢复完成
[2025-07-18 14:30:04] [INFO] 恢复DM8日志数据卷...
[2025-07-18 14:30:04] [INFO] 解压日志卷备份文件...
[2025-07-18 14:30:04] [INFO] 修复日志目录权限...
[2025-07-18 14:30:05] [SUCCESS] DM8日志数据卷恢复完成
[2025-07-18 14:30:05] [INFO] 启动DM8服务...
[2025-07-18 14:30:06] [INFO] 等待DM8启动...
[2025-07-18 14:30:06] [INFO] 等待60秒让DM8完全启动...
[2025-07-18 14:31:06] [SUCCESS] DM8容器启动成功
[2025-07-18 14:31:06] [INFO] 验证恢复结果...
[2025-07-18 14:31:06] [SUCCESS] 容器状态: 运行中
[2025-07-18 14:31:06] [SUCCESS] 容器健康状态: 健康
[2025-07-18 14:31:06] [INFO] 测试数据库连接...
[2025-07-18 14:31:09] [WARN] 数据库连接: 暂时无法连接 (数据库可能仍在启动中)
[2025-07-18 14:31:09] [INFO] 建议等待几分钟后手动验证数据库状态
[2025-07-18 14:31:09] [INFO] 已清理临时目录
[2025-07-18 14:31:09] [SUCCESS] 恢复成功完成
[2025-07-18 14:31:09] [SUCCESS] DM8服务可在以下地址访问: localhost:5236
[2025-07-18 14:31:09] [SUCCESS] 用户名: SYSDBA, 密码: GDYtd@2025
[2025-07-18 14:31:09] [SUCCESS] docker exec -it dm8-database /home/<USER>/dmdbms/bin/disql SYSDBA/'"GDYtd@2025"'@localhost:5236
