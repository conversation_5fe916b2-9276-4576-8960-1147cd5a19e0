#!/bin/bash

# 测试nginx离线构建脚本
# 用途：验证修正后的构建方案

set -e

echo "================================================================================"
echo "                        测试nginx离线构建"
echo "================================================================================"

# 检查当前目录结构
echo "=== 检查项目结构 ==="
echo "当前目录内容："
ls -la

echo ""
echo "packages目录内容："
if [ -d "packages" ]; then
    ls -la packages/
else
    echo "❌ packages目录不存在"
fi

echo ""
echo "centos7-rpms目录内容："
if [ -d "centos7-rpms" ]; then
    ls -la centos7-rpms/ | head -10
    echo "RPM包总数: $(ls centos7-rpms/*.rpm 2>/dev/null | wc -l)"
else
    echo "❌ centos7-rpms目录不存在"
fi

echo ""
echo "scripts目录内容："
if [ -d "scripts" ]; then
    ls -la scripts/
else
    echo "❌ scripts目录不存在"
fi

echo ""
echo "=== 检查关键文件 ==="

# 检查nginx源码包
if [ -f "packages/nginx-1.24.0.tar.gz" ]; then
    echo "✓ nginx源码包存在"
    echo "  文件大小: $(du -h packages/nginx-1.24.0.tar.gz | cut -f1)"
else
    echo "❌ nginx源码包不存在"
fi

# 检查不应该存在的源码包
for pkg in "pcre-8.45.tar.gz" "zlib-1.2.13.tar.gz" "openssl-1.1.1w.tar.gz"; do
    if [ -f "packages/$pkg" ]; then
        echo "⚠️  警告: $pkg 仍然存在，但现在应该使用RPM包"
    else
        echo "✓ $pkg 不存在（正确，应使用RPM包）"
    fi
done

# 检查构建脚本
if [ -f "scripts/build-nginx.sh" ]; then
    echo "✓ 构建脚本存在"
    echo "  检查脚本内容..."
    if grep -q "rpm -ivh" scripts/build-nginx.sh; then
        echo "  ✓ 脚本包含RPM安装命令"
    else
        echo "  ❌ 脚本缺少RPM安装命令"
    fi
    
    if grep -q "tar.*pcre.*tar.gz" scripts/build-nginx.sh; then
        echo "  ❌ 脚本仍包含PCRE源码解压命令"
    else
        echo "  ✓ 脚本不包含PCRE源码解压命令"
    fi
    
    if grep -q "tar.*zlib.*tar.gz" scripts/build-nginx.sh; then
        echo "  ❌ 脚本仍包含zlib源码解压命令"
    else
        echo "  ✓ 脚本不包含zlib源码解压命令"
    fi
    
    if grep -q "tar.*openssl.*tar.gz" scripts/build-nginx.sh; then
        echo "  ❌ 脚本仍包含OpenSSL源码解压命令"
    else
        echo "  ✓ 脚本不包含OpenSSL源码解压命令"
    fi
else
    echo "❌ 构建脚本不存在"
fi

echo ""
echo "=== 检查RPM包完整性 ==="
if [ -d "centos7-rpms" ]; then
    # 检查关键RPM包
    key_rpms=("gcc" "gcc-c++" "make" "pcre-devel" "zlib-devel" "openssl-devel")
    for rpm in "${key_rpms[@]}"; do
        if ls centos7-rpms/${rpm}-*.rpm >/dev/null 2>&1; then
            echo "✓ $rpm RPM包存在"
        else
            echo "❌ $rpm RPM包缺失"
        fi
    done
fi

echo ""
echo "=== 模拟构建测试 ==="
echo "检查Dockerfile..."
if [ -f "Dockerfile" ]; then
    echo "✓ Dockerfile存在"
    
    # 检查Dockerfile是否正确
    if grep -q "COPY packages/" Dockerfile; then
        echo "  ✓ Dockerfile复制packages目录"
    fi
    
    if grep -q "COPY centos7-rpms/" Dockerfile; then
        echo "  ✓ Dockerfile复制centos7-rpms目录"
    fi
    
    if grep -q "rpm -ivh.*centos7-rpms" Dockerfile; then
        echo "  ✓ Dockerfile包含RPM安装命令"
    fi
else
    echo "❌ Dockerfile不存在"
fi

echo ""
echo "================================================================================"
echo "                              测试结果"
echo "================================================================================"
echo "如果以上检查都通过，说明修正方案正确："
echo "1. ✓ 只有nginx使用源码包编译"
echo "2. ✓ PCRE、zlib、OpenSSL使用RPM包安装"
echo "3. ✓ 构建脚本已修正，不再解压不存在的源码包"
echo ""
echo "现在可以运行: ./build-nginx-offline.sh"
echo "================================================================================"
