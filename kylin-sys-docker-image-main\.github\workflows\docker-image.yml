name: Publish Docker image

on:
  workflow_dispatch:

env:
  TEST_TAG: user/app:test

jobs:
  push_to_registry:
    strategy:
      matrix:
        SP_VERSION: [1, 2, 3]

    name: Push Docker image to Docker Hub
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          platforms: linux/amd64,linux/arm64
          context: .
          build-args: |
            SP_VERSION=${{ matrix.SP_VERSION }}
          file: ./kylin_v10.sys.Dockerfile
          push: true
          tags: hxsoong/kylin:v10-sp${{ matrix.SP_VERSION }}
