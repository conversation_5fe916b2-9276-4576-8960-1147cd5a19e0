#!/bin/bash

# 离线nginx Docker构建 - 完整依赖包下载脚本
# 用途：在联网环境下载所有必要的nginx源码包和CentOS 7编译依赖RPM包

set -e

echo "================================================================================"
echo "                    离线nginx Docker构建 - 依赖包下载脚本"
echo "================================================================================"
echo "用途：下载nginx源码包和CentOS 7编译依赖RPM包"
echo "环境：必须在联网环境执行"
echo "================================================================================"

# 定义版本号
NGINX_VERSION="1.24.0"
PCRE_VERSION="8.45"
ZLIB_VERSION="1.3.1"
OPENSSL_VERSION="1.1.1w"

# 创建目录
echo "创建下载目录..."
mkdir -p packages
mkdir -p centos7-rpms

echo ""
echo "=== 第一步：下载nginx及其依赖源码包 ==="

# 下载nginx源码包（只下载nginx，其他依赖用RPM包）
echo "下载nginx ${NGINX_VERSION}..."
if [ ! -f "packages/nginx-${NGINX_VERSION}.tar.gz" ]; then
    wget -O packages/nginx-${NGINX_VERSION}.tar.gz \
        http://nginx.org/download/nginx-${NGINX_VERSION}.tar.gz
    echo "✓ nginx源码包下载完成"
else
    echo "✓ nginx源码包已存在"
fi

echo "ℹ️  注意：PCRE、zlib、OpenSSL将使用RPM包安装，不需要下载源码包"

echo ""
echo "=== 第二步：下载CentOS 7编译依赖RPM包 ==="

# CentOS 7镜像源
CENTOS_BASE_URL="http://vault.centos.org/7.9.2009/os/x86_64/Packages"
CENTOS_UPDATES_URL="http://vault.centos.org/7.9.2009/updates/x86_64/Packages"
ALI_BASE_URL="https://mirrors.aliyun.com/centos-vault/7.9.2009/os/x86_64/Packages"
ALI_UPDATES_URL="https://mirrors.aliyun.com/centos-vault/7.9.2009/updates/x86_64/Packages"

# 定义需要下载的RPM包
declare -A rpm_packages=(
    # 基础编译工具
    ["gcc"]="gcc-4.8.5-44.el7.x86_64.rpm"
    ["gcc-c++"]="gcc-c++-4.8.5-44.el7.x86_64.rpm"
    ["make"]="make-3.82-24.el7.x86_64.rpm"
    
    # 开发库
    ["pcre-devel"]="pcre-devel-8.32-17.el7.x86_64.rpm"
    ["zlib-devel"]="zlib-devel-1.2.7-18.el7.x86_64.rpm"
    ["openssl-devel"]="openssl-devel-1.0.2k-25.el7_9.x86_64.rpm"
    ["GeoIP-devel"]="GeoIP-devel-1.5.0-14.el7.x86_64.rpm"
    
    # 基础依赖包
    ["glibc-devel"]="glibc-devel-2.17-326.el7_9.x86_64.rpm"
    ["glibc-headers"]="glibc-headers-2.17-326.el7_9.x86_64.rpm"
    ["kernel-headers"]="kernel-headers-3.10.0-1160.102.1.el7.x86_64.rpm"
    ["libgcc"]="libgcc-4.8.5-44.el7.x86_64.rpm"
    ["libstdc++"]="libstdc++-4.8.5-44.el7.x86_64.rpm"
    ["libstdc++-devel"]="libstdc++-devel-4.8.5-44.el7.x86_64.rpm"
    ["pcre"]="pcre-8.32-17.el7.x86_64.rpm"
    ["zlib"]="zlib-1.2.7-18.el7.x86_64.rpm"
    ["openssl"]="openssl-1.0.2k-25.el7_9.x86_64.rpm"
    ["openssl-libs"]="openssl-libs-1.0.2k-25.el7_9.x86_64.rpm"
    ["GeoIP"]="GeoIP-1.5.0-14.el7.x86_64.rpm"
    ["krb5-devel"]="krb5-devel-1.15.1-55.el7_9.x86_64.rpm"
    ["libcom_err-devel"]="libcom_err-devel-1.42.9-19.el7.x86_64.rpm"
    ["libselinux-devel"]="libselinux-devel-2.5-15.el7.x86_64.rpm"
    ["libsepol-devel"]="libsepol-devel-2.5-10.el7.x86_64.rpm"
    ["libverto-devel"]="libverto-devel-0.2.5-4.el7.x86_64.rpm"
    ["keyutils-libs-devel"]="keyutils-libs-devel-1.5.8-3.el7.x86_64.rpm"
)

# 下载RPM包函数
download_rpm() {
    local package_name=$1
    local rpm_file=$2
    
    if [ -f "centos7-rpms/$rpm_file" ]; then
        echo "✓ $package_name ($rpm_file) 已存在"
        return 0
    fi
    
    echo "正在下载: $package_name ($rpm_file)"
    
    # 尝试从多个镜像源下载
    for base_url in "$CENTOS_BASE_URL" "$CENTOS_UPDATES_URL" "$ALI_BASE_URL" "$ALI_UPDATES_URL"; do
        if wget -q --spider "$base_url/$rpm_file" 2>/dev/null; then
            echo "  从 $base_url 下载..."
            if wget -O "centos7-rpms/$rpm_file" "$base_url/$rpm_file" 2>/dev/null; then
                echo "  ✓ $package_name 下载成功"
                return 0
            fi
        fi
    done
    
    echo "  ✗ $package_name 下载失败"
    return 1
}

# 下载所有RPM包
failed_packages=()
for package in "${!rpm_packages[@]}"; do
    rpm_file="${rpm_packages[$package]}"
    if ! download_rpm "$package" "$rpm_file"; then
        failed_packages+=("$package")
    fi
done

echo ""
echo "=== 下载结果汇总 ==="
echo "nginx源码包："
ls -la packages/

echo ""
echo "CentOS 7 RPM包："
ls -la centos7-rpms/

if [ ${#failed_packages[@]} -gt 0 ]; then
    echo ""
    echo "⚠️  以下包下载失败，请手动下载："
    for pkg in "${failed_packages[@]}"; do
        echo "  - $pkg (${rpm_packages[$pkg]})"
    done
fi

echo ""
echo "================================================================================"
echo "                              下载完成"
echo "================================================================================"
echo "✓ nginx源码包已下载到: packages/ 目录"
echo "✓ CentOS 7 RPM包已下载到: centos7-rpms/ 目录"
echo ""
echo "下一步："
echo "1. 将整个项目目录打包: tar -czf nginx-offline-build.tar.gz ."
echo "2. 传输到断网环境"
echo "3. 在断网环境运行: ./build-nginx-offline.sh"
echo "================================================================================"
