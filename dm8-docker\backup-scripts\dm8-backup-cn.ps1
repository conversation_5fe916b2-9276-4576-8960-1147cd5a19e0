# DM8 数据库完整备份脚本
# 版本: 1.0
# 作者: AI Assistant
# 日期: 2025-07-17

param(
    [string]$BackupDir = ".\backups",
    [switch]$ForceBackup,
    [switch]$VerboseLog
)

# 全局变量
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$BackupName = "dm8_backup_$Timestamp"
$BackupPath = Join-Path $BackupDir $BackupName
$TempDir = Join-Path $env:TEMP "temp_backup_$Timestamp"
$LogFile = Join-Path $BackupPath "backup_log.txt"

# 日志记录函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogContent = "[$Time] [$Level] $Message"

    switch ($Level) {
        "ERROR" { Write-Host $LogContent -ForegroundColor Red }
        "WARN"  { Write-Host $LogContent -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $LogContent -ForegroundColor Green }
        default { Write-Host $LogContent -ForegroundColor White }
    }

    if (Test-Path $BackupPath) {
        $LogContent | Add-Content -Path $LogFile -Encoding UTF8
    }
}

# 进度显示函数
function Show-Progress {
    param([string]$Activity, [string]$Status, [int]$PercentComplete)
    Write-Progress -Activity $Activity -Status $Status -PercentComplete $PercentComplete
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "DM8 数据库完整备份脚本 v1.0" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Log "开始DM8完整备份"
Write-Log "备份目录: $BackupPath"
Write-Log "临时目录: $TempDir"

# 检查backups目录权限
if (!(Test-Path $BackupDir)) {
    try {
        New-Item -ItemType Directory -Path $BackupDir -Force -ErrorAction Stop | Out-Null
        Write-Log "创建backups根目录成功" "SUCCESS"
    } catch {
        Write-Log "无法创建backups目录: $($_.Exception.Message)" "ERROR"
        Write-Log "请以管理员身份运行PowerShell" "ERROR"
        exit 1
    }
}

# 创建目录
try {
    if (!(Test-Path $BackupPath)) {
        New-Item -ItemType Directory -Path $BackupPath -Force -ErrorAction Stop | Out-Null
    }
    if (!(Test-Path $TempDir)) {
        New-Item -ItemType Directory -Path $TempDir -Force -ErrorAction Stop | Out-Null
    }
    Write-Log "备份目录创建成功" "SUCCESS"
} catch {
    Write-Log "创建备份目录失败: $($_.Exception.Message)" "ERROR"
    Write-Log "请检查目录权限或以管理员身份运行" "ERROR"
    exit 1
}

try {
    # 步骤1: 检查Docker环境
    Show-Progress "环境检查" "检查Docker环境..." 10
    Write-Log "检查Docker环境..."
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker未运行或未安装"
    }
    Write-Log "Docker版本: $dockerVersion" "SUCCESS"

    # 步骤2: 检查容器状态
    Show-Progress "环境检查" "检查DM8容器状态..." 20
    Write-Log "检查DM8容器状态..."
    $ContainerStatus = docker inspect dm8-server --format='{{.State.Status}}' 2>$null
    if ($ContainerStatus -ne "running") {
        Write-Log "警告: DM8容器未运行，状态: $ContainerStatus" "WARN"
        if (-not $ForceBackup) {
            throw "容器未运行，使用 -ForceBackup 参数强制备份"
        }
    } else {
        Write-Log "DM8容器运行正常" "SUCCESS"
    }

    # 步骤3: 创建备份信息文件
    Show-Progress "备份信息" "创建备份信息文件..." 40
    Write-Log "创建备份信息文件"
    try {
        $backupInfo = @{
            backup_time = $Timestamp
            backup_name = $BackupName
            container_name = "dm8-server"
            data_volume = "dm8_data_volumes"
            logs_volume = "dm8_logs_volumes"
            backup_type = "完整备份"
            dm8_version = docker exec dm8-server cat /opt/dmdbms/version.txt 2>$null
        }
        $backupInfo | ConvertTo-Json -Depth 3 | Out-File -FilePath (Join-Path $BackupPath "backup_info.json") -Encoding UTF8
        Write-Log "备份信息文件创建成功" "SUCCESS"
    } catch {
        Write-Log "备份信息文件创建失败: $($_.Exception.Message)" "WARN"
    }

    # 步骤4: 备份项目文件
    Show-Progress "备份项目文件" "备份Docker配置文件和项目文件..." 50
    Write-Log "备份项目文件"

    # 创建项目文件备份目录
    $ProjectFilesDir = Join-Path $BackupPath "project_files"
    try {
        New-Item -ItemType Directory -Path $ProjectFilesDir -Force -ErrorAction Stop | Out-Null
    } catch {
        Write-Log "创建项目文件目录失败: $($_.Exception.Message)" "ERROR"
        return
    }

    $ConfigFiles = @("docker-compose.yml", "Dockerfile", "docker-entrypoint.sh", "install_dm8.exp", "test-connection.py", "dm.ini")
    $BackedUpFiles = 0
    foreach ($File in $ConfigFiles) {
        if (Test-Path $File) {
            Copy-Item $File -Destination $ProjectFilesDir -Force
            Write-Log "已备份: $File" "SUCCESS"
            $BackedUpFiles++
        } else {
            Write-Log "文件不存在: $File" "WARN"
        }
    }

    # 备份dm8安装文件目录（如果存在）
    if (Test-Path "dm8_20250506_x86_rh7_64") {
        Copy-Item "dm8_20250506_x86_rh7_64" -Destination $ProjectFilesDir -Recurse -Force
        Write-Log "已备份: dm8_20250506_x86_rh7_64 目录" "SUCCESS"
        $BackedUpFiles++
    }

    Write-Log "项目文件备份完成，已备份 $BackedUpFiles 个文件/目录" "SUCCESS"

    # 步骤5: 停止容器确保数据一致性
    if ($ContainerStatus -eq "running") {
        Show-Progress "准备数据卷" "停止容器确保数据一致性..." 60
        Write-Log "停止DM8容器确保数据一致性..."
        docker-compose stop dm8 2>&1 | Out-Null
        Start-Sleep -Seconds 5
        Write-Log "容器已停止" "SUCCESS"
    }

    # 步骤6: 备份数据卷
    Show-Progress "备份数据卷" "备份DM8数据卷..." 70
    Write-Log "备份DM8数据卷..."

    # 备份主数据卷 - 使用正确的数据卷名称
    Write-Log "备份主数据卷: dm8_data_volumes"
    try {
        $DataBackupContainer = docker create -v dm8_data_volumes:/data alpine 2>&1
        if ($LASTEXITCODE -eq 0) {
            docker cp "${DataBackupContainer}:/data" "${TempDir}/dm8_data" 2>&1 | Out-Null
            docker rm $DataBackupContainer 2>&1 | Out-Null

            if (Test-Path "${TempDir}/dm8_data") {
                # 检查数据大小
                $DataSize = (Get-ChildItem "${TempDir}/dm8_data" -Recurse | Measure-Object -Property Length -Sum).Sum
                $DataSizeMB = [math]::Round($DataSize / 1MB, 2)
                Write-Log "数据卷大小: ${DataSizeMB}MB"

                if ($DataSizeMB -gt 2000) {
                    # 如果数据超过2GB，直接复制文件夹，不压缩
                    Write-Log "数据卷过大(${DataSizeMB}MB)，跳过压缩直接复制文件夹" "WARN"
                    Copy-Item "${TempDir}/dm8_data" -Destination "${TempDir}/dm8_data_volume" -Recurse -Force
                    Write-Log "主数据卷备份完成(未压缩)" "SUCCESS"
                } else {
                    # 小于2GB才压缩
                    try {
                        Compress-Archive -Path "${TempDir}/dm8_data" -DestinationPath "${TempDir}/dm8_data_volume.zip" -Force
                        Write-Log "主数据卷备份完成" "SUCCESS"
                    } catch {
                        Write-Log "压缩失败，改为直接复制: $($_.Exception.Message)" "WARN"
                        Copy-Item "${TempDir}/dm8_data" -Destination "${TempDir}/dm8_data_volume" -Recurse -Force
                        Write-Log "主数据卷备份完成(未压缩)" "SUCCESS"
                    }
                }
            } else {
                Write-Log "主数据卷数据复制失败" "ERROR"
            }
        } else {
            Write-Log "创建备份容器失败: $DataBackupContainer" "ERROR"
        }
    } catch {
        Write-Log "主数据卷备份异常: $($_.Exception.Message)" "ERROR"
    }

    # 备份日志数据卷
    Write-Log "备份日志数据卷: dm8_logs_volumes"
    try {
        $LogsBackupContainer = docker create -v dm8_logs_volumes:/logs alpine 2>&1
        if ($LASTEXITCODE -eq 0) {
            docker cp "${LogsBackupContainer}:/logs" "${TempDir}/dm8_logs" 2>&1 | Out-Null
            docker rm $LogsBackupContainer 2>&1 | Out-Null

            if (Test-Path "${TempDir}/dm8_logs") {
                # 检查日志大小
                $LogsSize = (Get-ChildItem "${TempDir}/dm8_logs" -Recurse | Measure-Object -Property Length -Sum).Sum
                $LogsSizeMB = [math]::Round($LogsSize / 1MB, 2)
                Write-Log "日志卷大小: ${LogsSizeMB}MB"

                if ($LogsSizeMB -gt 2000) {
                    # 如果日志超过2GB，直接复制文件夹，不压缩
                    Write-Log "日志卷过大(${LogsSizeMB}MB)，跳过压缩直接复制文件夹" "WARN"
                    Copy-Item "${TempDir}/dm8_logs" -Destination "${TempDir}/dm8_logs_volume" -Recurse -Force
                    Write-Log "日志数据卷备份完成(未压缩)" "SUCCESS"
                } else {
                    # 小于2GB才压缩
                    try {
                        Compress-Archive -Path "${TempDir}/dm8_logs" -DestinationPath "${TempDir}/dm8_logs_volume.zip" -Force
                        Write-Log "日志数据卷备份完成" "SUCCESS"
                    } catch {
                        Write-Log "日志压缩失败，改为直接复制: $($_.Exception.Message)" "WARN"
                        Copy-Item "${TempDir}/dm8_logs" -Destination "${TempDir}/dm8_logs_volume" -Recurse -Force
                        Write-Log "日志数据卷备份完成(未压缩)" "SUCCESS"
                    }
                }
            } else {
                Write-Log "日志数据卷数据复制失败" "WARN"
            }
        } else {
            Write-Log "创建日志备份容器失败: $LogsBackupContainer" "WARN"
        }
    } catch {
        Write-Log "日志数据卷备份异常: $($_.Exception.Message)" "WARN"
    }

    # 步骤7: 备份容器配置信息
    Show-Progress "备份配置" "备份容器配置信息..." 80
    Write-Log "备份容器配置信息"
    try {
        $containerConfig = docker inspect dm8-server 2>$null | ConvertFrom-Json
        $containerConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath (Join-Path $BackupPath "container_config.json") -Encoding UTF8
        Write-Log "容器配置信息备份完成" "SUCCESS"
    } catch {
        Write-Log "容器配置信息备份失败: $($_.Exception.Message)" "WARN"
    }

    # 移动备份文件到备份目录
    $DataFileZip = Join-Path $TempDir "dm8_data_volume.zip"
    $DataFileDir = Join-Path $TempDir "dm8_data_volume"
    $LogsFileZip = Join-Path $TempDir "dm8_logs_volume.zip"
    $LogsFileDir = Join-Path $TempDir "dm8_logs_volume"

    # 移动主数据备份文件（压缩或未压缩）
    if (Test-Path $DataFileZip) {
        $TargetFile = Join-Path $BackupPath (Split-Path $DataFileZip -Leaf)
        Move-Item $DataFileZip $TargetFile -Force
        $Size = [math]::Round((Get-Item $TargetFile).Length / 1MB, 2)
        Write-Log "已移动: $(Split-Path $DataFileZip -Leaf) (${Size}MB)" "SUCCESS"
    } elseif (Test-Path $DataFileDir) {
        $TargetDir = Join-Path $BackupPath (Split-Path $DataFileDir -Leaf)
        Move-Item $DataFileDir $TargetDir -Force
        $Size = (Get-ChildItem $TargetDir -Recurse | Measure-Object -Property Length -Sum).Sum
        $SizeMB = [math]::Round($Size / 1MB, 2)
        Write-Log "已移动: $(Split-Path $DataFileDir -Leaf) (${SizeMB}MB, 未压缩)" "SUCCESS"
    }

    # 移动日志备份文件（压缩或未压缩）
    if (Test-Path $LogsFileZip) {
        $TargetFile = Join-Path $BackupPath (Split-Path $LogsFileZip -Leaf)
        Move-Item $LogsFileZip $TargetFile -Force
        $Size = [math]::Round((Get-Item $TargetFile).Length / 1MB, 2)
        Write-Log "已移动: $(Split-Path $LogsFileZip -Leaf) (${Size}MB)" "SUCCESS"
    } elseif (Test-Path $LogsFileDir) {
        $TargetDir = Join-Path $BackupPath (Split-Path $LogsFileDir -Leaf)
        Move-Item $LogsFileDir $TargetDir -Force
        $Size = (Get-ChildItem $TargetDir -Recurse | Measure-Object -Property Length -Sum).Sum
        $SizeMB = [math]::Round($Size / 1MB, 2)
        Write-Log "已移动: $(Split-Path $LogsFileDir -Leaf) (${SizeMB}MB, 未压缩)" "SUCCESS"
    }

    Write-Log "数据卷备份完成" "SUCCESS"

    # 步骤8: 重启所有服务
    if ($ContainerStatus -eq "running") {
        Show-Progress "恢复服务" "重启所有服务..." 90
        Write-Log "重启所有服务..."
        docker-compose up -d 2>&1 | Out-Null

        # 等待DM8启动
        Write-Log "等待30秒让DM8重启..."
        Start-Sleep -Seconds 30

        # 检查容器状态
        $NewContainerStatus = docker inspect dm8-server --format='{{.State.Status}}' 2>$null
        if ($NewContainerStatus -eq "running") {
            Write-Log "DM8服务重启成功" "SUCCESS"
        } else {
            Write-Log "DM8服务重启可能有问题，状态: $NewContainerStatus" "WARN"
        }
    }

} catch {
    Write-Log "备份失败: $($_.Exception.Message)" "ERROR"
    exit 1
} finally {
    # 清理临时目录
    if (Test-Path $TempDir) {
        Remove-Item $TempDir -Recurse -Force
        Write-Log "已清理临时目录" "INFO"
    }
    Write-Progress -Activity "备份完成" -Completed
}

# 步骤9: 创建恢复说明
Show-Progress "生成文档" "创建恢复说明..." 95
Write-Log "创建恢复说明文档..."

$RestoreInstructions = @"
# DM8 Docker 完整备份恢复说明
备份时间: $Timestamp
备份名称: $BackupName
备份类型: 完整备份（项目文件 + Docker配置 + 数据卷）

## 备份内容
1. 项目文件目录 (project_files/):
   - docker-compose.yml
   - Dockerfile
   - docker-entrypoint.sh
   - install_dm8.exp
   - test-connection.py
   - dm8_20250506_x86_rh7_64/ (DM8安装文件)

2. 数据卷备份:
   - dm8_data_volume.tar.gz (完整数据库数据卷)

3. 配置信息:
   - container_config.json (容器配置)
   - backup_info.json (备份信息)

## 恢复命令
.\backup-scripts\dm8-restore-cn.ps1 -BackupPath ".\backups\$BackupName"

## 注意事项
- 恢复将完全替换现有数据和配置
- 确保Docker Desktop正在运行
- 恢复期间现有服务将被停止
- 恢复完成后服务将自动启动

## 访问信息
- DM8: localhost:5236
- 用户名: SYSDBA
- 密码: GDYtd@2025
"@

$RestoreInstructions | Out-File -FilePath (Join-Path $BackupPath "restore_instructions.txt") -Encoding UTF8

# 计算备份大小
$BackupSize = (Get-ChildItem $BackupPath -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
$backupSizeStr = "$([math]::Round($BackupSize, 2)) MB"

Show-Progress "备份完成" "备份已完成!" 100
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "备份完成!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Log "备份位置: $BackupPath" "SUCCESS"
Write-Log "备份大小: $backupSizeStr" "SUCCESS"

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "备份完成!" -ForegroundColor Green
Write-Host "备份位置: $BackupPath" -ForegroundColor Cyan
Write-Host "备份大小: $backupSizeStr" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Green

Write-Progress -Activity "备份完成" -Completed
