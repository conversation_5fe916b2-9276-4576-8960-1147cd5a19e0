services:
  mysql:
    build:
      context: .
      dockerfile: Dockerfile
    image: mysql-online:5.7.44
    container_name: mysql5.7.44
    restart: always

    # 环境变量
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_USER=root
      - MYSQL_PASSWORD=root
      - MYSQL_DATABASE=mysql
      - TZ=Asia/Shanghai

    # 端口映射
    ports:
      - "3306:3306"

    # 数据卷挂载
    volumes:
      # MySQL数据目录 - 修正路径
      - mysql_data:/usr/local/mysql/data
      # MySQL日志目录 - 修正路径
      - mysql_logs:/usr/local/mysql/logs
      # 配置文件挂载 - 修正路径
      - ./my.cnf:/etc/my.cnf:ro
      # 初始化脚本挂载（如果存在）
      # - ./init-mysql.sql:/docker-entrypoint-initdb.d/init-mysql.sql:ro


    # 网络设置
    networks:
      - mysql_network

    # 健康检查 - 修正路径
    healthcheck:
      test: ["CMD", "/usr/local/mysql/bin/mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

    # 资源限制
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 512M
          cpus: '0.5'

    # 安全选项
    security_opt:
      - no-new-privileges:true

    # 让容器以root身份启动，MySQL进程会自动切换到mysql用户

# 数据卷定义
volumes:
  mysql_data:
    driver: local
    name: mysql_data_online
  mysql_logs:
    driver: local
    name: mysql_logs_online

# 网络定义
networks:
  mysql_network:
    driver: bridge
    name: mysql_network
