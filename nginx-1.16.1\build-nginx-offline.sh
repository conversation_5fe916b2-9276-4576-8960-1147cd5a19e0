#!/bin/bash

# 离线nginx Docker构建脚本
# 用途：在断网环境中构建nginx Docker镜像
# 前提：已运行download-all-packages.sh下载所有依赖包

set -e

echo "================================================================================"
echo "                    离线nginx Docker构建脚本"
echo "================================================================================"
echo "用途：在断网环境构建nginx Docker镜像"
echo "前提：已下载所有依赖包（packages/ 和 centos7-rpms/ 目录）"
echo "================================================================================"

# 检查Docker环境
echo "=== 第一步：检查环境 ==="
if ! command -v docker &> /dev/null; then
    echo "❌ 错误: Docker未安装或不在PATH中"
    echo "请先安装Docker"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo "❌ 错误: Docker daemon未运行"
    echo "请启动Docker服务"
    exit 1
fi

echo "✓ Docker环境检查通过"

# 检查必要文件
echo ""
echo "=== 第二步：检查依赖文件 ==="

# 检查nginx源码包（只需要nginx源码，其他依赖用RPM包）
required_packages=(
    "packages/nginx-1.24.0.tar.gz"
)

missing_packages=()
for package in "${required_packages[@]}"; do
    if [ ! -f "$package" ]; then
        missing_packages+=("$package")
    else
        echo "✓ $package 存在"
    fi
done

# 检查RPM包目录
if [ ! -d "centos7-rpms" ]; then
    echo "❌ 错误: centos7-rpms 目录不存在"
    missing_packages+=("centos7-rpms/")
elif [ $(ls centos7-rpms/*.rpm 2>/dev/null | wc -l) -eq 0 ]; then
    echo "❌ 错误: centos7-rpms 目录中没有RPM包"
    missing_packages+=("centos7-rpms/*.rpm")
else
    rpm_count=$(ls centos7-rpms/*.rpm | wc -l)
    echo "✓ centos7-rpms 目录包含 $rpm_count 个RPM包"
fi

# 检查脚本和配置文件
required_files=(
    "scripts/build-nginx.sh"
    "config/nginx.conf"
    "config/default.conf"
    "Dockerfile"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        missing_packages+=("$file")
    else
        echo "✓ $file 存在"
    fi
done

if [ ${#missing_packages[@]} -ne 0 ]; then
    echo ""
    echo "❌ 错误: 以下必要文件缺失："
    for file in "${missing_packages[@]}"; do
        echo "  - $file"
    done
    echo ""
    echo "请确保已运行 download-all-packages.sh 并传输了完整的项目目录"
    exit 1
fi

echo "✓ 所有必要文件检查通过"

# 构建Docker镜像
echo ""
echo "=== 第三步：构建nginx Docker镜像 ==="
echo "开始构建Docker镜像，这可能需要几分钟时间..."

if docker build -t nginx-offline:latest .; then
    echo "✓ Docker镜像构建成功"
else
    echo "❌ Docker镜像构建失败"
    echo "请检查构建日志中的错误信息"
    exit 1
fi

# 询问是否启动容器
echo ""
echo "=== 第四步：启动nginx容器 ==="
read -p "是否现在启动nginx容器？(y/n): " start_container

if [[ $start_container =~ ^[Yy]$ ]]; then
    # 检查端口占用
    if netstat -tlnp 2>/dev/null | grep -q ":80 "; then
        echo "⚠️  警告: 端口80已被占用"
        read -p "请输入其他端口号 (默认8080): " port
        port=${port:-8080}
        docker run -d -p ${port}:80 --name nginx-offline-server nginx-offline:latest
        echo "✓ nginx容器已启动，访问地址: http://localhost:${port}"
    else
        docker run -d -p 80:80 --name nginx-offline-server nginx-offline:latest
        echo "✓ nginx容器已启动，访问地址: http://localhost"
    fi
    
    # 等待容器启动
    echo "等待nginx启动..."
    sleep 3
    
    # 测试连接
    echo "测试nginx连接..."
    test_port=${port:-80}
    if curl -f http://localhost:${test_port} >/dev/null 2>&1; then
        echo "✓ nginx运行正常"
    else
        echo "⚠️  nginx可能未正常启动，请检查日志: docker logs nginx-offline-server"
    fi
fi

echo ""
echo "================================================================================"
echo "                              构建完成"
echo "================================================================================"
echo "✓ nginx Docker镜像构建成功: nginx-offline:latest"
echo ""
echo "常用管理命令："
echo "  查看容器状态: docker ps"
echo "  查看镜像信息: docker images nginx-offline"
echo "  查看容器日志: docker logs nginx-offline-server"
echo "  进入容器调试: docker exec -it nginx-offline-server /bin/bash"
echo "  停止容器: docker stop nginx-offline-server"
echo "  删除容器: docker rm nginx-offline-server"
echo "  启动新容器: docker run -d -p 80:80 --name nginx-server nginx-offline:latest"
echo ""
echo "nginx配置文件位置："
echo "  主配置: /etc/nginx/nginx.conf"
echo "  站点配置: /etc/nginx/conf.d/"
echo "  网站根目录: /usr/share/nginx/html"
echo ""
echo "验证nginx模块："
echo "  docker exec nginx-offline-server /usr/local/nginx/sbin/nginx -V"
echo "================================================================================"
