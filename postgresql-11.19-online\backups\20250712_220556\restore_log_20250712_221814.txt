﻿[2025-07-12 22:18:14] [INFO] 开始PostgreSQL完整恢复
[2025-07-12 22:18:14] [INFO] 备份路径: D:\Code\MicrosoftCode\postgresql-11.19-online\backups\20250712_220556
[2025-07-12 22:18:14] [INFO] 临时目录: D:\Code\MicrosoftCode\postgresql-11.19-online\temp_restore_20250712_221814
[2025-07-12 22:18:14] [SUCCESS] 临时目录创建成功
[2025-07-12 22:18:14] [INFO] 检查Docker环境...
[2025-07-12 22:18:14] [SUCCESS] Docker版本: Docker version 28.0.4, build b8034c0
[2025-07-12 22:18:14] [INFO] 验证备份文件...
[2025-07-12 22:18:14] [SUCCESS] 找到备份文件: postgres_data_volume.zip (6.03MB)
[2025-07-12 22:18:14] [SUCCESS] 找到备份文件: postgres_logs_volume.zip (0MB)
[2025-07-12 22:18:14] [INFO] 检测容器用户ID...
[2025-07-12 22:18:15] [SUCCESS] PostgreSQL用户ID: 1000
[2025-07-12 22:18:15] [INFO] 停止现有服务...
[2025-07-12 22:18:24] [SUCCESS] 现有服务已停止
[2025-07-12 22:18:24] [INFO] 删除现有数据卷...
[2025-07-12 22:18:25] [SUCCESS] 已删除数据卷: postgresql_11_19_data_online
[2025-07-12 22:18:25] [SUCCESS] 已删除数据卷: postgresql_11_19_logs_online
[2025-07-12 22:18:25] [SUCCESS] 已删除数据卷: pgadmin_data_online
[2025-07-12 22:18:25] [INFO] 恢复配置文件...
[2025-07-12 22:18:25] [SUCCESS] 已恢复: docker-compose.yml
[2025-07-12 22:18:25] [SUCCESS] 已恢复: Dockerfile
[2025-07-12 22:18:25] [SUCCESS] 已恢复: postgresql.conf
[2025-07-12 22:18:25] [SUCCESS] 已恢复: pg_hba.conf
[2025-07-12 22:18:25] [SUCCESS] 已恢复: docker-entrypoint.sh
[2025-07-12 22:18:25] [SUCCESS] 已恢复: init-scripts目录
[2025-07-12 22:18:25] [SUCCESS] 配置文件恢复完成，已恢复 6 个文件/目录
[2025-07-12 22:18:25] [INFO] 创建新数据卷...
[2025-07-12 22:18:25] [SUCCESS] 已创建数据卷: postgresql_11_19_data_online
[2025-07-12 22:18:25] [SUCCESS] 已创建数据卷: postgresql_11_19_logs_online
[2025-07-12 22:18:25] [SUCCESS] 已创建数据卷: pgadmin_data_online
[2025-07-12 22:18:25] [INFO] 恢复PostgreSQL主数据卷...
[2025-07-12 22:18:41] [INFO] 修复数据目录权限...
[2025-07-12 22:18:42] [SUCCESS] PostgreSQL主数据卷恢复完成
[2025-07-12 22:18:42] [INFO] 恢复PostgreSQL日志卷...
[2025-07-12 22:18:43] [INFO] 修复日志目录权限...
[2025-07-12 22:18:43] [SUCCESS] PostgreSQL日志卷恢复完成
[2025-07-12 22:18:43] [INFO] 恢复pgAdmin数据卷...
[2025-07-12 22:18:44] [INFO] 修复pgAdmin数据目录权限...
[2025-07-12 22:18:45] [SUCCESS] pgAdmin数据卷恢复完成，权限已修复
[2025-07-12 22:18:45] [INFO] 启动PostgreSQL服务...
[2025-07-12 22:18:51] [INFO] 等待PostgreSQL启动...
[2025-07-12 22:18:57] [INFO] 检查PostgreSQL状态 (尝试 1/20)...
[2025-07-12 22:18:57] [SUCCESS] PostgreSQL服务启动成功
[2025-07-12 22:18:57] [INFO] 等待pgAdmin启动...
[2025-07-12 22:19:02] [INFO] 检查pgAdmin状态 (尝试 1/10)...
[2025-07-12 22:19:02] [SUCCESS] pgAdmin容器运行正常
[2025-07-12 22:19:05] [INFO] pgAdmin Web界面暂未响应，继续等待...
[2025-07-12 22:19:10] [INFO] 检查pgAdmin状态 (尝试 2/10)...
[2025-07-12 22:19:10] [SUCCESS] pgAdmin容器运行正常
[2025-07-12 22:19:13] [INFO] pgAdmin Web界面暂未响应，继续等待...
[2025-07-12 22:19:18] [INFO] 检查pgAdmin状态 (尝试 3/10)...
[2025-07-12 22:19:18] [SUCCESS] pgAdmin容器运行正常
[2025-07-12 22:19:21] [SUCCESS] pgAdmin容器已运行超过30秒，认为启动成功（Web界面可能需要更长时间初始化）
[2025-07-12 22:19:21] [INFO] 恢复SQL数据...
[2025-07-12 22:19:21] [INFO] 从以下文件导入SQL数据: postgresql_all_databases.sql
[2025-07-12 22:19:22] [WARN] SQL数据导入完成，但验证失败
[2025-07-12 22:19:22] [INFO] 验证恢复结果...
[2025-07-12 22:19:22] [SUCCESS] PostgreSQL容器状态: 运行中
[2025-07-12 22:19:22] [SUCCESS] PostgreSQL数据库连接: 正常
[2025-07-12 22:19:22] [SUCCESS] PostgreSQL服务恢复成功
[2025-07-12 22:19:22] [SUCCESS] pgAdmin容器状态: 运行中
[2025-07-12 22:19:32] [WARN] pgAdmin Web界面: 无法访问 - 操作超时。
[2025-07-12 22:19:32] [INFO] 建议检查pgAdmin容器日志: docker logs pgadmin4
[2025-07-12 22:19:32] [INFO] 已清理临时目录
[2025-07-12 22:19:32] [SUCCESS] 恢复成功完成
[2025-07-12 22:19:32] [SUCCESS] PostgreSQL服务可在以下地址访问: localhost:3433
[2025-07-12 22:19:32] [SUCCESS] 用户名: postgres, 密码: postgres
[2025-07-12 22:19:32] [SUCCESS] pgAdmin: http://localhost:8080
