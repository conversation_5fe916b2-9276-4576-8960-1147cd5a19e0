('D:\\Code\\MicrosoftCode\\tempmail-desktop\\build\\tempmail\\TempMail-Desktop.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\build\\tempmail\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\build\\tempmail\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\build\\tempmail\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\build\\tempmail\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\build\\tempmail\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\build\\tempmail\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('main', 'D:\\Code\\MicrosoftCode\\tempmail-desktop\\main.py', 'PYSOURCE'),
  ('webview\\lib\\WebBrowserInterop.x64.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\lib\\WebBrowserInterop.x64.dll',
   'BINARY'),
  ('webview\\lib\\runtimes\\win-x86\\native\\WebView2Loader.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\lib\\runtimes\\win-x86\\native\\WebView2Loader.dll',
   'BINARY'),
  ('webview\\lib\\Microsoft.Web.WebView2.WinForms.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\lib\\Microsoft.Web.WebView2.WinForms.dll',
   'BINARY'),
  ('webview\\lib\\Microsoft.Web.WebView2.Core.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\lib\\Microsoft.Web.WebView2.Core.dll',
   'BINARY'),
  ('webview\\lib\\runtimes\\win-arm64\\native\\WebView2Loader.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\lib\\runtimes\\win-arm64\\native\\WebView2Loader.dll',
   'BINARY'),
  ('webview\\lib\\WebBrowserInterop.x86.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\lib\\WebBrowserInterop.x86.dll',
   'BINARY'),
  ('webview\\lib\\runtimes\\win-x64\\native\\WebView2Loader.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\lib\\runtimes\\win-x64\\native\\WebView2Loader.dll',
   'BINARY'),
  ('python313.dll',
   'D:\\DevelopmentTools\\Python3.13\\python313.dll',
   'BINARY'),
  ('pythonnet\\runtime\\Python.Runtime.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\pythonnet\\runtime\\Python.Runtime.dll',
   'BINARY'),
  ('clr_loader\\ffi\\dlls\\x86\\ClrLoader.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\ffi\\dlls\\x86\\ClrLoader.dll',
   'BINARY'),
  ('clr_loader\\ffi\\dlls\\amd64\\ClrLoader.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\clr_loader\\ffi\\dlls\\amd64\\ClrLoader.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libssl-1_1-x64.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libssl-1_1-x64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('_lzma.pyd',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd', 'D:\\DevelopmentTools\\Python3.13\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_decimal.pyd',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\DevelopmentTools\\Python3.13\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'D:\\DevelopmentTools\\Python3.13\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('_sqlite3.pyd',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp313-win_amd64.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\etree.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp313-win_amd64.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\_elementpath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp313-win_amd64.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\sax.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp313-win_amd64.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\objectify.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp313-win_amd64.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\html\\diff.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\_difflib.cp313-win_amd64.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\html\\_difflib.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp313-win_amd64.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\builder.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp313-win_amd64.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\_cffi_backend.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PyQt5\\QtNetwork.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\QtNetwork.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp313-win_amd64.pyd',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\sip.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\DevelopmentTools\\Python3.13\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\DevelopmentTools\\Python3.13\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('sqlite3.dll',
   'D:\\DevelopmentTools\\Python3.13\\DLLs\\sqlite3.dll',
   'BINARY'),
  ('python3.dll', 'D:\\DevelopmentTools\\Python3.13\\python3.dll', 'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\ucrtbase.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\DevelopmentTools\\java\\jdk1.8.0_411\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('backend\\__pycache__\\app.cpython-313.pyc',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\__pycache__\\app.cpython-313.pyc',
   'DATA'),
  ('backend\\api\\__init__.py',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\api\\__init__.py',
   'DATA'),
  ('backend\\api\\__pycache__\\__init__.cpython-313.pyc',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\api\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('backend\\api\\__pycache__\\advanced_tempmail_api.cpython-313.pyc',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\api\\__pycache__\\advanced_tempmail_api.cpython-313.pyc',
   'DATA'),
  ('backend\\api\\__pycache__\\real_tempmail_api.cpython-313.pyc',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\api\\__pycache__\\real_tempmail_api.cpython-313.pyc',
   'DATA'),
  ('backend\\api\\__pycache__\\tempmail_api.cpython-313.pyc',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\api\\__pycache__\\tempmail_api.cpython-313.pyc',
   'DATA'),
  ('backend\\api\\advanced_tempmail_api.py',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\api\\advanced_tempmail_api.py',
   'DATA'),
  ('backend\\api\\real_tempmail_api.py',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\api\\real_tempmail_api.py',
   'DATA'),
  ('backend\\api\\tempmail_api.py',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\api\\tempmail_api.py',
   'DATA'),
  ('backend\\app.py',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\app.py',
   'DATA'),
  ('backend\\database\\__init__.py',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\database\\__init__.py',
   'DATA'),
  ('backend\\database\\__pycache__\\__init__.cpython-313.pyc',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\database\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('backend\\database\\__pycache__\\db_manager.cpython-313.pyc',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\database\\__pycache__\\db_manager.cpython-313.pyc',
   'DATA'),
  ('backend\\database\\db_manager.py',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\database\\db_manager.py',
   'DATA'),
  ('backend\\utils\\__init__.py',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\utils\\__init__.py',
   'DATA'),
  ('backend\\utils\\__pycache__\\__init__.cpython-313.pyc',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\utils\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('backend\\utils\\__pycache__\\helpers.cpython-313.pyc',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\utils\\__pycache__\\helpers.cpython-313.pyc',
   'DATA'),
  ('backend\\utils\\helpers.py',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\backend\\utils\\helpers.py',
   'DATA'),
  ('frontend\\css\\style.css',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\frontend\\css\\style.css',
   'DATA'),
  ('frontend\\index.html',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\frontend\\index.html',
   'DATA'),
  ('frontend\\js\\app.js',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\frontend\\js\\app.js',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE.BSD',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\WHEEL',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\METADATA',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\licenses\\LICENSE.APACHE',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\RECORD',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\RECORD',
   'DATA'),
  ('cryptography-45.0.4.dist-info\\INSTALLER',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\cryptography-45.0.4.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('webview\\js\\api.js',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\js\\api.js',
   'DATA'),
  ('webview\\js\\finish.js',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\js\\finish.js',
   'DATA'),
  ('webview\\lib\\pywebview-android.jar',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\lib\\pywebview-android.jar',
   'DATA'),
  ('webview\\js\\customize.js',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\js\\customize.js',
   'DATA'),
  ('webview\\js\\lib\\polyfill.js',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\js\\lib\\polyfill.js',
   'DATA'),
  ('webview\\js\\lib\\dom_json.js',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\webview\\js\\lib\\dom_json.js',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('flask-3.1.1.dist-info\\INSTALLER',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask-3.1.1.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('flask-3.1.1.dist-info\\REQUESTED',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask-3.1.1.dist-info\\REQUESTED',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\WHEEL',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\METADATA',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\INSTALLER',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\REQUESTED',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click-8.2.1.dist-info\\REQUESTED',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('flask-3.1.1.dist-info\\METADATA',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask-3.1.1.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.1.dist-info\\WHEEL',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask-3.1.1.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\RECORD',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\RECORD',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('flask-3.1.1.dist-info\\licenses\\LICENSE.txt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask-3.1.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\werkzeug-3.1.3.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-3.1.1.dist-info\\entry_points.txt',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask-3.1.1.dist-info\\entry_points.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\REQUESTED',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\REQUESTED',
   'DATA'),
  ('flask-3.1.1.dist-info\\RECORD',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\flask-3.1.1.dist-info\\RECORD',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'D:\\DevelopmentTools\\Python3.13\\Lib\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('base_library.zip',
   'D:\\Code\\MicrosoftCode\\tempmail-desktop\\build\\tempmail\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
