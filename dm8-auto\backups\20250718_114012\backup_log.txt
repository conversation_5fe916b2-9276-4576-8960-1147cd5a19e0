[2025-07-18 11:40:12] [SUCCESS] 备份目录创建成功
[2025-07-18 11:40:12] [INFO] 检查Docker环境...
[2025-07-18 11:40:12] [SUCCESS] Docker版本: Docker version 28.0.4, build b8034c0
[2025-07-18 11:40:12] [INFO] 检查DM8容器状态...
[2025-07-18 11:40:12] [SUCCESS] DM8容器运行正常
[2025-07-18 11:40:12] [INFO] 开始备份配置文件
[2025-07-18 11:41:19] [SUCCESS] 配置文件备份成功
[2025-07-18 11:41:19] [INFO] 数据卷备份包含完整数据库数据，无需额外SQL导出
[2025-07-18 11:41:19] [INFO] 备份Docker配置文件
[2025-07-18 11:41:19] [SUCCESS] 已备份: docker-compose.yml
[2025-07-18 11:41:19] [SUCCESS] 已备份: Dockerfile
[2025-07-18 11:41:19] [WARN] 文件不存在: dm.ini
[2025-07-18 11:41:19] [SUCCESS] Docker配置备份完成，已备份 2 个文件
[2025-07-18 11:41:19] [INFO] 停止DM8容器确保数据一致性...
[2025-07-18 11:41:29] [SUCCESS] 容器已停止
[2025-07-18 11:41:29] [INFO] 备份DM8数据卷...
[2025-07-18 11:41:29] [INFO] 备份主数据卷: dm8_database_data
[2025-07-18 11:41:38] [SUCCESS] 主数据卷备份完成
[2025-07-18 11:41:38] [INFO] 备份日志数据卷: dm8_database_logs
[2025-07-18 11:41:39] [SUCCESS] 日志数据卷备份完成
[2025-07-18 11:41:39] [SUCCESS] 已移动: dm8_data_volume.zip (5.13MB)
[2025-07-18 11:41:39] [SUCCESS] 已移动: dm8_logs_volume.zip (0.09MB)
[2025-07-18 11:41:39] [SUCCESS] 已移动: dm8_config_backup.zip (740.38MB)
[2025-07-18 11:41:39] [SUCCESS] 数据卷备份完成
[2025-07-18 11:41:39] [INFO] 重启所有服务...
[2025-07-18 11:41:39] [INFO] 等待30秒让DM8重启...
[2025-07-18 11:42:09] [SUCCESS] DM8服务重启成功
[2025-07-18 11:42:09] [INFO] 已清理临时目录
[2025-07-18 11:42:09] [INFO] 创建恢复说明文档...
[2025-07-18 11:42:09] [SUCCESS] 备份位置: .\backups\20250718_114012
[2025-07-18 11:42:09] [SUCCESS] 备份大小: 745.6 MB
