#!/usr/bin/expect -f

# 设置超时时间
set timeout 600
log_user 1

puts "=== 开始达梦数据库自动安装 ==="
puts "安装程序路径: /tmp/dm8_setup/DMInstall.bin"
puts "目标安装目录: /home/<USER>/dmdbms"

# 启动安装程序
spawn /tmp/dm8_setup/DMInstall.bin -i

expect {
    # 最重要的匹配 - 是否确认安装（放在最前面，优先匹配）
    "*是否确认安装? (Y/y:是 N/n:否)*" {
        puts "\n>>> 步骤8: 是否确认安装 - 输入y"
        send "y\r"
        exp_continue
    }
    "*Y/y:是 N/n:否*" {
        puts "\n>>> 步骤8: 确认安装选择(Y/y:是 N/n:否) - 输入y"
        send "y\r"
        exp_continue
    }

    # 1. 安装语言选择 - 输入1（简体中文）
    "*请选择安装语言*" {
        puts "\n>>> 步骤1: 选择安装语言 - 输入1（简体中文）"
        send "1\r"
        exp_continue
    }
    "*Please select the installer's language*" {
        puts "\n>>> 步骤1: 选择安装语言 - 输入1（简体中文）"
        send "1\r"
        exp_continue
    }

    # 2. Key文件询问 - 选择N（不需要key文件）
    "*Key*" {
        puts "\n>>> 步骤2: Key文件询问 - 选择N（不需要key文件）"
        send "N\r"
        exp_continue
    }
    "*是否输入Key文件路径*" {
        puts "\n>>> 步骤2: 是否输入Key文件路径 - 选择N"
        send "N\r"
        exp_continue
    }
    "*key file*" {
        puts "\n>>> 步骤2: key file询问 - 选择N"
        send "N\r"
        exp_continue
    }

    # 3. 时区设置 - 选择Y
    "*是否设置时区*" {
        puts "\n>>> 步骤3: 是否设置时区 - 选择Y"
        send "Y\r"
        exp_continue
    }
    "*TimeZone*" {
        puts "\n>>> 步骤3: TimeZone设置 - 选择Y"
        send "Y\r"
        exp_continue
    }

    # 4. 请选择时区 - 选择21
    "*请选择时区*" {
        puts "\n>>> 步骤4: 请选择时区 - 选择21（中国上海）"
        send "21\r"
        exp_continue
    }
    "*时区*" {
        puts "\n>>> 步骤4: 时区选择 - 选择21"
        send "21\r"
        exp_continue
    }
    "*timezone*" {
        puts "\n>>> 步骤4: timezone选择 - 选择21"
        send "21\r"
        exp_continue
    }

    # 5. 请选择安装类型 - 选择1（典型安装）
    "*请选择安装类型*" {
        puts "\n>>> 步骤5: 请选择安装类型 - 选择1（典型安装）"
        send "1\r"
        exp_continue
    }
    "*安装类型*" {
        puts "\n>>> 步骤5: 安装类型 - 选择1（典型安装）"
        send "1\r"
        exp_continue
    }
    "*Installation Type*" {
        puts "\n>>> 步骤5: Installation Type - 选择1（典型安装）"
        send "1\r"
        exp_continue
    }

    # 7. 是否确认安装路径 - 输入Y（放在安装目录前面，优先匹配）
    "*是否确认安装路径*" {
        puts "\n>>> 步骤7: 是否确认安装路径 - 输入Y"
        send "Y\r"
        exp_continue
    }
    "*确认安装路径*" {
        puts "\n>>> 步骤7: 确认安装路径 - 输入Y"
        send "Y\r"
        exp_continue
    }
    "*确认路径*" {
        puts "\n>>> 步骤7: 确认路径 - 输入Y"
        send "Y\r"
        exp_continue
    }
    "*Confirm*" {
        puts "\n>>> 步骤7: Confirm确认 - 选择Y"
        send "Y\r"
        exp_continue
    }

    # 6. 请选择安装目录 - 直接按回车键（使用默认配置的目录）
    "*请选择安装目录*" {
        puts "\n>>> 步骤6: 请选择安装目录 - 直接按回车键使用默认目录"
        send "\r"
        exp_continue
    }
    "*\[/home/<USER>/dmdbms\]*" {
        puts "\n>>> 步骤6: 看到安装目录提示 - 直接按回车键确认"
        send "\r"
        exp_continue
    }
    "*\[*dmdbms*\]*" {
        puts "\n>>> 步骤6: 安装目录包含dmdbms - 直接按回车键确认"
        send "\r"
        exp_continue
    }
    "*Installation Path*" {
        puts "\n>>> 步骤6: Installation Path - 直接按回车键"
        send "\r"
        exp_continue
    }
    # 通用的安装目录匹配（放在最后，避免干扰其他匹配）
    "*安装目录*" {
        puts "\n>>> 步骤6: 安装目录提示 - 直接按回车键"
        send "\r"
        exp_continue
    }

    # 其他确认步骤
    "*是否安装*" {
        puts "\n>>> 确认安装 - 选择Y"
        send "Y\r"
        exp_continue
    }
    "*是否继续*" {
        puts "\n>>> 是否继续 - 选择Y"
        send "Y\r"
        exp_continue
    }
    "*Continue*" {
        puts "\n>>> Continue - 选择Y"
        send "Y\r"
        exp_continue
    }

    # 跳过配置文件
    "*配置文件*" {
        puts "\n>>> 跳过配置文件 - 选择N"
        send "N\r"
        exp_continue
    }
    "*config file*" {
        puts "\n>>> 跳过config file - 选择N"
        send "N\r"
        exp_continue
    }

    # 安装完成
    "*root_installer.sh*" {
        puts "\n>>> 安装完成，需要执行root_installer.sh"
        exp_continue
    }
    "*安装完成*" {
        puts "\n>>> 安装完成"
        exp_continue
    }
    "*Installation completed*" {
        puts "\n>>> Installation completed"
        exp_continue
    }

    # 按键继续
    "*按任意键继续*" {
        puts "\n>>> 按任意键继续"
        send "\r"
        exp_continue
    }
    "*Press any key*" {
        puts "\n>>> Press any key"
        send "\r"
        exp_continue
    }

    # 默认处理 - 对于任何包含方括号的提示，使用默认值
    "*\[*\]*" {
        puts "\n>>> 检测到默认选择提示，使用默认值"
        send "\r"
        exp_continue
    }

    eof {
        puts "\n>>> 安装程序结束"
        exit 0
    }
    timeout {
        puts "\n>>> 安装超时"
        exit 1
    }
}

puts "\n>>> expect脚本执行完成"
exit 0
