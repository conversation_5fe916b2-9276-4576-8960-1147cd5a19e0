# PowerShell脚本：下载缺失的CentOS 7 RPM包

Write-Host "================================================================================"
Write-Host "                    下载缺失的CentOS 7 RPM包"
Write-Host "================================================================================"

# 创建下载目录
$downloadDir = "centos7-rpms-additional"
if (!(Test-Path $downloadDir)) {
    New-Item -ItemType Directory -Path $downloadDir
}

# CentOS 7镜像源
$baseUrl = "http://vault.centos.org/7.9.2009/os/x86_64/Packages"

# 需要下载的包列表
$packages = @(
    "libmpc-1.0.1-3.el7.x86_64.rpm",
    "mpfr-3.1.1-4.el7.x86_64.rpm", 
    "gmp-6.0.0-15.el7.x86_64.rpm"
)

Write-Host "开始下载缺失的RPM包..."

foreach ($package in $packages) {
    Write-Host "下载: $package"
    $url = "$baseUrl/$package"
    $outputPath = "$downloadDir\$package"
    
    try {
        Invoke-WebRequest -Uri $url -OutFile $outputPath -ErrorAction Stop
        Write-Host "✓ $package 下载成功" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ $package 下载失败: $($_.Exception.Message)" -ForegroundColor Red
        
        # 尝试备用源
        $altUrls = @(
            "http://mirror.centos.org/centos/7/os/x86_64/Packages",
            "https://buildlogs.centos.org/centos/7/os/x86_64/Packages"
        )
        
        $downloaded = $false
        foreach ($altUrl in $altUrls) {
            try {
                $altFullUrl = "$altUrl/$package"
                Write-Host "尝试备用源: $altFullUrl"
                Invoke-WebRequest -Uri $altFullUrl -OutFile $outputPath -ErrorAction Stop
                Write-Host "✓ $package 从备用源下载成功" -ForegroundColor Green
                $downloaded = $true
                break
            }
            catch {
                Write-Host "备用源也失败: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
        
        if (!$downloaded) {
            Write-Host "❌ $package 从所有源下载失败" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "================================================================================"
Write-Host "                        下载完成"
Write-Host "================================================================================"
Write-Host "下载的包保存在: $downloadDir\"
Write-Host ""
Write-Host "请将下载的包复制到 centos7-rpms\ 目录中："
Write-Host "Copy-Item $downloadDir\*.rpm centos7-rpms\"
Write-Host ""
Write-Host "然后重新运行Docker构建："
Write-Host "docker build -t nginx-offline-build -f Dockerfile ."
Write-Host "================================================================================"
