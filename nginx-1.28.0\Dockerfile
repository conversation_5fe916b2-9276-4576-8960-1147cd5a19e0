# nginx 1.28.0 离线构建 Dockerfile
# 基于CentOS 7，使用RPM包依赖，完全离线构建

FROM centos:7

# 设置工作目录
WORKDIR /tmp/build

# 复制所有必需文件
COPY packages/ /tmp/packages/
COPY centos7-rpms/ /tmp/centos7-rpms/
COPY scripts/ /tmp/scripts/
COPY config/ /tmp/config/

# 安装RPM包（分步安装以确保成功）
RUN echo "安装基础依赖..." && \
    rpm -ivh /tmp/centos7-rpms/glibc-*.rpm --force --nodeps || true && \
    rpm -ivh /tmp/centos7-rpms/libgcc-*.rpm --force --nodeps || true && \
    rpm -ivh /tmp/centos7-rpms/gmp-*.rpm --force --nodeps || true && \
    rpm -ivh /tmp/centos7-rpms/mpfr-*.rpm --force --nodeps || true && \
    rpm -ivh /tmp/centos7-rpms/libmpc-*.rpm --force --nodeps || true && \
    echo "安装编译工具..." && \
    rpm -ivh /tmp/centos7-rpms/binutils-*.rpm --force --nodeps || true && \
    rpm -ivh /tmp/centos7-rpms/kernel-headers-*.rpm --force --nodeps || true && \
    rpm -ivh /tmp/centos7-rpms/glibc-headers-*.rpm --force --nodeps || true && \
    rpm -ivh /tmp/centos7-rpms/glibc-devel-*.rpm --force --nodeps || true && \
    rpm -ivh /tmp/centos7-rpms/libstdc++-*.rpm --force --nodeps || true && \
    rpm -ivh /tmp/centos7-rpms/gcc-4.8.5-*.rpm --force --nodeps || true && \
    rpm -ivh /tmp/centos7-rpms/gcc-c++-*.rpm --force --nodeps || true && \
    echo "安装make工具..." && \
    rpm -ivh /tmp/centos7-rpms/make-*.rpm --force --nodeps || true && \
    echo "安装其他依赖..." && \
    rpm -ivh /tmp/centos7-rpms/*.rpm --force --nodeps || true && \
    echo "验证工具安装..." && \
    ls -la /usr/bin/gcc* || echo "gcc未找到" && \
    ls -la /usr/bin/make* || echo "make未找到" && \
    which gcc || echo "gcc不在PATH中" && \
    which make || echo "make不在PATH中"

# 创建nginx用户和组
RUN groupadd nginx 2>/dev/null || true && \
    useradd -g nginx -s /sbin/nologin -M nginx 2>/dev/null || true

# 设置执行权限并运行编译脚本
RUN chmod +x /tmp/scripts/build-nginx.sh && \
    /tmp/scripts/build-nginx.sh

# 创建必要的目录
RUN mkdir -p /var/log/nginx /var/cache/nginx /etc/nginx/conf.d /usr/share/nginx/html

# 复制配置文件
RUN cp /tmp/config/nginx.conf /etc/nginx/nginx.conf && \
    cp /tmp/config/default.conf /etc/nginx/conf.d/default.conf

# 创建默认首页
RUN echo '<html><head><title>Welcome to nginx!</title></head><body><h1>Welcome to nginx!</h1><p>If you see this page, the nginx web server is successfully installed and working. Further configuration is required.</p><p>For online documentation and support please refer to <a href="http://nginx.org/">nginx.org</a>.</p><p><em>Thank you for using nginx.</em></p></body></html>' > /usr/share/nginx/html/index.html

# 清理临时文件
RUN rm -rf /tmp/build /tmp/packages /tmp/centos7-rpms /tmp/scripts /tmp/config

# 设置权限
RUN chown -R nginx:nginx /var/log/nginx /var/cache/nginx /usr/share/nginx/html

# 暴露端口
EXPOSE 80 443

# 启动nginx
CMD ["/usr/local/nginx/sbin/nginx", "-g", "daemon off;"]
