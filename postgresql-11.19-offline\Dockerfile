FROM centos:7

# 设置环境变量
ENV PG_VERSION=11.19
ENV PG_USER=postgres
ENV PG_PASSWORD=postgres
ENV PG_DB=postgres
ENV PG_PORT=3433
ENV PGDATA=/var/lib/postgresql/data
ENV PATH="/usr/local/pgsql/bin:$PATH"

# 复制RPM包和PostgreSQL源码包
COPY rpm-packages/ /tmp/rpm-packages/
COPY postgresql-${PG_VERSION}.tar.gz /tmp/

# 安装完整离线RPM包（现在包含所有依赖）
RUN cd /tmp/rpm-packages && \
    # 重建RPM数据库
    rpm --rebuilddb && \
    echo "========================================" && \
    echo "安装PostgreSQL 11.19完整离线RPM包" && \
    echo "包总数: $(ls *.rpm | wc -l)" && \
    echo "========================================" && \
    # 第1步: 安装基础系统库
    echo "第1步: 安装基础系统库..." && \
    rpm -ivh --force --nodeps \
        glibc-*.rpm \
        libgcc-*.rpm \
        libstdc++-*.rpm 2>/dev/null || true && \
    # 第2步: 安装内核头文件
    echo "第2步: 安装内核头文件..." && \
    rpm -ivh --force --nodeps \
        kernel-headers-*.rpm \
        glibc-headers-*.rpm 2>/dev/null || true && \
    # 第3步: 安装数学库（GCC关键依赖）
    echo "第3步: 安装数学库..." && \
    rpm -ivh --force --nodeps \
        gmp-*.rpm \
        mpfr-*.rpm \
        libmpc-*.rpm 2>/dev/null || true && \
    # 第4步: 安装编译工具链
    echo "第4步: 安装编译工具链..." && \
    rpm -ivh --force --nodeps \
        binutils-*.rpm \
        cpp-*.rpm \
        gcc-*.rpm \
        make-*.rpm \
        autoconf-*.rpm \
        automake-*.rpm \
        m4-*.rpm 2>/dev/null || true && \
    # 第5步: 安装开发库
    echo "第5步: 安装开发库..." && \
    rpm -ivh --force --nodeps \
        glibc-devel-*.rpm \
        libstdc++-devel-*.rpm \
        *-devel*.rpm 2>/dev/null || true && \
    # 第6步: 安装其余所有包
    echo "第6步: 安装其余包..." && \
    rpm -ivh --force --nodeps *.rpm 2>/dev/null || true && \
    # 验证关键工具
    echo "验证安装结果..." && \
    gcc --version && \
    make --version && \
    echo "✅ 编译环境准备完成" && \
    # 清理RPM包
    rm -rf /tmp/rpm-packages

# 创建postgres用户
RUN useradd -m -s /bin/bash postgres

# 编译PostgreSQL 11.19（离线版本，使用完整配置）
RUN cd /tmp && \
    tar -xzf postgresql-${PG_VERSION}.tar.gz && \
    cd postgresql-${PG_VERSION} && \
    ./configure \
        --prefix=/usr/local/pgsql \
        --with-openssl \
        --with-libxml \
        --with-libxslt \
        --with-uuid=e2fs \
        --enable-thread-safety && \
    make -j$(nproc) && \
    make install && \
    cd /tmp && \
    rm -rf postgresql-${PG_VERSION}*

# 创建必要的目录
RUN mkdir -p ${PGDATA} && \
    mkdir -p /var/log/postgresql && \
    mkdir -p /etc/postgresql && \
    mkdir -p /docker-entrypoint-initdb.d && \
    chown -R postgres:postgres ${PGDATA} && \
    chown -R postgres:postgres /var/log/postgresql && \
    chown -R postgres:postgres /etc/postgresql

# 复制配置文件
COPY postgresql.conf /etc/postgresql/postgresql.conf
COPY pg_hba.conf /etc/postgresql/pg_hba.conf
COPY docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh

# 设置权限
RUN chmod +x /usr/local/bin/docker-entrypoint.sh && \
    chown postgres:postgres /etc/postgresql/postgresql.conf && \
    chown postgres:postgres /etc/postgresql/pg_hba.conf

# 切换到postgres用户
USER postgres

# 暴露端口
EXPOSE ${PG_PORT}

# 设置数据卷
VOLUME ["${PGDATA}", "/var/log/postgresql", "/etc/postgresql"]

# 设置入口点
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
CMD ["postgres"]
