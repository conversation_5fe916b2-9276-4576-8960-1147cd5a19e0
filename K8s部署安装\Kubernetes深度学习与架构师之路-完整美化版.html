<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kubernetes深度学习与架构师之路 - 从专家到大师</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --advanced-color: #9f7aea;
            --expert-color: #38b2ac;
            --master-color: #d69e2e;
            --architect-color: #e53e3e;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 12px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 欢迎框样式 */
        .welcome-box {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .welcome-box::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 8s ease-in-out infinite;
        }

        .welcome-box h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .welcome-box p {
            font-size: 16px;
            line-height: 1.8;
            color: var(--text-primary);
            position: relative;
            z-index: 1;
        }

        /* 学习路径样式 */
        .learning-path {
            margin-top: 30px;
            position: relative;
            z-index: 1;
        }

        .learning-path h4 {
            color: var(--secondary-color);
            margin-bottom: 25px;
        }

        .path-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .path-step {
            background: var(--light-surface);
            border-radius: 15px;
            padding: 25px;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .path-step:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .step-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 20px;
            margin-bottom: 15px;
            box-shadow: var(--shadow-md);
        }

        .step-content h5 {
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .step-content p {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.6;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box,
        .advanced-box,
        .expert-box,
        .master-box,
        .architect-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover,
        .advanced-box:hover,
        .expert-box:hover,
        .master-box:hover,
        .architect-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        .advanced-box {
            background: linear-gradient(135deg, rgba(159, 122, 234, 0.1) 0%, rgba(159, 122, 234, 0.05) 100%);
            border-left-color: var(--advanced-color);
            color: #553c9a;
        }

        .expert-box {
            background: linear-gradient(135deg, rgba(56, 178, 172, 0.1) 0%, rgba(56, 178, 172, 0.05) 100%);
            border-left-color: var(--expert-color);
            color: #234e52;
        }

        .master-box {
            background: linear-gradient(135deg, rgba(214, 158, 46, 0.1) 0%, rgba(214, 158, 46, 0.05) 100%);
            border-left-color: var(--master-color);
            color: #744210;
        }

        .architect-box {
            background: linear-gradient(135deg, rgba(229, 62, 62, 0.1) 0%, rgba(229, 62, 62, 0.05) 100%);
            border-left-color: var(--architect-color);
            color: #742a2a;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: var(--light-surface);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        th,
        td {
            padding: 18px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 技能等级标签 */
        .skill-level {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin: 4px;
            box-shadow: var(--shadow-sm);
        }

        .level-expert {
            background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
            color: white;
        }

        .level-master {
            background: linear-gradient(135deg, #d69e2e 0%, #b7791f 100%);
            color: white;
        }

        .level-architect {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
            color: white;
        }

        .level-guru {
            background: linear-gradient(135deg, #805ad5 0%, #6b46c1 100%);
            color: white;
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-decoration: none;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            z-index: 999;
            font-size: 20px;
        }

        .back-to-top:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .content-wrapper {
                padding: 30px;
            }

            .container {
                padding: 40px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
                border-radius: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 16px;
                margin-right: 15px;
            }

            .path-steps {
                grid-template-columns: 1fr;
            }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
        }
    </style>
</head>

<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-crown"></i> K8s架构师之路</h2>
            <p>从专家到大师的深度学习</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#expert-assessment"><i class="fas fa-medal"></i>1. 专家级评估</a></li>
                <li><a href="#source-code-deep-dive"><i class="fas fa-code"></i>2. 源码深度解析</a></li>
                <li><a href="#custom-controllers"><i class="fas fa-robot"></i>3. 自定义控制器</a></li>
                <li><a href="#operators-development"><i class="fas fa-cogs"></i>4. Operator开发</a></li>
                <li><a href="#multi-cluster-management"><i class="fas fa-globe"></i>5. 多集群管理</a></li>
                <li><a href="#service-mesh-mastery"><i class="fas fa-project-diagram"></i>6. 服务网格精通</a></li>
                <li><a href="#platform-engineering"><i class="fas fa-building"></i>7. 平台工程</a></li>
                <li><a href="#cloud-native-architecture"><i class="fas fa-cloud"></i>8. 云原生架构</a></li>
                <li><a href="#performance-optimization"><i class="fas fa-tachometer-alt"></i>9. 性能优化</a></li>
                <li><a href="#security-architecture"><i class="fas fa-shield-alt"></i>10. 安全架构</a></li>
                <li><a href="#community-contribution"><i class="fas fa-users"></i>11. 社区贡献</a></li>
                <li><a href="#thought-leadership"><i class="fas fa-lightbulb"></i>12. 技术领导力</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-crown"></i> Kubernetes深度学习与架构师之路</h1>

                <div class="welcome-box">
                    <h3><i class="fas fa-trophy"></i> 欢迎来到Kubernetes的巅峰之旅！</h3>
                    <p>如果你已经掌握了Kubernetes的进阶技能，能够熟练管理生产环境的集群，那么现在是时候向更高的层次迈进了！这份指南将带你从Kubernetes专家成长为云原生架构师和技术领导者。</p>

                    <div class="learning-path">
                        <h4><i class="fas fa-mountain"></i> 大师级学习路线图</h4>
                        <div class="path-steps">
                            <div class="path-step">
                                <div class="step-icon">🎯</div>
                                <div class="step-content">
                                    <h5>深度理解</h5>
                                    <p>深入源码，理解K8s的设计哲学</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">🛠️</div>
                                <div class="step-content">
                                    <h5>扩展开发</h5>
                                    <p>开发自定义控制器和Operator</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">🏗️</div>
                                <div class="step-content">
                                    <h5>架构设计</h5>
                                    <p>设计企业级云原生架构</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">👑</div>
                                <div class="step-content">
                                    <h5>技术领导</h5>
                                    <p>成为技术领域的思想领袖</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <section id="expert-assessment">
                    <h2><span class="step-number">1</span>专家级技能评估</h2>

                    <div class="master-box">
                        <h3><i class="fas fa-medal"></i> 欢迎来到Kubernetes的巅峰挑战！</h3>
                        <p>在开始深度学习之前，我们需要确认你已经具备了专家级的基础。这不是简单的技能检查，而是对你Kubernetes理解深度的全面评估。只有通过这个评估，你才能真正踏上架构师之路。</p>
                    </div>

                    <h3><i class="fas fa-brain"></i> 深度理解评估</h3>
                    <div class="expert-box">
                        <h4><i class="fas fa-microscope"></i> 请诚实评估你对以下概念的理解深度：</h4>
                        <table>
                            <tr>
                                <th><i class="fas fa-cogs"></i> 核心概念</th>
                                <th><i class="fas fa-star"></i> 要求等级</th>
                                <th><i class="fas fa-question-circle"></i> 自我检验问题</th>
                            </tr>
                            <tr>
                                <td><strong>控制器模式</strong></td>
                                <td><span class="level-expert">专家级</span></td>
                                <td>能解释控制循环的工作原理？能设计自定义控制器？</td>
                            </tr>
                            <tr>
                                <td><strong>API Server架构</strong></td>
                                <td><span class="level-expert">专家级</span></td>
                                <td>理解API Server的请求处理流程？知道准入控制器的作用？</td>
                            </tr>
                            <tr>
                                <td><strong>调度器原理</strong></td>
                                <td><span class="level-expert">专家级</span></td>
                                <td>能解释调度算法？知道如何自定义调度策略？</td>
                            </tr>
                            <tr>
                                <td><strong>网络模型</strong></td>
                                <td><span class="level-master">大师级</span></td>
                                <td>深入理解CNI规范？能分析网络数据包流向？</td>
                            </tr>
                            <tr>
                                <td><strong>存储架构</strong></td>
                                <td><span class="level-expert">专家级</span></td>
                                <td>理解CSI驱动开发？知道存储快照的实现原理？</td>
                            </tr>
                            <tr>
                                <td><strong>安全模型</strong></td>
                                <td><span class="level-master">大师级</span></td>
                                <td>能设计零信任安全架构？理解Pod安全标准？</td>
                            </tr>
                        </table>
                    </div>

                    <h3><i class="fas fa-tools"></i> 实战经验评估</h3>
                    <div class="architect-box">
                        <h4><i class="fas fa-industry"></i> 生产环境经验检查：</h4>
                        <div class="path-steps">
                            <div class="path-step">
                                <div class="step-icon">🏭</div>
                                <div class="step-content">
                                    <h5>大规模集群管理</h5>
                                    <p>管理过100+节点的集群？处理过大规模故障？</p>
                                    <span class="level-expert">专家级</span>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">🔧</div>
                                <div class="step-content">
                                    <h5>性能调优</h5>
                                    <p>进行过集群性能优化？解决过资源瓶颈？</p>
                                    <span class="level-master">大师级</span>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">🚨</div>
                                <div class="step-content">
                                    <h5>故障排查</h5>
                                    <p>处理过复杂的生产故障？建立过监控体系？</p>
                                    <span class="level-expert">专家级</span>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">🏗️</div>
                                <div class="step-content">
                                    <h5>架构设计</h5>
                                    <p>设计过企业级K8s架构？制定过技术标准？</p>
                                    <span class="level-architect">架构师级</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h3><i class="fas fa-code"></i> 编程能力评估</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-laptop-code"></i> 深度学习需要的编程技能：</h4>
                        <table>
                            <tr>
                                <th><i class="fas fa-code"></i> 技能领域</th>
                                <th><i class="fas fa-star"></i> 最低要求</th>
                                <th><i class="fas fa-check-circle"></i> 验证方式</th>
                            </tr>
                            <tr>
                                <td><strong>Go语言</strong></td>
                                <td><span class="level-expert">专家级</span></td>
                                <td>能阅读K8s源码？能开发控制器？</td>
                            </tr>
                            <tr>
                                <td><strong>YAML/JSON</strong></td>
                                <td><span class="level-master">大师级</span></td>
                                <td>能设计复杂的资源模板？理解CRD规范？</td>
                            </tr>
                            <tr>
                                <td><strong>Shell脚本</strong></td>
                                <td><span class="level-expert">专家级</span></td>
                                <td>能编写复杂的自动化脚本？</td>
                            </tr>
                            <tr>
                                <td><strong>容器技术</strong></td>
                                <td><span class="level-master">大师级</span></td>
                                <td>理解容器运行时？能优化镜像构建？</td>
                            </tr>
                        </table>
                    </div>

                    <h3><i class="fas fa-graduation-cap"></i> 学习目标定位</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-target"></i> 根据你的评估结果，选择适合的学习目标：</h4>

                        <h5><i class="fas fa-crown"></i> 目标A：技术专家 → 架构师</h5>
                        <div class="info-box">
                            <p><strong>适合人群：</strong>已有3-5年K8s经验，想要转型做架构设计</p>
                            <p><strong>学习重点：</strong>架构设计、技术选型、团队协作</p>
                            <p><strong>时间投入：</strong>6-12个月</p>
                            <ul>
                                <li>深入学习K8s源码和设计原理</li>
                                <li>掌握多集群管理和平台工程</li>
                                <li>培养架构思维和技术视野</li>
                                <li>提升沟通和领导能力</li>
                            </ul>
                        </div>

                        <h5><i class="fas fa-rocket"></i> 目标B：平台工程师</h5>
                        <div class="advanced-box">
                            <p><strong>适合人群：</strong>专注于构建开发者平台和工具链</p>
                            <p><strong>学习重点：</strong>平台工程、开发者体验、自动化</p>
                            <p><strong>时间投入：</strong>4-8个月</p>
                            <ul>
                                <li>掌握Operator开发和自定义控制器</li>
                                <li>学习平台工程最佳实践</li>
                                <li>构建完整的开发者工具链</li>
                                <li>优化开发者体验和效率</li>
                            </ul>
                        </div>

                        <h5><i class="fas fa-brain"></i> 目标C：技术专家</h5>
                        <div class="expert-box">
                            <p><strong>适合人群：</strong>想要在技术深度上达到顶尖水平</p>
                            <p><strong>学习重点：</strong>源码贡献、性能优化、创新研究</p>
                            <p><strong>时间投入：</strong>持续学习</p>
                            <ul>
                                <li>深度参与K8s社区贡献</li>
                                <li>研究前沿技术和创新方案</li>
                                <li>成为某个技术领域的权威</li>
                                <li>指导和培养其他技术人员</li>
                            </ul>
                        </div>
                    </div>

                    <h3><i class="fas fa-road"></i> 学习路径规划</h3>
                    <div class="master-box">
                        <h4><i class="fas fa-map-marked-alt"></i> 个性化学习计划制定：</h4>

                        <h5>📊 第一阶段：深度理论（2-3个月）</h5>
                        <ul>
                            <li>深入学习K8s源码和架构设计</li>
                            <li>理解分布式系统的核心原理</li>
                            <li>掌握云原生技术栈的演进历史</li>
                        </ul>

                        <h5>🛠️ 第二阶段：实践开发（3-4个月）</h5>
                        <ul>
                            <li>开发自定义控制器和Operator</li>
                            <li>参与开源项目贡献代码</li>
                            <li>构建企业级解决方案</li>
                        </ul>

                        <h5>🏗️ 第三阶段：架构设计（2-3个月）</h5>
                        <ul>
                            <li>设计大规模云原生架构</li>
                            <li>制定技术标准和最佳实践</li>
                            <li>领导技术团队和项目</li>
                        </ul>

                        <h5>👑 第四阶段：技术领导（持续）</h5>
                        <ul>
                            <li>建立技术影响力和声誉</li>
                            <li>分享知识和培养人才</li>
                            <li>推动技术创新和发展</li>
                        </ul>
                    </div>

                    <div class="danger-box">
                        <h4><i class="fas fa-exclamation-triangle"></i> 重要提醒</h4>
                        <p>如果你发现自己在上述评估中还有明显的短板，强烈建议先回到进阶教程巩固基础：</p>
                        <ul>
                            <li><strong>理论基础不足：</strong>重新学习K8s架构和核心概念</li>
                            <li><strong>实战经验缺乏：</strong>多参与生产环境项目</li>
                            <li><strong>编程能力不够：</strong>加强Go语言和容器技术学习</li>
                            <li><strong>视野不够开阔：</strong>多了解云原生生态和最新技术</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-rocket"></i> 准备好迎接挑战了吗？</h4>
                        <p>如果你已经：</p>
                        <ul>
                            <li>✅ 对K8s核心概念有深度理解</li>
                            <li>✅ 具备丰富的生产环境经验</li>
                            <li>✅ 拥有扎实的编程基础</li>
                            <li>✅ 明确了学习目标和路径</li>
                        </ul>
                        <p><strong>那么，让我们开始这场通往Kubernetes大师之路的征程吧！🚀</strong></p>
                    </div>
                </section>

                <section id="source-code-deep-dive">
                    <h2><span class="step-number">2</span>源码深度解析</h2>

                    <div class="architect-box">
                        <h3><i class="fas fa-code"></i> 深入Kubernetes的内核</h3>
                        <p>要成为真正的Kubernetes专家，你必须理解它的内部工作原理。这不仅仅是阅读文档，而是要深入源码，理解每一个设计决策背后的思考。</p>
                    </div>

                    <h3><i class="fas fa-sitemap"></i> 源码架构概览</h3>
                    <div class="master-box">
                        <h4><i class="fas fa-folder-tree"></i> Kubernetes源码结构解析</h4>
                        <p>Kubernetes的源码组织体现了其模块化的设计哲学：</p>

                        <pre><code>kubernetes/
├── cmd/                    # 各组件的入口点
│   ├── kube-apiserver/    # API Server主程序
│   ├── kube-controller-manager/  # 控制器管理器
│   ├── kube-scheduler/    # 调度器
│   └── kubelet/          # 节点代理
├── pkg/                   # 核心业务逻辑
│   ├── api/              # API定义
│   ├── controller/       # 控制器实现
│   ├── scheduler/        # 调度逻辑
│   └── kubelet/         # kubelet实现
├── staging/              # 独立的子项目
│   ├── src/k8s.io/api/  # API类型定义
│   ├── src/k8s.io/client-go/  # 客户端库
│   └── src/k8s.io/apimachinery/ # API机制
└── vendor/               # 第三方依赖</code></pre>
                    </div>

                    <h3><i class="fas fa-cogs"></i> 控制器模式深度解析</h3>
                    <div class="expert-box">
                        <h4><i class="fas fa-sync"></i> 控制循环的精髓</h4>
                        <p>控制器模式是Kubernetes的核心设计模式，理解它是掌握K8s的关键：</p>

                        <pre><code>// 控制器的基本结构
type Controller struct {
    // 工作队列，存储需要处理的对象
    workqueue workqueue.RateLimitingInterface

    // 资源的Informer，监听资源变化
    deploymentInformer appsinformers.DeploymentInformer

    // 资源的Lister，用于查询资源
    deploymentsLister appslisters.DeploymentLister

    // 同步状态标志
    deploymentsSynced cache.InformerSynced
}

// 控制循环的核心逻辑
func (c *Controller) processNextWorkItem() bool {
    obj, shutdown := c.workqueue.Get()
    if shutdown {
        return false
    }

    defer c.workqueue.Done(obj)

    // 处理对象
    if err := c.syncHandler(key); err != nil {
        // 处理失败，重新入队
        c.workqueue.AddRateLimited(key)
        return true
    }

    // 处理成功，从队列中移除
    c.workqueue.Forget(obj)
    return true
}</code></pre>
                    </div>

                    <div class="info-box">
                        <h4><i class="fas fa-lightbulb"></i> 深度理解要点</h4>
                        <ul>
                            <li><strong>声明式API：</strong>用户声明期望状态，控制器负责实现</li>
                            <li><strong>最终一致性：</strong>系统会最终达到期望状态，但不保证立即</li>
                            <li><strong>幂等性：</strong>多次执行相同操作结果一致</li>
                            <li><strong>错误处理：</strong>失败时重试，成功时清理</li>
                        </ul>
                    </div>
                </section>

                <section id="custom-controllers">
                    <h2><span class="step-number">3</span>自定义控制器开发</h2>

                    <div class="master-box">
                        <h3><i class="fas fa-robot"></i> 从理论到实践的跨越</h3>
                        <p>理解了控制器模式的原理后，现在我们要亲手开发一个自定义控制器。这是从Kubernetes用户向Kubernetes开发者转变的关键一步。</p>
                    </div>

                    <h3><i class="fas fa-blueprint"></i> 控制器设计原则</h3>
                    <div class="architect-box">
                        <h4><i class="fas fa-compass"></i> 设计哲学</h4>
                        <p>在开发自定义控制器之前，我们需要理解Kubernetes的设计哲学：</p>
                        <ul>
                            <li><strong>声明式优于命令式：</strong>用户声明期望状态，而不是执行步骤</li>
                            <li><strong>控制器负责协调：</strong>持续监控并调整实际状态向期望状态靠拢</li>
                            <li><strong>幂等性保证：</strong>多次执行相同操作结果一致</li>
                            <li><strong>错误恢复机制：</strong>失败时能够自动重试和恢复</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-code"></i> 实战：开发一个应用控制器</h3>
                    <div class="expert-box">
                        <h4><i class="fas fa-rocket"></i> 项目目标</h4>
                        <p>我们将开发一个名为"AppController"的控制器，它能够：</p>
                        <ul>
                            <li>管理应用的完整生命周期</li>
                            <li>自动处理配置更新</li>
                            <li>实现蓝绿部署策略</li>
                            <li>提供健康检查和自愈能力</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-file-code"></i> 1. 定义CRD（自定义资源定义）</h4>
                    <pre><code>// api/v1/app_types.go
package v1

import (
    metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// AppSpec 定义应用的期望状态
type AppSpec struct {
    // 应用镜像
    Image string `json:"image"`

    // 副本数量
    Replicas *int32 `json:"replicas,omitempty"`

    // 端口配置
    Port int32 `json:"port"`

    // 环境变量
    Env []EnvVar `json:"env,omitempty"`

    // 部署策略
    Strategy DeploymentStrategy `json:"strategy,omitempty"`
}

// AppStatus 定义应用的当前状态
type AppStatus struct {
    // 当前副本数
    ReadyReplicas int32 `json:"readyReplicas"`

    // 部署状态
    Phase AppPhase `json:"phase"`

    // 状态消息
    Message string `json:"message,omitempty"`

    // 最后更新时间
    LastUpdateTime metav1.Time `json:"lastUpdateTime,omitempty"`
}

// App 是应用资源的完整定义
// +kubebuilder:object:root=true
// +kubebuilder:subresource:status
type App struct {
    metav1.TypeMeta   `json:",inline"`
    metav1.ObjectMeta `json:"metadata,omitempty"`

    Spec   AppSpec   `json:"spec,omitempty"`
    Status AppStatus `json:"status,omitempty"`
}</code></pre>

                    <h4><i class="fas fa-cogs"></i> 2. 实现控制器逻辑</h4>
                    <pre><code>// controllers/app_controller.go
func (r *AppReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
    log := r.Log.WithValues("app", req.NamespacedName)

    // 获取App实例
    var app appsv1.App
    if err := r.Get(ctx, req.NamespacedName, &app); err != nil {
        if errors.IsNotFound(err) {
            // App已被删除，清理相关资源
            return ctrl.Result{}, nil
        }
        return ctrl.Result{}, err
    }

    // 协调Deployment
    if err := r.reconcileDeployment(ctx, &app); err != nil {
        return ctrl.Result{}, err
    }

    // 协调Service
    if err := r.reconcileService(ctx, &app); err != nil {
        return ctrl.Result{}, err
    }

    // 更新状态
    if err := r.updateStatus(ctx, &app); err != nil {
        return ctrl.Result{}, err
    }

    return ctrl.Result{RequeueAfter: time.Minute * 5}, nil
}</code></pre>

                    <h3><i class="fas fa-test-tube"></i> 测试和调试</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-bug"></i> 调试技巧</h4>
                        <ul>
                            <li><strong>日志记录：</strong>在关键位置添加详细日志</li>
                            <li><strong>事件记录：</strong>使用Event API记录重要操作</li>
                            <li><strong>指标监控：</strong>暴露Prometheus指标</li>
                            <li><strong>单元测试：</strong>为控制器逻辑编写测试</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-graduation-cap"></i> 实践建议</h4>
                        <p>开发自定义控制器是一个复杂的过程，建议：</p>
                        <ul>
                            <li>从简单的控制器开始，逐步增加复杂性</li>
                            <li>深入学习controller-runtime框架</li>
                            <li>参考Kubernetes官方控制器的实现</li>
                            <li>在测试环境中充分验证</li>
                        </ul>
                    </div>
                </section>

                <section id="operators-development">
                    <h2><span class="step-number">4</span>Operator开发精通</h2>

                    <div class="architect-box">
                        <h3><i class="fas fa-cogs"></i> Operator：应用运维的自动化</h3>
                        <p>Operator是Kubernetes的一个重要概念，它将人类运维专家的知识编码成软件，实现应用的自动化运维。掌握Operator开发是成为Kubernetes专家的必经之路。</p>
                    </div>

                    <h3><i class="fas fa-layer-group"></i> Operator成熟度模型</h3>
                    <div class="master-box">
                        <h4><i class="fas fa-stairs"></i> 五个成熟度级别</h4>
                        <table>
                            <tr>
                                <th><i class="fas fa-level-up-alt"></i> 级别</th>
                                <th><i class="fas fa-tasks"></i> 能力</th>
                                <th><i class="fas fa-lightbulb"></i> 示例</th>
                            </tr>
                            <tr>
                                <td><span class="level-expert">Level 1</span></td>
                                <td>基本安装</td>
                                <td>自动化应用部署</td>
                            </tr>
                            <tr>
                                <td><span class="level-expert">Level 2</span></td>
                                <td>无缝升级</td>
                                <td>滚动更新、版本管理</td>
                            </tr>
                            <tr>
                                <td><span class="level-master">Level 3</span></td>
                                <td>完整生命周期</td>
                                <td>备份、恢复、扩缩容</td>
                            </tr>
                            <tr>
                                <td><span class="level-master">Level 4</span></td>
                                <td>深度洞察</td>
                                <td>监控、告警、性能调优</td>
                            </tr>
                            <tr>
                                <td><span class="level-architect">Level 5</span></td>
                                <td>自动驾驶</td>
                                <td>故障自愈、智能调度</td>
                            </tr>
                        </table>
                    </div>

                    <h3><i class="fas fa-database"></i> 实战：开发MySQL Operator</h3>
                    <div class="expert-box">
                        <h4><i class="fas fa-target"></i> 项目目标</h4>
                        <p>我们将开发一个生产级的MySQL Operator，具备以下能力：</p>
                        <ul>
                            <li>自动化MySQL集群部署</li>
                            <li>主从复制配置</li>
                            <li>自动备份和恢复</li>
                            <li>故障检测和自愈</li>
                            <li>性能监控和调优</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-file-code"></i> 1. 定义MySQL集群CRD</h4>
                    <pre><code>// api/v1/mysqlcluster_types.go
type MySQLClusterSpec struct {
    // MySQL版本
    Version string `json:"version"`

    // 集群大小
    Size int32 `json:"size"`

    // 存储配置
    Storage StorageSpec `json:"storage"`

    // 备份策略
    BackupPolicy *BackupPolicy `json:"backupPolicy,omitempty"`

    // 监控配置
    Monitoring *MonitoringSpec `json:"monitoring,omitempty"`
}

type MySQLClusterStatus struct {
    // 集群状态
    Phase ClusterPhase `json:"phase"`

    // 主节点信息
    Master *NodeStatus `json:"master,omitempty"`

    // 从节点信息
    Slaves []NodeStatus `json:"slaves,omitempty"`

    // 备份状态
    LastBackup *BackupStatus `json:"lastBackup,omitempty"`
}</code></pre>

                    <h4><i class="fas fa-brain"></i> 2. 实现智能控制逻辑</h4>
                    <pre><code>func (r *MySQLClusterReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
    // 获取集群实例
    cluster := &mysqlv1.MySQLCluster{}
    if err := r.Get(ctx, req.NamespacedName, cluster); err != nil {
        return ctrl.Result{}, client.IgnoreNotFound(err)
    }

    // 状态机处理
    switch cluster.Status.Phase {
    case mysqlv1.ClusterPhaseInitializing:
        return r.handleInitializing(ctx, cluster)
    case mysqlv1.ClusterPhaseRunning:
        return r.handleRunning(ctx, cluster)
    case mysqlv1.ClusterPhaseUpgrading:
        return r.handleUpgrading(ctx, cluster)
    case mysqlv1.ClusterPhaseFailed:
        return r.handleFailed(ctx, cluster)
    default:
        return r.handleUnknown(ctx, cluster)
    }
}</code></pre>

                    <h3><i class="fas fa-tools"></i> Operator开发工具链</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-toolbox"></i> 推荐工具</h4>
                        <ul>
                            <li><strong>Operator SDK：</strong>快速生成Operator脚手架</li>
                            <li><strong>Kubebuilder：</strong>Kubernetes官方推荐的开发框架</li>
                            <li><strong>KUDO：</strong>声明式Operator开发平台</li>
                            <li><strong>Helm Operator：</strong>基于Helm Chart的Operator</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-star"></i> 最佳实践</h4>
                        <ul>
                            <li>遵循Operator成熟度模型，逐步提升能力</li>
                            <li>实现完整的可观测性（日志、指标、追踪）</li>
                            <li>设计优雅的错误处理和恢复机制</li>
                            <li>提供丰富的文档和示例</li>
                            <li>建立完善的测试体系</li>
                        </ul>
                    </div>
                </section>

                <section id="multi-cluster-management">
                    <h2><span class="step-number">5</span>多集群管理</h2>

                    <div class="architect-box">
                        <h3><i class="fas fa-globe"></i> 企业级多集群架构</h3>
                        <p>随着Kubernetes在企业中的广泛应用，单一集群已经无法满足复杂的业务需求。多集群管理成为云原生架构师必须掌握的核心技能。</p>
                    </div>

                    <h3><i class="fas fa-sitemap"></i> 多集群架构模式</h3>
                    <div class="master-box">
                        <h4><i class="fas fa-project-diagram"></i> 常见架构模式</h4>
                        <div class="path-steps">
                            <div class="path-step">
                                <div class="step-icon">🏢</div>
                                <div class="step-content">
                                    <h5>环境隔离模式</h5>
                                    <p>开发、测试、生产环境分别使用独立集群</p>
                                    <span class="level-expert">专家级</span>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">🌍</div>
                                <div class="step-content">
                                    <h5>地理分布模式</h5>
                                    <p>不同地区部署集群，提供就近服务</p>
                                    <span class="level-master">大师级</span>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">🏭</div>
                                <div class="step-content">
                                    <h5>租户隔离模式</h5>
                                    <p>为不同租户提供独立的集群资源</p>
                                    <span class="level-architect">架构师级</span>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">⚡</div>
                                <div class="step-content">
                                    <h5>混合云模式</h5>
                                    <p>跨云厂商的集群联邦管理</p>
                                    <span class="level-guru">大师级</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h3><i class="fas fa-network-wired"></i> 集群联邦技术</h3>
                    <div class="expert-box">
                        <h4><i class="fas fa-link"></i> 主流解决方案对比</h4>
                        <table>
                            <tr>
                                <th><i class="fas fa-tools"></i> 方案</th>
                                <th><i class="fas fa-star"></i> 成熟度</th>
                                <th><i class="fas fa-lightbulb"></i> 适用场景</th>
                                <th><i class="fas fa-balance-scale"></i> 优缺点</th>
                            </tr>
                            <tr>
                                <td><strong>Admiral</strong></td>
                                <td><span class="level-expert">专家级</span></td>
                                <td>服务网格多集群</td>
                                <td>与Istio深度集成，但功能相对单一</td>
                            </tr>
                            <tr>
                                <td><strong>Submariner</strong></td>
                                <td><span class="level-master">大师级</span></td>
                                <td>网络连通性</td>
                                <td>网络连接能力强，但配置复杂</td>
                            </tr>
                            <tr>
                                <td><strong>Liqo</strong></td>
                                <td><span class="level-advanced">高级</span></td>
                                <td>资源共享</td>
                                <td>创新的资源共享模式，但还在发展中</td>
                            </tr>
                            <tr>
                                <td><strong>Cluster API</strong></td>
                                <td><span class="level-architect">架构师级</span></td>
                                <td>集群生命周期管理</td>
                                <td>标准化程度高，但学习曲线陡峭</td>
                            </tr>
                        </table>
                    </div>

                    <h3><i class="fas fa-shield-alt"></i> 多集群安全架构</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-lock"></i> 安全挑战与解决方案</h4>
                        <ul>
                            <li><strong>身份认证：</strong>统一的身份认证和授权体系</li>
                            <li><strong>网络安全：</strong>集群间安全通信和网络隔离</li>
                            <li><strong>数据保护：</strong>敏感数据的加密和访问控制</li>
                            <li><strong>合规审计：</strong>跨集群的操作审计和合规检查</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-lightbulb"></i> 实施建议</h4>
                        <p>多集群管理是一个复杂的系统工程，建议：</p>
                        <ul>
                            <li>从简单的环境隔离开始，逐步扩展到复杂场景</li>
                            <li>重点关注网络连通性和安全性</li>
                            <li>建立统一的监控和运维体系</li>
                            <li>制定详细的灾难恢复计划</li>
                        </ul>
                    </div>
                </section>

                <section id="service-mesh-mastery">
                    <h2><span class="step-number">6</span>服务网格精通</h2>

                    <div class="architect-box">
                        <h3><i class="fas fa-project-diagram"></i> 服务网格：微服务通信的基础设施</h3>
                        <p>服务网格是云原生架构中的关键组件，它为微服务间的通信提供了统一的基础设施层。掌握服务网格技术是现代架构师的必备技能。</p>
                    </div>

                    <h3><i class="fas fa-layer-group"></i> 服务网格架构深度解析</h3>
                    <div class="master-box">
                        <h4><i class="fas fa-sitemap"></i> Istio架构组件</h4>
                        <ul>
                            <li><strong>数据平面（Data Plane）：</strong>Envoy代理，处理服务间通信</li>
                            <li><strong>控制平面（Control Plane）：</strong>Istiod，管理和配置代理</li>
                            <li><strong>Pilot：</strong>服务发现和流量管理</li>
                            <li><strong>Citadel：</strong>安全和证书管理</li>
                            <li><strong>Galley：</strong>配置验证和分发</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-route"></i> 高级流量管理</h3>
                    <div class="expert-box">
                        <h4><i class="fas fa-traffic-light"></i> 流量管理策略</h4>
                        <pre><code># 金丝雀发布配置
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: reviews-canary
spec:
  hosts:
  - reviews
  http:
  - match:
    - headers:
        canary:
          exact: "true"
    route:
    - destination:
        host: reviews
        subset: v2
  - route:
    - destination:
        host: reviews
        subset: v1
      weight: 90
    - destination:
        host: reviews
        subset: v2
      weight: 10</code></pre>
                    </div>

                    <h3><i class="fas fa-shield-alt"></i> 零信任安全模型</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-lock"></i> mTLS和安全策略</h4>
                        <ul>
                            <li><strong>自动mTLS：</strong>服务间通信自动加密</li>
                            <li><strong>授权策略：</strong>细粒度的访问控制</li>
                            <li><strong>JWT验证：</strong>端用户身份认证</li>
                            <li><strong>安全审计：</strong>全链路安全监控</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-graduation-cap"></i> 进阶实践</h4>
                        <p>服务网格的高级应用包括：</p>
                        <ul>
                            <li>多集群服务网格部署</li>
                            <li>自定义Envoy过滤器开发</li>
                            <li>服务网格性能调优</li>
                            <li>与可观测性工具的深度集成</li>
                        </ul>
                    </div>
                </section>

                <section id="platform-engineering">
                    <h2><span class="step-number">7</span>平台工程</h2>

                    <div class="architect-box">
                        <h3><i class="fas fa-building"></i> 构建开发者平台</h3>
                        <p>平台工程是当前云原生领域的热门话题。它专注于构建和维护支持软件交付的工具链和工作流，让开发者能够自助式地部署和管理应用。</p>
                    </div>

                    <h3><i class="fas fa-users"></i> 开发者体验设计</h3>
                    <div class="master-box">
                        <h4><i class="fas fa-heart"></i> 以开发者为中心的设计原则</h4>
                        <ul>
                            <li><strong>自助服务：</strong>开发者能够独立完成常见任务</li>
                            <li><strong>认知负载最小化：</strong>隐藏复杂性，暴露必要接口</li>
                            <li><strong>快速反馈：</strong>提供即时的状态反馈和错误信息</li>
                            <li><strong>标准化：</strong>统一的工具、流程和最佳实践</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-tools"></i> 平台工程工具栈</h3>
                    <div class="expert-box">
                        <h4><i class="fas fa-layer-group"></i> 核心组件</h4>
                        <div class="path-steps">
                            <div class="path-step">
                                <div class="step-icon">🎯</div>
                                <div class="step-content">
                                    <h5>应用抽象层</h5>
                                    <p>Backstage、Port、Humanitec</p>
                                    <span class="level-architect">架构师级</span>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">🚀</div>
                                <div class="step-content">
                                    <h5>部署引擎</h5>
                                    <p>ArgoCD、Flux、Tekton</p>
                                    <span class="level-expert">专家级</span>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">📊</div>
                                <div class="step-content">
                                    <h5>可观测性</h5>
                                    <p>Prometheus、Grafana、Jaeger</p>
                                    <span class="level-master">大师级</span>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">🔒</div>
                                <div class="step-content">
                                    <h5>安全扫描</h5>
                                    <p>Falco、OPA、Twistlock</p>
                                    <span class="level-expert">专家级</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h3><i class="fas fa-chart-line"></i> 平台成熟度评估</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-stairs"></i> 成熟度模型</h4>
                        <table>
                            <tr>
                                <th><i class="fas fa-level-up-alt"></i> 级别</th>
                                <th><i class="fas fa-tasks"></i> 特征</th>
                                <th><i class="fas fa-target"></i> 目标</th>
                            </tr>
                            <tr>
                                <td><span class="level-expert">Level 1</span></td>
                                <td>基础自动化</td>
                                <td>CI/CD流水线</td>
                            </tr>
                            <tr>
                                <td><span class="level-expert">Level 2</span></td>
                                <td>自助服务</td>
                                <td>开发者门户</td>
                            </tr>
                            <tr>
                                <td><span class="level-master">Level 3</span></td>
                                <td>智能化运维</td>
                                <td>自动扩缩容、自愈</td>
                            </tr>
                            <tr>
                                <td><span class="level-architect">Level 4</span></td>
                                <td>预测性分析</td>
                                <td>AI驱动的优化</td>
                            </tr>
                        </table>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-rocket"></i> 实施策略</h4>
                        <p>构建成功的开发者平台需要：</p>
                        <ul>
                            <li>深入理解开发者需求和痛点</li>
                            <li>采用渐进式的平台演进策略</li>
                            <li>建立平台团队和治理机制</li>
                            <li>持续收集反馈并迭代改进</li>
                        </ul>
                    </div>
                </section>

                <section id="cloud-native-architecture">
                    <h2><span class="step-number">8</span>云原生架构设计</h2>

                    <div class="architect-box">
                        <h3><i class="fas fa-cloud"></i> 企业级云原生架构</h3>
                        <p>云原生架构设计是架构师的核心技能。它不仅仅是技术的组合，更是一种设计哲学和方法论，需要综合考虑业务需求、技术约束和组织能力。</p>
                    </div>

                    <h3><i class="fas fa-blueprint"></i> 架构设计原则</h3>
                    <div class="master-box">
                        <h4><i class="fas fa-compass"></i> 云原生十二要素</h4>
                        <ul>
                            <li><strong>代码库：</strong>一个代码库，多个部署</li>
                            <li><strong>依赖：</strong>显式声明和隔离依赖</li>
                            <li><strong>配置：</strong>在环境中存储配置</li>
                            <li><strong>后端服务：</strong>把后端服务当作附加资源</li>
                            <li><strong>构建、发布、运行：</strong>严格分离构建和运行</li>
                            <li><strong>进程：</strong>以一个或多个无状态进程运行应用</li>
                            <li><strong>端口绑定：</strong>通过端口绑定提供服务</li>
                            <li><strong>并发：</strong>通过进程模型进行扩展</li>
                            <li><strong>易处理：</strong>快速启动和优雅终止</li>
                            <li><strong>开发环境与线上环境等价：</strong>尽可能保持一致</li>
                            <li><strong>日志：</strong>把日志当作事件流</li>
                            <li><strong>管理进程：</strong>后台管理任务当作一次性进程运行</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-sitemap"></i> 参考架构模式</h3>
                    <div class="expert-box">
                        <h4><i class="fas fa-layer-group"></i> 分层架构设计</h4>
                        <pre><code>┌─────────────────────────────────────────┐
│              用户接入层                    │
│  CDN + API Gateway + Load Balancer      │
├─────────────────────────────────────────┤
│              应用服务层                    │
│  微服务 + 服务网格 + 容器编排             │
├─────────────────────────────────────────┤
│              数据服务层                    │
│  数据库 + 缓存 + 消息队列 + 存储          │
├─────────────────────────────────────────┤
│              基础设施层                    │
│  Kubernetes + 容器运行时 + 网络 + 存储   │
└─────────────────────────────────────────┘</code></pre>
                    </div>

                    <h3><i class="fas fa-balance-scale"></i> 架构权衡决策</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-scale-balanced"></i> 关键权衡点</h4>
                        <ul>
                            <li><strong>性能 vs 成本：</strong>资源配置和成本控制的平衡</li>
                            <li><strong>一致性 vs 可用性：</strong>CAP定理在分布式系统中的应用</li>
                            <li><strong>复杂性 vs 灵活性：</strong>架构复杂度与业务适应性</li>
                            <li><strong>安全性 vs 便利性：</strong>安全措施与开发效率的平衡</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-lightbulb"></i> 架构演进策略</h4>
                        <p>成功的云原生架构需要：</p>
                        <ul>
                            <li>从业务需求出发，技术服务于业务</li>
                            <li>采用演进式架构，支持持续改进</li>
                            <li>建立架构治理机制和标准</li>
                            <li>重视非功能性需求（性能、安全、可维护性）</li>
                        </ul>
                    </div>
                </section>

                <section id="performance-optimization">
                    <h2><span class="step-number">9</span>性能优化大师</h2>

                    <div class="master-box">
                        <h3><i class="fas fa-tachometer-alt"></i> 性能优化的艺术与科学</h3>
                        <p>性能优化是一门既需要深厚理论基础，又需要丰富实战经验的技能。在云原生环境中，性能优化涉及多个层面，从应用代码到基础设施，每一层都有优化的空间。</p>
                    </div>

                    <h3><i class="fas fa-chart-line"></i> 性能监控体系</h3>
                    <div class="expert-box">
                        <h4><i class="fas fa-eye"></i> 四个黄金信号</h4>
                        <ul>
                            <li><strong>延迟（Latency）：</strong>请求处理时间</li>
                            <li><strong>流量（Traffic）：</strong>系统处理的请求量</li>
                            <li><strong>错误（Errors）：</strong>失败请求的比率</li>
                            <li><strong>饱和度（Saturation）：</strong>系统资源利用率</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-cogs"></i> 集群级性能调优</h3>
                    <div class="architect-box">
                        <h4><i class="fas fa-server"></i> 关键优化点</h4>
                        <table>
                            <tr>
                                <th><i class="fas fa-layer-group"></i> 层面</th>
                                <th><i class="fas fa-tools"></i> 优化项</th>
                                <th><i class="fas fa-chart-bar"></i> 预期收益</th>
                            </tr>
                            <tr>
                                <td><strong>节点层</strong></td>
                                <td>内核参数、CPU亲和性</td>
                                <td>10-30%性能提升</td>
                            </tr>
                            <tr>
                                <td><strong>网络层</strong></td>
                                <td>CNI优化、负载均衡</td>
                                <td>20-50%延迟降低</td>
                            </tr>
                            <tr>
                                <td><strong>存储层</strong></td>
                                <td>存储类选择、IO优化</td>
                                <td>2-10倍IOPS提升</td>
                            </tr>
                            <tr>
                                <td><strong>应用层</strong></td>
                                <td>资源配置、JVM调优</td>
                                <td>30-100%吞吐量提升</td>
                            </tr>
                        </table>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-trophy"></i> 性能优化方法论</h4>
                        <p>系统性的性能优化方法：</p>
                        <ol>
                            <li><strong>建立基线：</strong>测量当前性能水平</li>
                            <li><strong>识别瓶颈：</strong>找到性能限制因素</li>
                            <li><strong>制定策略：</strong>选择合适的优化方案</li>
                            <li><strong>实施优化：</strong>逐步实施优化措施</li>
                            <li><strong>验证效果：</strong>测量优化后的性能</li>
                            <li><strong>持续监控：</strong>建立长期监控机制</li>
                        </ol>
                    </div>
                </section>

                <section id="security-architecture">
                    <h2><span class="step-number">10</span>安全架构设计</h2>

                    <div class="danger-box">
                        <h3><i class="fas fa-shield-alt"></i> 零信任安全架构</h3>
                        <p>在云原生环境中，传统的边界安全模型已经不再适用。零信任安全架构成为保护现代应用和基础设施的核心理念。</p>
                    </div>

                    <h3><i class="fas fa-lock"></i> 安全设计原则</h3>
                    <div class="architect-box">
                        <h4><i class="fas fa-shield-virus"></i> 纵深防御策略</h4>
                        <ul>
                            <li><strong>最小权限原则：</strong>只授予完成任务所需的最小权限</li>
                            <li><strong>默认拒绝：</strong>默认拒绝所有访问，明确授权才允许</li>
                            <li><strong>持续验证：</strong>每次访问都需要验证身份和权限</li>
                            <li><strong>加密传输：</strong>所有数据传输都必须加密</li>
                            <li><strong>审计日志：</strong>记录所有安全相关的操作</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-user-shield"></i> 身份和访问管理</h3>
                    <div class="expert-box">
                        <h4><i class="fas fa-key"></i> IAM最佳实践</h4>
                        <pre><code># RBAC配置示例
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: production
  name: pod-reader
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "watch", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: read-pods
  namespace: production
subjects:
- kind: User
  name: jane
  apiGroup: rbac.authorization.k8s.io
roleRef:
  kind: Role
  name: pod-reader
  apiGroup: rbac.authorization.k8s.io</code></pre>
                    </div>

                    <h3><i class="fas fa-bug"></i> 安全扫描和合规</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-search"></i> 安全工具链</h4>
                        <ul>
                            <li><strong>镜像扫描：</strong>Trivy、Clair、Anchore</li>
                            <li><strong>配置检查：</strong>Falco、OPA Gatekeeper</li>
                            <li><strong>运行时保护：</strong>Falco、Sysdig Secure</li>
                            <li><strong>网络安全：</strong>Calico、Cilium Network Policies</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-certificate"></i> 合规框架</h4>
                        <p>常见的合规标准和框架：</p>
                        <ul>
                            <li>CIS Kubernetes Benchmark</li>
                            <li>NIST Cybersecurity Framework</li>
                            <li>SOC 2 Type II</li>
                            <li>ISO 27001</li>
                            <li>PCI DSS（支付卡行业数据安全标准）</li>
                        </ul>
                    </div>
                </section>

                <section id="community-contribution">
                    <h2><span class="step-number">11</span>社区贡献</h2>

                    <div class="success-box">
                        <h3><i class="fas fa-users"></i> 参与开源社区</h3>
                        <p>参与Kubernetes社区不仅能提升技术水平，还能建立行业影响力。从代码贡献到技术布道，每一种参与方式都有其价值。</p>
                    </div>

                    <h3><i class="fas fa-code-branch"></i> 贡献方式</h3>
                    <div class="master-box">
                        <h4><i class="fas fa-hands-helping"></i> 多元化贡献路径</h4>
                        <div class="path-steps">
                            <div class="path-step">
                                <div class="step-icon">💻</div>
                                <div class="step-content">
                                    <h5>代码贡献</h5>
                                    <p>修复Bug、新功能开发、性能优化</p>
                                    <span class="level-expert">专家级</span>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">📚</div>
                                <div class="step-content">
                                    <h5>文档改进</h5>
                                    <p>完善文档、翻译、教程编写</p>
                                    <span class="level-master">大师级</span>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">🐛</div>
                                <div class="step-content">
                                    <h5>问题报告</h5>
                                    <p>Bug报告、功能请求、测试反馈</p>
                                    <span class="level-expert">专家级</span>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">🎤</div>
                                <div class="step-content">
                                    <h5>技术布道</h5>
                                    <p>演讲、博客、培训、社区活动</p>
                                    <span class="level-architect">架构师级</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h3><i class="fas fa-graduation-cap"></i> 成为Kubernetes专家</h3>
                    <div class="expert-box">
                        <h4><i class="fas fa-star"></i> 专家成长路径</h4>
                        <ol>
                            <li><strong>深入学习：</strong>掌握Kubernetes核心原理和源码</li>
                            <li><strong>实践应用：</strong>在生产环境中积累经验</li>
                            <li><strong>分享知识：</strong>通过博客、演讲分享经验</li>
                            <li><strong>参与社区：</strong>贡献代码、参与讨论</li>
                            <li><strong>指导他人：</strong>帮助新人成长</li>
                            <li><strong>推动创新：</strong>提出新的解决方案</li>
                        </ol>
                    </div>

                    <div class="info-box">
                        <h4><i class="fas fa-trophy"></i> 社区认可</h4>
                        <p>Kubernetes社区的认可体系：</p>
                        <ul>
                            <li><strong>Contributor：</strong>代码或文档贡献者</li>
                            <li><strong>Reviewer：</strong>代码审查者</li>
                            <li><strong>Approver：</strong>代码批准者</li>
                            <li><strong>Maintainer：</strong>项目维护者</li>
                            <li><strong>Steering Committee：</strong>指导委员会成员</li>
                        </ul>
                    </div>
                </section>

                <section id="thought-leadership">
                    <h2><span class="step-number">12</span>技术领导力</h2>

                    <div class="architect-box">
                        <h3><i class="fas fa-lightbulb"></i> 从技术专家到技术领袖</h3>
                        <p>技术领导力不仅仅是技术能力的体现，更是影响力、远见和执行力的综合。在云原生时代，技术领袖需要具备更广阔的视野和更强的适应能力。</p>
                    </div>

                    <h3><i class="fas fa-brain"></i> 技术远见</h3>
                    <div class="master-box">
                        <h4><i class="fas fa-telescope"></i> 前瞻性思维</h4>
                        <ul>
                            <li><strong>技术趋势洞察：</strong>敏锐捕捉技术发展方向</li>
                            <li><strong>业务技术融合：</strong>将技术与业务价值结合</li>
                            <li><strong>生态系统思维：</strong>理解技术在整个生态中的位置</li>
                            <li><strong>风险评估能力：</strong>平衡创新与稳定性</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-users-cog"></i> 团队建设</h3>
                    <div class="expert-box">
                        <h4><i class="fas fa-people-arrows"></i> 高效团队特征</h4>
                        <table>
                            <tr>
                                <th><i class="fas fa-star"></i> 特征</th>
                                <th><i class="fas fa-lightbulb"></i> 表现</th>
                                <th><i class="fas fa-tools"></i> 培养方法</th>
                            </tr>
                            <tr>
                                <td><strong>技术卓越</strong></td>
                                <td>高质量的代码和架构</td>
                                <td>代码审查、技术分享、持续学习</td>
                            </tr>
                            <tr>
                                <td><strong>协作效率</strong></td>
                                <td>顺畅的沟通和协作</td>
                                <td>敏捷实践、工具标准化、文档规范</td>
                            </tr>
                            <tr>
                                <td><strong>创新能力</strong></td>
                                <td>主动解决问题和改进</td>
                                <td>鼓励实验、容忍失败、奖励创新</td>
                            </tr>
                            <tr>
                                <td><strong>学习成长</strong></td>
                                <td>持续提升技能和知识</td>
                                <td>培训计划、导师制度、技术挑战</td>
                            </tr>
                        </table>
                    </div>

                    <h3><i class="fas fa-chart-line"></i> 影响力建设</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-megaphone"></i> 扩大技术影响力</h4>
                        <ul>
                            <li><strong>内部影响：</strong>在组织内建立技术权威</li>
                            <li><strong>行业影响：</strong>在行业内建立专业声誉</li>
                            <li><strong>社区影响：</strong>在开源社区中发挥作用</li>
                            <li><strong>思想影响：</strong>推动技术理念和最佳实践</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-road"></i> 持续成长</h3>
                    <div class="architect-box">
                        <h4><i class="fas fa-infinity"></i> 终身学习</h4>
                        <p>技术领袖的成长永无止境：</p>
                        <ul>
                            <li><strong>技术深度：</strong>在专业领域保持领先</li>
                            <li><strong>技术广度：</strong>了解相关技术领域</li>
                            <li><strong>业务理解：</strong>深入理解业务需求</li>
                            <li><strong>管理技能：</strong>提升团队管理能力</li>
                            <li><strong>沟通表达：</strong>增强影响力和说服力</li>
                        </ul>
                    </div>

                    <div class="master-box">
                        <h4><i class="fas fa-crown"></i> 成为技术领袖的里程碑</h4>
                        <ol>
                            <li><strong>技术专家阶段：</strong>在某个技术领域达到专家水平</li>
                            <li><strong>架构师阶段：</strong>能够设计复杂的技术架构</li>
                            <li><strong>技术经理阶段：</strong>领导技术团队完成项目</li>
                            <li><strong>技术总监阶段：</strong>制定技术战略和标准</li>
                            <li><strong>CTO阶段：</strong>负责整个组织的技术方向</li>
                            <li><strong>行业领袖阶段：</strong>在行业内具有重要影响力</li>
                        </ol>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-heart"></i> 结语：技术改变世界</h4>
                        <p>恭喜你完成了这个深度学习之旅！从Kubernetes专家到架构师，从技术深度到领导力，你已经掌握了成为云原生领域技术领袖的核心技能。</p>

                        <p><strong>记住：</strong></p>
                        <ul>
                            <li>🎯 <strong>技术服务于业务：</strong>始终以创造业务价值为目标</li>
                            <li>🌱 <strong>保持学习热情：</strong>技术在不断发展，学习永不停止</li>
                            <li>🤝 <strong>分享与协作：</strong>通过分享知识和协作创造更大价值</li>
                            <li>🚀 <strong>推动技术进步：</strong>不仅要跟上技术发展，更要推动技术创新</li>
                            <li>💡 <strong>培养下一代：</strong>帮助更多人成长为优秀的技术人才</li>
                        </ul>

                        <p><strong>愿你在云原生的世界里，不仅成为技术的掌控者，更成为技术发展的推动者和引领者！🌟</strong></p>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <a href="#" class="back-to-top" id="backToTop"><i class="fas fa-arrow-up"></i></a>

    <script>
        // 移动端菜单切换
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const sidebar = document.getElementById('sidebar');

        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', function () {
                sidebar.classList.toggle('active');
            });
        }

        // 侧边栏导航高亮
        function updateActiveNav() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.sidebar a');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                if (window.pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        }

        // 返回顶部功能
        function toggleBackToTop() {
            const backToTop = document.getElementById("backToTop");
            if (document.body.scrollTop > 300 || document.documentElement.scrollTop > 300) {
                backToTop.style.display = "flex";
            } else {
                backToTop.style.display = "none";
            }
        }

        // 滚动事件监听
        window.addEventListener('scroll', function () {
            updateActiveNav();
            toggleBackToTop();
        });

        // 返回顶部点击事件
        const backToTopBtn = document.getElementById("backToTop");
        if (backToTopBtn) {
            backToTopBtn.addEventListener('click', function (e) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // 平滑滚动
        document.querySelectorAll('.sidebar a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
                // 移动端关闭菜单
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                }
            });
        });

        // 点击外部关闭移动端菜单
        document.addEventListener('click', function (e) {
            if (window.innerWidth <= 768 && !sidebar.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                sidebar.classList.remove('active');
            }
        });

        // 初始化导航高亮
        updateActiveNav();

        // 页面加载动画
        window.addEventListener('load', function () {
            document.body.style.opacity = '1';
        });
    </script>
</body>

</html>