﻿# PostgreSQL 11.19 离线版完整备份恢复说明
备份时间: 20250712_231429
备份类型: 完整备份（配置文件 + SQL数据 + 数据卷）

## 备份内容
1. 配置文件: docker-compose.yml, Dockerfile, postgresql.conf, pg_hba.conf, docker-entrypoint.sh
2. 初始化脚本: init-scripts目录
3. SQL数据: postgresql_all_databases.sql.zip
4. 数据卷:
   - postgres_data_volume.zip (主数据)
   - postgres_logs_volume.zip (日志)
   - pgadmin_data_volume.zip (pgAdmin数据)

## 恢复命令
.\backup-scripts\postgresql-restore-cn.ps1 -BackupPath ".\backups\20250712_231429"

## 注意事项
- 恢复将完全替换现有数据
- 确保Docker Desktop正在运行
- 恢复期间现有服务将被停止
- 恢复完成后服务将自动启动

## 访问信息
- PostgreSQL: localhost:3433
- 用户名: postgres
- 密码: postgres
- 数据库: postgres
- pgAdmin: http://localhost:8080 (<EMAIL> / <EMAIL>)
