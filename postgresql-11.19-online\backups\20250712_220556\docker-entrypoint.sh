#!/bin/bash
set -e



# 初始化函数
init_database() {
    echo "初始化PostgreSQL数据库..."
    
    # 初始化数据库集群
    /usr/local/pgsql/bin/initdb -D "$PGDATA" --auth-local=trust --auth-host=md5 --username="$PG_USER"
    
    # 复制配置文件
    cp /etc/postgresql/postgresql.conf "$PGDATA/postgresql.conf"
    cp /etc/postgresql/pg_hba.conf "$PGDATA/pg_hba.conf"
    
    echo "数据库初始化完成"
}

# 检查数据库是否已初始化
check_database() {
    if [ -s "$PGDATA/PG_VERSION" ]; then
        echo "数据库已存在，版本: $(cat $PGDATA/PG_VERSION)"
        return 0
    else
        echo "数据库未初始化"
        return 1
    fi
}

# 启动临时PostgreSQL服务进行初始配置
start_temp_server() {
    echo "启动临时PostgreSQL服务..."
    /usr/local/pgsql/bin/pg_ctl -D "$PGDATA" -o "-c listen_addresses='' -p $PG_PORT" -w start
}

# 停止临时PostgreSQL服务
stop_temp_server() {
    echo "停止临时PostgreSQL服务..."
    /usr/local/pgsql/bin/pg_ctl -D "$PGDATA" -m fast -w stop
}

# 配置数据库
configure_database() {
    echo "配置数据库..."
    
    # 设置postgres用户密码
    /usr/local/pgsql/bin/psql -v ON_ERROR_STOP=1 --username "$PG_USER" --dbname postgres --port "$PG_PORT" <<-EOSQL
        ALTER USER postgres PASSWORD '$PG_PASSWORD';
        CREATE DATABASE testdb OWNER postgres;
        GRANT ALL PRIVILEGES ON DATABASE testdb TO postgres;
EOSQL
    
    echo "数据库配置完成"
}

# 运行初始化脚本
run_init_scripts() {
    echo "运行初始化脚本..."
    
    if [ -d "/docker-entrypoint-initdb.d" ]; then
        for f in /docker-entrypoint-initdb.d/*; do
            case "$f" in
                *.sh)
                    if [ -x "$f" ]; then
                        echo "执行脚本: $f"
                        "$f"
                    else
                        echo "源脚本: $f"
                        . "$f"
                    fi
                    ;;
                *.sql)
                    echo "执行SQL文件: $f"
                    /usr/local/pgsql/bin/psql -v ON_ERROR_STOP=1 --username "$PG_USER" --dbname postgres --port "$PG_PORT" -f "$f"
                    ;;
                *.sql.gz)
                    echo "执行压缩SQL文件: $f"
                    gunzip -c "$f" | /usr/local/pgsql/bin/psql -v ON_ERROR_STOP=1 --username "$PG_USER" --dbname postgres --port "$PG_PORT"
                    ;;
                *)
                    echo "忽略文件: $f"
                    ;;
            esac
        done
    fi
    
    echo "初始化脚本执行完成"
}

# 主函数
main() {
    # 检查数据目录是否为空
    if [ ! -s "$PGDATA/PG_VERSION" ]; then
        echo "数据目录为空，开始初始化..."
        
        # 初始化数据库
        init_database
        
        # 启动临时服务进行配置
        start_temp_server
        
        # 配置数据库
        configure_database
        
        # 运行初始化脚本
        run_init_scripts
        
        # 停止临时服务
        stop_temp_server
        
        echo "数据库初始化和配置完成"
    else
        echo "数据库已存在，跳过初始化"
    fi
    
    # 启动PostgreSQL服务
    echo "启动PostgreSQL服务..."
    exec /usr/local/pgsql/bin/postgres -D "$PGDATA" -c config_file="$PGDATA/postgresql.conf"
}

# 设置环境变量默认值
export PGDATA=${PGDATA:-/var/lib/postgresql/data}
export PG_USER=${PG_USER:-postgres}
export PG_PASSWORD=${PG_PASSWORD:-postgres}
export PG_PORT=${PG_PORT:-3433}

# 执行主函数
main "$@"
