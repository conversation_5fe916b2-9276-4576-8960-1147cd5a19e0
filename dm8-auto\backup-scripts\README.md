# DM8 数据库备份系统说明

## 📋 重要说明

### 🎯 关于数据卷备份 vs SQL导出

**数据卷备份已包含完整数据库数据！**

- `dm8_database_data` 数据卷包含：
  - `MAIN.DBF` - 主数据文件
  - `SYSTEM.DBF` - 系统数据文件
  - `ROLL.DBF` - 回滚段文件
  - `TEMP.DBF` - 临时数据文件
  - `DAMENG01.log`, `DAMENG02.log` - 事务日志文件
  - `dm.ctl` - 控制文件
  - 所有用户数据和表结构

**结论：通常情况下，只需要备份数据卷即可！**

### 📦 备份内容

#### 🔥 完整备份
1. **数据卷备份**：
   - `dm8_data_volume.zip` - 完整数据库数据
   - `dm8_logs_volume.zip` - 日志数据
   - `dm8_config_backup.zip` - 容器内配置

2. **配置文件备份**：
   - `docker-compose.yml`
   - `Dockerfile`
   - `dm.ini` (如果存在)

## 🚀 使用方法

### 交互式菜单
```powershell
.\backup-manager.ps1
```

### 命令行模式
```powershell
# 创建备份
.\backup-manager.ps1 -Action backup

# 恢复备份
.\backup-manager.ps1 -Action restore -BackupPath ".\backups\20250717_154010"

# 列出备份
.\backup-manager.ps1 -Action list

# 验证备份
.\backup-manager.ps1 -Action verify

# 清理旧备份
.\backup-manager.ps1 -Action cleanup
```

## 📊 备份验证

系统会自动验证备份完整性：
- ✅ **必需文件**：`dm8_data_volume.zip`, `dm8_config_backup.zip`
- 🔧 **可选文件**：`dm8_logs_volume.zip`, SQL导出文件, 配置文件

## 🔄 恢复流程

数据卷恢复包含完整数据库数据：

1. **停止现有服务**
2. **删除现有数据卷**
3. **恢复配置文件**
4. **创建新数据卷**
5. **恢复数据卷内容** ← 包含完整数据库数据
6. **启动服务**
7. **验证恢复结果**

## ⚠️ 注意事项

1. **数据一致性**：备份时会短暂停止容器确保数据一致性
2. **存储空间**：确保有足够空间存储备份文件
3. **权限问题**：恢复时会自动修复文件权限
4. **网络连接**：确保Docker环境正常运行

## 🛠️ 故障排除

### 常见问题

1. **数据卷备份失败**
   - 检查Docker是否运行
   - 确认数据卷名称正确
   - 检查磁盘空间

2. **容器启动失败**
   - 检查端口占用
   - 验证数据卷权限
   - 查看容器日志

3. **数据库连接失败**
   - 等待数据库完全启动
   - 检查网络配置
   - 验证用户密码

### 日志文件

每次备份都会生成详细日志：
- 位置：`.\backups\[时间戳]\backup_log.txt`
- 包含：操作步骤、错误信息、文件大小等

## 📈 最佳实践

1. **定期备份**：建议每日或每周定期备份
2. **数据卷备份**：数据卷备份包含完整数据库，无需额外导出
3. **验证备份**：定期验证备份文件完整性
4. **异地存储**：将备份文件复制到其他位置
5. **测试恢复**：定期测试恢复流程
6. **清理旧备份**：定期清理过期备份释放空间

### 🎯 备份恢复说明

- **数据卷备份**：包含完整数据库数据（.DBF文件、日志文件、控制文件等）
- **配置备份**：Docker配置和容器内配置文件
- **一键恢复**：数据卷恢复即可完整恢复数据库，无需额外操作

## 🔧 自定义配置

### 修改备份保留天数
```powershell
.\backup-manager.ps1 -Action cleanup -KeepDays 30
```

### 强制备份（容器未运行时）
```powershell
.\backup-scripts\dm8-backup-cn.ps1 -ForceBackup
```

### 详细日志模式
```powershell
.\backup-scripts\dm8-backup-cn.ps1 -VerboseLog
```

---

**版本**: 1.0
**更新日期**: 2025-07-17
**作者**: AI Assistant
