# 达梦数据库 DM8 Docker 镜像 - 高度优化的多阶段构建版本
# 基于银河麒麟操作系统

# ================================
# 第一阶段：构建阶段（安装达梦数据库）
# ================================
FROM hxsoong/kylin:v10-sp3 AS builder

# 设置工作目录
WORKDIR /tmp

# 一次性安装所有构建依赖并清理
RUN yum update -y && \
    yum install -y \
    libaio \
    numactl-libs \
    unzip \
    wget \
    net-tools \
    lsof \
    which \
    sudo \
    psmisc \
    openssl \
    expect \
    glibc-langpack-zh \
    tar \
    && yum clean all \
    && rm -rf /var/cache/yum/* \
    && rm -rf /var/log/yum.log

# 创建dmdba用户和组
RUN groupadd -g 1001 dmdba && \
    useradd -u 1001 -g dmdba -m -s /bin/bash dmdba && \
    echo "dmdba:$(openssl passwd -1 dmdba)" | chpasswd -e && \
    echo "dmdba ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# 复制安装文件和脚本
COPY dm8_20250506_x86_rh7_64/ /tmp/dm8_setup/
COPY install_dm8.exp /tmp/install_dm8.exp
COPY dm.ini /tmp/dm.ini

# 安装达梦数据库并立即清理安装文件
RUN echo "开始安装达梦数据库..." && \
    chmod 755 /tmp/dm8_setup/DMInstall.bin && \
    chmod +x /tmp/install_dm8.exp && \
    /tmp/install_dm8.exp && \
    echo "安装完成，立即清理安装文件..." && \
    rm -rf /tmp/dm8_setup && \
    rm -f /tmp/install_dm8.exp && \
    echo "配置数据库..." && \
    mkdir -p /opt/dmdbms/data /opt/dmdbms/log && \
    cp /tmp/dm.ini /opt/dmdbms/bin/dm.ini && \
    rm -f /tmp/dm.ini && \
    chown -R dmdba:dmdba /opt/dmdbms

# 深度清理：移除不必要的文件和目录
RUN echo "执行深度清理..." && \
    # 清理系统缓存
    yum clean all && \
    rm -rf /var/cache/yum/* && \
    rm -rf /var/log/yum.log && \
    rm -rf /tmp/* && \
    rm -rf /var/tmp/* && \
    # 清理达梦安装目录中的非必需文件
    find /opt/dmdbms -name "*.log" -delete 2>/dev/null || true && \
    find /opt/dmdbms -name "*.tmp" -delete 2>/dev/null || true && \
    find /opt/dmdbms -name "*.bak" -delete 2>/dev/null || true && \
    # 移除文档和示例文件（如果存在）
    rm -rf /opt/dmdbms/doc 2>/dev/null || true && \
    rm -rf /opt/dmdbms/samples 2>/dev/null || true && \
    rm -rf /opt/dmdbms/demo 2>/dev/null || true && \
    # 清理用户目录
    rm -rf /root/.cache 2>/dev/null || true && \
    rm -rf /home/<USER>/.cache 2>/dev/null || true

# ================================
# 第二阶段：运行时阶段（最小化镜像）
# ================================
FROM hxsoong/kylin:v10-sp3 AS runtime

# 设置标签信息
LABEL maintainer="Your Name <<EMAIL>>" \
      version="1.0" \
      description="DM8 Database optimized Docker image" \
      dm.version="8.0"

# 只安装运行时必需的最小依赖包
RUN yum update -y && \
    yum install -y --setopt=install_weak_deps=False \
    libaio \
    numactl-libs \
    net-tools \
    lsof \
    which \
    sudo \
    psmisc \
    openssl \
    glibc-langpack-zh \
    && yum clean all \
    && rm -rf /var/cache/yum/* \
    && rm -rf /var/log/yum.log \
    && rm -rf /tmp/* \
    && rm -rf /var/tmp/*

# 创建dmdba用户和组
RUN groupadd -g 1001 dmdba && \
    useradd -u 1001 -g dmdba -m -s /bin/bash dmdba && \
    echo "dmdba:$(openssl passwd -1 dmdba)" | chpasswd -e && \
    echo "dmdba ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers && \
    # 清理用户创建过程中的临时文件
    rm -rf /var/log/lastlog /var/log/faillog 2>/dev/null || true

# 从构建阶段复制已安装的达梦数据库（只复制必要文件）
COPY --from=builder --chown=dmdba:dmdba /opt/dmdbms /opt/dmdbms

# 复制启动脚本
COPY docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# 设置环境变量
ENV DM_HOME=/opt/dmdbms \
    PATH=$DM_HOME/bin:$PATH \
    LD_LIBRARY_PATH=$DM_HOME/bin:$LD_LIBRARY_PATH \
    LANG=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8

# 创建数据卷挂载点
VOLUME ["/opt/dmdbms/data", "/opt/dmdbms/log"]

# 暴露达梦数据库默认端口
EXPOSE 5236

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD su - dmdba -c "disql SYSDBA/SYSDBA@localhost:5236 -e \"SELECT 1;\"" || exit 1

# 切换到dmdba用户
USER dmdba

# 设置工作目录为达梦安装目录
WORKDIR /opt/dmdbms

# 设置启动命令
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
CMD ["dmserver"]
