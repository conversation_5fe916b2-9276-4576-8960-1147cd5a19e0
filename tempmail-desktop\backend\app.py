#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时邮箱桌面应用后端主文件
提供 Flask API 服务和 pywebview JS API
"""

import os
import json
import time
import random
import string
import sqlite3
from datetime import datetime, timedelta
from flask import Flask, jsonify, request
from flask_cors import CORS
from backend.api.tempmail_api import TempMailService
from backend.api.advanced_tempmail_api import AdvancedTempMailService
from backend.api.real_tempmail_api import RealTempMailService
from backend.database.db_manager import DatabaseManager

class TempMailAPI:
    """提供给前端 JavaScript 调用的 API 类"""

    def __init__(self, api_mode='real'):
        self.db_manager = DatabaseManager()

        # 选择使用哪种 API 实现
        if api_mode == 'real':
            try:
                self.tempmail_service = RealTempMailService()
                print("[OK] 使用真实 TempMail.Plus API（基于发现的端点）")
            except Exception as e:
                print(f"[ERROR] 真实 API 初始化失败，回退到高级 API: {e}")
                try:
                    self.tempmail_service = AdvancedTempMailService()
                    print("[OK] 使用高级 TempMail API（页面抓取）")
                except Exception as e2:
                    print(f"[ERROR] 高级 API 初始化失败，回退到基础 API: {e2}")
                    self.tempmail_service = TempMailService()
                    print("[OK] 使用基础 TempMail API（模拟数据）")
        elif api_mode == 'advanced':
            try:
                self.tempmail_service = AdvancedTempMailService()
                print("[OK] 使用高级 TempMail API（页面抓取）")
            except Exception as e:
                print(f"[ERROR] 高级 API 初始化失败，回退到基础 API: {e}")
                self.tempmail_service = TempMailService()
                print("[OK] 使用基础 TempMail API（模拟数据）")
        else:
            self.tempmail_service = TempMailService()
            print("[OK] 使用基础 TempMail API（模拟数据）")

        self.current_email = None
        self.current_pin = None
        self.current_epin = ""  # 存储当前的epin值

    def _safe_encode_for_json(self, data):
        """安全编码数据以便JSON序列化，特别处理emoji"""
        try:
            if isinstance(data, dict):
                safe_dict = {}
                for key, value in data.items():
                    try:
                        safe_key = self._safe_encode_for_json(key)
                        safe_value = self._safe_encode_for_json(value)
                        safe_dict[safe_key] = safe_value
                    except Exception as e:
                        print(f"[WARNING] 处理字典项失败 {key}: {e}")
                        safe_dict[str(key)] = str(value) if value is not None else None
                return safe_dict
            elif isinstance(data, list):
                safe_list = []
                for item in data:
                    try:
                        safe_list.append(self._safe_encode_for_json(item))
                    except Exception as e:
                        print(f"[WARNING] 处理列表项失败: {e}")
                        safe_list.append(str(item) if item is not None else None)
                return safe_list
            elif isinstance(data, str):
                # 确保字符串可以安全地进行JSON序列化
                try:
                    # 测试JSON序列化
                    import json
                    json.dumps(data, ensure_ascii=False)
                    return data
                except (UnicodeEncodeError, UnicodeDecodeError, TypeError) as e:
                    print(f"[WARNING] 字符串编码问题: {e}")
                    # 如果有问题，使用安全编码
                    try:
                        return data.encode('utf-8', errors='replace').decode('utf-8', errors='replace')
                    except Exception as e2:
                        print(f"[WARNING] 字符串安全编码也失败: {e2}")
                        return str(data)
            elif data is None:
                return None
            elif isinstance(data, (int, float, bool)):
                return data
            else:
                # 对于其他类型，转换为字符串
                try:
                    return str(data)
                except Exception as e:
                    print(f"[WARNING] 转换为字符串失败: {e}")
                    return "无法显示的内容"
        except Exception as e:
            print(f"[WARNING] 数据安全编码失败: {e}")
            import traceback
            print(f"[WARNING] 异常堆栈: {traceback.format_exc()}")
            return str(data) if data is not None else None

    def generate_email(self, domain='mailto.plus', duration=60, custom_prefix=None):
        """生成新的临时邮箱地址"""
        try:
            print(f"[SEARCH] generate_email 方法接收到的参数:")
            print(f"  - domain: {domain} (类型: {type(domain)})")
            print(f"  - duration: {duration} (类型: {type(duration)})")
            print(f"  - custom_prefix: {custom_prefix} (类型: {type(custom_prefix)})")

            # 处理参数传递问题：如果 domain 是字典，说明整个参数对象被传递给了 domain
            if isinstance(domain, dict):
                # 从字典中提取正确的参数
                params = domain
                domain = params.get('domain', 'mailto.plus')
                duration = params.get('duration', 60)
                custom_prefix = params.get('custom_prefix', None)
                print(f"[WRENCH] 修正参数:")
                print(f"  - domain: {domain}")
                print(f"  - duration: {duration}")
                print(f"  - custom_prefix: {custom_prefix}")
            elif not isinstance(domain, str):
                domain = str(domain)

            # 生成用户名：使用自定义前缀或随机生成
            if custom_prefix and isinstance(custom_prefix, str) and custom_prefix.strip():
                print(f"[TARGET] 检测到有效的自定义前缀: '{custom_prefix}'")
                # 清理自定义前缀，只保留字母数字和部分特殊字符
                import re
                username = re.sub(r'[^a-zA-Z0-9._-]', '', custom_prefix.strip().lower())
                if not username:  # 如果清理后为空，使用随机生成
                    username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
                    print(f"[WARNING] 自定义前缀清理后为空，使用随机前缀: {username}")
                elif len(username) > 20:  # 限制长度
                    username = username[:20]
                    print(f"[WARNING] 自定义前缀过长，截取为: {username}")
                else:
                    print(f"[OK] 使用清理后的自定义前缀: {username}")
                print(f"[EMAIL] 最终使用自定义前缀: {username}")
            else:
                # 生成随机用户名
                username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=8))
                print(f"[EMAIL] custom_prefix 无效，使用随机前缀: {username}")
                print(f"[EMAIL] custom_prefix 检查详情:")
                print(f"  - custom_prefix 是否存在: {custom_prefix is not None}")
                print(f"  - custom_prefix 是否为字符串: {isinstance(custom_prefix, str)}")
                if custom_prefix:
                    print(f"  - custom_prefix.strip() 是否非空: {bool(custom_prefix.strip()) if hasattr(custom_prefix, 'strip') else 'N/A'}")

            email_address = f"{username}@{domain}"

            # 计算过期时间
            expire_time = datetime.now() + timedelta(minutes=duration)

            # 保存到数据库
            email_id = self.db_manager.create_email(email_address, expire_time)

            self.current_email = {
                'id': email_id,
                'address': email_address,
                'domain': domain,
                'username': username,
                'expire_time': expire_time.isoformat(),
                'created_time': datetime.now().isoformat()
            }

            return {
                'success': True,
                'email': self.current_email
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def get_current_email(self):
        """获取当前邮箱信息"""
        if self.current_email:
            return {
                'success': True,
                'email': self.current_email
            }
        return {
            'success': False,
            'error': '没有活动的邮箱地址'
        }

    def get_emails(self):
        """获取邮件列表"""
        try:
            if not self.current_email:
                return {'success': False, 'error': '没有活动的邮箱地址'}

            # 设置epin到tempmail_service
            self.tempmail_service.epin = self.current_epin
            print(f"[LOCK] 设置epin到tempmail_service: {self.current_epin}")

            # 从 TempMail.Plus API 获取邮件
            emails = self.tempmail_service.get_emails(self.current_email['address'])

            # 保存到数据库（只有生成的邮箱才有id，直接输入的邮箱跳过数据库保存）
            if self.current_email.get('id'):
                for email in emails:
                    self.db_manager.save_email(
                        self.current_email['id'],
                        email.get('from', ''),
                        email.get('subject', ''),
                        email.get('body', ''),
                        email.get('date', datetime.now().isoformat())
                    )
                print(f"[MEMO] 已保存 {len(emails)} 封邮件到数据库")
            else:
                print(f"[INFO] 直接输入的邮箱，跳过数据库保存")

            # 安全编码处理邮件列表
            safe_emails = self._safe_encode_for_json(emails)

            return {
                'success': True,
                'emails': safe_emails
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def get_email_detail(self, email_id):
        """获取邮件详情"""
        import threading
        import time

        try:
            print(f"[SEARCH] [DEBUG] 开始获取邮件详情")
            print(f"[SEARCH] [DEBUG] email_id: {email_id}")
            print(f"[SEARCH] [DEBUG] email_id类型: {type(email_id)}")

            # 检查当前邮箱状态
            if not self.current_email:
                print(f"[WARNING] [DEBUG] 没有活动的邮箱地址，尝试从数据库获取最近的邮箱")

                # 尝试从数据库获取最近的邮箱
                try:
                    recent_emails = self.db_manager.get_recent_emails(limit=1)
                    if recent_emails:
                        recent_email = recent_emails[0]
                        email_address = recent_email.get('email_address')
                        if email_address and '@' in email_address:
                            username, domain = email_address.split('@')
                            self.current_email = {
                                'address': email_address,
                                'domain': domain,
                                'username': username,
                                'created_time': None,
                                'expire_time': None,
                                'is_auto_recovered': True
                            }
                            print(f"[OK] [DEBUG] 自动恢复邮箱地址: {email_address}")
                        else:
                            print(f"[ERROR] [ERROR] 无法恢复有效的邮箱地址")
                            return {'success': False, 'error': '没有活动的邮箱地址'}
                    else:
                        print(f"[ERROR] [ERROR] 数据库中没有邮箱记录")
                        return {'success': False, 'error': '没有活动的邮箱地址'}
                except Exception as db_error:
                    print(f"[ERROR] [ERROR] 从数据库恢复邮箱失败: {db_error}")
                    return {'success': False, 'error': '没有活动的邮箱地址'}

            print(f"[EMAIL] [DEBUG] 当前邮箱: {self.current_email}")
            print(f"[LOCK] [DEBUG] 当前epin: {self.current_epin}")

            # 设置epin到tempmail_service
            self.tempmail_service.epin = self.current_epin
            print(f"[LOCK] [DEBUG] 已设置epin到tempmail_service: {self.current_epin}")

            # 使用线程和超时机制来获取邮件详情
            result_container = {'result': None, 'error': None}

            def get_email_with_timeout():
                try:
                    print(f"[GLOBE] [DEBUG] 开始调用API获取邮件详情...")
                    print(f"[GLOBE] [DEBUG] 邮件ID: {email_id}")
                    print(f"[GLOBE] [DEBUG] 邮箱地址: {self.current_email['address']}")
                    print(f"[GLOBE] [DEBUG] 当前epin: {self.current_epin}")

                    email_detail = self.tempmail_service.get_email_content(
                        email_id,
                        self.current_email['address']
                    )
                    result_container['result'] = email_detail
                    print(f"[EMAIL] [DEBUG] API返回的邮件详情类型: {type(email_detail)}")
                    if isinstance(email_detail, dict):
                        print(f"[EMAIL] [DEBUG] API返回的邮件详情键: {list(email_detail.keys())}")
                        if 'error' in email_detail:
                            print(f"[ERROR] [DEBUG] API返回错误: {email_detail['error']}")
                        if 'body' in email_detail:
                            print(f"[EMAIL] [DEBUG] 邮件正文长度: {len(str(email_detail['body']))}")
                            print(f"[EMAIL] [DEBUG] 邮件正文前100字符: {str(email_detail['body'])[:100]}")
                    else:
                        print(f"[EMAIL] [DEBUG] API返回的邮件详情: {email_detail}")
                except Exception as e:
                    result_container['error'] = str(e)
                    print(f"[COLLISION] [THREAD-EXCEPTION] 线程内异常: {e}")
                    import traceback
                    print(f"[COLLISION] [THREAD-EXCEPTION] 异常堆栈: {traceback.format_exc()}")

            # 启动线程
            thread = threading.Thread(target=get_email_with_timeout)
            thread.daemon = True
            thread.start()

            # 等待最多15秒
            thread.join(timeout=15)

            if thread.is_alive():
                print(f"[WARNING] [TIMEOUT] API调用超时，返回备用结果")
                return {
                    'success': True,
                    'email': {
                        'id': email_id,
                        'from': '系统提示',
                        'subject': '邮件详情获取超时',
                        'body': '邮件详情获取超时，请稍后重试。这可能是由于网络问题或API响应缓慢导致的。',
                        'html_body': '<p>邮件详情获取超时，请稍后重试。这可能是由于网络问题或API响应缓慢导致的。</p>',
                        'date': time.strftime('%Y-%m-%d %H:%M:%S'),
                        'read': True
                    }
                }

            # 检查结果
            if result_container['error']:
                print(f"[ERROR] [ERROR] 线程执行错误: {result_container['error']}")
                return {'success': False, 'error': result_container['error']}

            email_detail = result_container['result']
            if email_detail and email_detail.get('error'):
                print(f"[ERROR] [ERROR] API返回错误: {email_detail['error']}")
                return {
                    'success': False,
                    'error': email_detail['error']
                }

            print(f"[OK] [SUCCESS] 邮件详情获取成功")

            # 准备返回数据
            result_email = email_detail or {
                'id': email_id,
                'from': '未知',
                'subject': '邮件详情',
                'body': '无法获取邮件详情',
                'date': time.strftime('%Y-%m-%d %H:%M:%S')
            }

            # 安全编码处理，确保emoji正确传输
            safe_result = {
                'success': True,
                'email': self._safe_encode_for_json(result_email)
            }

            return safe_result

        except Exception as e:
            print(f"[COLLISION] [EXCEPTION] 获取邮件详情异常: {e}")
            print(f"[COLLISION] [EXCEPTION] 异常类型: {type(e)}")
            import traceback
            print(f"[COLLISION] [EXCEPTION] 异常堆栈: {traceback.format_exc()}")
            return {'success': False, 'error': str(e)}

    def delete_email(self, email_id):
        """删除邮件"""
        try:
            if not self.current_email:
                return {'success': False, 'error': '没有当前邮箱'}

            email_address = self.current_email.get('address')
            if not email_address:
                return {'success': False, 'error': '邮箱地址无效'}

            print(f"[DELETE] 开始删除邮件 ID: {email_id}")

            # 调用API删除邮件
            if hasattr(self.tempmail_service, 'delete_email'):
                try:
                    # 使用当前的epin
                    current_epin = getattr(self, 'current_epin', '') or ''
                    print(f"[LOCK] 使用epin进行删除: {current_epin}")

                    # 设置epin和邮箱地址到tempmail_service
                    self.tempmail_service.epin = current_epin
                    self.tempmail_service.current_email_address = email_address

                    # 调用API删除邮件
                    api_delete_result = self.tempmail_service.delete_email(email_id)
                    print(f"[SATELLITE] API删除结果: {api_delete_result}")

                    if api_delete_result:
                        print(f"[OK] API删除成功")

                        # API删除成功，同时删除本地数据库记录（如果有的话）
                        if self.current_email.get('id'):
                            try:
                                self.db_manager.delete_email(email_id)
                                print(f"[DELETE] 本地数据库记录已删除")
                            except Exception as e:
                                print(f"[WARNING] 删除本地数据库记录失败: {e}")

                        return {'success': True, 'method': 'api_delete'}
                    else:
                        print(f"[ERROR] API删除失败")
                        return {'success': False, 'error': 'API删除邮件失败'}

                except Exception as e:
                    print(f"[ERROR] API删除异常: {e}")
                    return {'success': False, 'error': f'删除邮件异常: {str(e)}'}
            else:
                # 如果没有API删除方法，只删除本地记录
                print(f"[WARNING] 没有API删除方法，只删除本地记录")
                self.db_manager.delete_email(email_id)
                return {'success': True, 'method': 'local_only'}

        except Exception as e:
            print(f"[ERROR] 删除邮件失败: {e}")
            return {'success': False, 'error': str(e)}

    def clear_all_emails(self):
        """清空当前邮箱的所有邮件"""
        try:
            if not self.current_email:
                return {'success': False, 'error': '没有当前邮箱'}

            email_address = self.current_email.get('address')
            if not email_address:
                return {'success': False, 'error': '邮箱地址无效'}

            print(f"[DELETE] 开始清空邮箱 {email_address} 的所有邮件...")

            # 直接调用官网API的清空邮件功能（逐个删除）
            if hasattr(self.tempmail_service, 'clear_all_emails'):
                try:
                    # 使用当前的epin
                    current_epin = getattr(self, 'current_epin', '') or ''
                    print(f"[LOCK] 使用epin进行官网清空: {current_epin}")

                    # 调用官网API清空邮件
                    api_clear_result = self.tempmail_service.clear_all_emails(email_address, current_epin)
                    print(f"[SATELLITE] 官网清空结果: {api_clear_result}")

                    if api_clear_result and api_clear_result.get('success'):
                        # 官网清空成功，清理本地数据库
                        deleted_count = api_clear_result.get('deleted_count', 0)
                        failed_count = api_clear_result.get('failed_count', 0)
                        total_count = api_clear_result.get('total_count', 0)

                        print(f"[OK] 官网清空成功，开始清理本地数据库...")

                        # 清理本地数据库中的邮件记录（如果有的话）
                        if self.current_email.get('id'):
                            try:
                                # 删除该邮箱的所有本地邮件记录
                                local_deleted = self.db_manager.clear_emails_by_mailbox(self.current_email['id'])
                                print(f"[DELETE] 本地数据库清理完成，删除 {local_deleted} 条记录")
                            except Exception as e:
                                print(f"[WARNING] 清理本地数据库失败: {e}")

                        return {
                            'success': True,
                            'deleted_count': deleted_count,
                            'failed_count': failed_count,
                            'total_count': total_count,
                            'method': 'official_api'
                        }
                    else:
                        error_msg = api_clear_result.get('error', '未知错误') if api_clear_result else '调用失败'
                        print(f"[ERROR] 官网清空失败: {error_msg}")
                        return {'success': False, 'error': f'官网清空失败: {error_msg}'}

                except Exception as api_error:
                    print(f"[ERROR] 官网清空异常: {api_error}")
                    import traceback
                    print(f"[ERROR] 异常堆栈: {traceback.format_exc()}")
                    return {'success': False, 'error': f'官网清空异常: {str(api_error)}'}
            else:
                return {'success': False, 'error': 'TempMail服务不支持清空邮件功能'}

        except Exception as e:
            print(f"[COLLISION] 清空邮件异常: {e}")
            import traceback
            print(f"[COLLISION] 异常堆栈: {traceback.format_exc()}")
            return {'success': False, 'error': str(e)}

    def set_pin(self, pin):
        """设置 PIN 码"""
        try:
            self.current_pin = pin
            # 生成epin（模拟官网的格式）
            self.current_epin = self._generate_epin(pin) if pin else ""

            if self.current_email:
                # 保存PIN码到数据库（只有生成的邮箱才有id）
                email_id = self.current_email.get('id') if isinstance(self.current_email, dict) else None
                if email_id:
                    try:
                        self.db_manager.update_email_pin(email_id, pin)
                        print(f"[OK] PIN码已保存到数据库，邮箱ID: {email_id}")
                    except Exception as db_error:
                        print(f"[WARNING] 保存PIN码到数据库失败: {db_error}")
                        # 不影响PIN码设置，继续执行
                else:
                    print(f"[INFO] 直接输入的邮箱，PIN码仅保存在内存中")

            print(f"[LOCK] PIN码设置成功: {pin}")
            print(f"[LOCK] 生成的epin: {self.current_epin}")

            return {'success': True, 'epin': self.current_epin, 'pin': pin}
        except Exception as e:
            print(f"[ERROR] 设置PIN码失败: {e}")
            return {'success': False, 'error': str(e)}

    def _generate_epin(self, pin):
        """生成epin格式（直接返回PIN码）"""
        try:
            # 确保pin是字符串类型
            if not isinstance(pin, str):
                pin = str(pin)

            print(f"[LOCK] PIN码处理:")
            print(f"   原始PIN: {pin}")
            print(f"   [CLIPBOARD] 直接使用PIN作为epin: {pin}")

            # 简单粗暴：直接返回PIN码
            return pin

        except Exception as e:
            print(f"[ERROR] 处理PIN码失败: {e}")
            return str(pin) if pin else ""

    def verify_pin(self, pin):
        """验证 PIN 码"""
        is_valid = self.current_pin == pin
        if is_valid:
            # PIN验证成功后，重新生成epin
            self.current_epin = self._generate_epin(pin)
            print(f"[UNLOCK] PIN验证成功，epin已更新: {self.current_epin}")
        return {'success': is_valid}

    def check_email_pin(self, email_address):
        """检查邮箱是否设置了PIN码保护"""
        try:
            if self.current_email and self.current_email.get('address') == email_address:
                # 检查当前邮箱是否有PIN码
                has_pin = bool(self.current_pin)
            else:
                # 从数据库检查其他邮箱的PIN码状态
                account = self.db_manager.get_email_account(email_address)
                has_pin = bool(account and account.get('pin_code'))

            return {'success': True, 'has_pin': has_pin}
        except Exception as e:
            return {'success': False, 'error': str(e), 'has_pin': False}

    def set_current_email(self, email_address):
        """设置当前邮箱地址（用于直接输入邮箱）"""
        try:
            # 验证邮箱格式
            import re
            email_pattern = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
            if not re.match(email_pattern, email_address):
                return {'success': False, 'error': '邮箱地址格式无效'}

            # 解析邮箱地址
            username, domain = email_address.split('@')

            # 设置当前邮箱
            self.current_email = {
                'address': email_address,
                'domain': domain,
                'username': username,
                'created_time': None,  # 直接输入的邮箱没有创建时间
                'expire_time': None,   # 直接输入的邮箱没有过期时间
                'is_direct_input': True  # 标记为直接输入
            }

            print(f"[OK] 设置当前邮箱: {email_address}")
            print(f"[EMAIL] 邮箱信息: {self.current_email}")

            return {'success': True, 'email': self.current_email}
        except Exception as e:
            print(f"[ERROR] 设置当前邮箱失败: {e}")
            return {'success': False, 'error': str(e)}

    def set_current_email_with_info(self, request_data):
        """设置当前邮箱地址（带有时效信息）"""
        try:
            email_address = request_data.get('email_address')
            domain = request_data.get('domain')
            duration = request_data.get('duration', 60)
            is_direct_input = request_data.get('is_direct_input', True)

            print(f"[WRENCH] 设置邮箱请求数据: {request_data}")

            # 验证邮箱格式
            import re
            email_pattern = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
            if not re.match(email_pattern, email_address):
                return {'success': False, 'error': '邮箱地址格式无效'}

            # 解析邮箱地址
            username, email_domain = email_address.split('@')

            # 计算创建时间和过期时间
            from datetime import datetime, timedelta
            created_time = datetime.now()
            expire_time = created_time + timedelta(minutes=duration)

            # 设置当前邮箱
            self.current_email = {
                'address': email_address,
                'domain': email_domain,
                'username': username,
                'created_time': created_time.isoformat(),
                'expire_time': expire_time.isoformat(),
                'duration': duration,
                'is_direct_input': is_direct_input
            }

            print(f"[OK] 设置当前邮箱: {email_address}")
            print(f"[EMAIL] 邮箱信息: {self.current_email}")
            print(f"[TIME] 创建时间: {self.current_email['created_time']}")
            print(f"[HOURGLASS] 过期时间: {self.current_email['expire_time']}")
            print(f"[TIMER] 时效: {duration} 分钟")

            return {'success': True, 'email': self.current_email}
        except Exception as e:
            print(f"[ERROR] 设置当前邮箱失败: {e}")
            import traceback
            print(f"[ERROR] 异常堆栈: {traceback.format_exc()}")
            return {'success': False, 'error': str(e)}

    def destroy_mailbox(self):
        """销毁邮箱"""
        try:
            if self.current_email:
                self.db_manager.delete_email_account(self.current_email['id'])
                self.current_email = None
                self.current_pin = None
                self.current_epin = ""  # 清理epin
            return {'success': True}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def test_pin_access(self, email_address, pin):
        """测试PIN码访问邮件（尝试不同的epin格式）"""
        try:
            import urllib.parse

            # 尝试多种epin格式
            epin_formats = [
                "",  # 空epin
                pin,  # 原始PIN（最重要的格式）
                urllib.parse.quote(pin),  # URL编码PIN
                self._generate_epin(pin),  # 智能处理后的格式
                "tv81***%2F-%2B%2Fsb.38",  # 官网示例格式（URL编码）
                "tv81***/-+/sb.38",  # 官网示例格式（原始）
            ]

            results = []

            for i, epin in enumerate(epin_formats):
                print(f"\n[SEARCH] 测试epin格式 {i+1}: {epin}")

                # 临时设置epin
                old_epin = self.tempmail_service.epin
                self.tempmail_service.epin = epin

                try:
                    # 尝试获取邮件
                    emails = self.tempmail_service.get_emails(email_address)
                    email_count = len(emails) if emails else 0

                    results.append({
                        'format_index': i + 1,
                        'epin': epin,
                        'email_count': email_count,
                        'success': True,
                        'emails': emails[:3] if emails else []  # 只返回前3封邮件作为示例
                    })

                    print(f"   ✓ 成功获取 {email_count} 封邮件")

                except Exception as e:
                    results.append({
                        'format_index': i + 1,
                        'epin': epin,
                        'email_count': 0,
                        'success': False,
                        'error': str(e)
                    })
                    print(f"   [ERROR] 获取失败: {e}")

                # 恢复原始epin
                self.tempmail_service.epin = old_epin

            return {
                'success': True,
                'results': results,
                'email_address': email_address,
                'pin': pin
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def get_domains(self):
        """获取可用域名列表"""
        return {
            'success': True,
            'domains': [
                'mailto.plus',
                'fexpost.com',
                'fexbox.org',
                'mailbox.in.ua',
                'rover.info',
                'chitthi.in',
                'fextemp.com',
                'any.pink',
                'merepost.com'
            ]
        }

def create_app(api_instance=None):
    """创建 Flask 应用"""
    app = Flask(__name__)
    CORS(app)  # 允许跨域请求

    # 使用传入的 API 实例，如果没有则创建新的
    api = api_instance if api_instance is not None else TempMailAPI()
    print(f"🔗 Flask 应用使用 API 实例: {id(api)}")

    @app.route('/api/generate-email', methods=['POST'])
    def generate_email():
        data = request.get_json() or {}
        print(f"[SEARCH] 接收到的原始数据: {data}")
        print(f"[SEARCH] 数据类型: {type(data)}")

        domain = data.get('domain', 'mailto.plus')
        duration = int(data.get('duration', 60))
        custom_prefix = data.get('custom_prefix', None)

        print(f"[SEARCH] 解析后的参数:")
        print(f"  - domain: {domain} (类型: {type(domain)})")
        print(f"  - duration: {duration} (类型: {type(duration)})")
        print(f"  - custom_prefix: {custom_prefix} (类型: {type(custom_prefix)})")

        result = api.generate_email(domain, duration, custom_prefix)
        print(f"生成结果: {result}")
        return jsonify(result)

    @app.route('/api/current-email', methods=['GET'])
    def get_current_email():
        return jsonify(api.get_current_email())

    @app.route('/api/emails', methods=['GET'])
    def get_emails():
        return jsonify(api.get_emails())

    @app.route('/api/delete-email/<int:email_id>', methods=['DELETE'])
    def delete_email(email_id):
        return jsonify(api.delete_email(email_id))

    @app.route('/api/email-detail/<email_id>', methods=['GET'])
    def get_email_detail(email_id):
        return jsonify(api.get_email_detail(email_id))

    @app.route('/api/set-pin', methods=['POST'])
    def set_pin():
        data = request.get_json()
        pin = data.get('pin')
        return jsonify(api.set_pin(pin))

    @app.route('/api/verify-pin', methods=['POST'])
    def verify_pin():
        data = request.get_json()
        pin = data.get('pin')
        return jsonify(api.verify_pin(pin))

    @app.route('/api/check-email-pin', methods=['POST'])
    def check_email_pin():
        data = request.get_json()
        email_address = data.get('email_address')
        return jsonify(api.check_email_pin(email_address))

    @app.route('/api/destroy-mailbox', methods=['POST'])
    def destroy_mailbox():
        return jsonify(api.destroy_mailbox())

    @app.route('/api/domains', methods=['GET'])
    def get_domains():
        return jsonify(api.get_domains())

    @app.route('/api/test-pin-access', methods=['POST'])
    def test_pin_access():
        data = request.get_json()
        email_address = data.get('email_address')
        pin = data.get('pin')
        return jsonify(api.test_pin_access(email_address, pin))

    @app.route('/api/set-current-email', methods=['POST'])
    def set_current_email():
        data = request.get_json()
        email_address = data.get('email_address')
        return jsonify(api.set_current_email(email_address))

    @app.route('/api/set-current-email-with-info', methods=['POST'])
    def set_current_email_with_info():
        data = request.get_json()
        return jsonify(api.set_current_email_with_info(data))

    @app.route('/test_frontend.html')
    def test_frontend():
        from flask import send_from_directory
        return send_from_directory('.', 'test_frontend.html')

    return app
