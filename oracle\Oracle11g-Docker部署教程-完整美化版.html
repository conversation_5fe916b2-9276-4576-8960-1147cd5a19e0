<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Oracle 11g R2 Docker 部署完整教程</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 12px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 欢迎框样式 */
        .welcome-box {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .welcome-box::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 8s ease-in-out infinite;
        }

        .welcome-box h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .welcome-box p {
            font-size: 16px;
            line-height: 1.8;
            color: var(--text-primary);
            position: relative;
            z-index: 1;
        }

        /* 学习路径样式 */
        .learning-path {
            margin-top: 30px;
            position: relative;
            z-index: 1;
        }

        .learning-path h4 {
            color: var(--secondary-color);
            margin-bottom: 25px;
        }

        .path-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .path-step {
            background: var(--light-surface);
            border-radius: 15px;
            padding: 25px;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .path-step:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .step-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 20px;
            margin-bottom: 15px;
            box-shadow: var(--shadow-md);
        }

        .step-content h5 {
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .step-content p {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.6;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box,
        .beginner-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover,
        .beginner-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        .beginner-box {
            background: linear-gradient(135deg, rgba(128, 90, 213, 0.1) 0%, rgba(128, 90, 213, 0.05) 100%);
            border-left-color: #805ad5;
            color: #553c9a;
        }

        /* 小白提示框样式 */
        .newbie-tip {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
            border: 2px solid #ffc107;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
        }

        .newbie-tip::before {
            content: '💡';
            position: absolute;
            top: -10px;
            left: 20px;
            background: #ffc107;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 16px;
        }

        .newbie-tip h4 {
            color: #856404;
            margin-top: 10px;
            margin-bottom: 15px;
        }

        .newbie-tip p {
            color: #856404;
            font-size: 14px;
            line-height: 1.6;
        }

        /* 时间线样式 */
        .timeline {
            position: relative;
            padding: 20px 0;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 30px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        }

        .timeline-item {
            position: relative;
            padding-left: 80px;
            margin-bottom: 30px;
        }

        .timeline-icon {
            position: absolute;
            left: 15px;
            top: 0;
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: bold;
            box-shadow: var(--shadow-md);
        }

        .timeline-content {
            background: var(--light-surface);
            padding: 20px;
            border-radius: 10px;
            box-shadow: var(--shadow-sm);
            border-left: 3px solid var(--primary-color);
        }

        .timeline-content h4 {
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .timeline-content p {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.6;
        }

        /* FAQ样式 */
        .faq-item {
            background: var(--light-surface);
            border-radius: 10px;
            margin-bottom: 15px;
            box-shadow: var(--shadow-sm);
            overflow: hidden;
        }

        .faq-question {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
        }

        .faq-question:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
        }

        .faq-question i {
            transition: transform 0.3s ease;
        }

        .faq-question.active i {
            transform: rotate(180deg);
        }

        .faq-answer {
            padding: 0 20px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .faq-answer.active {
            padding: 20px;
            max-height: 500px;
        }

        .faq-answer p {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* 进度条样式 */
        .progress-container {
            background: #f0f0f0;
            border-radius: 10px;
            padding: 3px;
            margin: 20px 0;
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            height: 20px;
            border-radius: 8px;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .content-wrapper {
                padding: 30px;
            }

            .container {
                padding: 40px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
                border-radius: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 16px;
                margin-right: 15px;
            }

            .path-steps {
                grid-template-columns: 1fr;
            }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
        }
    </style>
</head>

<body>
    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-database"></i> Oracle Docker 部署</h2>
            <p>从零开始构建Oracle 11g R2镜像</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#prerequisites"><i class="fas fa-cog"></i>1. 环境准备</a></li>
                <li><a href="#download"><i class="fas fa-download"></i>2. 下载安装包</a></li>
                <li><a href="#dockerfile"><i class="fas fa-file-code"></i>3. 创建Dockerfile</a></li>
                <li><a href="#scripts"><i class="fas fa-terminal"></i>4. 创建脚本</a></li>
                <li><a href="#build"><i class="fas fa-hammer"></i>5. 构建镜像</a></li>
                <li><a href="#run"><i class="fas fa-play"></i>6. 运行容器</a></li>
                <li><a href="#verify"><i class="fas fa-check-circle"></i>7. 验证安装</a></li>
                <li><a href="#troubleshooting"><i class="fas fa-wrench"></i>8. 故障排除</a></li>
                <li><a href="#summary"><i class="fas fa-trophy"></i>9. 完成总结</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-database"></i> Oracle 11g R2 Docker 部署完整教程</h1>

                <div class="welcome-box">
                    <h3><i class="fas fa-rocket"></i> 欢迎来到Oracle Docker部署世界！</h3>
                    <p>这是一份专门为<strong>完全零基础</strong>的小白准备的Oracle 11g R2
                        Docker镜像构建教程。我们将从最基础的概念开始，手把手教你构建一个完整的Oracle数据库Docker镜像，而不是直接使用网上的现成镜像。</p>

                    <div class="newbie-tip">
                        <h4><i class="fas fa-graduation-cap"></i> 小白必读</h4>
                        <p><strong>什么是Docker？</strong>
                            Docker是一个容器化平台，可以把应用程序和它的运行环境打包在一起，就像把软件装在一个"盒子"里，这个盒子可以在任何支持Docker的机器上运行。</p>
                        <p><strong>什么是Oracle数据库？</strong> Oracle是世界上最流行的企业级数据库之一，用于存储和管理大量数据。</p>
                        <p><strong>为什么要自己构建镜像？</strong> 自己构建可以完全控制安装过程，了解每一个步骤，确保安全性和定制化需求。</p>
                    </div>

                    <div class="learning-path">
                        <h4><i class="fas fa-map"></i> 学习路径（预计总时间：3-4小时）</h4>
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-icon">1</div>
                                <div class="timeline-content">
                                    <h4>环境准备 (30分钟)</h4>
                                    <p>安装Docker，检查系统要求，创建工作目录</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-icon">2</div>
                                <div class="timeline-content">
                                    <h4>下载安装包 (45分钟)</h4>
                                    <p>注册Oracle账户，下载2.4GB的安装文件</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-icon">3</div>
                                <div class="timeline-content">
                                    <h4>编写配置文件 (30分钟)</h4>
                                    <p>创建Dockerfile和安装脚本</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-icon">4</div>
                                <div class="timeline-content">
                                    <h4>构建镜像 (1-2小时)</h4>
                                    <p>执行构建命令，等待镜像构建完成</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-icon">5</div>
                                <div class="timeline-content">
                                    <h4>测试验证 (15分钟)</h4>
                                    <p>启动容器，连接数据库，验证功能</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="info-box">
                        <h4><i class="fas fa-star"></i> 学完本教程你将获得</h4>
                        <ul>
                            <li><i class="fas fa-check"></i> 一个完整可用的Oracle 11g R2 Docker镜像</li>
                            <li><i class="fas fa-check"></i> Docker容器化技术的实战经验</li>
                            <li><i class="fas fa-check"></i> Oracle数据库的基础知识</li>
                            <li><i class="fas fa-check"></i> 解决常见问题的能力</li>
                            <li><i class="fas fa-check"></i> 可以在简历上写的项目经验</li>
                        </ul>
                    </div>
                </div>

                <section id="prerequisites">
                    <h2><span class="step-number">1</span>环境准备</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-lightbulb"></i> 开始之前</h3>
                        <p>构建Oracle Docker镜像需要一定的系统资源和时间。请确保您的开发环境满足以下要求，这样可以避免构建过程中出现问题。</p>
                    </div>

                    <div class="newbie-tip">
                        <h4><i class="fas fa-question-circle"></i> 小白疑问解答</h4>
                        <p><strong>Q: 我完全不懂Docker，能学会吗？</strong></p>
                        <p>A: 当然可以！本教程专门为零基础用户设计，每个步骤都有详细说明。</p>
                        <p><strong>Q: 我的电脑配置不高，能运行吗？</strong></p>
                        <p>A: 只要满足最低要求（8GB内存），就可以运行。构建过程可能会慢一些，但完全可行。</p>
                        <p><strong>Q: 出错了怎么办？</strong></p>
                        <p>A: 教程最后有详细的故障排除指南，常见问题都有解决方案。</p>
                    </div>

                    <h3><i class="fas fa-server"></i> 系统要求</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-check-circle"></i> 硬件要求</h4>
                        <ul>
                            <li><i class="fas fa-memory"></i> <strong>内存：</strong>至少 8GB RAM（推荐 16GB）</li>
                            <li><i class="fas fa-hdd"></i> <strong>存储：</strong>至少 20GB 可用磁盘空间</li>
                            <li><i class="fas fa-microchip"></i> <strong>CPU：</strong>多核处理器（推荐 4核以上）</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-tools"></i> 软件环境</h3>
                    <div class="path-steps">
                        <div class="path-step">
                            <div class="step-icon"><i class="fab fa-docker"></i></div>
                            <div class="step-content">
                                <h5>Docker Engine</h5>
                                <p>版本 20.10+ 已安装并正常运行</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fab fa-linux"></i></div>
                            <div class="step-content">
                                <h5>操作系统</h5>
                                <p>Linux、macOS 或 Windows（推荐Linux）</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-terminal"></i></div>
                            <div class="step-content">
                                <h5>命令行工具</h5>
                                <p>熟悉基本的命令行操作</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-user"></i></div>
                            <div class="step-content">
                                <h5>Oracle账户</h5>
                                <p>用于下载官方安装包</p>
                            </div>
                        </div>
                    </div>

                    <h3><i class="fas fa-check"></i> 验证Docker环境</h3>
                    <pre><code># 检查Docker版本
docker --version

# 检查Docker服务状态
docker info

# 测试Docker运行
docker run hello-world

# 检查可用磁盘空间
df -h

# 检查内存使用情况
free -h</code></pre>

                    <div class="warning-box">
                        <h4><i class="fas fa-exclamation-triangle"></i> 重要提示</h4>
                        <p><strong>构建时间：</strong>整个构建过程可能需要 1-2 小时，请确保网络稳定。</p>
                        <p><strong>磁盘空间：</strong>最终镜像大小约为 8-10GB，请预留足够空间。</p>
                        <p><strong>许可协议：</strong>使用Oracle软件需要遵守相关许可协议。</p>
                    </div>

                    <h3><i class="fas fa-folder"></i> 创建工作目录</h3>
                    <pre><code># 创建Oracle Docker构建目录
mkdir -p ~/oracle-docker/********
cd ~/oracle-docker/********

# 创建子目录结构
mkdir -p {scripts,config,logs}

# 验证目录结构
tree . || ls -la</code></pre>
                </section>

                <section id="download">
                    <h2><span class="step-number">2</span>下载Oracle安装包</h2>

                    <div class="warning-box">
                        <h4><i class="fas fa-exclamation-triangle"></i> 重要提示</h4>
                        <p><strong>许可要求：</strong>您需要拥有有效的Oracle账户才能下载官方安装包。请确保遵守Oracle的许可协议。</p>
                        <p><strong>文件大小：</strong>安装包总计约2.4GB，请确保网络稳定。</p>
                    </div>

                    <h3><i class="fas fa-globe"></i> 访问Oracle官网</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-link"></i> 下载地址</h4>
                        <p>前往Oracle官方下载页面：</p>
                        <p><strong>URL：</strong><code>https://www.oracle.com/database/technologies/oracle-database-software-downloads.html</code>
                        </p>
                        <p>选择 <strong>Oracle Database 11g Release 2</strong> 版本</p>
                    </div>

                    <div class="newbie-tip">
                        <h4><i class="fas fa-user-plus"></i> 注册Oracle账户详细步骤</h4>
                        <p><strong>第1步：</strong> 点击上面的链接，进入Oracle官网</p>
                        <p><strong>第2步：</strong> 如果没有账户，点击"Create Account"注册新账户</p>
                        <p><strong>第3步：</strong> 填写个人信息（可以使用真实信息，Oracle账户是免费的）</p>
                        <p><strong>第4步：</strong> 验证邮箱，激活账户</p>
                        <p><strong>第5步：</strong> 登录后找到"Oracle Database 11g Release 2"</p>
                        <p><strong>注意：</strong> 下载前需要接受许可协议，这是正常流程</p>
                    </div>

                    <h3><i class="fas fa-download"></i> 下载所需文件</h3>
                    <div class="path-steps">
                        <div class="path-step">
                            <div class="step-icon">1</div>
                            <div class="step-content">
                                <h5>第一个安装包</h5>
                                <p><code>linux.x64_11gR2_database_1of2.zip</code><br>大小：约1.3GB</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon">2</div>
                            <div class="step-content">
                                <h5>第二个安装包</h5>
                                <p><code>linux.x64_11gR2_database_2of2.zip</code><br>大小：约1.1GB</p>
                            </div>
                        </div>
                    </div>

                    <h3><i class="fas fa-check-circle"></i> 验证下载文件</h3>
                    <pre><code># 进入工作目录
cd ~/oracle-docker/********

# 验证下载的文件
ls -la *.zip

# 应该看到类似输出：
# -rw-r--r-- 1 <USER> <GROUP> 1395582860 Dec 22 10:00 linux.x64_11gR2_database_1of2.zip
# -rw-r--r-- 1 <USER> <GROUP> 1151304589 Dec 22 10:05 linux.x64_11gR2_database_2of2.zip

# 检查文件完整性（可选）
md5sum *.zip</code></pre>

                    <h3><i class="fas fa-archive"></i> 解压安装包</h3>
                    <pre><code># 解压第一个文件
unzip linux.x64_11gR2_database_1of2.zip

# 解压第二个文件
unzip linux.x64_11gR2_database_2of2.zip

# 验证解压结果
ls -la database/

# 应该看到Oracle安装程序目录结构
# drwxr-xr-x  4 <USER> <GROUP>  4096 Dec 22 10:10 install/
# drwxr-xr-x  2 <USER> <GROUP>  4096 Dec 22 10:10 response/
# drwxr-xr-x 14 <USER> <GROUP>  4096 Dec 22 10:10 stage/
# -rwxr-xr-x  1 <USER> <GROUP>  3226 Dec 22 10:10 runInstaller*
# -rw-r--r--  1 <USER> <GROUP>  8192 Dec 22 10:10 welcome.html</code></pre>

                    <div class="success-box">
                        <h4><i class="fas fa-check"></i> 下载完成检查清单</h4>
                        <ul>
                            <li><i class="fas fa-check"></i> 两个ZIP文件已下载完成</li>
                            <li><i class="fas fa-check"></i> 文件大小正确（总计约2.4GB）</li>
                            <li><i class="fas fa-check"></i> 解压后database目录结构完整</li>
                            <li><i class="fas fa-check"></i> runInstaller文件具有执行权限</li>
                        </ul>
                    </div>
                </section>

                <section id="dockerfile">
                    <h2><span class="step-number">3</span>创建Dockerfile</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-info-circle"></i> Dockerfile说明</h3>
                        <p>Dockerfile是构建Docker镜像的核心文件。我们将创建一个基于CentOS 7的Oracle 11g R2镜像，包含完整的数据库功能。</p>
                    </div>

                    <h3><i class="fas fa-file-code"></i> 创建主Dockerfile</h3>
                    <pre><code># 创建Dockerfile
cat > Dockerfile << 'EOF'
# Oracle 11g R2 Docker镜像构建文件
# 基于CentOS 7构建，包含完整的Oracle 11g R2数据库

FROM centos:7

# 维护者信息
LABEL maintainer="<EMAIL>"
LABEL description="Oracle Database 11g Release 2 (********) Enterprise Edition"
LABEL version="********"

# 设置环境变量
ENV ORACLE_BASE=/opt/oracle \
    ORACLE_HOME=/opt/oracle/product/********/dbhome_1 \
    ORACLE_SID=ORCL \
    ORACLE_PDB=ORCLPDB \
    ORACLE_PWD=Oracle123 \
    ORACLE_CHARACTERSET=AL32UTF8 \
    ORACLE_EDITION=EE \
    PATH=$ORACLE_HOME/bin:$PATH \
    LD_LIBRARY_PATH=$ORACLE_HOME/lib:/lib:/usr/lib \
    CLASSPATH=$ORACLE_HOME/jlib:$ORACLE_HOME/rdbms/jlib

# 安装系统依赖包
RUN yum -y update && \
    yum -y install \
        binutils \
        compat-libcap1 \
        compat-libstdc++-33 \
        compat-libstdc++-33.i686 \
        gcc \
        gcc-c++ \
        glibc \
        glibc.i686 \
        glibc-devel \
        glibc-devel.i686 \
        ksh \
        libaio \
        libaio.i686 \
        libaio-devel \
        libaio-devel.i686 \
        libgcc \
        libgcc.i686 \
        libstdc++ \
        libstdc++.i686 \
        libstdc++-devel \
        libXi \
        libXtst \
        make \
        sysstat \
        unzip \
        which \
        net-tools \
        sudo && \
    yum clean all

# 创建oracle用户和组
RUN groupadd -g 54321 oinstall && \
    groupadd -g 54322 dba && \
    groupadd -g 54323 oper && \
    useradd -u 54321 -g oinstall -G dba,oper oracle && \
    echo "oracle:Oracle123" | chpasswd

# 创建必要的目录
RUN mkdir -p $ORACLE_BASE && \
    mkdir -p $ORACLE_HOME && \
    mkdir -p /opt/oracle/oradata && \
    mkdir -p /opt/oracle/fast_recovery_area && \
    mkdir -p /opt/oracle/admin && \
    mkdir -p /opt/oracle/cfgtoollogs && \
    mkdir -p /opt/oracle/checkpoints && \
    chown -R oracle:oinstall /opt/oracle && \
    chmod -R 775 /opt/oracle

# 设置系统参数
RUN echo "oracle soft nofile 1024" >> /etc/security/limits.conf && \
    echo "oracle hard nofile 65536" >> /etc/security/limits.conf && \
    echo "oracle soft nproc 2047" >> /etc/security/limits.conf && \
    echo "oracle hard nproc 16384" >> /etc/security/limits.conf && \
    echo "oracle soft stack 10240" >> /etc/security/limits.conf && \
    echo "oracle hard stack 32768" >> /etc/security/limits.conf

# 复制Oracle安装文件
COPY database/ /tmp/database/
COPY scripts/ /opt/oracle/scripts/

# 设置文件权限
RUN chown -R oracle:oinstall /tmp/database && \
    chown -R oracle:oinstall /opt/oracle/scripts && \
    chmod +x /opt/oracle/scripts/*.sh

# 切换到oracle用户执行安装
USER oracle

# 执行Oracle静默安装
RUN /opt/oracle/scripts/install_oracle.sh

# 切换回root用户进行后续配置
USER root

# 配置Oracle服务
RUN /opt/oracle/scripts/configure_oracle.sh

# 暴露端口
EXPOSE 1521 5500

# 设置数据卷
VOLUME ["/opt/oracle/oradata", "/opt/oracle/fast_recovery_area"]

# 设置启动脚本
COPY scripts/startup.sh /opt/oracle/scripts/
RUN chmod +x /opt/oracle/scripts/startup.sh

# 设置容器启动命令
CMD ["/opt/oracle/scripts/startup.sh"]
EOF</code></pre>

                    <div class="info-box">
                        <h4><i class="fas fa-lightbulb"></i> Dockerfile关键点说明</h4>
                        <ul>
                            <li><strong>基础镜像：</strong>使用CentOS 7，兼容性最好</li>
                            <li><strong>环境变量：</strong>设置Oracle运行所需的所有环境变量</li>
                            <li><strong>系统依赖：</strong>安装Oracle 11g所需的所有系统包</li>
                            <li><strong>用户管理：</strong>创建oracle用户和必要的用户组</li>
                            <li><strong>目录结构：</strong>创建Oracle标准目录结构</li>
                            <li><strong>数据持久化：</strong>配置数据卷用于数据持久化</li>
                        </ul>
                    </div>
                </section>

                <section id="scripts">
                    <h2><span class="step-number">4</span>创建安装脚本</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-magic"></i> 脚本说明</h3>
                        <p>我们需要创建几个关键脚本来自动化Oracle的安装、配置和启动过程。这些脚本将在Docker构建过程中被调用。</p>
                    </div>

                    <div class="newbie-tip">
                        <h4><i class="fas fa-scroll"></i> 什么是脚本？</h4>
                        <p><strong>脚本</strong>就是一系列命令的集合，写在一个文件里，可以一次性执行多个操作。</p>
                        <p><strong>为什么要用脚本？</strong> 因为Oracle安装过程很复杂，需要执行很多命令，用脚本可以自动化这个过程。</p>
                        <p><strong>脚本怎么运行？</strong> Docker在构建镜像时会自动调用这些脚本。</p>
                        <p><strong>我需要理解脚本内容吗？</strong> 不需要完全理解，按照教程复制粘贴即可，但了解大概作用会更好。</p>
                    </div>

                    <h3><i class="fas fa-terminal"></i> 创建Oracle安装脚本</h3>
                    <pre><code># 创建Oracle静默安装脚本
cat > scripts/install_oracle.sh << 'EOF'
#!/bin/bash
# Oracle 11g R2 静默安装脚本

set -e

echo "开始Oracle 11g R2静默安装..."

# 设置环境变量
export ORACLE_BASE=/opt/oracle
export ORACLE_HOME=/opt/oracle/product/********/dbhome_1
export ORACLE_SID=ORCL

# 创建响应文件目录
mkdir -p $ORACLE_BASE/install

# 创建数据库安装响应文件
cat > $ORACLE_BASE/install/db_install.rsp << 'INSTALL_EOF'
oracle.install.responseFileVersion=/oracle/install/rspfmt_dbinstall_response_schema_v11_2_0
oracle.install.option=INSTALL_DB_SWONLY
ORACLE_HOSTNAME=localhost
UNIX_GROUP_NAME=oinstall
INVENTORY_LOCATION=/opt/oracle/oraInventory
SELECTED_LANGUAGES=en,zh_CN
ORACLE_HOME=/opt/oracle/product/********/dbhome_1
ORACLE_BASE=/opt/oracle
oracle.install.db.InstallEdition=EE
oracle.install.db.DBA_GROUP=dba
oracle.install.db.OPER_GROUP=oper
DECLINE_SECURITY_UPDATES=true
INSTALL_EOF

echo "开始执行Oracle安装程序..."

# 执行安装
cd /tmp/database
./runInstaller -silent -responseFile $ORACLE_BASE/install/db_install.rsp -ignorePrereq -ignoreSysPrereqs -waitforcompletion

echo "Oracle软件安装完成"

# 清理安装文件
rm -rf /tmp/database
rm -rf $ORACLE_BASE/install

echo "Oracle 11g R2安装脚本执行完成"
EOF

chmod +x scripts/install_oracle.sh</code></pre>

                    <h3><i class="fas fa-cogs"></i> 创建启动脚本</h3>
                    <pre><code># 创建容器启动脚本
cat > scripts/startup.sh << 'EOF'
#!/bin/bash
# Oracle 11g R2 容器启动脚本

set -e

# 设置环境变量
export ORACLE_HOME=/opt/oracle/product/********/dbhome_1
export ORACLE_SID=ORCL
export PATH=$ORACLE_HOME/bin:$PATH

echo "启动Oracle数据库服务..."

# 切换到oracle用户
su - oracle -c "
export ORACLE_HOME=/opt/oracle/product/********/dbhome_1
export ORACLE_SID=ORCL
export PATH=\$ORACLE_HOME/bin:\$PATH

# 启动监听器
lsnrctl start

# 启动数据库
sqlplus / as sysdba << 'SQL_EOF'
startup;
alter database open;
exit;
SQL_EOF

echo 'Oracle数据库启动完成'
"

# 保持容器运行
echo "Oracle数据库服务已启动，容器保持运行状态..."
tail -f $ORACLE_HOME/network/log/listener.log
EOF

chmod +x scripts/startup.sh</code></pre>
                </section>

                <section id="build">
                    <h2><span class="step-number">5</span>构建Docker镜像</h2>

                    <div class="warning-box">
                        <h4><i class="fas fa-clock"></i> 构建时间提醒</h4>
                        <p><strong>预计时间：</strong>整个构建过程可能需要1-2小时，请耐心等待。</p>
                        <p><strong>网络要求：</strong>确保网络稳定，避免构建过程中断。</p>
                    </div>

                    <h3><i class="fas fa-list-check"></i> 验证文件结构</h3>
                    <pre><code># 检查目录结构
tree . || ls -la

# 应该看到类似结构：
# .
# ├── Dockerfile
# ├── database/
# │   ├── install/
# │   ├── response/
# │   ├── runInstaller*
# │   └── ...
# └── scripts/
#     ├── install_oracle.sh*
#     └── startup.sh*</code></pre>

                    <h3><i class="fas fa-hammer"></i> 开始构建镜像</h3>

                    <div class="newbie-tip">
                        <h4><i class="fas fa-clock"></i> 构建过程说明</h4>
                        <p><strong>构建时间：</strong> 整个过程需要1-2小时，请耐心等待</p>
                        <p><strong>网络要求：</strong> 需要下载CentOS基础镜像和各种软件包</p>
                        <p><strong>不要关闭：</strong> 构建过程中不要关闭终端或电脑</p>
                        <p><strong>正常现象：</strong> 看到很多文字滚动是正常的，说明在安装软件</p>
                    </div>

                    <pre><code># 构建Oracle 11g R2 Docker镜像（推荐命令）
docker build -t oracle11g:******** .

# 如果需要查看详细构建过程（适合调试）
docker build --no-cache --progress=plain -t oracle11g:******** .

# 如果内存不足，可以限制内存使用
docker build --memory=8g -t oracle11g:******** .</code></pre>

                    <div class="info-box">
                        <h4><i class="fas fa-info-circle"></i> 构建过程中你会看到</h4>
                        <div class="progress-container">
                            <div class="progress-bar" style="width: 100%;">构建进度示例</div>
                        </div>
                        <ul>
                            <li><strong>Step 1/15:</strong> 下载CentOS基础镜像</li>
                            <li><strong>Step 2/15:</strong> 设置环境变量</li>
                            <li><strong>Step 3/15:</strong> 安装系统依赖包（最耗时）</li>
                            <li><strong>Step 4/15:</strong> 创建Oracle用户</li>
                            <li><strong>Step 5/15:</strong> 创建目录结构</li>
                            <li><strong>Step 6-10:</strong> 复制文件和设置权限</li>
                            <li><strong>Step 11-15:</strong> 执行Oracle安装（最耗时）</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-check-double"></i> 验证镜像构建</h3>
                    <pre><code># 查看构建的镜像
docker images | grep oracle11g

# 检查镜像详细信息
docker inspect oracle11g:********</code></pre>
                </section>

                <section id="run">
                    <h2><span class="step-number">6</span>运行容器</h2>

                    <h3><i class="fas fa-database"></i> 创建数据卷</h3>
                    <pre><code># 创建Oracle数据卷
docker volume create oracle_data
docker volume create oracle_logs

# 查看创建的卷
docker volume ls | grep oracle</code></pre>

                    <h3><i class="fas fa-play"></i> 运行Oracle容器</h3>
                    <pre><code># 运行Oracle 11g R2容器
docker run -d \
  --name oracle11g \
  -p 1521:1521 \
  -p 5500:5500 \
  -e ORACLE_SID=ORCL \
  -e ORACLE_PWD=Oracle123 \
  -v oracle_data:/opt/oracle/oradata \
  -v oracle_logs:/opt/oracle/fast_recovery_area \
  --shm-size=2g \
  oracle11g:********

# 查看容器状态
docker ps | grep oracle11g

# 查看启动日志
docker logs -f oracle11g</code></pre>
                </section>

                <section id="verify">
                    <h2><span class="step-number">7</span>验证安装</h2>

                    <h3><i class="fas fa-plug"></i> 连接数据库测试</h3>
                    <pre><code># 进入容器
docker exec -it oracle11g bash

# 切换到oracle用户
su - oracle

# 连接数据库
sqlplus / as sysdba

# 执行测试SQL
SQL> select instance_name, status from v$instance;
SQL> select name from v$database;
SQL> exit;</code></pre>

                    <div class="success-box">
                        <h4><i class="fas fa-trophy"></i> 恭喜！部署完成</h4>
                        <p>您已经成功完成了Oracle 11g R2 Docker镜像的构建和部署！</p>
                        <ul>
                            <li><i class="fas fa-check"></i> Oracle数据库已成功安装</li>
                            <li><i class="fas fa-check"></i> 容器正常运行</li>
                            <li><i class="fas fa-check"></i> 数据库连接正常</li>
                            <li><i class="fas fa-check"></i> 数据持久化已配置</li>
                        </ul>
                    </div>
                </section>

                <section id="troubleshooting">
                    <h2><span class="step-number">8</span>故障排除</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-tools"></i> 遇到问题不要慌</h3>
                        <p>构建过程中遇到问题是很正常的，这里整理了最常见的问题和解决方案。按照步骤排查，99%的问题都能解决。</p>
                    </div>

                    <h3><i class="fas fa-question-circle"></i> 常见问题FAQ</h3>

                    <div class="faq-item">
                        <div class="faq-question" onclick="toggleFaq(this)">
                            <span><i class="fas fa-memory"></i> 问题1：构建过程中提示内存不足</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><strong>错误信息：</strong> "Cannot allocate memory" 或 "Out of memory"</p>
                            <p><strong>解决方案：</strong></p>
                            <ul>
                                <li>关闭其他占用内存的程序</li>
                                <li>在Docker Desktop中增加内存分配到8GB以上</li>
                                <li>重启Docker服务</li>
                                <li>如果还不行，尝试在构建时限制并发：<code>docker build --memory=8g -t oracle11g:******** .</code></li>
                            </ul>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question" onclick="toggleFaq(this)">
                            <span><i class="fas fa-download"></i> 问题2：下载Oracle安装包失败</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><strong>可能原因：</strong></p>
                            <ul>
                                <li>网络连接不稳定</li>
                                <li>Oracle账户未激活</li>
                                <li>未接受许可协议</li>
                            </ul>
                            <p><strong>解决方案：</strong></p>
                            <ul>
                                <li>使用稳定的网络环境，避免使用移动热点</li>
                                <li>检查邮箱，确保Oracle账户已激活</li>
                                <li>重新登录Oracle官网，确保接受了许可协议</li>
                                <li>可以使用下载工具（如迅雷）进行断点续传</li>
                            </ul>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question" onclick="toggleFaq(this)">
                            <span><i class="fas fa-exclamation-triangle"></i> 问题3：Docker构建过程中断</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><strong>错误信息：</strong> 构建过程突然停止或报错</p>
                            <p><strong>解决方案：</strong></p>
                            <ul>
                                <li>检查网络连接是否稳定</li>
                                <li>确保磁盘空间充足（至少20GB）</li>
                                <li>重新运行构建命令，Docker会从上次中断的地方继续</li>
                                <li>如果多次失败，使用 <code>docker build --no-cache</code> 重新开始</li>
                            </ul>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question" onclick="toggleFaq(this)">
                            <span><i class="fas fa-plug"></i> 问题4：容器启动后无法连接数据库</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><strong>错误信息：</strong> "TNS: listener does not currently know of service"</p>
                            <p><strong>解决方案：</strong></p>
                            <ul>
                                <li>等待2-3分钟，Oracle数据库启动需要时间</li>
                                <li>检查容器是否正在运行：<code>docker ps</code></li>
                                <li>查看容器日志：<code>docker logs oracle11g</code></li>
                                <li>检查端口是否被占用：<code>netstat -an | grep 1521</code></li>
                                <li>重启容器：<code>docker restart oracle11g</code></li>
                            </ul>
                        </div>
                    </div>

                    <div class="faq-item">
                        <div class="faq-question" onclick="toggleFaq(this)">
                            <span><i class="fas fa-key"></i> 问题5：忘记数据库密码</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="faq-answer">
                            <p><strong>默认密码：</strong></p>
                            <ul>
                                <li>SYS用户密码：Oracle123</li>
                                <li>SYSTEM用户密码：Oracle123</li>
                                <li>数据库SID：ORCL</li>
                            </ul>
                            <p><strong>连接示例：</strong></p>
                            <p><code>sqlplus system/Oracle123@localhost:1521/ORCL</code></p>
                        </div>
                    </div>

                    <h3><i class="fas fa-search"></i> 日志查看命令</h3>
                    <pre><code># 查看容器运行状态
docker ps -a

# 查看容器详细日志
docker logs oracle11g

# 实时查看日志
docker logs -f oracle11g

# 进入容器查看Oracle日志
docker exec -it oracle11g bash
find /opt/oracle -name "*.log" -type f

# 查看监听器状态
docker exec -it oracle11g su - oracle -c "lsnrctl status"

# 查看数据库状态
docker exec -it oracle11g su - oracle -c "sqlplus / as sysdba" << EOF
select status from v\$instance;
exit;
EOF</code></pre>

                    <div class="success-box">
                        <h4><i class="fas fa-life-ring"></i> 还是解决不了？</h4>
                        <p>如果按照上述方法还是无法解决问题，可以尝试以下方式：</p>
                        <ul>
                            <li><i class="fas fa-search"></i> 复制错误信息到搜索引擎查找解决方案</li>
                            <li><i class="fas fa-users"></i> 在技术论坛（如CSDN、博客园）寻求帮助</li>
                            <li><i class="fas fa-book"></i> 查阅Oracle官方文档</li>
                            <li><i class="fas fa-redo"></i> 最后的办法：删除所有容器和镜像，重新开始</li>
                        </ul>
                    </div>
                </section>

                <!-- 总结部分 -->
                <section id="summary">
                    <h2><span class="step-number">🎉</span>恭喜完成！</h2>

                    <div class="success-box">
                        <h3><i class="fas fa-trophy"></i> 你已经成功完成了Oracle Docker部署！</h3>
                        <p>通过这个教程，你不仅获得了一个可用的Oracle数据库，更重要的是掌握了Docker容器化技术的实战经验。</p>
                    </div>

                    <div class="path-steps">
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-database"></i></div>
                            <div class="step-content">
                                <h5>Oracle数据库</h5>
                                <p>完整的Oracle 11g R2企业版数据库</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fab fa-docker"></i></div>
                            <div class="step-content">
                                <h5>Docker技能</h5>
                                <p>掌握了镜像构建和容器管理</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-tools"></i></div>
                            <div class="step-content">
                                <h5>运维经验</h5>
                                <p>学会了数据库部署和故障排除</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-graduation-cap"></i></div>
                            <div class="step-content">
                                <h5>项目经验</h5>
                                <p>可以写在简历上的实战项目</p>
                            </div>
                        </div>
                    </div>

                    <h3><i class="fas fa-rocket"></i> 下一步学习建议</h3>

                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-icon">1</div>
                            <div class="timeline-content">
                                <h4>深入学习Oracle</h4>
                                <p>学习SQL语句、数据库设计、性能优化等</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-icon">2</div>
                            <div class="timeline-content">
                                <h4>扩展Docker技能</h4>
                                <p>学习Docker Compose、Docker Swarm、Kubernetes等</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-icon">3</div>
                            <div class="timeline-content">
                                <h4>实际项目应用</h4>
                                <p>在实际项目中使用这个Oracle数据库</p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-icon">4</div>
                            <div class="timeline-content">
                                <h4>分享你的经验</h4>
                                <p>写博客、做分享，帮助其他小白学习</p>
                            </div>
                        </div>
                    </div>

                    <div class="info-box">
                        <h4><i class="fas fa-lightbulb"></i> 小白进阶提示</h4>
                        <ul>
                            <li><i class="fas fa-bookmark"></i> <strong>收藏本教程：</strong>以后遇到问题可以随时查看</li>
                            <li><i class="fas fa-copy"></i> <strong>备份镜像：</strong>使用 <code>docker save</code> 命令备份你的镜像
                            </li>
                            <li><i class="fas fa-share"></i> <strong>分享给朋友：</strong>帮助更多人学习Docker和Oracle</li>
                            <li><i class="fas fa-heart"></i> <strong>持续学习：</strong>技术在不断发展，保持学习的热情</li>
                        </ul>
                    </div>

                    <div class="newbie-tip">
                        <h4><i class="fas fa-medal"></i> 给自己一个赞！</h4>
                        <p>作为一个小白，能够完成这么复杂的Oracle Docker部署，说明你有很强的学习能力和动手能力。</p>
                        <p>这个项目涉及了Linux系统、Docker容器、Oracle数据库等多个技术领域，你都成功掌握了！</p>
                        <p><strong>记住：</strong>每一个技术大牛都是从小白开始的，继续保持学习的热情，你一定会越来越厉害！</p>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <!-- JavaScript功能 -->
    <script>
        // 移动端菜单切换
        document.getElementById('mobileMenuBtn')?.addEventListener('click', function () {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('active');
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 高亮当前章节
        window.addEventListener('scroll', function () {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.sidebar a[href^="#"]');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // FAQ折叠功能
        function toggleFaq(element) {
            const answer = element.nextElementSibling;
            const icon = element.querySelector('i.fa-chevron-down');

            if (answer.classList.contains('active')) {
                answer.classList.remove('active');
                element.classList.remove('active');
                icon.style.transform = 'rotate(0deg)';
            } else {
                // 关闭其他打开的FAQ
                document.querySelectorAll('.faq-answer.active').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelectorAll('.faq-question.active').forEach(item => {
                    item.classList.remove('active');
                    item.querySelector('i.fa-chevron-down').style.transform = 'rotate(0deg)';
                });

                // 打开当前FAQ
                answer.classList.add('active');
                element.classList.add('active');
                icon.style.transform = 'rotate(180deg)';
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function () {
            // 添加加载动画
            const sections = document.querySelectorAll('section');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, { threshold: 0.1 });

            sections.forEach(section => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(section);
            });

            // 显示进度提示
            console.log('🎉 Oracle Docker教程已加载完成！');
            console.log('💡 小提示：遇到问题可以查看故障排除部分');
        });

        // 复制代码功能
        function copyCode(button) {
            const codeBlock = button.nextElementSibling.querySelector('code');
            const text = codeBlock.textContent;

            navigator.clipboard.writeText(text).then(() => {
                button.textContent = '已复制!';
                button.style.background = '#28a745';
                setTimeout(() => {
                    button.textContent = '复制代码';
                    button.style.background = '#007bff';
                }, 2000);
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                button.textContent = '已复制!';
                setTimeout(() => {
                    button.textContent = '复制代码';
                }, 2000);
            });
        }
    </script>
</body>

</html>