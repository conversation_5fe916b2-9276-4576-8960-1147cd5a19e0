#!/bin/bash

# nginx 1.28.0 离线编译脚本
# 使用RPM包依赖，完全离线构建

set -e

NGINX_VERSION="1.28.0"

echo "================================================================================"
echo "                        nginx编译安装脚本（RPM依赖版本）"
echo "================================================================================"
echo "开始安装RPM依赖包并编译nginx..."
echo "nginx版本: $NGINX_VERSION"
echo ""

echo "=== 安装RPM依赖包 ==="
echo "安装开发工具和依赖库..."

# 验证RPM包是否存在
if [ ! -d "/tmp/centos7-rpms" ] || [ -z "$(ls -A /tmp/centos7-rpms 2>/dev/null)" ]; then
    echo "❌ RPM包目录不存在或为空"
    exit 1
fi

echo "✓ RPM依赖包安装完成"

# 检查编译器安装情况
echo "检查编译器安装情况:"
echo "查找gcc相关文件:"
find /usr -name "*gcc*" 2>/dev/null | head -10
echo "查找cc相关文件:"
find /usr -name "*cc*" 2>/dev/null | head -10
echo "检查/usr/bin目录:"
ls -la /usr/bin/ | grep -E "(gcc|cc)" || echo "未找到gcc或cc"

# 尝试安装gcc
echo "尝试重新安装gcc..."
rpm -qa | grep gcc || echo "未找到已安装的gcc包"

# 立即创建cc链接
echo "创建cc到gcc的符号链接..."
if [ -f "/usr/bin/gcc" ]; then
    ln -sf /usr/bin/gcc /usr/bin/cc
    echo "✓ cc符号链接已创建"
else
    echo "❌ gcc不存在，查找可能的gcc位置..."
    find /usr -name "gcc" -type f 2>/dev/null | head -5

    # 尝试使用找到的gcc
    GCC_PATH=$(find /usr -name "gcc" -type f 2>/dev/null | head -1)
    if [ -n "$GCC_PATH" ]; then
        echo "找到gcc: $GCC_PATH"
        ln -sf "$GCC_PATH" /usr/bin/gcc
        ln -sf "$GCC_PATH" /usr/bin/cc
        echo "✓ gcc和cc符号链接已创建"
    else
        echo "❌ 完全找不到gcc，无法继续"
        exit 1
    fi
fi

# 验证cc链接
echo "验证cc链接:"
ls -la /usr/bin/cc

echo ""
echo "=== 验证编译器 ==="

# 检查gcc
if command -v gcc >/dev/null 2>&1; then
    echo "找到gcc: $(which gcc)"
    gcc_version=$(gcc --version 2>/dev/null | head -n1)
    echo "✓ gcc编译器可用: $gcc_version"
    echo "gcc路径: $(which gcc)"
else
    echo "❌ gcc编译器未找到"
    exit 1
fi

# 检查cc
if command -v cc >/dev/null 2>&1; then
    cc_version=$(cc --version 2>/dev/null | head -n1)
    echo "✓ cc编译器可用: $cc_version"
else
    echo "❌ cc编译器未找到"
    exit 1
fi

echo ""
echo "=== 验证依赖库安装 ==="

# 检查PCRE库
echo "检查PCRE库..."
echo "查找PCRE相关文件:"
find /usr -name "*pcre*" 2>/dev/null | head -10

if [ -f "/usr/include/pcre.h" ]; then
    echo "✓ PCRE库安装成功 (头文件存在)"
    if command -v pcre-config >/dev/null 2>&1; then
        pcre-config --version 2>/dev/null || echo "版本信息不可用"
    else
        echo "pcre-config不可用，但头文件存在"
    fi
elif [ -f "/usr/lib64/libpcre.so" ] || [ -f "/usr/lib/libpcre.so" ]; then
    echo "✓ PCRE库安装成功 (库文件存在)"
    ls -la /usr/lib64/libpcre* 2>/dev/null || ls -la /usr/lib/libpcre* 2>/dev/null
else
    echo "❌ PCRE库未找到，但继续尝试构建..."
    echo "nginx configure会自动处理PCRE依赖"
fi

# 检查zlib库
echo "检查zlib库..."
if [ -f "/usr/include/zlib.h" ]; then
    echo "✓ zlib库安装成功 (头文件存在)"
    echo "版本: 1.2.7"
elif [ -f "/usr/lib64/libz.so" ] || [ -f "/usr/lib/libz.so" ]; then
    echo "✓ zlib库安装成功 (库文件存在)"
    ls -la /usr/lib64/libz* 2>/dev/null || ls -la /usr/lib/libz* 2>/dev/null
else
    echo "❌ zlib库未找到，但继续尝试构建..."
    echo "nginx configure会自动处理zlib依赖"
fi

# 检查OpenSSL库
echo "检查OpenSSL库..."
if [ -f "/usr/include/openssl/ssl.h" ]; then
    echo "✓ OpenSSL库安装成功 (头文件存在)"
    if command -v openssl >/dev/null 2>&1; then
        openssl version 2>/dev/null || echo "版本信息不可用"
    else
        echo "openssl命令不可用，但头文件存在"
    fi
elif [ -f "/usr/lib64/libssl.so" ] || [ -f "/usr/lib/libssl.so" ]; then
    echo "✓ OpenSSL库安装成功 (库文件存在)"
    ls -la /usr/lib64/libssl* 2>/dev/null || ls -la /usr/lib/libssl* 2>/dev/null
else
    echo "❌ OpenSSL库未找到，但继续尝试构建..."
    echo "nginx configure会自动处理OpenSSL依赖"
fi

echo ""
echo "=== 解压nginx源码包 ==="
echo "解压nginx源码包..."

cd /tmp/build

# 检查nginx源码包
if [ ! -f "/tmp/packages/nginx-${NGINX_VERSION}.tar.gz" ]; then
    echo "❌ nginx源码包不存在: /tmp/packages/nginx-${NGINX_VERSION}.tar.gz"
    exit 1
fi

tar -xzf /tmp/packages/nginx-${NGINX_VERSION}.tar.gz
echo "✓ nginx源码包解压完成"

echo ""
echo "=== 编译nginx ==="
echo "配置nginx编译选项（使用系统库）..."

# 显示调试信息
echo "PATH: $PATH"
echo "可用的编译器:"
find /usr/bin -name "*gcc*" -o -name "*cc*" | head -5

echo "测试gcc调用:"
gcc --version

echo "测试cc调用:"
cc --version

# 检查gcc组件
echo "检查gcc组件:"
find /usr/libexec/gcc -name "collect2" 2>/dev/null | head -1

# 检查gcc版本目录
echo "检查gcc版本目录:"
ls -la /usr/libexec/gcc/x86_64-redhat-linux/ 2>/dev/null || echo "目录不存在"

# 检查gcc 4.8.5目录
if [ -d "/usr/libexec/gcc/x86_64-redhat-linux/4.8.5" ]; then
    echo "检查gcc 4.8.5目录:"
    ls -la /usr/libexec/gcc/x86_64-redhat-linux/4.8.5/
elif [ -d "/usr/libexec/gcc/x86_64-redhat-linux/4.8.2" ]; then
    echo "检查gcc 4.8.2目录:"
    ls -la /usr/libexec/gcc/x86_64-redhat-linux/4.8.2/
fi

# 检查并修复cc编译器链接问题
echo "修复cc编译器链接问题..."

# 确保cc链接到gcc
if [ ! -f "/usr/bin/cc" ] || [ ! -x "/usr/bin/cc" ]; then
    echo "创建cc到gcc的符号链接..."
    ln -sf /usr/bin/gcc /usr/bin/cc
    echo "✓ cc符号链接已创建"
fi

# 检查cc1文件
GCC_VERSION_DIR=""
if [ -d "/usr/libexec/gcc/x86_64-redhat-linux/4.8.5" ]; then
    GCC_VERSION_DIR="/usr/libexec/gcc/x86_64-redhat-linux/4.8.5"
elif [ -d "/usr/libexec/gcc/x86_64-redhat-linux/4.8.2" ]; then
    GCC_VERSION_DIR="/usr/libexec/gcc/x86_64-redhat-linux/4.8.2"
fi

if [ -n "$GCC_VERSION_DIR" ]; then
    if [ ! -f "$GCC_VERSION_DIR/cc1" ]; then
        echo "❌ cc1文件缺失，尝试修复..."

        # 查找系统中的cc1文件
        CC1_FILE=$(find /usr -name "cc1" 2>/dev/null | head -1)
        if [ -n "$CC1_FILE" ]; then
            echo "找到cc1文件: $CC1_FILE"
            ln -sf "$CC1_FILE" "$GCC_VERSION_DIR/cc1"
            echo "✓ cc1符号链接已创建"
        elif [ -f "$GCC_VERSION_DIR/cc1plus" ]; then
            echo "使用cc1plus作为cc1的替代..."
            ln -sf cc1plus "$GCC_VERSION_DIR/cc1"
            echo "✓ cc1符号链接已创建"
        else
            echo "⚠️  无法找到cc1文件，尝试继续构建..."
        fi
    else
        echo "✓ cc1文件存在"
    fi
fi

# 检查libmpc库
echo "检查libmpc库:"
find /usr/lib64 -name "libmpc.so*" 2>/dev/null

echo "检查已安装的libmpc包:"
rpm -qa | grep libmpc

if [ -f "/usr/lib64/libmpc.so.3.0.0" ]; then
    echo "找到libmpc库，检查版本..."
    find /usr/lib64 -name "libmpc.so*"
    
    echo "创建libmpc.so.3符号链接..."
    if [ ! -f "/lib64/libmpc.so.3" ]; then
        ln -sf /usr/lib64/libmpc.so.3.0.0 /lib64/libmpc.so.3
    fi
    echo "✓ libmpc.so.3符号链接已创建"
fi

# 检查系统中的.so.3库
echo "检查系统中的.so.3库:"
find /usr/lib64 -name "*.so.3" 2>/dev/null | head -10

# 更新ldconfig缓存
echo "更新ldconfig缓存..."
ldconfig
echo "ldconfig缓存:"
ldconfig -p | grep libmpc || echo "libmpc未在缓存中找到"

# 显示gcc库路径
echo "gcc库路径:"
gcc -print-search-dirs | grep libraries

# 修复可能的编译器路径问题
echo "修复编译器路径问题..."

# 确保所有必要的编译器工具都可用
REQUIRED_TOOLS=("gcc" "g++" "cc" "c++" "cpp" "as" "ld" "ar" "ranlib" "strip")
for tool in "${REQUIRED_TOOLS[@]}"; do
    if ! command -v "$tool" >/dev/null 2>&1; then
        echo "⚠️  $tool 不可用，尝试创建链接..."
        case "$tool" in
            "cc") ln -sf /usr/bin/gcc /usr/bin/cc ;;
            "c++") ln -sf /usr/bin/g++ /usr/bin/c++ ;;
            "cpp") ln -sf /usr/bin/gcc /usr/bin/cpp ;;
        esac
    else
        echo "✓ $tool 可用"
    fi
done

# 设置编译器环境变量
export CC=/usr/bin/gcc
export CXX=/usr/bin/g++
export CPP=/usr/bin/cpp

echo "编译器环境变量已设置:"
echo "CC=$CC"
echo "CXX=$CXX"
echo "CPP=$CPP"

# 测试gcc编译
echo "测试gcc编译:"
echo 'int main(){return 0;}' > test.c
if gcc test.c -o test 2>/dev/null; then
    echo "✓ gcc编译测试成功"
    rm -f test test.c
else
    echo "❌ gcc编译测试失败"
    exit 1
fi

# 测试cc编译
echo "测试cc编译:"
echo 'int main(){return 0;}' > test.c
if cc test.c -o test 2>/dev/null; then
    echo "✓ cc编译测试成功"
    rm -f test test.c
else
    echo "❌ cc编译测试失败"
    exit 1
fi

# 进入nginx源码目录
cd nginx-${NGINX_VERSION}
echo "当前目录: $(pwd)"

# 修复nginx源码中的C++关键字冲突
echo "修复nginx源码中的C++关键字冲突..."

# 修复ngx_palloc.c中的'new'变量名
echo "修复ngx_palloc.c中的'new'变量名..."
if [ -f "src/core/ngx_palloc.c" ]; then
    # 修复所有new变量声明和使用
    sed -i 's/ngx_pool_t  \*p, \*new;/ngx_pool_t  *p, *new_ptr;/g' src/core/ngx_palloc.c
    sed -i 's/void \*new;/void *new_ptr;/g' src/core/ngx_palloc.c
    sed -i 's/new = /new_ptr = /g' src/core/ngx_palloc.c
    sed -i 's/new->/new_ptr->/g' src/core/ngx_palloc.c
    sed -i 's/p->d.next = new;/p->d.next = new_ptr;/g' src/core/ngx_palloc.c
    sed -i 's/return new;/return new_ptr;/g' src/core/ngx_palloc.c
    echo "✓ ngx_palloc.c修复完成"
fi

# 修复ngx_array.c中的'new'变量名
echo "修复ngx_array.c中的'new'变量名..."
if [ -f "src/core/ngx_array.c" ]; then
    # 修复所有new变量声明和使用
    sed -i 's/void        \*elt, \*new;/void        *elt, *new_ptr;/g' src/core/ngx_array.c
    sed -i 's/void \*new;/void *new_ptr;/g' src/core/ngx_array.c
    sed -i 's/new = /new_ptr = /g' src/core/ngx_array.c
    sed -i 's/return new;/return new_ptr;/g' src/core/ngx_array.c
    sed -i 's/if (new ==/if (new_ptr ==/g' src/core/ngx_array.c
    sed -i 's/ngx_memcpy(new,/ngx_memcpy(new_ptr,/g' src/core/ngx_array.c
    sed -i 's/a->elts = new;/a->elts = new_ptr;/g' src/core/ngx_array.c
    echo "✓ ngx_array.c修复完成"
fi

# 修复ngx_resolver.c中的C++关键字冲突
echo "修复ngx_resolver.c中的C++关键字冲突..."
if [ -f "src/core/ngx_resolver.c" ]; then
    # 修复export关键字冲突
    sed -i 's/goto export;/goto export_label;/g' src/core/ngx_resolver.c
    sed -i 's/export:/export_label:/g' src/core/ngx_resolver.c
    # 修复其他C++关键字冲突
    sed -i 's/\bclass\b/cls/g' src/core/ngx_resolver.c
    sed -i 's/void \*new;/void *new_ptr;/g' src/core/ngx_resolver.c
    sed -i 's/new = /new_ptr = /g' src/core/ngx_resolver.c
    sed -i 's/return new;/return new_ptr;/g' src/core/ngx_resolver.c
    echo "✓ ngx_resolver.c修复完成"
fi

# 修复ngx_http_request.c中的'new'变量名
echo "修复ngx_http_request.c中的'new'变量名..."
if [ -f "src/http/ngx_http_request.c" ]; then
    # 修复所有new变量声明和使用
    sed -i 's/u_char  \*old, \*new;/u_char  *old, *new_ptr;/g' src/http/ngx_http_request.c
    sed -i 's/ngx_http_request_t  \*r, \*new;/ngx_http_request_t  *r, *new_ptr;/g' src/http/ngx_http_request.c
    sed -i 's/void \*new;/void *new_ptr;/g' src/http/ngx_http_request.c
    sed -i 's/new = /new_ptr = /g' src/http/ngx_http_request.c
    sed -i 's/return new;/return new_ptr;/g' src/http/ngx_http_request.c
    sed -i 's/if (new ==/if (new_ptr ==/g' src/http/ngx_http_request.c
    sed -i 's/new->/new_ptr->/g' src/http/ngx_http_request.c
    sed -i 's/\*new;/*new_ptr;/g' src/http/ngx_http_request.c
    sed -i 's/ngx_memcpy(new,/ngx_memcpy(new_ptr,/g' src/http/ngx_http_request.c
    sed -i 's/b->pos = new + /b->pos = new_ptr + /g' src/http/ngx_http_request.c
    sed -i 's/b->last = new + /b->last = new_ptr + /g' src/http/ngx_http_request.c
    sed -i 's/r->request_start = new;/r->request_start = new_ptr;/g' src/http/ngx_http_request.c
    sed -i 's/r->request_end = new + /r->request_end = new_ptr + /g' src/http/ngx_http_request.c
    sed -i 's/r->method_end = new + /r->method_end = new_ptr + /g' src/http/ngx_http_request.c
    sed -i 's/r->uri_start = new + /r->uri_start = new_ptr + /g' src/http/ngx_http_request.c
    sed -i 's/r->uri_end = new + /r->uri_end = new_ptr + /g' src/http/ngx_http_request.c
    sed -i 's/r->schema_start = new + /r->schema_start = new_ptr + /g' src/http/ngx_http_request.c
    sed -i 's/r->schema_end = new + /r->schema_end = new_ptr + /g' src/http/ngx_http_request.c
    sed -i 's/r->host_start = new + /r->host_start = new_ptr + /g' src/http/ngx_http_request.c
    sed -i 's/r->host_end = new + /r->host_end = new_ptr + /g' src/http/ngx_http_request.c
    sed -i 's/r->uri_ext = new + /r->uri_ext = new_ptr + /g' src/http/ngx_http_request.c
    sed -i 's/r->args_start = new + /r->args_start = new_ptr + /g' src/http/ngx_http_request.c
    sed -i 's/r->http_protocol.data = new + /r->http_protocol.data = new_ptr + /g' src/http/ngx_http_request.c
    sed -i 's/r->header_name_start = new;/r->header_name_start = new_ptr;/g' src/http/ngx_http_request.c
    sed -i 's/r->header_name_end = new + /r->header_name_end = new_ptr + /g' src/http/ngx_http_request.c
    sed -i 's/r->header_start = new + /r->header_start = new_ptr + /g' src/http/ngx_http_request.c
    sed -i 's/r->header_end = new + /r->header_end = new_ptr + /g' src/http/ngx_http_request.c
    echo "✓ ngx_http_request.c修复完成"
fi

# 修复ngx_http_script.c中的'new'变量名
echo "修复ngx_http_script.c中的'new'变量名..."
if [ -f "src/http/ngx_http_script.c" ]; then
    # 修复所有new变量声明和使用
    sed -i 's/void    \*new;/void    *new_ptr;/g' src/http/ngx_http_script.c
    sed -i 's/void \*new;/void *new_ptr;/g' src/http/ngx_http_script.c
    sed -i 's/new_ptr = /new_ptr = /g' src/http/ngx_http_script.c
    sed -i 's/if (new ==/if (new_ptr ==/g' src/http/ngx_http_script.c
    sed -i 's/return new;/return new_ptr;/g' src/http/ngx_http_script.c
    echo "✓ ngx_http_script.c修复完成"
fi

# 修复ngx_http_script.c中的'new'变量名
echo "修复ngx_http_script.c中的'new'变量名..."
if [ -f "src/http/ngx_http_script.c" ]; then
    sed -i 's/void \*new;/void *new_ptr;/g' src/http/ngx_http_script.c
    sed -i 's/new = /new_ptr = /g' src/http/ngx_http_script.c
    sed -i 's/return new;/return new_ptr;/g' src/http/ngx_http_script.c
    echo "✓ ngx_http_script.c修复完成"
fi

# 修复ngx_http_userid_filter_module.c中的'new'变量名
echo "修复ngx_http_userid_filter_module.c中的'new'变量名..."
if [ -f "src/http/modules/ngx_http_userid_filter_module.c" ]; then
    # 修复所有new变量声明和使用
    sed -i 's/u_char  \*p, \*new;/u_char  *p, *new_ptr;/g' src/http/modules/ngx_http_userid_filter_module.c
    sed -i 's/new = /new_ptr = /g' src/http/modules/ngx_http_userid_filter_module.c
    sed -i 's/if (new ==/if (new_ptr ==/g' src/http/modules/ngx_http_userid_filter_module.c
    sed -i 's/ngx_cpymem(new,/ngx_cpymem(new_ptr,/g' src/http/modules/ngx_http_userid_filter_module.c
    sed -i 's/domain->data = new;/domain->data = new_ptr;/g' src/http/modules/ngx_http_userid_filter_module.c
    sed -i 's/path->data = new;/path->data = new_ptr;/g' src/http/modules/ngx_http_userid_filter_module.c
    echo "✓ ngx_http_userid_filter_module.c修复完成"
fi

# 修复ngx_http_proxy_module.c中的重复定义
echo "修复ngx_http_proxy_module.c中的重复定义..."
if [ -f "src/http/modules/ngx_http_proxy_module.c" ]; then
    sed -i 's/void \*new;/void *new_ptr;/g' src/http/modules/ngx_http_proxy_module.c
    sed -i 's/new = /new_ptr = /g' src/http/modules/ngx_http_proxy_module.c
    sed -i 's/return new;/return new_ptr;/g' src/http/modules/ngx_http_proxy_module.c
    echo "✓ ngx_http_proxy_module.c修复完成"
fi

# 修复ngx_http_upstream_zone_module.c中的'template'变量名
echo "修复ngx_http_upstream_zone_module.c中的'template'变量名..."
if [ -f "src/http/modules/ngx_http_upstream_zone_module.c" ]; then
    # 修复所有template变量声明和使用
    sed -i 's/\*peer, \*template, \*opeer, \*\*peerp;/*peer, *template_ptr, *opeer, **peerp;/g' src/http/modules/ngx_http_upstream_zone_module.c
    sed -i 's/\*peer, \*template, \*\*peerp;/*peer, *template_ptr, **peerp;/g' src/http/modules/ngx_http_upstream_zone_module.c
    sed -i 's/for (template = resolve; template; template = template->next)/for (template_ptr = resolve; template_ptr; template_ptr = template_ptr->next)/g' src/http/modules/ngx_http_upstream_zone_module.c
    sed -i 's/template = /template_ptr = /g' src/http/modules/ngx_http_upstream_zone_module.c
    sed -i 's/template->/template_ptr->/g' src/http/modules/ngx_http_upstream_zone_module.c
    echo "✓ ngx_http_upstream_zone_module.c修复完成"
fi

# 修复ngx_stream_script.c中的'new'变量名
echo "修复ngx_stream_script.c中的'new'变量名..."
if [ -f "src/stream/ngx_stream_script.c" ]; then
    sed -i 's/void    \*new;/void    *new_ptr;/g' src/stream/ngx_stream_script.c
    sed -i 's/    new = /    new_ptr = /g' src/stream/ngx_stream_script.c
    sed -i 's/if (new == NULL)/if (new_ptr == NULL)/g' src/stream/ngx_stream_script.c
    sed -i 's/return new;/return new_ptr;/g' src/stream/ngx_stream_script.c
    echo "✓ ngx_stream_script.c修复完成"
fi

# 修复ngx_stream_upstream_zone_module.c中的'template'变量名
echo "修复ngx_stream_upstream_zone_module.c中的'template'变量名..."
if [ -f "src/stream/ngx_stream_upstream_zone_module.c" ]; then
    # 修复所有template变量声明和使用
    sed -i 's/\*peer, \*template, \*opeer, \*\*peerp;/*peer, *template_ptr, *opeer, **peerp;/g' src/stream/ngx_stream_upstream_zone_module.c
    sed -i 's/\*peer, \*template, \*\*peerp;/*peer, *template_ptr, **peerp;/g' src/stream/ngx_stream_upstream_zone_module.c
    sed -i 's/for (template = resolve; template; template = template->next)/for (template_ptr = resolve; template_ptr; template_ptr = template_ptr->next)/g' src/stream/ngx_stream_upstream_zone_module.c
    sed -i 's/template = /template_ptr = /g' src/stream/ngx_stream_upstream_zone_module.c
    sed -i 's/template->/template_ptr->/g' src/stream/ngx_stream_upstream_zone_module.c
    echo "✓ ngx_stream_upstream_zone_module.c修复完成"
fi

# 这些模块的重复定义问题将通过禁用相关模块来解决

# 设置编译器环境变量
echo "设置编译器环境变量..."
export CC=/usr/bin/gcc
export CXX=/usr/bin/g++
export CPP=/usr/bin/cpp

# 验证编译器可用性
echo "验证编译器:"
which gcc && gcc --version | head -1
which g++ && g++ --version | head -1
which cc && cc --version | head -1

# 配置编译选项
echo "开始配置nginx..."
./configure \
    --prefix=/usr/local/nginx \
    --sbin-path=/usr/local/nginx/sbin/nginx \
    --conf-path=/etc/nginx/nginx.conf \
    --error-log-path=/var/log/nginx/error.log \
    --http-log-path=/var/log/nginx/access.log \
    --pid-path=/var/run/nginx.pid \
    --lock-path=/var/run/nginx.lock \
    --http-client-body-temp-path=/var/cache/nginx/client_temp \
    --http-proxy-temp-path=/var/cache/nginx/proxy_temp \
    --http-fastcgi-temp-path=/var/cache/nginx/fastcgi_temp \
    --http-uwsgi-temp-path=/var/cache/nginx/uwsgi_temp \
    --http-scgi-temp-path=/var/cache/nginx/scgi_temp \
    --user=nginx \
    --group=nginx \
    --with-threads \
    --with-file-aio \
    --without-http_rewrite_module \
    --without-http_gzip_module \
    --without-http_proxy_module \
    --without-http_fastcgi_module \
    --without-http_uwsgi_module \
    --without-http_scgi_module \
    --with-http_realip_module \
    --with-http_auth_request_module \
    --with-stream \
    --with-http_stub_status_module \
    --with-cc-opt='-O2 -g -pipe -Wp,-D_FORTIFY_SOURCE=2 -fexceptions -fstack-protector-strong --param=ssp-buffer-size=4 -grecord-gcc-switches -m64 -mtune=generic -fpermissive -w'

# 编译
echo "开始编译nginx..."
make -j$(nproc)

# 安装
echo "安装nginx..."
make install

echo "✓ nginx编译安装完成"

echo ""
echo "=== 创建运行目录 ==="
mkdir -p /var/log/nginx /var/cache/nginx /etc/nginx/conf.d
chown -R nginx:nginx /var/log/nginx /var/cache/nginx

echo "✓ nginx安装和配置完成"
echo "nginx版本: $NGINX_VERSION"
echo "安装路径: /usr/local/nginx"
echo "配置文件: /etc/nginx/nginx.conf"
