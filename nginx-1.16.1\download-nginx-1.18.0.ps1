# PowerShell脚本：下载nginx 1.18.0

Write-Host "下载nginx 1.18.0..."

$url = "http://nginx.org/download/nginx-1.18.0.tar.gz"
$outputPath = "packages\nginx-1.18.0.tar.gz"

try {
    Invoke-WebRequest -Uri $url -OutFile $outputPath -ErrorAction Stop
    Write-Host "✓ nginx-1.18.0.tar.gz 下载成功" -ForegroundColor Green
}
catch {
    Write-Host "❌ nginx-1.18.0.tar.gz 下载失败: $($_.Exception.Message)" -ForegroundColor Red
    
    # 尝试备用源
    $altUrls = @(
        "https://nginx.org/download/nginx-1.18.0.tar.gz",
        "http://nginx.org/download/nginx-1.18.0.tar.gz"
    )
    
    $downloaded = $false
    foreach ($altUrl in $altUrls) {
        try {
            Write-Host "尝试备用源: $altUrl"
            Invoke-WebRequest -Uri $altUrl -OutFile $outputPath -ErrorAction Stop
            Write-Host "✓ nginx-1.18.0.tar.gz 从备用源下载成功" -ForegroundColor Green
            $downloaded = $true
            break
        }
        catch {
            Write-Host "备用源也失败: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
    
    if (!$downloaded) {
        Write-Host "❌ nginx-1.18.0.tar.gz 从所有源下载失败" -ForegroundColor Red
    }
}

Write-Host "下载完成！"
