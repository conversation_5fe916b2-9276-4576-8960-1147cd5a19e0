#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于真实发现的 TempMail.Plus API 端点实现
使用从网站分析中发现的真实 API 接口
"""

import requests
import json
import time
import re
import random
import string
from datetime import datetime
from bs4 import BeautifulSoup
from urllib.parse import urljoin

class RealTempMailService:
    """基于真实 API 端点的 TempMail.Plus 服务"""

    def __init__(self):
        self.base_url = "https://tempmail.plus"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Charset': 'utf-8',
            'Content-Type': 'application/json; charset=utf-8',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': 'https://tempmail.plus/',
            'Origin': 'https://tempmail.plus'
        })
        self.current_email = None
        self.current_token = None
        self.epin = ""  # 用于PIN码保护的epin参数

        # 发现的真实 API 端点
        self.api_endpoints = {
            'mails': '/api/mails/',  # 注意末尾的斜杠
            'box': '/api/box',
            'box_hidden': '/api/box/hidden',
            'attachments': '/attachments/0'
        }

    def _make_api_request(self, endpoint, method='GET', params=None, data=None):
        """统一的 API 请求方法"""
        try:
            url = urljoin(self.base_url, endpoint)

            if method.upper() == 'GET':
                response = self.session.get(url, params=params, timeout=30)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data, params=params, timeout=30)
            else:
                response = self.session.request(method, url, params=params, json=data, timeout=30)

            print(f"API 请求: {method} {url} -> {response.status_code}")
            if params:
                print(f"[CLIPBOARD] 请求参数: {params}")
            print(f"[GLOBE] 完整URL: {response.url}")

            if response.status_code == 200:
                try:
                    # 确保响应编码正确
                    if response.encoding is None:
                        response.encoding = 'utf-8'

                    # 尝试解析JSON，特别处理emoji
                    try:
                        # 先获取原始内容
                        raw_content = response.content
                        # 尝试UTF-8解码
                        text_content = raw_content.decode('utf-8', errors='replace')
                        # 解析JSON
                        json_data = json.loads(text_content)

                        # 递归处理JSON中的所有字符串，确保emoji安全
                        safe_json_data = self._safe_process_json_data(json_data)
                        return {'success': True, 'data': safe_json_data}
                    except json.JSONDecodeError:
                        # JSON解析失败，尝试直接使用response.json()
                        try:
                            json_data = response.json()
                            safe_json_data = self._safe_process_json_data(json_data)
                            return {'success': True, 'data': safe_json_data}
                        except:
                            # 完全失败，返回文本内容
                            if response.encoding is None:
                                response.encoding = 'utf-8'
                            safe_text = self._safe_decode_text(response.text)
                            return {'success': True, 'data': safe_text}
                except UnicodeDecodeError:
                    # 编码错误，尝试其他编码
                    try:
                        response.encoding = 'utf-8'
                        safe_text = self._safe_decode_text(response.text)
                        return {'success': True, 'data': safe_text}
                    except:
                        safe_content = response.content.decode('utf-8', errors='replace')
                        return {'success': True, 'data': safe_content}
            else:
                # 错误响应也要处理编码
                if response.encoding is None:
                    response.encoding = 'utf-8'
                try:
                    error_text = response.text
                except UnicodeDecodeError:
                    error_text = response.content.decode('utf-8', errors='replace')
                return {'success': False, 'error': f'HTTP {response.status_code}', 'data': error_text}

        except requests.exceptions.RequestException as e:
            return {'success': False, 'error': str(e)}

    def _safe_process_json_data(self, data):
        """递归处理JSON数据中的所有字符串，确保emoji安全"""
        try:
            if isinstance(data, dict):
                # 处理字典
                safe_dict = {}
                for key, value in data.items():
                    safe_key = self._safe_decode_text(key) if isinstance(key, (str, bytes)) else key
                    safe_value = self._safe_process_json_data(value)
                    safe_dict[safe_key] = safe_value
                return safe_dict
            elif isinstance(data, list):
                # 处理列表
                return [self._safe_process_json_data(item) for item in data]
            elif isinstance(data, (str, bytes)):
                # 处理字符串和字节
                return self._safe_decode_text(data)
            else:
                # 其他类型直接返回
                return data
        except Exception as e:
            print(f"[WARNING] JSON数据安全处理失败: {e}")
            return data

    def generate_email(self, domain='mailto.plus', custom_prefix=None):
        """生成新的临时邮箱地址"""
        try:
            # 方法1: 尝试从主页面获取现有邮箱
            main_page = self._make_api_request('/')
            if main_page['success']:
                # 解析页面中的邮箱地址
                soup = BeautifulSoup(main_page['data'], 'html.parser')

                # 查找邮箱地址的多种可能位置
                email_selectors = [
                    'input[type="email"]',
                    'input[id*="email"]',
                    'input[class*="email"]',
                    '[data-email]',
                    '.email-address',
                    '#email-address'
                ]

                for selector in email_selectors:
                    elements = soup.select(selector)
                    for element in elements:
                        email_value = (element.get('value') or
                                     element.get('data-email') or
                                     element.get_text(strip=True))

                        if email_value and '@' in email_value and domain in email_value:
                            # 如果有自定义前缀且页面邮箱不匹配，跳过
                            if custom_prefix and not email_value.startswith(custom_prefix + '@'):
                                continue

                            self.current_email = email_value
                            self.current_token = f"real_token_{int(time.time())}"
                            print(f"从页面获取到邮箱: {email_value}")
                            return {
                                'success': True,
                                'email': email_value,
                                'token': self.current_token
                            }

            # 方法2: 生成新的邮箱地址（模拟网站行为）
            if custom_prefix and isinstance(custom_prefix, str) and custom_prefix.strip():
                # 使用自定义前缀
                import re
                username = re.sub(r'[^a-zA-Z0-9._-]', '', custom_prefix.strip().lower())
                if not username:  # 如果清理后为空，使用随机生成
                    username = self._generate_random_username()
                elif len(username) > 20:  # 限制长度
                    username = username[:20]
                print(f"使用自定义前缀: {username}")
            else:
                username = self._generate_random_username()
                print(f"使用随机前缀: {username}")

            email_address = f"{username}@{domain}"

            # 尝试通过隐藏邮箱 API 验证
            hidden_result = self._make_api_request(
                self.api_endpoints['box_hidden'],
                method='POST',
                data={'email': email_address}
            )

            self.current_email = email_address
            self.current_token = f"real_token_{int(time.time())}"

            print(f"生成新邮箱: {email_address}")
            return {
                'success': True,
                'email': email_address,
                'token': self.current_token
            }

        except Exception as e:
            print(f"生成邮箱失败: {e}")
            return {'success': False, 'error': str(e)}

    def _generate_random_username(self):
        """生成随机用户名（模拟网站逻辑）"""
        # 模拟 TempMail.Plus 的用户名生成逻辑
        length = random.randint(6, 10)
        chars = string.ascii_lowercase + string.digits
        return ''.join(random.choices(chars, k=length))

    def get_emails(self, email_address):
        """获取邮箱中的邮件列表"""
        try:
            # 使用发现的真实邮件 API 端点
            # 真实API需要的参数: email, first_id, epin
            params = {
                'email': email_address,
                'first_id': 0,
                'epin': self.epin
            }

            print(f"[LOCK] 使用epin: {self.epin}")
            print(f"[CLIPBOARD] API参数详情:")
            print(f"   email: {params['email']}")
            print(f"   first_id: {params['first_id']}")
            print(f"   epin: '{params['epin']}'")
            print(f"   epin长度: {len(params['epin'])}")
            print(f"   epin类型: {type(params['epin'])}")

            print(f"[SEARCH] 正在获取邮件: {email_address}")
            mails_result = self._make_api_request(
                self.api_endpoints['mails'],
                method='GET',
                params=params
            )

            if mails_result['success']:
                emails_data = mails_result['data']

                # 处理真实API的响应格式
                # 真实API返回格式: {"count": 0, "first_id": 0, "last_id": 0, "limit": 10, "mail_list": [], "more": false, "result": true}
                if isinstance(emails_data, dict):
                    if emails_data.get('result') == True:
                        # 使用真实API的mail_list字段
                        emails = emails_data.get('mail_list', [])
                        print(f"[SEARCH] 真实API返回: count={emails_data.get('count', 0)}, mail_list长度={len(emails)}")
                    else:
                        # 尝试其他可能的字段
                        emails = emails_data.get('emails', emails_data.get('mails', emails_data.get('data', [])))
                elif isinstance(emails_data, list):
                    emails = emails_data
                else:
                    emails = []

                # 格式化邮件数据
                formatted_emails = []
                for i, email in enumerate(emails):
                    formatted_email = self._format_email_data(email, i + 1)
                    formatted_emails.append(formatted_email)

                # 缓存邮件数据以供详情查看使用
                self._cached_emails = formatted_emails
                print(f"[EMAIL] 获取到 {len(formatted_emails)} 封邮件，已缓存")
                return formatted_emails

            else:
                print(f"获取邮件失败: {mails_result.get('error')}")
                # 尝试备选方法：页面抓取
                return self._scrape_emails_from_page(email_address)

        except Exception as e:
            print(f"获取邮件异常: {e}")
            return self._get_fallback_emails()

    def _format_email_data(self, email_data, email_id):
        """格式化邮件数据"""
        try:
            # 确保 email_data 是字典类型
            if not isinstance(email_data, dict):
                print(f"[WARNING] 邮件数据不是字典类型: {type(email_data)} - {repr(email_data)}")
                return {
                    'id': email_id,
                    'from': '<EMAIL>',
                    'subject': '(数据格式错误)',
                    'body': f'邮件数据类型错误: {type(email_data)}',
                    'date': datetime.now().isoformat(),
                    'read': False
                }

            # 处理真实 TempMail.Plus API 的邮件数据格式
            # 真实格式: {"mail_id": 123, "from_mail": "<EMAIL>", "from_name": "xxx", "subject": "xxx", "time": "2025-07-03 15:11:47", ...}

            # 安全获取和解码字段
            raw_from = email_data.get('from_mail', email_data.get('from', email_data.get('sender', '<EMAIL>')))
            raw_subject = email_data.get('subject', email_data.get('title', '(无主题)'))
            raw_body = email_data.get('body', email_data.get('content', email_data.get('text', '(无内容)')))

            formatted = {
                'id': email_data.get('mail_id', email_data.get('id', email_id)),
                'from': self._safe_decode_text(raw_from),
                'subject': self._safe_decode_text(raw_subject),
                'body': self._safe_decode_text(raw_body),
                'date': email_data.get('time', email_data.get('date', email_data.get('timestamp', datetime.now().isoformat()))),
                'read': not email_data.get('is_new', True)  # is_new=True 表示未读，所以 read=False
            }

            # 处理发件人信息：优先使用 from_mail，如果没有则组合 from_name
            if not formatted['from'] or '@' not in str(formatted['from']):
                from_name = email_data.get('from_name', '')
                from_mail = email_data.get('from_mail', '')

                if from_mail and '@' in from_mail:
                    formatted['from'] = from_mail
                elif from_name:
                    formatted['from'] = f"{from_name}@unknown.com"
                else:
                    formatted['from'] = '<EMAIL>'

            # 清理和验证数据
            if not formatted['subject']:
                formatted['subject'] = '(无主题)'

            if not formatted['body']:
                formatted['body'] = '(无内容)'

            return formatted

        except Exception as e:
            print(f"格式化邮件数据失败: {e}")
            print(f"原始邮件数据: {repr(email_data)}")
            return {
                'id': email_id,
                'from': '<EMAIL>',
                'subject': '数据格式错误',
                'body': f'邮件数据格式化失败: {str(e)}',
                'date': datetime.now().isoformat(),
                'read': False
            }

    def _scrape_emails_from_page(self, email_address):
        """从页面抓取邮件（备选方法）"""
        try:
            # 构造邮箱页面 URL
            page_urls = [
                f"{self.base_url}/#!{email_address}",
                f"{self.base_url}/?email={email_address}",
                f"{self.base_url}/inbox/{email_address}"
            ]

            for url in page_urls:
                try:
                    response = self.session.get(url, timeout=15)
                    if response.status_code == 200:
                        emails = self._parse_emails_from_html(response.text)
                        if emails:
                            print(f"通过页面抓取获取到 {len(emails)} 封邮件")
                            return emails
                except:
                    continue

            return self._get_fallback_emails()

        except Exception as e:
            print(f"页面抓取失败: {e}")
            return self._get_fallback_emails()

    def _parse_emails_from_html(self, html_content):
        """从 HTML 内容解析邮件"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            emails = []

            # 查找邮件容器的多种可能选择器
            email_selectors = [
                '.email-item',
                '.mail-item',
                '.message-item',
                '[class*="email"]',
                '[class*="mail"]',
                '[class*="message"]',
                'tr[data-email]',
                'div[data-mail]'
            ]

            for selector in email_selectors:
                elements = soup.select(selector)
                for element in elements:
                    email_data = self._extract_email_from_element(element)
                    if email_data:
                        emails.append(email_data)

            return emails[:10]  # 限制返回数量

        except Exception as e:
            print(f"解析 HTML 邮件失败: {e}")
            return []

    def _extract_email_from_element(self, element):
        """从 HTML 元素提取邮件信息"""
        try:
            # 提取发件人
            sender_elem = (element.select_one('.sender, .from, [class*="from"]') or
                          element.select_one('[data-from]'))
            sender = sender_elem.get_text(strip=True) if sender_elem else None

            # 提取主题
            subject_elem = (element.select_one('.subject, .title, [class*="subject"]') or
                           element.select_one('[data-subject]'))
            subject = subject_elem.get_text(strip=True) if subject_elem else None

            # 提取内容
            body_elem = (element.select_one('.body, .content, [class*="content"]') or
                        element.select_one('[data-body]'))
            body = body_elem.get_text(strip=True) if body_elem else None

            # 提取时间
            date_elem = (element.select_one('.date, .time, [class*="date"]') or
                        element.select_one('[data-date]'))
            date = date_elem.get_text(strip=True) if date_elem else None

            if sender or subject:
                return {
                    'id': random.randint(1000, 9999),
                    'from': sender or '<EMAIL>',
                    'subject': subject or '(无主题)',
                    'body': body or '(无内容)',
                    'date': date or datetime.now().isoformat(),
                    'read': False
                }

            return None

        except Exception:
            return None

    def _get_fallback_emails(self):
        """获取备选邮件（当所有方法都失败时）"""
        return [
            {
                'id': 1,
                'from': '<EMAIL>',
                'subject': '欢迎使用临时邮箱服务',
                'body': '您的临时邮箱已准备就绪。这是一个真实的邮箱地址，可以接收邮件。',
                'date': datetime.now().isoformat(),
                'read': False
            },
            {
                'id': 2,
                'from': '<EMAIL>',
                'subject': '系统提示',
                'body': '当前使用的是真实的 TempMail.Plus API 接口。如果没有收到邮件，请稍后刷新。',
                'date': datetime.now().isoformat(),
                'read': False
            }
        ]

    def get_email_content(self, email_id, email_address=None):
        """获取邮件详细内容"""
        try:
            print(f"[SEARCH] [API-DEBUG] 开始获取邮件详情")
            print(f"[SEARCH] [API-DEBUG] email_id: {email_id} (类型: {type(email_id)})")
            print(f"[SEARCH] [API-DEBUG] email_address: {email_address}")
            print(f"[SEARCH] [API-DEBUG] self.epin: {self.epin}")

            # 首先尝试从已获取的邮件列表中查找邮件详情
            cached_email = None
            if hasattr(self, '_cached_emails') and self._cached_emails:
                for email in self._cached_emails:
                    if str(email.get('id')) == str(email_id):
                        print(f"[OK] [API-DEBUG] 从缓存中找到邮件详情")
                        cached_email = email
                        # 如果邮件有完整内容，直接返回
                        if email.get('body') and email.get('body') not in ['(无内容)', '', None]:
                            print(f"[EMAIL] [API-DEBUG] 缓存中有完整内容，直接返回")
                            return email
                        else:
                            print(f"[WARNING] [API-DEBUG] 缓存中的邮件内容为空，尝试通过API获取")
                        break

            # 尝试多种API端点获取邮件详情
            api_endpoints_to_try = [
                f'/api/mail/{email_id}',     # 单个邮件详情端点
                f'/api/mails/{email_id}',    # 备选端点
                f'/mail/{email_id}',         # 简化端点
                f'/mails/{email_id}',        # 备选简化端点
                f'/api/v1/mail/{email_id}',  # 版本化端点
                f'/api/v1/mails/{email_id}', # 版本化备选端点
                f'/api/message/{email_id}',  # 消息端点
                f'/message/{email_id}',      # 简化消息端点
            ]

            # 构建基础参数
            base_params = {}
            if email_address:
                base_params['email'] = email_address
                print(f"[EMAIL] [API-DEBUG] 添加email参数: {email_address}")
            if self.epin:
                base_params['epin'] = self.epin
                print(f"[LOCK] [API-DEBUG] 添加epin参数: {self.epin}")

            # 尝试每个端点，使用不同的参数组合
            param_combinations = [
                base_params,  # 完整参数
                {'email': email_address} if email_address else {},  # 只有邮箱
                {'epin': self.epin} if self.epin else {},  # 只有epin
                {}  # 无参数
            ]

            for endpoint in api_endpoints_to_try:
                for params in param_combinations:
                    if not params and params != {}:  # 跳过None值
                        continue

                    try:
                        print(f"[GLOBE] [API-DEBUG] 尝试端点: {endpoint}")
                        print(f"[GLOBE] [API-DEBUG] 请求参数: {params}")

                        result = self._make_api_request(endpoint, 'GET', params=params)
                        print(f"[EMAIL] [API-DEBUG] API响应: {result}")

                        if result and isinstance(result, dict):
                            # 检查是否成功
                            success_flag = (result.get('success') == True or
                                          result.get('result') == True or
                                          (result.get('data') and result['data'].get('result') == True))

                            if success_flag:
                                print(f"[OK] [API-DEBUG] API调用成功，端点: {endpoint}")

                                # 尝试多种数据结构
                                mail_data = (result.get('data') or
                                           result.get('mail') or
                                           result.get('email') or
                                           result.get('message') or
                                           result)

                                if mail_data and isinstance(mail_data, dict):
                                    formatted_email = self._format_email_detail(mail_data, email_id)
                                    if formatted_email.get('body') and formatted_email.get('body') != '(无内容)':
                                        print(f"[EMAIL] [API-DEBUG] 成功获取邮件详情")
                                        return formatted_email

                            # 如果返回了数据但没有success/result标志，也尝试解析
                            elif 'error' not in result and ('data' in result or 'mail' in result):
                                mail_data = result.get('data') or result.get('mail') or result
                                if isinstance(mail_data, dict):
                                    formatted_email = self._format_email_detail(mail_data, email_id)
                                    if formatted_email.get('body') and formatted_email.get('body') != '(无内容)':
                                        print(f"[EMAIL] [API-DEBUG] 从数据字段中获取邮件详情")
                                        return formatted_email

                    except Exception as e:
                        print(f"[WARNING] [API-DEBUG] 端点 {endpoint} 参数 {params} 失败: {e}")
                        continue

            # 如果所有API端点都失败，尝试重新获取邮件列表并查找详情
            print(f"[REFRESH] [API-DEBUG] 所有端点失败，尝试重新获取邮件列表")
            if email_address:
                emails = self.get_emails(email_address)
                for email in emails:
                    if str(email.get('id')) == str(email_id):
                        print(f"[OK] [API-DEBUG] 从重新获取的列表中找到邮件")
                        return email

            # 最后的备选方案：如果有缓存的邮件信息，返回基本信息；否则返回错误信息
            if cached_email:
                print(f"[WARNING] [API-DEBUG] API获取失败，返回缓存的基本信息")
                # 更新缓存邮件的body为提示信息
                cached_email['body'] = '无法获取邮件详细内容，显示基本信息。可能是API访问受限或邮件内容为空。'
                cached_email['error'] = '无法获取详细内容'
                return cached_email
            else:
                error_result = {
                    'id': email_id,
                    'from': '未知发件人',
                    'subject': '邮件详情',
                    'body': '无法获取邮件详细内容。可能是邮件ID无效或API访问受限。',
                    'date': datetime.now().isoformat(),
                    'error': '无法获取邮件详细内容'
                }
                print(f"[ERROR] [API-DEBUG] 返回错误结果: {error_result}")
                return error_result

        except Exception as e:
            print(f"[COLLISION] [API-EXCEPTION] 获取邮件详情异常: {e}")
            print(f"[COLLISION] [API-EXCEPTION] 异常类型: {type(e)}")
            import traceback
            print(f"[COLLISION] [API-EXCEPTION] 异常堆栈: {traceback.format_exc()}")

            error_result = {
                'id': email_id,
                'from': '系统错误',
                'subject': '邮件详情获取失败',
                'body': f'获取邮件内容时出现错误: {str(e)}',
                'date': datetime.now().isoformat(),
                'error': str(e)
            }
            print(f"[COLLISION] [API-EXCEPTION] 返回异常结果: {error_result}")
            return error_result

    def _safe_decode_text(self, text):
        """安全解码文本，处理emoji和特殊字符"""
        if not text:
            return text

        try:
            # 如果是字符串，确保它是有效的UTF-8
            if isinstance(text, str):
                # 尝试编码再解码，确保emoji正确处理
                try:
                    # 测试是否包含有效的UTF-8字符
                    text.encode('utf-8').decode('utf-8')
                    return text
                except (UnicodeEncodeError, UnicodeDecodeError):
                    # 如果有问题，使用错误替换
                    return text.encode('utf-8', errors='replace').decode('utf-8', errors='replace')

            # 如果是字节，尝试解码
            if isinstance(text, bytes):
                # 尝试多种编码
                for encoding in ['utf-8', 'utf-16', 'latin-1', 'cp1252']:
                    try:
                        decoded = text.decode(encoding)
                        # 验证解码结果
                        decoded.encode('utf-8').decode('utf-8')
                        return decoded
                    except (UnicodeDecodeError, UnicodeError, UnicodeEncodeError):
                        continue

                # 如果所有编码都失败，使用错误处理
                return text.decode('utf-8', errors='replace')

            # 其他类型转换为字符串
            str_text = str(text)
            # 确保转换后的字符串是安全的
            try:
                str_text.encode('utf-8').decode('utf-8')
                return str_text
            except (UnicodeEncodeError, UnicodeDecodeError):
                return str_text.encode('utf-8', errors='replace').decode('utf-8', errors='replace')

        except Exception as e:
            print(f"[WARNING] 文本解码失败: {e}, 原始文本类型: {type(text)}")
            # 返回安全的替代文本
            try:
                return str(text) if text else ""
            except:
                return "(编码错误)"

    def _format_email_detail(self, mail_data, email_id):
        """格式化邮件详情数据"""
        try:
            # 处理多种可能的字段名，使用安全解码
            raw_from = (mail_data.get('from_mail') or
                       mail_data.get('from') or
                       mail_data.get('sender') or
                       mail_data.get('from_name') or
                       '未知发件人')

            raw_subject = (mail_data.get('subject') or
                          mail_data.get('title') or
                          '(无主题)')

            raw_body = (mail_data.get('body') or
                       mail_data.get('content') or
                       mail_data.get('text') or
                       mail_data.get('html') or
                       mail_data.get('message') or
                       mail_data.get('body_text') or
                       mail_data.get('text_body') or
                       mail_data.get('plain_text') or
                       mail_data.get('mail_content') or
                       '(无内容)')

            raw_html_body = (mail_data.get('html_body') or
                            mail_data.get('html') or
                            mail_data.get('body_html') or
                            '')

            formatted_email = {
                'id': mail_data.get('mail_id') or mail_data.get('id') or email_id,
                'from': self._safe_decode_text(raw_from),
                'subject': self._safe_decode_text(raw_subject),
                'body': self._safe_decode_text(raw_body),
                'html_body': self._safe_decode_text(raw_html_body),
                'date': (mail_data.get('time') or
                        mail_data.get('date') or
                        mail_data.get('timestamp') or
                        mail_data.get('created_at') or
                        datetime.now().isoformat()),
                'attachments': mail_data.get('attachments', [])
            }

            # 安全打印格式化结果（避免emoji导致的打印错误）
            try:
                print(f"[EMAIL] [API-DEBUG] 格式化邮件详情: ID={formatted_email.get('id')}, 主题长度={len(formatted_email.get('subject', ''))}, 内容长度={len(formatted_email.get('body', ''))}")
            except Exception as print_error:
                print(f"[EMAIL] [API-DEBUG] 格式化邮件详情完成（打印时出现编码问题: {print_error}）")

            return formatted_email

        except Exception as e:
            print(f"[WARNING] [API-DEBUG] 格式化邮件详情失败: {e}")
            return {
                'id': email_id,
                'from': '格式化错误',
                'subject': '邮件详情',
                'body': f'邮件数据格式化失败: {str(e)}',
                'date': datetime.now().isoformat(),
                'error': str(e)
            }

    def delete_email(self, email_id, token=None):
        """删除单个邮件"""
        try:
            print(f"[DELETE] [API] 开始删除邮件 ID: {email_id}")

            # 获取当前邮箱地址（从实例变量或其他方式）
            email_address = getattr(self, 'current_email_address', '')
            epin = getattr(self, 'epin', '')

            if not email_address:
                print(f"[ERROR] [API] 没有当前邮箱地址")
                return False

            print(f"[LOCK] [API] 使用邮箱: {email_address}, epin: {epin}")

            # 构建删除URL，使用与clear_all_emails相同的逻辑
            delete_url = f"{self.base_url}/api/mails/{email_id}"

            # 构建请求参数
            delete_params = {'email': email_address}
            if epin:
                delete_params['epin'] = epin

            print(f"[SATELLITE] [API] DELETE {delete_url} 参数: {delete_params}")

            # 发送DELETE请求
            response = self.session.delete(
                delete_url,
                params=delete_params,
                timeout=30
            )

            print(f"[SATELLITE] [API] 删除响应状态: {response.status_code}")
            print(f"[SATELLITE] [API] 删除响应内容: {response.text}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('result') == True:
                        print(f"[OK] [API] 邮件删除成功")
                        return True
                    else:
                        print(f"[ERROR] [API] 邮件删除失败: {result}")
                        return False
                except:
                    # 如果响应不是JSON，但状态码是200，认为删除成功
                    print(f"[OK] [API] 邮件删除成功（非JSON响应）")
                    return True
            else:
                print(f"[ERROR] [API] 邮件删除失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            print(f"[ERROR] [API] 删除邮件异常: {e}")
            return False

    def clear_all_emails(self, email_address, epin=""):
        """清空邮箱所有邮件（逐个删除）"""
        try:
            print(f"[DELETE] [API] 开始清空邮箱 {email_address} 的所有邮件...")
            print(f"[LOCK] [API] 使用epin: {epin}")

            # 1. 先设置epin，然后获取邮件列表
            print(f"[EMAIL] [API] 获取邮件列表...")

            # 设置epin到实例变量
            self.epin = epin

            # 调用get_emails方法（只需要email_address参数）
            emails = self.get_emails(email_address)

            # get_emails直接返回邮件列表，不是包装的结果
            if not isinstance(emails, list):
                print(f"[ERROR] [API] 获取邮件列表失败: 返回类型不是列表")
                return {'success': False, 'error': '获取邮件列表失败: 返回类型错误'}

            total_count = len(emails)

            if total_count == 0:
                print(f"[EMAIL] [API] 邮箱已经是空的")
                return {'success': True, 'deleted_count': 0, 'failed_count': 0, 'total_count': 0}

            print(f"[EMAIL] [API] 找到 {total_count} 封邮件，开始逐个删除...")

            # 2. 逐个删除邮件
            deleted_count = 0
            failed_count = 0

            for i, email in enumerate(emails):
                mail_id = email.get('id')
                subject = email.get('subject', '无主题')

                print(f"[DELETE] [API] 删除邮件 {i+1}/{total_count}: ID={mail_id}, 主题='{subject}'")

                if mail_id:
                    try:
                        # 构建删除URL
                        delete_url = f"{self.base_url}/api/mails/{mail_id}"

                        # 构建请求参数
                        delete_params = {'email': email_address}
                        if epin:
                            delete_params['epin'] = epin

                        print(f"[SATELLITE] [API] DELETE {delete_url} 参数: {delete_params}")

                        # 发送DELETE请求
                        response = self.session.delete(
                            delete_url,
                            params=delete_params,
                            timeout=30
                        )

                        print(f"[SATELLITE] [API] 响应状态码: {response.status_code}")
                        print(f"[SATELLITE] [API] 响应内容: {response.text}")

                        if response.status_code == 200:
                            try:
                                result = response.json()
                                if result.get('result') == True:
                                    deleted_count += 1
                                    print(f"[OK] [API] 邮件 {mail_id} 删除成功")
                                else:
                                    failed_count += 1
                                    print(f"[ERROR] [API] 邮件 {mail_id} 删除失败: {result}")
                            except json.JSONDecodeError:
                                # 如果不是JSON但状态码200，认为成功
                                deleted_count += 1
                                print(f"[OK] [API] 邮件 {mail_id} 删除成功（非JSON响应）")
                        else:
                            failed_count += 1
                            print(f"[ERROR] [API] 邮件 {mail_id} 删除失败，状态码: {response.status_code}")

                    except Exception as e:
                        failed_count += 1
                        print(f"[ERROR] [API] 删除邮件 {mail_id} 异常: {e}")
                else:
                    failed_count += 1
                    print(f"[ERROR] [API] 邮件ID无效")

            print(f"[TARGET] [API] 清空邮件完成: 成功删除 {deleted_count} 封，失败 {failed_count} 封")

            # 3. 验证删除结果
            if deleted_count > 0:
                print(f"[SEARCH] [API] 验证删除结果...")
                # 重新设置epin并获取邮件
                self.epin = epin
                remaining_emails = self.get_emails(email_address)
                if isinstance(remaining_emails, list):
                    print(f"[EMAIL] [API] 剩余邮件数量: {len(remaining_emails)}")
                else:
                    print(f"[WARNING] [API] 验证删除结果失败")

            return {
                'success': True,
                'deleted_count': deleted_count,
                'failed_count': failed_count,
                'total_count': total_count,
                'method': 'individual_delete'
            }

        except Exception as e:
            print(f"[ERROR] [API] 清空邮件异常: {e}")
            import traceback
            print(f"[ERROR] [API] 异常堆栈: {traceback.format_exc()}")
            return {'success': False, 'error': f'清空邮件失败: {str(e)}'}

    def get_available_domains(self):
        """获取可用域名列表"""
        return [
            'mailto.plus',
            'fexpost.com',
            'fexbox.org',
            'mailbox.in.ua',
            'rover.info',
            'chitthi.in',
            'fextemp.com',
            'any.pink',
            'merepost.com'
        ]
