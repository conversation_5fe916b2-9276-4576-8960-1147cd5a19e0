# 简化版GCC下载脚本
Write-Host "下载完整的GCC工具链..."

# 创建目录
$dir = "centos7-gcc-complete"
New-Item -ItemType Directory -Path $dir -Force | Out-Null

# 阿里云镜像源
$baseUrl = "https://mirrors.aliyun.com/centos/7.9.2009/os/x86_64/Packages"

# 核心GCC包列表
$packages = @(
    "gcc-4.8.5-44.el7.x86_64.rpm",
    "gcc-c++-4.8.5-44.el7.x86_64.rpm",
    "libmpc-1.0.1-3.el7.x86_64.rpm",
    "mpfr-3.1.1-4.el7.x86_64.rpm", 
    "gmp-6.0.0-15.el7.x86_64.rpm",
    "libstdc++-4.8.5-44.el7.x86_64.rpm",
    "libstdc++-devel-4.8.5-44.el7.x86_64.rpm",
    "libgcc-4.8.5-44.el7.x86_64.rpm",
    "glibc-devel-2.17-326.el7_9.x86_64.rpm",
    "glibc-headers-2.17-326.el7_9.x86_64.rpm",
    "kernel-headers-3.10.0-1160.102.1.el7.x86_64.rpm",
    "make-3.82-24.el7.x86_64.rpm",
    "binutils-2.27-44.base.el7.x86_64.rpm",
    "openssl-1.0.2k-25.el7_9.x86_64.rpm",
    "openssl-devel-1.0.2k-25.el7_9.x86_64.rpm",
    "openssl-libs-1.0.2k-25.el7_9.x86_64.rpm",
    "krb5-devel-1.15.1-55.el7_9.x86_64.rpm",
    "keyutils-libs-devel-1.5.8-3.el7.x86_64.rpm",
    "libcom_err-devel-1.42.9-19.el7.x86_64.rpm",
    "libselinux-devel-2.5-15.el7.x86_64.rpm",
    "libsepol-devel-2.5-10.el7.x86_64.rpm",
    "libverto-devel-0.2.5-4.el7.x86_64.rpm",
    "pcre-8.32-17.el7.x86_64.rpm",
    "pcre-devel-8.32-17.el7.x86_64.rpm",
    "zlib-1.2.7-18.el7.x86_64.rpm",
    "zlib-devel-1.2.7-18.el7.x86_64.rpm",
    "GeoIP-1.5.0-14.el7.x86_64.rpm",
    "GeoIP-devel-1.5.0-14.el7.x86_64.rpm"
)

$success = 0
$failed = @()

foreach ($pkg in $packages) {
    Write-Host "下载: $pkg"
    $url = "$baseUrl/$pkg"
    $output = "$dir\$pkg"
    
    if (Test-Path $output) {
        Write-Host "  已存在" -ForegroundColor Yellow
        $success++
        continue
    }
    
    try {
        Invoke-WebRequest -Uri $url -OutFile $output
        Write-Host "  成功" -ForegroundColor Green
        $success++
    }
    catch {
        Write-Host "  失败" -ForegroundColor Red
        $failed += $pkg
    }
}

Write-Host ""
Write-Host "下载完成: 成功 $success, 失败 $($failed.Count)"
if ($failed.Count -gt 0) {
    Write-Host "失败的包:"
    $failed | ForEach-Object { Write-Host "  $_" }
}

Write-Host ""
Write-Host "接下来执行:"
Write-Host "Rename-Item centos7-rpms centos7-rpms-backup"
Write-Host "Copy-Item $dir centos7-rpms -Recurse"
