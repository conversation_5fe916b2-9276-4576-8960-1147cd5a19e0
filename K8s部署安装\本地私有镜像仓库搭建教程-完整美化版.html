<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地私有镜像仓库搭建完整教程</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 13px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 机器标识样式 */
        .machine-tag {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            margin: 0 8px 12px 0;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .machine-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .machine-tag:hover::before {
            left: 100%;
        }

        .machine-registry {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .machine-client {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
        }

        .machine-docker {
            background: linear-gradient(135deg, #45b7d1 0%, #96c93d 100%);
            color: white;
        }



        .machine-all {
            background: linear-gradient(135deg, #a55eea 0%, #8e44ad 100%);
            color: white;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: var(--light-surface);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        th,
        td {
            padding: 18px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 下载链接 */
        .download-link {
            background: linear-gradient(135deg, var(--success-color) 0%, #38a169 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            display: inline-flex;
            align-items: center;
            margin: 8px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            font-size: 14px;
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
        }

        .download-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .download-link:hover::before {
            left: 100%;
        }

        .download-link:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
        }

        .download-link i {
            margin-right: 8px;
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-decoration: none;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            z-index: 999;
            font-size: 20px;
        }

        .back-to-top:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .content-wrapper {
                padding: 30px;
            }

            .container {
                padding: 40px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
                border-radius: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 16px;
                margin-right: 15px;
            }
        }

        /* 打印样式 */
        @media print {

            .sidebar,
            .back-to-top,
            .mobile-menu-btn {
                display: none;
            }

            .main-content {
                margin-left: 0;
            }

            body {
                background: white;
                font-size: 12px;
            }

            .container {
                box-shadow: none;
                padding: 0;
            }

            pre {
                background: #f8f9fa;
                color: #333;
                border: 1px solid #ddd;
            }

            .info-box,
            .warning-box,
            .success-box,
            .danger-box {
                border: 1px solid #ddd;
                background: #f8f9fa;
            }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
        }
    </style>
</head>

<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-archive"></i> 私有镜像仓库</h2>
            <p>本地Docker Registry搭建指南</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#overview"><i class="fas fa-eye"></i>1. 概述</a></li>
                <li><a href="#environment-preparation"><i class="fas fa-cog"></i>2. 环境准备</a></li>
                <li><a href="#registry-deployment"><i class="fas fa-server"></i>3. Registry部署</a></li>
                <li><a href="#ssl-configuration"><i class="fas fa-lock"></i>4. SSL配置</a></li>
                <li><a href="#auth-configuration"><i class="fas fa-user-shield"></i>5. 认证配置</a></li>
                <li><a href="#web-ui-deployment"><i class="fas fa-desktop"></i>6. Web UI部署</a></li>
                <li><a href="#client-configuration"><i class="fas fa-laptop"></i>7. 客户端配置</a></li>
                <li><a href="#image-operations"><i class="fas fa-box"></i>8. 镜像操作</a></li>
                <li><a href="#backup-restore"><i class="fas fa-database"></i>9. 备份恢复</a></li>
                <li><a href="#monitoring"><i class="fas fa-chart-line"></i>10. 监控维护</a></li>
                <li><a href="#troubleshooting"><i class="fas fa-bug"></i>11. 故障排查</a></li>
                <li><a href="#deployment-verification"><i class="fas fa-check-double"></i>12. 部署验证</a></li>
                <li><a href="#summary"><i class="fas fa-flag-checkered"></i>13. 总结</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-archive"></i> 本地私有镜像仓库搭建完整教程</h1>

                <div class="info-box">
                    <strong><i class="fas fa-info-circle"></i>
                        教程说明：</strong>本教程详细介绍了如何在本地环境搭建私有Docker镜像仓库，包括Registry服务部署、SSL证书配置、用户认证、Web
                    UI界面等完整功能。每个步骤都明确标注了执行的机器类型，请严格按照指示操作。
                </div>

                <div class="warning-box">
                    <strong><i class="fas fa-exclamation-triangle"></i> 机器标识说明：</strong>
                    <div style="margin-top: 15px;">
                        <span class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</span> -
                        运行Docker Registry服务的主机<br>
                        <span class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</span> -
                        需要推送/拉取镜像的机器<br>
                        <span class="machine-tag machine-docker"><i class="fas fa-box"></i> Docker主机</span> -
                        安装了Docker的机器<br>

                        <span class="machine-tag machine-all"><i class="fas fa-globe"></i> 所有机器</span> - 在所有相关机器上执行
                    </div>
                </div>

                <section id="overview">
                    <h2><span class="step-number">1</span>概述</h2>

                    <h3><i class="fas fa-question-circle"></i> 1.1 什么是私有镜像仓库</h3>
                    <p>私有镜像仓库是企业内部用于存储、管理和分发Docker镜像的服务。相比公共仓库，私有仓库具有以下优势：</p>
                    <ul>
                        <li><strong><i class="fas fa-shield-alt"></i> 安全性：</strong>镜像存储在内网，避免敏感信息泄露</li>
                        <li><strong><i class="fas fa-tachometer-alt"></i> 速度：</strong>内网传输速度快，减少镜像拉取时间</li>
                        <li><strong><i class="fas fa-cogs"></i> 控制：</strong>完全控制镜像的访问权限和存储策略</li>
                        <li><strong><i class="fas fa-dollar-sign"></i> 成本：</strong>避免公共仓库的存储和流量费用</li>
                    </ul>

                    <h3><i class="fas fa-sitemap"></i> 1.2 架构组件</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-puzzle-piece"></i> 组件</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                            <th><i class="fas fa-server"></i> 部署位置</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-archive"></i> Docker Registry</td>
                            <td>核心镜像存储服务</td>
                            <td>Registry服务器</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-lock"></i> Nginx + SSL</td>
                            <td>反向代理和HTTPS支持</td>
                            <td>Registry服务器</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-user-shield"></i> 认证服务</td>
                            <td>用户身份验证</td>
                            <td>Registry服务器</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-desktop"></i> Registry UI</td>
                            <td>Web管理界面</td>
                            <td>Registry服务器</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-network-wired"></i> 1.3 网络规划示例</h3>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 集成说明：</strong>
                        本教程基于已部署的K8s集群，私有镜像仓库将部署在K8s Master节点上，与K8s集群配合使用。
                    </div>
                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 角色</th>
                            <th><i class="fas fa-server"></i> 主机名</th>
                            <th><i class="fas fa-globe"></i> IP地址</th>
                            <th><i class="fas fa-comment"></i> 说明</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-archive"></i> Registry服务器</td>
                            <td>k8s-master</td>
                            <td>*************</td>
                            <td>部署在K8s Master节点</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-laptop"></i> K8s Worker节点</td>
                            <td>k8s-worker01/02/03</td>
                            <td>*************-63</td>
                            <td>作为Registry客户端使用</td>
                        </tr>
                    </table>
                </section>

                <section id="environment-preparation">
                    <h2><span class="step-number">2</span>环境准备</h2>

                    <h3><i class="fas fa-desktop"></i> 2.1 系统要求</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-list"></i> 项目</th>
                            <th><i class="fas fa-cogs"></i> 要求</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-linux"></i> 操作系统</td>
                            <td>CentOS 7+, Ubuntu 18.04+, 银河麒麟 V10</td>
                            <td>支持Docker的Linux发行版</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-memory"></i> 内存</td>
                            <td>Registry服务器至少4GB，客户端至少2GB</td>
                            <td>根据镜像数量和并发调整</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-microchip"></i> CPU</td>
                            <td>Registry服务器至少2核，客户端至少1核</td>
                            <td>多核有助于并发处理</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-hdd"></i> 磁盘</td>
                            <td>Registry服务器至少100GB可用空间</td>
                            <td>根据镜像存储需求调整</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-network-wired"></i> 网络</td>
                            <td>服务器间网络互通，开放5000端口</td>
                            <td>确保防火墙配置正确</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-box"></i> 2.2 安装Docker</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-globe"></i> 所有机器</div>
                    <p>在所有需要使用Docker的机器上安装Docker：</p>

                    <h4><i class="fas fa-linux"></i> CentOS/RHEL/银河麒麟系统</h4>
                    <pre><code># 卸载旧版本Docker
sudo yum remove docker docker-client docker-client-latest docker-common docker-latest docker-latest-logrotate docker-logrotate docker-engine

# 安装依赖包
sudo yum install -y yum-utils device-mapper-persistent-data lvm2

# 添加Docker官方仓库
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# 或使用阿里云镜像（推荐）
sudo yum-config-manager --add-repo http://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo

# 安装Docker CE
sudo yum install -y docker-ce docker-ce-cli containerd.io

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 验证安装
docker --version
sudo docker run hello-world</code></pre>

                    <h4><i class="fas fa-ubuntu"></i> Ubuntu/Debian系统</h4>
                    <pre><code># 更新包索引
sudo apt-get update

# 安装依赖包
sudo apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release

# 添加Docker官方GPG密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 添加Docker仓库
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装Docker CE
sudo apt-get update
sudo apt-get install -y docker-ce docker-ce-cli containerd.io

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 验证安装
docker --version
sudo docker run hello-world</code></pre>

                    <h3><i class="fas fa-user-plus"></i> 2.3 配置Docker用户权限</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-globe"></i> 所有机器</div>
                    <p>为了避免每次都使用sudo，将当前用户添加到docker组：</p>
                    <pre><code># 将当前用户添加到docker组
sudo usermod -aG docker $USER

# 重新登录或执行以下命令使权限生效
newgrp docker

# 验证权限
docker run hello-world</code></pre>

                    <h3><i class="fas fa-shield-alt"></i> 2.4 配置防火墙</h3>
                    <div class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</div>
                    <p>开放Registry服务所需的端口：</p>

                    <h4><i class="fas fa-fire"></i> CentOS/RHEL/银河麒麟系统（firewalld）</h4>
                    <pre><code># 开放Registry端口（5000）
sudo firewall-cmd --permanent --add-port=5000/tcp

# 开放HTTPS端口（443）
sudo firewall-cmd --permanent --add-port=443/tcp

# 开放HTTP端口（80，用于重定向）
sudo firewall-cmd --permanent --add-port=80/tcp

# 重载防火墙配置
sudo firewall-cmd --reload

# 查看开放的端口
sudo firewall-cmd --list-ports</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> K8s集成提示：</strong>
                        如果您的K8s集群已经配置了防火墙规则，Registry端口可能已经开放。可以跳过防火墙配置步骤。
                    </div>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 端口冲突提醒：</strong>
                        如果您已经部署了Kuboard（默认使用80端口），请注意避免端口冲突。建议Registry使用443端口（HTTPS），Kuboard使用80端口。
                    </div>

                    <h4><i class="fas fa-shield-alt"></i> Ubuntu/Debian系统（ufw）</h4>
                    <pre><code># 开放Registry端口
sudo ufw allow 5000/tcp

# 开放HTTPS端口
sudo ufw allow 443/tcp

# 开放HTTP端口
sudo ufw allow 80/tcp

# 查看防火墙状态
sudo ufw status</code></pre>

                    <h3><i class="fas fa-folder"></i> 2.5 创建工作目录</h3>
                    <div class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</div>
                    <pre><code># 创建Registry相关目录
sudo mkdir -p /k8s/registry/{data,certs,auth,config}

# 设置目录权限
sudo chown -R $USER:$USER /k8s/registry

# 查看目录结构
tree /k8s/registry

# 如果没有tree命令，可以使用ls查看
ls -la /k8s/registry/</code></pre>

                    <h3><i class="fas fa-cog"></i> 2.6 环境准备脚本</h3>
                    <div class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</div>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 说明：</strong>运行此脚本可以自动完成Registry部署的环境准备工作
                    </div>

                    <pre><code>#!/bin/bash
# Registry环境准备脚本

echo "=== Registry环境准备开始 ==="

# 1. 检查操作系统
echo "1. 检查操作系统..."
if [ -f /etc/os-release ]; then
    . /etc/os-release
    echo "操作系统: $NAME $VERSION"
else
    echo "⚠️  无法识别操作系统"
fi

# 2. 检查Docker安装
echo "2. 检查Docker安装..."
if command -v docker &> /dev/null; then
    docker_version=$(docker --version)
    echo "✅ Docker已安装: $docker_version"

    # 检查Docker服务状态
    if systemctl is-active --quiet docker; then
        echo "✅ Docker服务运行正常"
    else
        echo "❌ Docker服务未运行，尝试启动..."
        sudo systemctl start docker
        if [ $? -eq 0 ]; then
            echo "✅ Docker服务启动成功"
        else
            echo "❌ Docker服务启动失败"
            exit 1
        fi
    fi
else
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 3. 检查Docker权限
echo "3. 检查Docker权限..."
if docker ps &> /dev/null; then
    echo "✅ Docker权限正常"
else
    echo "⚠️  当前用户无Docker权限，需要使用sudo或加入docker组"
fi

# 4. 检查端口占用
echo "4. 检查端口占用..."
if command -v netstat &> /dev/null; then
    port_5000=$(netstat -tlnp 2>/dev/null | grep ":5000 " | wc -l)
    port_443=$(netstat -tlnp 2>/dev/null | grep ":443 " | wc -l)

    if [ $port_5000 -gt 0 ]; then
        echo "⚠️  端口5000已被占用"
        netstat -tlnp | grep ":5000 "
    else
        echo "✅ 端口5000可用"
    fi

    if [ $port_443 -gt 0 ]; then
        echo "⚠️  端口443已被占用"
        netstat -tlnp | grep ":443 "
    else
        echo "✅ 端口443可用"
    fi
else
    echo "⚠️  netstat命令不可用，无法检查端口占用"
fi

# 5. 检查磁盘空间
echo "5. 检查磁盘空间..."
available_space=$(df /k8s 2>/dev/null | tail -1 | awk '{print $4}' || echo "0")
if [ $available_space -gt 10485760 ]; then  # 10GB in KB
    echo "✅ 磁盘空间充足: $(($available_space / 1024 / 1024))GB 可用"
else
    echo "⚠️  磁盘空间不足，建议至少10GB可用空间"
fi

# 6. 创建工作目录
echo "6. 创建工作目录..."
sudo mkdir -p /k8s/registry/{data,certs,auth,config}
sudo chown -R $USER:$USER /k8s/registry
echo "✅ 工作目录创建完成"

# 7. 检查防火墙状态
echo "7. 检查防火墙状态..."
if command -v firewall-cmd &> /dev/null; then
    if systemctl is-active --quiet firewalld; then
        echo "防火墙状态: 运行中"
        open_ports=$(firewall-cmd --list-ports 2>/dev/null)
        echo "已开放端口: $open_ports"

        if [[ $open_ports == *"5000/tcp"* ]]; then
            echo "✅ 端口5000已开放"
        else
            echo "⚠️  端口5000未开放，建议运行: sudo firewall-cmd --permanent --add-port=5000/tcp"
        fi
    else
        echo "防火墙状态: 未运行"
    fi
elif command -v ufw &> /dev/null; then
    ufw_status=$(ufw status 2>/dev/null | head -1)
    echo "防火墙状态: $ufw_status"
else
    echo "⚠️  未检测到防火墙管理工具"
fi

# 8. 检查SSL工具
echo "8. 检查SSL工具..."
if command -v openssl &> /dev/null; then
    openssl_version=$(openssl version)
    echo "✅ OpenSSL可用: $openssl_version"
else
    echo "❌ OpenSSL未安装，请安装: sudo yum install -y openssl"
fi

echo "=== Registry环境准备完成 ==="
echo "🎉 环境检查完成！"

# 显示下一步操作提示
echo ""
echo "=== 下一步操作 ==="
echo "1. 创建Registry配置文件"
echo "2. 启动Registry容器"
echo "3. 配置SSL证书"
echo "4. 配置用户认证"</code></pre>

                    <h3><i class="fas fa-play"></i> 2.7 运行环境准备脚本</h3>
                    <pre><code># 保存环境准备脚本
cat > /k8s/registry/prepare-env.sh << 'EOF'
# 将上面的脚本内容粘贴到这里
EOF

# 设置执行权限
chmod +x /k8s/registry/prepare-env.sh

# 运行环境准备脚本
/k8s/registry/prepare-env.sh</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 环境准备完成：</strong>Docker已安装，防火墙已配置，工作目录已创建。
                    </div>
                </section>

                <section id="registry-deployment">
                    <h2><span class="step-number">3</span>Registry部署</h2>

                    <h3><i class="fas fa-file-alt"></i> 3.1 创建Registry配置文件</h3>
                    <div class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</div>
                    <p>创建Registry的配置文件，定义存储、日志等参数：</p>

                    <pre><code># 创建Registry配置文件
cat > /k8s/registry/config/config.yml << 'EOF'
version: 0.1
log:
  accesslog:
    disabled: false
  level: info
  formatter: text
  fields:
    service: registry
    environment: production

storage:
  cache:
    blobdescriptor: inmemory
  filesystem:
    rootdirectory: /var/lib/registry
  delete:
    enabled: true

http:
  addr: :5000
  headers:
    X-Content-Type-Options: [nosniff]
    Access-Control-Allow-Origin: ['*']
    Access-Control-Allow-Methods: ['HEAD', 'GET', 'OPTIONS', 'DELETE']
    Access-Control-Allow-Headers: ['Authorization', 'Accept', 'Cache-Control']

health:
  storagedriver:
    enabled: true
    interval: 10s
    threshold: 3

compatibility:
  schema1:
    enabled: true
EOF</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 配置说明：</strong>
                        <ul>
                            <li><strong>storage.filesystem.rootdirectory：</strong>镜像存储路径</li>
                            <li><strong>storage.delete.enabled：</strong>允许删除镜像</li>
                            <li><strong>http.addr：</strong>Registry监听端口</li>
                            <li><strong>http.headers：</strong>CORS跨域配置，支持Web UI访问</li>
                            <li><strong>health：</strong>健康检查配置</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-rocket"></i> 3.2 启动Registry容器</h3>
                    <div class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</div>

                    <h4><i class="fas fa-play"></i> 3.2.1 基础版本（HTTP）</h4>
                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 启动顺序：</strong>
                        <ol style="margin-top: 10px;">
                            <li>确保Docker服务正常运行</li>
                            <li>确保配置文件已创建</li>
                            <li>确保工作目录权限正确</li>
                            <li>启动Registry容器</li>
                            <li>测试Registry API</li>
                        </ol>
                    </div>
                    <p>首先启动基础的HTTP版本进行测试：</p>
                    <pre><code># 启动Registry容器（HTTP版本）
docker run -d \
  --name registry \
  --restart=always \
  -p 5000:5000 \
  -v /k8s/registry/data:/var/lib/registry \
  -v /k8s/registry/config/config.yml:/etc/docker/registry/config.yml \
  registry:2

# 检查容器状态
docker ps | grep registry

# 查看容器日志
docker logs registry

# 测试Registry API
curl http://localhost:5000/v2/</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>如果看到返回
                        <code>{}</code>，说明Registry服务启动成功。
                    </div>

                    <h4><i class="fas fa-vial"></i> 3.2.2 测试推送镜像</h4>
                    <div class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</div>
                    <pre><code># 拉取一个测试镜像
docker pull hello-world

# 给镜像打标签（使用实际IP地址）
docker tag hello-world *************:5000/hello-world

# 推送镜像到私有仓库
docker push *************:5000/hello-world

# 查看仓库中的镜像
curl http://*************:5000/v2/_catalog

# 查看镜像标签
curl http://*************:5000/v2/hello-world/tags/list</code></pre>

                    <h4><i class="fas fa-trash"></i> 3.2.3 清理测试容器</h4>
                    <pre><code># 停止并删除测试容器
docker stop registry
docker rm registry

# 清理测试数据（可选）
sudo rm -rf /k8s/registry/data/*</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i>
                            注意：</strong>HTTP版本的Registry仅用于测试，生产环境必须使用HTTPS。接下来我们将配置SSL证书。
                    </div>
                </section>

                <section id="ssl-configuration">
                    <h2><span class="step-number">4</span>SSL配置</h2>

                    <h3><i class="fas fa-certificate"></i> 4.1 生成自签名SSL证书</h3>
                    <div class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</div>
                    <p>为了安全起见，我们需要为Registry配置HTTPS。这里演示如何生成自签名证书：</p>

                    <h4><i class="fas fa-key"></i> 4.1.1 生成私钥和证书</h4>
                    <pre><code># 进入证书目录
cd /k8s/registry/certs

# 生成私钥
openssl genrsa -out registry.key 4096

# 生成证书签名请求（使用实际IP地址）
openssl req -new -key registry.key -out registry.csr -subj "/C=CN/ST=Beijing/L=Beijing/O=MyCompany/OU=IT/CN=*************"

# 生成自签名证书（有效期10年）
openssl x509 -req -days 3650 -in registry.csr -signkey registry.key -out registry.crt

# 设置证书权限
chmod 600 registry.key
chmod 644 registry.crt

# 查看证书信息
openssl x509 -in registry.crt -text -noout</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 生产环境建议：</strong>
                        <ul>
                            <li>使用权威CA签发的证书</li>
                            <li>或使用Let's Encrypt免费证书</li>
                            <li>确保证书的CN字段与访问域名一致</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-shield-alt"></i> 4.1.2 配置客户端信任证书</h4>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <p>将自签名证书添加到客户端的信任列表：</p>

                    <pre><code># 创建Docker证书目录（使用实际IP地址）
sudo mkdir -p /etc/docker/certs.d/*************:5000

# 复制证书到客户端（需要先从Registry服务器复制过来）
sudo cp registry.crt /etc/docker/certs.d/*************:5000/ca.crt

# 或者将证书添加到系统信任列表
# CentOS/RHEL/银河麒麟系统：
sudo cp registry.crt /etc/pki/ca-trust/source/anchors/registry-*************.crt
sudo update-ca-trust

# Ubuntu/Debian系统：
sudo cp registry.crt /usr/local/share/ca-certificates/registry-*************.crt
sudo update-ca-certificates</code></pre>

                    <h3><i class="fas fa-server"></i> 4.2 配置Nginx反向代理</h3>
                    <div class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</div>
                    <p>使用Nginx作为反向代理，提供HTTPS支持和负载均衡：</p>

                    <h4><i class="fas fa-download"></i> 4.2.1 安装Nginx</h4>
                    <pre><code># CentOS/RHEL/银河麒麟系统
sudo yum install -y nginx

# Ubuntu/Debian系统
sudo apt-get install -y nginx

# 启动Nginx服务
sudo systemctl start nginx
sudo systemctl enable nginx</code></pre>

                    <h4><i class="fas fa-file-alt"></i> 4.2.2 创建Nginx配置</h4>
                    <pre><code># 创建Registry专用配置文件
sudo tee /etc/nginx/conf.d/registry.conf << 'EOF'
upstream docker-registry {
    server localhost:5000;
}

server {
    listen 80;
    server_name registry-server;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name registry-server;

    ssl_certificate /k8s/registry/certs/registry.crt;
    ssl_certificate_key /k8s/registry/certs/registry.key;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    client_max_body_size 0;
    chunked_transfer_encoding on;

    location /v2/ {
        if ($http_user_agent ~ "^(docker\/1\.(3|4|5(?!\.[0-9]-dev))|Go ).*$" ) {
            return 404;
        }

        proxy_pass                          http://docker-registry;
        proxy_set_header  Host              $http_host;
        proxy_set_header  X-Real-IP         $remote_addr;
        proxy_set_header  X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_set_header  X-Forwarded-Proto $scheme;
        proxy_read_timeout                  900;
    }

    location / {
        return 404;
    }
}
EOF

# 测试Nginx配置
sudo nginx -t

# 重载Nginx配置
sudo systemctl reload nginx</code></pre>

                    <h3><i class="fas fa-rocket"></i> 4.3 启动HTTPS版Registry</h3>
                    <div class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</div>
                    <pre><code># 启动Registry容器（HTTPS版本）
docker run -d \
  --name registry \
  --restart=always \
  -p 5000:5000 \
  -v /k8s/registry/data:/var/lib/registry \
  -v /k8s/registry/config/config.yml:/etc/docker/registry/config.yml \
  registry:2

# 检查服务状态
docker ps | grep registry
sudo systemctl status nginx

# 测试HTTPS访问
curl -k https://registry-server/v2/</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> SSL配置完成：</strong>Registry现在支持HTTPS访问，提供了更好的安全性。
                    </div>
                </section>

                <section id="auth-configuration">
                    <h2><span class="step-number">5</span>认证配置</h2>

                    <h3><i class="fas fa-user-shield"></i> 5.1 配置基本认证</h3>
                    <div class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</div>
                    <p>为Registry添加用户认证，防止未授权访问：</p>

                    <h4><i class="fas fa-users"></i> 5.1.1 创建用户密码文件</h4>
                    <pre><code># 安装htpasswd工具
# CentOS/RHEL/银河麒麟系统
sudo yum install -y httpd-tools

# Ubuntu/Debian系统
sudo apt-get install -y apache2-utils

# 创建第一个用户（会提示输入密码）
htpasswd -c /k8s/registry/auth/htpasswd admin

# 添加更多用户（不使用-c参数）
htpasswd /k8s/registry/auth/htpasswd developer
htpasswd /k8s/registry/auth/htpasswd tester

# 查看用户列表
cat /k8s/registry/auth/htpasswd</code></pre>

                    <h4><i class="fas fa-file-alt"></i> 5.1.2 更新Registry配置</h4>
                    <pre><code># 更新Registry配置文件，添加认证
cat > /k8s/registry/config/config.yml << 'EOF'
version: 0.1
log:
  accesslog:
    disabled: false
  level: info
  formatter: text
  fields:
    service: registry
    environment: production

storage:
  cache:
    blobdescriptor: inmemory
  filesystem:
    rootdirectory: /var/lib/registry
  delete:
    enabled: true

http:
  addr: :5000
  headers:
    X-Content-Type-Options: [nosniff]
    Access-Control-Allow-Origin: ['*']
    Access-Control-Allow-Methods: ['HEAD', 'GET', 'OPTIONS', 'DELETE']
    Access-Control-Allow-Headers: ['Authorization', 'Accept', 'Cache-Control']

auth:
  htpasswd:
    realm: basic-realm
    path: /auth/htpasswd

health:
  storagedriver:
    enabled: true
    interval: 10s
    threshold: 3

compatibility:
  schema1:
    enabled: true
EOF

                    <h4><i class="fas fa-sync-alt"></i> 5.1.3 重启Registry服务</h4>
                    <pre><code># 停止当前Registry容器
docker stop registry
docker rm registry

# 启动带认证的Registry容器
docker run -d \
  --name registry \
  --restart=always \
  -p 5000:5000 \
  -v /k8s/registry/data:/var/lib/registry \
  -v /k8s/registry/config/config.yml:/etc/docker/registry/config.yml \
  -v /k8s/registry/auth:/auth \
  registry:2

# 检查容器状态
docker ps | grep registry
docker logs registry</code></pre>

                    <h4><i class="fas fa-vial"></i> 5.1.4 测试认证</h4>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <pre><code># 测试未认证访问（应该返回401错误）
curl -k https://registry-server/v2/

# 测试认证访问
curl -k -u admin:password https://registry-server/v2/

# Docker登录测试
docker login registry-server
# 输入用户名和密码

# 推送镜像测试
docker tag hello-world registry-server/hello-world
docker push registry-server/hello-world

# 查看镜像列表
curl -k -u admin:password https://registry-server/v2/_catalog</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 认证配置完成：</strong>Registry现在需要用户名和密码才能访问，提供了访问控制。
                    </div>
                </section>

                <section id="web-ui-deployment">
                    <h2><span class="step-number">6</span>Web UI部署</h2>

                    <h3><i class="fas fa-desktop"></i> 6.1 部署Registry UI</h3>
                    <div class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</div>
                    <p>在同一台服务器上部署Web界面来管理Registry中的镜像：</p>

                    <h4><i class="fas fa-rocket"></i> 6.1.1 启动Registry UI容器</h4>
                    <pre><code># 启动Registry UI容器
docker run -d \
  --name registry-ui \
  --restart=always \
  -p 8080:80 \
  -e REGISTRY_TITLE="Private Docker Registry" \
  -e REGISTRY_URL="https://registry-server" \
  -e REGISTRY_USERNAME="admin" \
  -e REGISTRY_PASSWORD="password" \
  -e SINGLE_REGISTRY=true \
  joxit/docker-registry-ui:latest

# 检查容器状态
docker ps | grep registry-ui
docker logs registry-ui</code></pre>

                    <h4><i class="fas fa-globe"></i> 6.1.2 配置Nginx代理（可选）</h4>
                    <pre><code># 为Registry UI配置Nginx代理
sudo tee /etc/nginx/conf.d/registry-ui.conf << 'EOF'
server {
    listen 8081;
    server_name registry-server;

    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# 重载Nginx配置
sudo nginx -t
sudo systemctl reload nginx</code></pre>

                    <h3><i class="fas fa-chart-bar"></i> 6.2 访问Web界面</h3>
                    <p>现在可以通过浏览器访问Registry的Web管理界面：</p>
                    <ul>
                        <li><strong>直接访问：</strong>http://*************:8080</li>
                        <li><strong>通过Nginx：</strong>http://*************:8081</li>
                    </ul>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> Web UI功能：</strong>
                        <ul>
                            <li>浏览所有镜像和标签</li>
                            <li>查看镜像详细信息</li>
                            <li>删除镜像和标签</li>
                            <li>搜索镜像</li>
                            <li>查看镜像历史</li>
                        </ul>
                    </div>
                </section>

                <section id="client-configuration">
                    <h2><span class="step-number">7</span>客户端配置</h2>

                    <h3><i class="fas fa-laptop"></i> 7.1 配置Docker客户端</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <p>配置客户端机器以使用私有Registry：</p>

                    <h4><i class="fas fa-file-alt"></i> 7.1.1 配置hosts文件</h4>
                    <pre><code># 编辑hosts文件，添加Registry服务器解析
sudo tee -a /etc/hosts << 'EOF'
************* registry-server
EOF

# 验证解析
ping -c 3 registry-server</code></pre>

                    <h4><i class="fas fa-cog"></i> 7.1.2 配置Docker daemon</h4>
                    <pre><code># 创建或编辑Docker daemon配置文件
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json << 'EOF'
{
  "insecure-registries": [],
  "registry-mirrors": [
    "https://registry-server"
  ],
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF

# 重启Docker服务
sudo systemctl restart docker

# 验证配置
docker info | grep -A 5 "Registry Mirrors"</code></pre>

                    <h4><i class="fas fa-sign-in-alt"></i> 7.1.3 登录Registry</h4>
                    <pre><code># 登录私有Registry
docker login registry-server
# 输入用户名：admin
# 输入密码：password

# 验证登录状态
cat ~/.docker/config.json</code></pre>

                    <h3><i class="fas fa-terminal"></i> 7.2 常用操作命令</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>

                    <h4><i class="fas fa-upload"></i> 7.2.1 推送镜像</h4>
                    <pre><code># 拉取公共镜像
docker pull nginx:latest
docker pull redis:alpine

# 给镜像打标签
docker tag nginx:latest registry-server/nginx:latest
docker tag redis:alpine registry-server/redis:alpine

# 推送镜像到私有仓库
docker push registry-server/nginx:latest
docker push registry-server/redis:alpine</code></pre>

                    <h4><i class="fas fa-download"></i> 7.2.2 拉取镜像</h4>
                    <pre><code># 从私有仓库拉取镜像
docker pull registry-server/nginx:latest
docker pull registry-server/redis:alpine

# 查看本地镜像
docker images | grep registry-server</code></pre>

                    <h4><i class="fas fa-search"></i> 7.2.3 查询镜像</h4>
                    <pre><code># 查看仓库中的所有镜像
curl -k -u admin:password https://registry-server/v2/_catalog

# 查看特定镜像的标签
curl -k -u admin:password https://registry-server/v2/nginx/tags/list

# 查看镜像详细信息
curl -k -u admin:password https://registry-server/v2/nginx/manifests/latest</code></pre>
                </section>

                <section id="image-operations">
                    <h2><span class="step-number">8</span>镜像操作</h2>

                    <h3><i class="fas fa-box"></i> 8.1 镜像管理</h3>
                    <div class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</div>

                    <h4><i class="fas fa-list"></i> 8.1.1 查看存储使用情况</h4>
                    <pre><code># 查看Registry数据目录大小
du -sh /k8s/registry/data

# 查看详细的存储结构
find /k8s/registry/data -type f -name "*.json" | head -10
find /k8s/registry/data -type f -name "data" | head -10

# 统计镜像数量
curl -k -u admin:password https://registry-server/v2/_catalog | jq '.repositories | length'</code></pre>

                    <h4><i class="fas fa-trash"></i> 8.1.2 删除镜像</h4>
                    <pre><code># 获取镜像的digest
DIGEST=$(curl -k -u admin:password -H "Accept: application/vnd.docker.distribution.manifest.v2+json" \
  https://registry-server/v2/nginx/manifests/latest | jq -r '.config.digest')

# 删除镜像
curl -k -u admin:password -X DELETE https://registry-server/v2/nginx/manifests/$DIGEST

# 运行垃圾回收清理存储空间
docker exec registry bin/registry garbage-collect /etc/docker/registry/config.yml</code></pre>

                    <h3><i class="fas fa-sync-alt"></i> 8.2 镜像同步</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>

                    <h4><i class="fas fa-download"></i> 8.2.1 批量同步脚本</h4>
                    <pre><code># 创建镜像同步脚本
cat > /k8s/registry/sync_images.sh << 'EOF'
#!/bin/bash

# 定义要同步的镜像列表
IMAGES=(
    "nginx:latest"
    "redis:alpine"
    "mysql:8.0"
    "postgres:13"
    "node:16-alpine"
    "python:3.9-slim"
)

REGISTRY="registry-server"

echo "开始同步镜像到私有仓库..."

for image in "${IMAGES[@]}"; do
    echo "处理镜像: $image"

    # 拉取公共镜像
    docker pull $image

    # 打标签
    docker tag $image $REGISTRY/$image

    # 推送到私有仓库
    docker push $REGISTRY/$image

    # 清理本地镜像（可选）
    # docker rmi $image $REGISTRY/$image

    echo "完成: $image"
    echo "---"
done

echo "所有镜像同步完成！"
EOF

# 设置执行权限
chmod +x /k8s/registry/sync_images.sh

# 执行同步
/k8s/registry/sync_images.sh</code></pre>
                </section>

                <section id="backup-restore">
                    <h2><span class="step-number">9</span>备份恢复</h2>

                    <h3><i class="fas fa-database"></i> 9.1 数据备份</h3>
                    <div class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</div>

                    <h4><i class="fas fa-save"></i> 9.1.1 创建备份脚本</h4>
                    <pre><code># 创建备份脚本
cat > /k8s/registry/backup.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/k8s/registry/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_NAME="registry_backup_$DATE"

# 创建备份目录
mkdir -p $BACKUP_DIR

echo "开始备份Registry数据..."

# 停止Registry容器
docker stop registry

# 创建数据备份
tar -czf $BACKUP_DIR/${BACKUP_NAME}_data.tar.gz -C /k8s/registry data

# 备份配置文件
tar -czf $BACKUP_DIR/${BACKUP_NAME}_config.tar.gz -C /k8s/registry config auth certs

# 重启Registry容器
docker start registry

# 清理旧备份（保留最近7天）
find $BACKUP_DIR -name "registry_backup_*" -mtime +7 -delete

echo "备份完成: $BACKUP_DIR/${BACKUP_NAME}_*.tar.gz"
EOF

# 设置执行权限
chmod +x /k8s/registry/backup.sh

# 执行备份
/k8s/registry/backup.sh</code></pre>

                    <h4><i class="fas fa-clock"></i> 9.1.2 设置定时备份</h4>
                    <pre><code># 添加定时任务（每天凌晨2点备份）
crontab -e

# 添加以下行：
0 2 * * * /k8s/registry/backup.sh >> /var/log/registry_backup.log 2>&1

# 查看定时任务
crontab -l</code></pre>

                    <h3><i class="fas fa-undo"></i> 9.2 数据恢复</h3>
                    <div class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</div>

                    <h4><i class="fas fa-upload"></i> 9.2.1 恢复数据</h4>
                    <pre><code># 停止Registry服务
docker stop registry
docker rm registry

# 恢复数据（替换为实际的备份文件名）
BACKUP_DATE="20231201_020000"
cd /k8s/registry

# 备份当前数据（以防恢复失败）
mv data data.old
mv config config.old

# 恢复数据
tar -xzf backups/registry_backup_${BACKUP_DATE}_data.tar.gz
tar -xzf backups/registry_backup_${BACKUP_DATE}_config.tar.gz

# 重启Registry服务
docker run -d \
  --name registry \
  --restart=always \
  -p 5000:5000 \
  -v /k8s/registry/data:/var/lib/registry \
  -v /k8s/registry/config/config.yml:/etc/docker/registry/config.yml \
  -v /k8s/registry/auth:/auth \
  registry:2

# 验证恢复
docker logs registry
curl -k -u admin:password https://registry-server/v2/_catalog</code></pre>
                </section>

                <section id="monitoring">
                    <h2><span class="step-number">10</span>监控维护</h2>

                    <h3><i class="fas fa-chart-line"></i> 10.1 监控脚本</h3>
                    <div class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</div>

                    <h4><i class="fas fa-heartbeat"></i> 10.1.1 健康检查脚本</h4>
                    <pre><code># 创建健康检查脚本
cat > /k8s/registry/health_check.sh << 'EOF'
#!/bin/bash

REGISTRY_URL="https://registry-server"
USERNAME="admin"
PASSWORD="password"

echo "Registry健康检查报告 - $(date)"
echo "================================"

# 检查Registry容器状态
echo "1. 容器状态检查:"
if docker ps | grep -q registry; then
    echo "   ✓ Registry容器运行正常"
else
    echo "   ✗ Registry容器未运行"
    exit 1
fi

# 检查API响应
echo "2. API响应检查:"
if curl -k -u $USERNAME:$PASSWORD -s $REGISTRY_URL/v2/ > /dev/null; then
    echo "   ✓ API响应正常"
else
    echo "   ✗ API响应异常"
fi

# 检查磁盘使用率
echo "3. 磁盘使用率检查:"
DISK_USAGE=$(df /k8s/registry/data | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -lt 80 ]; then
    echo "   ✓ 磁盘使用率: ${DISK_USAGE}%"
else
    echo "   ⚠ 磁盘使用率过高: ${DISK_USAGE}%"
fi

# 检查镜像数量
echo "4. 镜像数量统计:"
IMAGE_COUNT=$(curl -k -u $USERNAME:$PASSWORD -s $REGISTRY_URL/v2/_catalog | jq '.repositories | length')
echo "   镜像总数: $IMAGE_COUNT"

# 检查内存使用
echo "5. 内存使用检查:"
MEMORY_USAGE=$(docker stats registry --no-stream --format "{{.MemPerc}}" | sed 's/%//')
echo "   Registry容器内存使用: ${MEMORY_USAGE}%"

echo "================================"
echo "检查完成"
EOF

# 设置执行权限
chmod +x /k8s/registry/health_check.sh

# 执行健康检查
/k8s/registry/health_check.sh</code></pre>

                    <h3><i class="fas fa-tools"></i> 10.2 维护任务</h3>

                    <h4><i class="fas fa-broom"></i> 10.2.1 清理任务</h4>
                    <pre><code># 创建清理脚本
cat > /k8s/registry/cleanup.sh << 'EOF'
#!/bin/bash

echo "开始Registry清理任务..."

# 清理Docker系统
echo "清理Docker系统..."
docker system prune -f

# 运行Registry垃圾回收
echo "运行Registry垃圾回收..."
docker exec registry bin/registry garbage-collect /etc/docker/registry/config.yml

# 清理旧日志
echo "清理旧日志..."
find /var/log -name "*.log" -mtime +30 -delete

# 清理临时文件
echo "清理临时文件..."
find /tmp -name "docker-*" -mtime +1 -delete

echo "清理任务完成"
EOF

# 设置执行权限
chmod +x /k8s/registry/cleanup.sh

# 设置定时清理（每周日凌晨3点）
crontab -e
# 添加：0 3 * * 0 /k8s/registry/cleanup.sh >> /var/log/registry_cleanup.log 2>&1</code></pre>
                </section>

                <section id="troubleshooting">
                    <h2><span class="step-number">11</span>故障排查</h2>

                    <h3><i class="fas fa-bug"></i> 11.1 常见问题</h3>

                    <h4><i class="fas fa-exclamation-triangle"></i> 11.1.1 连接问题</h4>
                    <div class="danger-box">
                        <strong>问题：</strong>客户端无法连接到Registry
                    </div>
                    <div class="info-box">
                        <strong>解决方案：</strong>
                        <ol>
                            <li>检查网络连通性：<code>ping registry-server</code></li>
                            <li>检查端口开放：<code>telnet registry-server 5000</code></li>
                            <li>检查防火墙设置：<code>sudo firewall-cmd --list-ports</code></li>
                            <li>检查Docker daemon配置：<code>docker info</code></li>
                        </ol>
                    </div>

                    <h4><i class="fas fa-lock"></i> 11.1.2 SSL证书问题</h4>
                    <div class="danger-box">
                        <strong>问题：</strong>SSL证书验证失败
                    </div>
                    <div class="info-box">
                        <strong>解决方案：</strong>
                        <ol>
                            <li>检查证书有效期：<code>openssl x509 -in registry.crt -text -noout</code></li>
                            <li>验证证书CN字段与访问域名是否一致</li>
                            <li>确保客户端已信任证书</li>
                            <li>临时跳过SSL验证：<code>curl -k</code></li>
                        </ol>
                    </div>

                    <h4><i class="fas fa-user-times"></i> 11.1.3 认证问题</h4>
                    <div class="danger-box">
                        <strong>问题：</strong>用户认证失败
                    </div>
                    <div class="info-box">
                        <strong>解决方案：</strong>
                        <ol>
                            <li>检查用户密码文件：<code>cat /k8s/registry/auth/htpasswd</code></li>
                            <li>验证密码正确性：<code>htpasswd -v /k8s/registry/auth/htpasswd username</code></li>
                            <li>检查Registry配置中的认证设置</li>
                            <li>重新生成用户密码：<code>htpasswd /k8s/registry/auth/htpasswd username</code></li>
                        </ol>
                    </div>

                    <h3><i class="fas fa-search"></i> 11.2 日志分析</h3>

                    <h4><i class="fas fa-file-alt"></i> 11.2.1 查看日志</h4>
                    <pre><code># 查看Registry容器日志
docker logs registry

# 实时查看日志
docker logs -f registry

# 查看Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 查看系统日志
sudo journalctl -u docker -f</code></pre>

                    <h4><i class="fas fa-chart-bar"></i> 11.2.2 性能分析</h4>
                    <pre><code># 查看容器资源使用
docker stats registry

# 查看系统资源
top
htop
iotop

# 查看网络连接
netstat -tulpn | grep :5000
ss -tulpn | grep :5000</code></pre>

                    <h3><i class="fas fa-tools"></i> 11.3 修复工具</h3>

                    <h4><i class="fas fa-wrench"></i> 11.3.1 Registry修复脚本</h4>
                    <pre><code># 创建修复脚本
cat > /k8s/registry/repair.sh << 'EOF'
#!/bin/bash

echo "Registry修复工具"
echo "==============="

# 检查并修复容器
echo "1. 检查Registry容器..."
if ! docker ps | grep -q registry; then
    echo "   重启Registry容器..."
    docker start registry || docker run -d \
      --name registry \
      --restart=always \
      -p 5000:5000 \
      -v /k8s/registry/data:/var/lib/registry \
      -v /k8s/registry/config/config.yml:/etc/docker/registry/config.yml \
      -v /k8s/registry/auth:/auth \
      registry:2
fi

# 检查并修复权限
echo "2. 检查文件权限..."
sudo chown -R $USER:$USER /k8s/registry/data
sudo chmod -R 755 /k8s/registry/data

# 检查并修复配置
echo "3. 检查配置文件..."
if [ ! -f /k8s/registry/config/config.yml ]; then
    echo "   重新创建配置文件..."
    # 这里可以添加重新创建配置的代码
fi

# 运行垃圾回收
echo "4. 运行垃圾回收..."
docker exec registry bin/registry garbage-collect /etc/docker/registry/config.yml

echo "修复完成"
EOF

chmod +x /k8s/registry/repair.sh</code></pre>
                </section>

                <section id="deployment-verification">
                    <h2><span class="step-number">12</span>部署验证脚本</h2>

                    <h3><i class="fas fa-check-circle"></i> 12.1 完整验证脚本</h3>
                    <div class="machine-tag machine-registry"><i class="fas fa-server"></i> Registry服务器</div>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 说明：</strong>运行此脚本可以全面验证Registry的部署状态和功能
                    </div>

                    <pre><code>#!/bin/bash
# Registry部署验证脚本

echo "=== Registry部署验证开始 ==="

# 1. 检查Docker服务
echo "1. 检查Docker服务..."
if systemctl is-active --quiet docker; then
    echo "✅ Docker服务运行正常"
else
    echo "❌ Docker服务未运行"
    exit 1
fi

# 2. 检查Registry容器
echo "2. 检查Registry容器..."
if docker ps | grep -q registry; then
    container_status=$(docker ps --filter "name=registry" --format "{{.Status}}")
    echo "✅ Registry容器运行正常"
    echo "   状态: $container_status"
else
    echo "❌ Registry容器未运行"
    echo "检查所有Registry相关容器:"
    docker ps -a | grep registry
    exit 1
fi

# 3. 检查Registry API
echo "3. 检查Registry API..."
if curl -s http://localhost:5000/v2/ | grep -q "{}"; then
    echo "✅ Registry API响应正常"
else
    echo "❌ Registry API响应异常"
    echo "尝试检查容器日志:"
    docker logs registry | tail -10
    exit 1
fi

# 4. 检查HTTPS配置（如果配置了SSL）
echo "4. 检查HTTPS配置..."
if docker ps | grep -q nginx; then
    echo "检测到Nginx容器"
    if curl -k -s https://*************/v2/ | grep -q "{}"; then
        echo "✅ HTTPS访问正常"
    else
        echo "⚠️  HTTPS访问可能有问题"
    fi
else
    echo "⚠️  未检测到Nginx容器，可能未配置HTTPS"
fi

# 5. 检查存储目录
echo "5. 检查存储目录..."
if [ -d "/k8s/registry/data" ]; then
    data_size=$(du -sh /k8s/registry/data 2>/dev/null | cut -f1)
    echo "✅ 存储目录存在，大小: $data_size"

    # 检查是否有镜像数据
    if [ -d "/k8s/registry/data/docker" ]; then
        image_count=$(find /k8s/registry/data/docker/registry/v2/repositories -maxdepth 1 -type d 2>/dev/null | wc -l)
        if [ $image_count -gt 1 ]; then
            echo "✅ 检测到 $((image_count-1)) 个镜像仓库"
        else
            echo "⚠️  暂无镜像数据"
        fi
    else
        echo "⚠️  暂无镜像数据"
    fi
else
    echo "❌ 存储目录不存在"
    exit 1
fi

# 6. 检查证书文件（如果配置了SSL）
echo "6. 检查证书文件..."
if [ -f "/k8s/registry/certs/registry.crt" ] && [ -f "/k8s/registry/certs/registry.key" ]; then
    echo "✅ SSL证书文件存在"

    # 检查证书有效期
    cert_expiry=$(openssl x509 -in /k8s/registry/certs/registry.crt -noout -enddate 2>/dev/null | cut -d= -f2)
    if [ -n "$cert_expiry" ]; then
        echo "   证书有效期至: $cert_expiry"
    fi
else
    echo "⚠️  SSL证书文件不存在，可能未配置HTTPS"
fi

# 7. 检查认证配置（如果配置了认证）
echo "7. 检查认证配置..."
if [ -f "/k8s/registry/auth/htpasswd" ]; then
    user_count=$(wc -l < /k8s/registry/auth/htpasswd)
    echo "✅ 认证文件存在，用户数: $user_count"
else
    echo "⚠️  认证文件不存在，可能未配置用户认证"
fi

# 8. 检查Web UI（如果部署了）
echo "8. 检查Web UI..."
if docker ps | grep -q registry-ui; then
    echo "✅ Registry Web UI容器运行正常"
    if curl -s http://localhost:8080 | grep -q "registry"; then
        echo "✅ Web UI访问正常"
    else
        echo "⚠️  Web UI访问可能有问题"
    fi
else
    echo "⚠️  未检测到Registry Web UI容器"
fi

# 9. 测试镜像推送拉取
echo "9. 测试镜像推送拉取..."
test_image="hello-world"
test_tag="*************:5000/test-hello-world"

# 拉取测试镜像
if docker pull $test_image &> /dev/null; then
    echo "✅ 测试镜像拉取成功"

    # 打标签
    docker tag $test_image $test_tag

    # 推送到私有仓库
    if docker push $test_tag &> /dev/null; then
        echo "✅ 镜像推送成功"

        # 删除本地镜像
        docker rmi $test_tag &> /dev/null

        # 从私有仓库拉取
        if docker pull $test_tag &> /dev/null; then
            echo "✅ 镜像拉取成功"

            # 清理测试镜像
            docker rmi $test_tag &> /dev/null
        else
            echo "❌ 镜像拉取失败"
        fi
    else
        echo "❌ 镜像推送失败"
        echo "可能的原因:"
        echo "- Registry服务异常"
        echo "- 网络连接问题"
        echo "- 认证配置问题"
    fi
else
    echo "⚠️  无法拉取测试镜像，跳过推送拉取测试"
fi

# 10. 检查Registry目录结构
echo "10. 检查Registry目录结构..."
echo "Registry目录结构:"
ls -la /k8s/registry/

# 11. 显示Registry信息
echo "11. Registry信息汇总..."
echo "=== Registry信息 ==="
echo "HTTP访问地址: http://*************:5000"
if [ -f "/k8s/registry/certs/registry.crt" ]; then
    echo "HTTPS访问地址: https://*************"
fi
if docker ps | grep -q registry-ui; then
    echo "Web UI地址: http://*************:8080"
fi

# 显示镜像列表
echo ""
echo "=== 当前镜像列表 ==="
curl -s http://*************:5000/v2/_catalog | python -m json.tool 2>/dev/null || echo "无法获取镜像列表"

echo ""
echo "=== Registry部署验证完成 ==="
echo "🎉 验证通过，Registry部署成功！"

echo ""
echo "=== 下一步操作 ==="
echo "1. 配置客户端机器的证书信任"
echo "2. 在客户端测试镜像推送拉取"
echo "3. 配置K8s集群使用私有仓库"
echo "4. 设置定期备份策略"</code></pre>

                    <h3><i class="fas fa-play"></i> 12.2 运行验证脚本</h3>
                    <pre><code># 保存验证脚本
cat > /k8s/registry/verify-registry.sh << 'EOF'
# 将上面的脚本内容粘贴到这里
EOF

# 设置执行权限
chmod +x /k8s/registry/verify-registry.sh

# 运行验证脚本
/k8s/registry/verify-registry.sh</code></pre>

                    <h3><i class="fas fa-laptop"></i> 12.3 客户端验证脚本</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <p>在客户端机器上运行此脚本验证Registry访问：</p>

                    <pre><code>#!/bin/bash
# Registry客户端验证脚本

echo "=== Registry客户端验证开始 ==="

REGISTRY_HOST="*************:5000"

# 1. 检查网络连通性
echo "1. 检查网络连通性..."
if ping -c 1 ************* &> /dev/null; then
    echo "✅ 网络连通正常"
else
    echo "❌ 网络连通失败"
    exit 1
fi

# 2. 检查Registry API访问
echo "2. 检查Registry API访问..."
if curl -s http://$REGISTRY_HOST/v2/ | grep -q "{}"; then
    echo "✅ Registry API访问正常"
else
    echo "❌ Registry API访问失败"
    exit 1
fi

# 3. 检查Docker配置
echo "3. 检查Docker配置..."
if [ -d "/etc/docker/certs.d/$REGISTRY_HOST" ]; then
    echo "✅ Docker证书目录已配置"
else
    echo "⚠️  Docker证书目录未配置"
fi

# 4. 测试镜像操作
echo "4. 测试镜像操作..."
test_image="hello-world"
test_tag="$REGISTRY_HOST/client-test-hello-world"

if docker pull $test_image &> /dev/null; then
    docker tag $test_image $test_tag

    if docker push $test_tag &> /dev/null; then
        echo "✅ 镜像推送成功"
        docker rmi $test_tag &> /dev/null

        if docker pull $test_tag &> /dev/null; then
            echo "✅ 镜像拉取成功"
            docker rmi $test_tag &> /dev/null
        else
            echo "❌ 镜像拉取失败"
        fi
    else
        echo "❌ 镜像推送失败"
    fi
else
    echo "⚠️  无法拉取测试镜像"
fi

echo "=== Registry客户端验证完成 ==="</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 验证成功标志：</strong>
                        <ul>
                            <li>所有检查项显示 ✅ 通过</li>
                            <li>Registry API响应正常</li>
                            <li>镜像推送拉取功能正常</li>
                            <li>客户端可以正常访问Registry</li>
                        </ul>
                    </div>
                </section>

                <section id="summary">
                    <h2><span class="step-number">13</span>总结</h2>

                    <h3><i class="fas fa-check-circle"></i> 12.1 部署总结</h3>
                    <p>通过本教程，我们成功搭建了一个完整的私有Docker镜像仓库，包括：</p>

                    <div class="success-box">
                        <strong><i class="fas fa-trophy"></i> 已完成的功能：</strong>
                        <ul>
                            <li><strong><i class="fas fa-server"></i> Registry服务：</strong>基于Docker Registry 2.0的核心镜像存储服务
                            </li>
                            <li><strong><i class="fas fa-lock"></i> HTTPS支持：</strong>通过Nginx反向代理提供SSL加密传输</li>
                            <li><strong><i class="fas fa-user-shield"></i> 用户认证：</strong>基于htpasswd的用户身份验证机制</li>
                            <li><strong><i class="fas fa-desktop"></i> Web界面：</strong>直观的Web管理界面用于镜像管理</li>
                            <li><strong><i class="fas fa-database"></i> 备份恢复：</strong>完整的数据备份和恢复方案</li>
                            <li><strong><i class="fas fa-chart-line"></i> 监控维护：</strong>健康检查和自动化维护脚本</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-lightbulb"></i> 12.2 最佳实践</h3>
                    <div class="info-box">
                        <strong><i class="fas fa-star"></i> 生产环境建议：</strong>
                        <ul>
                            <li><strong>安全性：</strong>使用权威CA签发的SSL证书，定期更新密码</li>
                            <li><strong>高可用：</strong>部署多个Registry实例，使用负载均衡</li>
                            <li><strong>存储：</strong>使用高性能存储，定期清理无用镜像</li>
                            <li><strong>监控：</strong>集成Prometheus等监控系统</li>
                            <li><strong>备份：</strong>定期备份数据，测试恢复流程</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-road"></i> 12.3 扩展方向</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-plus"></i> 扩展功能</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                            <th><i class="fas fa-star"></i> 优先级</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-shield-alt"></i> 镜像扫描</td>
                            <td>集成Clair等安全扫描工具</td>
                            <td>高</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-sync-alt"></i> 镜像同步</td>
                            <td>与其他Registry的双向同步</td>
                            <td>中</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-users"></i> LDAP集成</td>
                            <td>与企业LDAP系统集成认证</td>
                            <td>中</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-cloud"></i> 对象存储</td>
                            <td>使用S3等对象存储后端</td>
                            <td>低</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-phone"></i> 12.4 技术支持</h3>
                    <div class="warning-box">
                        <strong><i class="fas fa-question-circle"></i> 遇到问题？</strong>
                        <ul>
                            <li>查看官方文档：<a href="https://docs.docker.com/registry/" target="_blank">Docker Registry文档</a>
                            </li>
                            <li>参考故障排查章节</li>
                            <li>检查系统日志和容器日志</li>
                            <li>在社区论坛寻求帮助</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <strong><i class="fas fa-graduation-cap"></i>
                            恭喜！</strong>您已经成功搭建了一个功能完整的私有Docker镜像仓库。现在可以开始在您的开发和生产环境中使用它了。
                    </div>
                </section>
            </div>
        </div>
    </div>

    <!-- 返回顶部按钮 -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // 移动端菜单切换
        document.getElementById('mobileMenuBtn').addEventListener('click', function () {
            document.getElementById('sidebar').classList.toggle('active');
        });

        // 返回顶部功能
        window.addEventListener('scroll', function () {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'flex';
            } else {
                backToTop.style.display = 'none';
            }
        });

        document.getElementById('backToTop').addEventListener('click', function (e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 侧边栏导航高亮
        window.addEventListener('scroll', function () {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.sidebar a[href^="#"]');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // 平滑滚动
        document.querySelectorAll('.sidebar a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }

                // 移动端关闭菜单
                if (window.innerWidth <= 768) {
                    document.getElementById('sidebar').classList.remove('active');
                }
            });
        });
    </script>
</body>

</html></code></pre>