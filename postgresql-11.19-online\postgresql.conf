# PostgreSQL 11.19 配置文件

# 连接和认证设置
listen_addresses = '*'
port = 3433
max_connections = 200

# 内存设置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# 日志设置
logging_collector = on
log_directory = '/var/log/postgresql'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_messages = info
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 0

# 性能设置
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# 区域设置
datestyle = 'iso, mdy'
timezone = 'UTC'
lc_messages = 'en_US.UTF-8'
lc_monetary = 'en_US.UTF-8'
lc_numeric = 'en_US.UTF-8'
lc_time = 'en_US.UTF-8'
default_text_search_config = 'pg_catalog.english'

# 大小写不敏感设置（通过扩展实现）
# 注意：PostgreSQL原生不支持大小写不敏感，但可以通过以下方式实现：
# 1. 使用citext扩展
# 2. 使用ILIKE操作符
# 3. 使用lower()函数

# 其他设置
dynamic_shared_memory_type = posix
max_wal_size = 1GB
min_wal_size = 80MB
