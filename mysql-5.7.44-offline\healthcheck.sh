#!/bin/bash

# MySQL健康检查脚本
# 用于Docker容器健康检查

set -e

# 等待一段时间让MySQL完全启动
sleep 2

# 检查MySQL进程是否运行
if ! pgrep mysqld > /dev/null 2>&1; then
    echo "MySQL进程未运行"
    exit 1
fi

# 检查socket文件是否存在
if [ ! -S "/usr/local/mysql/mysql.sock" ]; then
    echo "MySQL socket文件不存在"
    exit 1
fi

# 检查MySQL是否可以连接（使用socket连接）
if ! /usr/local/mysql/bin/mysqladmin ping --socket=/usr/local/mysql/mysql.sock --silent --connect-timeout=5 > /dev/null 2>&1; then
    echo "MySQL无法通过socket连接"
    exit 1
fi

# 检查MySQL是否可以执行查询
if ! /usr/local/mysql/bin/mysql --socket=/usr/local/mysql/mysql.sock -uroot -proot -e "SELECT 1;" > /dev/null 2>&1; then
    echo "MySQL无法执行查询"
    exit 1
fi

echo "MySQL健康检查通过"
exit 0
