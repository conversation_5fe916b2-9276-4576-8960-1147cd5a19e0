<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nginx进阶教程 - 深入掌握高级功能</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 12px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 欢迎框样式 */
        .welcome-box {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .welcome-box::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 8s ease-in-out infinite;
        }

        .welcome-box h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .welcome-box p {
            font-size: 16px;
            line-height: 1.8;
            color: var(--text-primary);
            position: relative;
            z-index: 1;
        }

        /* 学习路径样式 */
        .learning-path {
            margin-top: 30px;
            position: relative;
            z-index: 1;
        }

        .learning-path h4 {
            color: var(--secondary-color);
            margin-bottom: 25px;
        }

        .path-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .path-step {
            background: var(--light-surface);
            border-radius: 15px;
            padding: 25px;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .path-step:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .step-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 20px;
            margin-bottom: 15px;
            box-shadow: var(--shadow-md);
        }

        .step-content h5 {
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .step-content p {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.6;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box,
        .advanced-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover,
        .advanced-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        .advanced-box {
            background: linear-gradient(135deg, rgba(128, 90, 213, 0.1) 0%, rgba(128, 90, 213, 0.05) 100%);
            border-left-color: #805ad5;
            color: #553c9a;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: var(--light-surface);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        th,
        td {
            padding: 18px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-decoration: none;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            z-index: 999;
            font-size: 20px;
        }

        .back-to-top:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .content-wrapper {
                padding: 30px;
            }

            .container {
                padding: 40px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
                border-radius: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 16px;
                margin-right: 15px;
            }

            .path-steps {
                grid-template-columns: 1fr;
            }

            pre {
                font-size: 12px;
                padding: 20px;
            }

            table {
                font-size: 14px;
            }

            th,
            td {
                padding: 12px;
            }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
        }
    </style>
</head>

<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-rocket"></i> Nginx进阶教程</h2>
            <p>深入掌握高级功能</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#advanced-modules"><i class="fas fa-puzzle-piece"></i>1. 高级模块</a></li>
                <li><a href="#lua-scripting"><i class="fas fa-code"></i>2. Lua脚本编程</a></li>
                <li><a href="#custom-modules"><i class="fas fa-cogs"></i>3. 自定义模块开发</a></li>
                <li><a href="#microservices"><i class="fas fa-cubes"></i>4. 微服务架构</a></li>
                <li><a href="#api-gateway"><i class="fas fa-gateway"></i>5. API网关</a></li>
                <li><a href="#caching-strategies"><i class="fas fa-memory"></i>6. 缓存策略</a></li>
                <li><a href="#streaming"><i class="fas fa-stream"></i>7. 流媒体服务</a></li>
                <li><a href="#containerization"><i class="fab fa-docker"></i>8. 容器化部署</a></li>
                <li><a href="#kubernetes"><i class="fas fa-dharmachakra"></i>9. Kubernetes集成</a></li>
                <li><a href="#automation"><i class="fas fa-robot"></i>10. 自动化运维</a></li>
                <li><a href="#monitoring-advanced"><i class="fas fa-chart-area"></i>11. 高级监控</a></li>
                <li><a href="#best-practices"><i class="fas fa-star"></i>12. 最佳实践</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-rocket"></i> Nginx进阶教程</h1>

                <div class="welcome-box">
                    <h3><i class="fas fa-rocket"></i> 欢迎进入Nginx高级世界！</h3>
                    <p>这是一份专为有一定Nginx基础的开发者和运维工程师准备的进阶教程。我们将深入探讨Nginx的高级功能、企业级应用场景和最佳实践。通过学习本教程，你将掌握构建高性能、高可用、可扩展的Web架构所需的核心技能。
                    </p>

                    <div class="learning-path">
                        <h4><i class="fas fa-route"></i> 进阶学习路径</h4>
                        <div class="path-steps">
                            <div class="path-step">
                                <div class="step-icon">1</div>
                                <div class="step-content">
                                    <h5>模块扩展</h5>
                                    <p>掌握高级模块和Lua脚本编程</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">2</div>
                                <div class="step-content">
                                    <h5>架构设计</h5>
                                    <p>学习微服务和API网关架构</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">3</div>
                                <div class="step-content">
                                    <h5>容器化</h5>
                                    <p>掌握Docker和Kubernetes部署</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">4</div>
                                <div class="step-content">
                                    <h5>运维自动化</h5>
                                    <p>实现监控、部署和运维自动化</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <section id="advanced-modules">
                    <h2><span class="step-number">1</span>高级模块</h2>

                    <div class="advanced-box">
                        <h3><i class="fas fa-info-circle"></i> 模块系统概述</h3>
                        <p>Nginx的强大之处在于其模块化架构。除了核心模块外，还有大量第三方模块可以扩展功能。掌握这些高级模块是成为Nginx专家的必经之路。</p>
                    </div>

                    <h3><i class="fas fa-puzzle-piece"></i> 核心高级模块</h3>

                    <h4><i class="fas fa-image"></i> 1. Image Filter模块</h4>
                    <div class="info-box">
                        <p>动态处理图片，实现缩放、裁剪、水印等功能：</p>
                        <pre><code># 编译时需要添加模块
./configure --with-http_image_filter_module

# 配置示例
location ~* \.(jpg|jpeg|png|gif)$ {
    # 设置图片处理缓存
    image_filter_buffer 10M;
    image_filter_jpeg_quality 95;

    # 根据参数动态调整图片大小
    set $width $arg_w;
    set $height $arg_h;

    if ($width != "") {
        image_filter resize $width $height;
    }

    # 添加水印
    if ($arg_watermark = "1") {
        image_filter_transparency on;
    }

    # 缓存处理后的图片
    expires 30d;
    add_header Cache-Control "public, immutable";
}</code></pre>
                        <p><strong>使用示例：</strong></p>
                        <ul>
                            <li><code>http://example.com/image.jpg?w=300&h=200</code> - 调整为300x200</li>
                            <li><code>http://example.com/image.jpg?watermark=1</code> - 添加水印</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-chart-line"></i> 2. Real-time Statistics模块</h4>
                    <div class="success-box">
                        <p>实时监控Nginx状态和性能指标：</p>
                        <pre><code># 安装nginx-module-vts
# Ubuntu: apt install nginx-module-vts
# 或编译: --add-module=nginx-module-vts

# 配置
http {
    vhost_traffic_status_zone;

    server {
        listen 80;

        # 状态页面
        location /status {
            vhost_traffic_status_display;
            vhost_traffic_status_display_format html;

            # 安全限制
            allow ***********/24;
            deny all;
        }

        # API格式状态
        location /status/format/json {
            vhost_traffic_status_display;
            vhost_traffic_status_display_format json;
        }
    }
}</code></pre>
                        <p><strong>监控指标包括：</strong></p>
                        <ul>
                            <li>请求数量和速率</li>
                            <li>响应时间分布</li>
                            <li>状态码统计</li>
                            <li>带宽使用情况</li>
                            <li>上游服务器状态</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-shield-alt"></i> 3. ModSecurity WAF模块</h4>
                    <div class="warning-box">
                        <p>集成Web应用防火墙，提供高级安全防护：</p>
                        <pre><code># 安装ModSecurity
# Ubuntu: apt install libmodsecurity3 nginx-module-modsecurity

# 配置
load_module modules/ngx_http_modsecurity_module.so;

http {
    server {
        listen 80;

        # 启用ModSecurity
        location / {
            modsecurity on;
            modsecurity_rules_file /etc/nginx/modsec/main.conf;

            proxy_pass http://backend;
        }
    }
}

# /etc/nginx/modsec/main.conf
Include /etc/modsecurity/modsecurity.conf
Include /etc/modsecurity/crs-setup.conf
Include /etc/modsecurity/rules/*.conf

# 自定义规则示例
SecRule ARGS "@detectSQLi" \
    "id:1001,\
    phase:2,\
    block,\
    msg:'SQL Injection Attack Detected',\
    logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}'"</code></pre>
                    </div>

                    <h4><i class="fas fa-compress-arrows-alt"></i> 4. Brotli压缩模块</h4>
                    <div class="info-box">
                        <p>比Gzip更高效的压缩算法：</p>
                        <pre><code># 安装brotli模块
# 编译: --add-module=ngx_brotli

http {
    # 启用Brotli压缩
    brotli on;
    brotli_comp_level 6;
    brotli_min_length 1000;

    # 压缩文件类型
    brotli_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/json
        application/xml
        image/svg+xml;

    # 静态Brotli文件
    brotli_static on;

    # 与Gzip配合使用
    gzip on;
    gzip_vary on;

    server {
        location ~* \.(js|css)$ {
            # 优先使用预压缩的.br文件
            try_files $uri$brotli_ext $uri =404;
        }
    }
}</code></pre>
                        <p><strong>性能对比：</strong></p>
                        <table>
                            <tr>
                                <th>压缩算法</th>
                                <th>压缩率</th>
                                <th>CPU使用</th>
                                <th>适用场景</th>
                            </tr>
                            <tr>
                                <td>Gzip</td>
                                <td>70-80%</td>
                                <td>中等</td>
                                <td>通用场景</td>
                            </tr>
                            <tr>
                                <td>Brotli</td>
                                <td>75-85%</td>
                                <td>较高</td>
                                <td>现代浏览器</td>
                            </tr>
                        </table>
                    </div>
                </section>

                <section id="lua-scripting">
                    <h2><span class="step-number">2</span>Lua脚本编程</h2>

                    <div class="advanced-box">
                        <h3><i class="fas fa-code"></i> OpenResty与Lua</h3>
                        <p>OpenResty是基于Nginx和LuaJIT的Web平台，允许使用Lua脚本扩展Nginx功能。这为Nginx带来了无限的可能性，可以实现复杂的业务逻辑、动态配置和高性能计算。
                        </p>
                    </div>

                    <h3><i class="fas fa-download"></i> 安装OpenResty</h3>
                    <div class="info-box">
                        <pre><code># Ubuntu/Debian安装
wget -qO - https://openresty.org/package/pubkey.gpg | sudo apt-key add -
echo "deb http://openresty.org/package/ubuntu $(lsb_release -sc) main" | sudo tee /etc/apt/sources.list.d/openresty.list
sudo apt update
sudo apt install openresty

# CentOS/RHEL安装
sudo yum install yum-utils
sudo yum-config-manager --add-repo https://openresty.org/package/centos/openresty.repo
sudo yum install openresty

# 验证安装
openresty -v</code></pre>
                    </div>

                    <h3><i class="fas fa-play"></i> Lua脚本基础</h3>

                    <h4><i class="fas fa-code"></i> 1. Hello World示例</h4>
                    <div class="success-box">
                        <pre><code>server {
    listen 80;

    location /hello {
        content_by_lua_block {
            ngx.say("Hello, World from Lua!")
            ngx.say("Current time: ", ngx.time())
            ngx.say("Request method: ", ngx.var.request_method)
        }
    }

    location /json {
        content_by_lua_block {
            local cjson = require "cjson"
            local data = {
                message = "Hello from Lua",
                timestamp = ngx.time(),
                client_ip = ngx.var.remote_addr
            }
            ngx.header.content_type = "application/json"
            ngx.say(cjson.encode(data))
        }
    }
}</code></pre>
                    </div>

                    <h4><i class="fas fa-database"></i> 2. 数据库集成</h4>
                    <div class="warning-box">
                        <p>直接在Nginx中访问数据库，实现高性能的数据查询：</p>
                        <pre><code># 安装lua-resty-mysql
# luarocks install lua-resty-mysql

server {
    location /api/user {
        content_by_lua_block {
            local mysql = require "resty.mysql"
            local cjson = require "cjson"

            -- 连接数据库
            local db, err = mysql:new()
            if not db then
                ngx.log(ngx.ERR, "failed to instantiate mysql: ", err)
                return ngx.exit(500)
            end

            local ok, err = db:connect{
                host = "127.0.0.1",
                port = 3306,
                database = "myapp",
                user = "nginx",
                password = "password",
                charset = "utf8",
                max_packet_size = 1024 * 1024,
            }

            if not ok then
                ngx.log(ngx.ERR, "failed to connect: ", err)
                return ngx.exit(500)
            end

            -- 执行查询
            local user_id = ngx.var.arg_id
            local res, err = db:query("SELECT * FROM users WHERE id = " .. user_id)

            if not res then
                ngx.log(ngx.ERR, "bad result: ", err)
                return ngx.exit(500)
            end

            -- 返回JSON结果
            ngx.header.content_type = "application/json"
            ngx.say(cjson.encode(res[1]))

            -- 关闭连接
            db:close()
        }
    }
}</code></pre>
                    </div>

                    <h4><i class="fas fa-memory"></i> 3. Redis缓存集成</h4>
                    <div class="info-box">
                        <p>使用Redis作为高速缓存层：</p>
                        <pre><code># 安装lua-resty-redis
# luarocks install lua-resty-redis

server {
    location /api/cache {
        content_by_lua_block {
            local redis = require "resty.redis"
            local cjson = require "cjson"

            local red = redis:new()
            red:set_timeouts(1000, 1000, 1000) -- 1秒超时

            -- 连接Redis
            local ok, err = red:connect("127.0.0.1", 6379)
            if not ok then
                ngx.log(ngx.ERR, "failed to connect to redis: ", err)
                return ngx.exit(500)
            end

            local cache_key = "user:" .. ngx.var.arg_id

            -- 尝试从缓存获取
            local cached_data, err = red:get(cache_key)

            if cached_data and cached_data ~= ngx.null then
                -- 缓存命中
                ngx.header.content_type = "application/json"
                ngx.header["X-Cache"] = "HIT"
                ngx.say(cached_data)
            else
                -- 缓存未命中，从数据库获取
                local user_data = {
                    id = ngx.var.arg_id,
                    name = "John Doe",
                    email = "<EMAIL>"
                }

                local json_data = cjson.encode(user_data)

                -- 存入缓存，过期时间300秒
                red:setex(cache_key, 300, json_data)

                ngx.header.content_type = "application/json"
                ngx.header["X-Cache"] = "MISS"
                ngx.say(json_data)
            end

            -- 连接池回收
            red:set_keepalive(10000, 100)
        }
    }
}</code></pre>
                    </div>

                    <h4><i class="fas fa-key"></i> 4. JWT认证</h4>
                    <div class="success-box">
                        <p>实现JWT令牌验证：</p>
                        <pre><code># 安装lua-resty-jwt
# luarocks install lua-resty-jwt

server {
    location /api/protected {
        access_by_lua_block {
            local jwt = require "resty.jwt"

            -- 获取Authorization头部
            local auth_header = ngx.var.http_authorization
            if not auth_header then
                ngx.log(ngx.ERR, "No Authorization header")
                ngx.status = 401
                ngx.say("Unauthorized")
                ngx.exit(401)
            end

            -- 提取token
            local token = auth_header:match("Bearer%s+(.+)")
            if not token then
                ngx.log(ngx.ERR, "No token found")
                ngx.status = 401
                ngx.say("Unauthorized")
                ngx.exit(401)
            end

            -- 验证JWT
            local jwt_obj = jwt:verify("your-secret-key", token)
            if not jwt_obj.valid then
                ngx.log(ngx.ERR, "Invalid token: ", jwt_obj.reason)
                ngx.status = 401
                ngx.say("Invalid token")
                ngx.exit(401)
            end

            -- 设置用户信息到变量
            ngx.var.user_id = jwt_obj.payload.user_id
            ngx.var.user_role = jwt_obj.payload.role
        }

        content_by_lua_block {
            ngx.say("Welcome, user ", ngx.var.user_id)
            ngx.say("Your role: ", ngx.var.user_role)
        }
    }
}</code></pre>
                    </div>
                </section>

                <section id="custom-modules">
                    <h2><span class="step-number">3</span>自定义模块开发</h2>

                    <div class="advanced-box">
                        <h3><i class="fas fa-cogs"></i> 模块开发概述</h3>
                        <p>当现有模块无法满足特殊需求时，可以开发自定义Nginx模块。这需要C语言编程技能，但能够实现最高性能的功能扩展。</p>
                    </div>

                    <h3><i class="fas fa-file-code"></i> 模块结构</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-folder"></i> 基本文件结构</h4>
                        <pre><code>ngx_http_hello_module/
├── config                    # 模块配置文件
├── ngx_http_hello_module.c   # 模块源代码
└── README.md                 # 说明文档

# config文件内容
ngx_addon_name=ngx_http_hello_module
HTTP_MODULES="$HTTP_MODULES ngx_http_hello_module"
NGX_ADDON_SRCS="$NGX_ADDON_SRCS $ngx_addon_dir/ngx_http_hello_module.c"</code></pre>
                    </div>

                    <h4><i class="fas fa-code"></i> 1. 简单Hello模块</h4>
                    <div class="warning-box">
                        <p>创建一个简单的Hello World模块：</p>
                        <pre><code>// ngx_http_hello_module.c
#include &lt;ngx_config.h&gt;
#include &lt;ngx_core.h&gt;
#include &lt;ngx_http.h&gt;

// 模块配置结构
typedef struct {
    ngx_str_t hello_string;
} ngx_http_hello_conf_t;

// 函数声明
static ngx_int_t ngx_http_hello_handler(ngx_http_request_t *r);
static void *ngx_http_hello_create_conf(ngx_conf_t *cf);
static char *ngx_http_hello_merge_conf(ngx_conf_t *cf, void *parent, void *child);
static char *ngx_http_hello(ngx_conf_t *cf, ngx_command_t *cmd, void *conf);

// 指令定义
static ngx_command_t ngx_http_hello_commands[] = {
    {
        ngx_string("hello_string"),
        NGX_HTTP_LOC_CONF|NGX_CONF_TAKE1,
        ngx_http_hello,
        NGX_HTTP_LOC_CONF_OFFSET,
        offsetof(ngx_http_hello_conf_t, hello_string),
        NULL
    },
    ngx_null_command
};

// 模块上下文
static ngx_http_module_t ngx_http_hello_module_ctx = {
    NULL,                          /* preconfiguration */
    NULL,                          /* postconfiguration */
    NULL,                          /* create main configuration */
    NULL,                          /* init main configuration */
    NULL,                          /* create server configuration */
    NULL,                          /* merge server configuration */
    ngx_http_hello_create_conf,    /* create location configuration */
    ngx_http_hello_merge_conf      /* merge location configuration */
};

// 模块定义
ngx_module_t ngx_http_hello_module = {
    NGX_MODULE_V1,
    &ngx_http_hello_module_ctx,    /* module context */
    ngx_http_hello_commands,       /* module directives */
    NGX_HTTP_MODULE,               /* module type */
    NULL,                          /* init master */
    NULL,                          /* init module */
    NULL,                          /* init process */
    NULL,                          /* init thread */
    NULL,                          /* exit thread */
    NULL,                          /* exit process */
    NULL,                          /* exit master */
    NGX_MODULE_V1_PADDING
};</code></pre>
                    </div>

                    <h4><i class="fas fa-hammer"></i> 2. 编译和安装</h4>
                    <div class="success-box">
                        <pre><code># 编译Nginx时添加模块
./configure --add-module=/path/to/ngx_http_hello_module
make && make install

# 或者编译为动态模块
./configure --add-dynamic-module=/path/to/ngx_http_hello_module
make modules

# 在nginx.conf中加载动态模块
load_module modules/ngx_http_hello_module.so;

# 使用模块
server {
    location /hello {
        hello_string "Hello from custom module!";
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-tools"></i> 高级模块功能</h3>

                    <h4><i class="fas fa-filter"></i> 1. 过滤器模块</h4>
                    <div class="info-box">
                        <p>创建内容过滤器，修改响应内容：</p>
                        <pre><code>// 注册过滤器
static ngx_int_t
ngx_http_hello_filter_init(ngx_conf_t *cf)
{
    ngx_http_next_header_filter = ngx_http_top_header_filter;
    ngx_http_top_header_filter = ngx_http_hello_header_filter;

    ngx_http_next_body_filter = ngx_http_top_body_filter;
    ngx_http_top_body_filter = ngx_http_hello_body_filter;

    return NGX_OK;
}

// 头部过滤器
static ngx_int_t
ngx_http_hello_header_filter(ngx_http_request_t *r)
{
    // 添加自定义头部
    ngx_table_elt_t *h = ngx_list_push(&r->headers_out.headers);
    if (h == NULL) {
        return NGX_ERROR;
    }

    h->hash = 1;
    ngx_str_set(&h->key, "X-Hello-Module");
    ngx_str_set(&h->value, "Active");

    return ngx_http_next_header_filter(r);
}

// 内容过滤器
static ngx_int_t
ngx_http_hello_body_filter(ngx_http_request_t *r, ngx_chain_t *in)
{
    // 在响应内容前添加文本
    ngx_buf_t *b;
    ngx_chain_t *cl;

    cl = ngx_alloc_chain_link(r->pool);
    if (cl == NULL) {
        return NGX_ERROR;
    }

    b = ngx_create_temp_buf(r->pool, sizeof("<!-- Hello Module -->") - 1);
    if (b == NULL) {
        return NGX_ERROR;
    }

    b->last = ngx_cpymem(b->last, "<!-- Hello Module -->",
                         sizeof("<!-- Hello Module -->") - 1);

    cl->buf = b;
    cl->next = in;

    return ngx_http_next_body_filter(r, cl);
}</code></pre>
                    </div>
                </section>

                <section id="microservices">
                    <h2><span class="step-number">4</span>微服务架构</h2>

                    <div class="advanced-box">
                        <h3><i class="fas fa-cubes"></i> 微服务与Nginx</h3>
                        <p>在微服务架构中，Nginx扮演着关键角色：服务网关、负载均衡器、服务发现代理。它帮助管理服务间通信，提供统一的入口点，并处理横切关注点如认证、限流、监控等。</p>
                    </div>

                    <h3><i class="fas fa-sitemap"></i> 微服务架构设计</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-network-wired"></i> 典型架构图</h4>
                        <pre><code>┌─────────────┐    ┌─────────────────────────────────────┐
│   客户端     │───▶│              Nginx                  │
│ (Web/Mobile) │    │         (API Gateway)               │
└─────────────┘    └─────────────────┬───────────────────┘
                                     │
                   ┌─────────────────┼─────────────────┐
                   │                 │                 │
                   ▼                 ▼                 ▼
            ┌─────────────┐  ┌─────────────┐  ┌─────────────┐
            │ 用户服务     │  │ 订单服务     │  │ 支付服务     │
            │ (Port 3001) │  │ (Port 3002) │  │ (Port 3003) │
            └─────────────┘  └─────────────┘  └─────────────┘
                   │                 │                 │
                   ▼                 ▼                 ▼
            ┌─────────────┐  ┌─────────────┐  ┌─────────────┐
            │   MySQL     │  │  PostgreSQL │  │    Redis    │
            │  Database   │  │  Database   │  │   Cache     │
            └─────────────┘  └─────────────┘  └─────────────┘</code></pre>
                    </div>

                    <h3><i class="fas fa-route"></i> 服务路由配置</h3>

                    <h4><i class="fas fa-map-signs"></i> 1. 基于路径的路由</h4>
                    <div class="success-box">
                        <pre><code>upstream user_service {
    server 127.0.0.1:3001 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3011 max_fails=3 fail_timeout=30s backup;
}

upstream order_service {
    server 127.0.0.1:3002 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3012 max_fails=3 fail_timeout=30s backup;
}

upstream payment_service {
    server 127.0.0.1:3003 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3013 max_fails=3 fail_timeout=30s backup;
}

server {
    listen 80;
    server_name api.example.com;

    # 用户服务路由
    location /api/v1/users {
        proxy_pass http://user_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Service-Name "user-service";

        # 健康检查
        proxy_next_upstream error timeout http_500 http_502 http_503;
        proxy_connect_timeout 5s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;
    }

    # 订单服务路由
    location /api/v1/orders {
        proxy_pass http://order_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Service-Name "order-service";
    }

    # 支付服务路由
    location /api/v1/payments {
        proxy_pass http://payment_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Service-Name "payment-service";
    }
}</code></pre>
                    </div>

                    <h4><i class="fas fa-tags"></i> 2. 基于头部的路由</h4>
                    <div class="warning-box">
                        <p>根据请求头部路由到不同版本的服务：</p>
                        <pre><code>upstream user_service_v1 {
    server 127.0.0.1:3001;
}

upstream user_service_v2 {
    server 127.0.0.1:3101;
}

server {
    listen 80;

    location /api/users {
        # 默认路由到v1
        set $upstream user_service_v1;

        # 根据API版本头部选择服务
        if ($http_api_version = "v2") {
            set $upstream user_service_v2;
        }

        # 根据用户类型路由
        if ($http_user_type = "premium") {
            set $upstream user_service_v2;
        }

        proxy_pass http://$upstream;
        proxy_set_header Host $host;
        proxy_set_header X-API-Version $http_api_version;
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-search"></i> 服务发现</h3>

                    <h4><i class="fas fa-sync"></i> 1. 动态上游配置</h4>
                    <div class="info-box">
                        <p>使用Lua脚本实现动态服务发现：</p>
                        <pre><code># 安装lua-resty-consul
# luarocks install lua-resty-consul

init_by_lua_block {
    local consul = require "resty.consul"

    -- 全局服务注册表
    services = {}

    -- 定期更新服务列表
    local function update_services()
        local c = consul:new({
            host = "127.0.0.1",
            port = 8500
        })

        -- 获取健康的服务实例
        local res, err = c:get_healthy_services("user-service")
        if res then
            services["user-service"] = res
        end

        local res, err = c:get_healthy_services("order-service")
        if res then
            services["order-service"] = res
        end
    end

    -- 启动定时器
    local ok, err = ngx.timer.every(30, update_services)
    if not ok then
        ngx.log(ngx.ERR, "failed to create timer: ", err)
    end
}

server {
    location /api/users {
        access_by_lua_block {
            local service_instances = services["user-service"]
            if not service_instances or #service_instances == 0 then
                ngx.log(ngx.ERR, "No healthy user-service instances")
                return ngx.exit(503)
            end

            -- 简单轮询选择实例
            local index = (ngx.time() % #service_instances) + 1
            local instance = service_instances[index]

            ngx.var.backend = instance.Service.Address .. ":" .. instance.Service.Port
        }

        proxy_pass http://$backend;
    }
}</code></pre>
                    </div>

                    <h4><i class="fas fa-heartbeat"></i> 2. 健康检查</h4>
                    <div class="success-box">
                        <p>实现主动健康检查：</p>
                        <pre><code>upstream user_service {
    server 127.0.0.1:3001;
    server 127.0.0.1:3011;

    # 被动健康检查
    max_fails 3;
    fail_timeout 30s;
}

server {
    # 健康检查端点
    location /health/user-service {
        access_by_lua_block {
            local http = require "resty.http"
            local httpc = http.new()

            local res, err = httpc:request_uri("http://127.0.0.1:3001/health", {
                method = "GET",
                timeout = 5000,
            })

            if not res or res.status ~= 200 then
                ngx.status = 503
                ngx.say("Service unhealthy")
                return ngx.exit(503)
            end

            ngx.status = 200
            ngx.say("Service healthy")
        }
    }

    # 主动健康检查（使用外部脚本）
    location /api/users {
        proxy_pass http://user_service;

        # 自定义错误页面
        error_page 502 503 504 /service_unavailable;
    }

    location /service_unavailable {
        internal;
        return 503 '{"error": "Service temporarily unavailable"}';
        add_header Content-Type application/json;
    }
}</code></pre>
                    </div>
                </section>

                <section id="api-gateway">
                    <h2><span class="step-number">5</span>API网关</h2>

                    <div class="advanced-box">
                        <h3><i class="fas fa-gateway"></i> API网关功能</h3>
                        <p>API网关是微服务架构的核心组件，提供统一的API入口。它处理认证、授权、限流、监控、协议转换等横切关注点，让后端服务专注于业务逻辑。</p>
                    </div>

                    <h3><i class="fas fa-key"></i> 认证与授权</h3>

                    <h4><i class="fas fa-shield-alt"></i> 1. API Key认证</h4>
                    <div class="info-box">
                        <pre><code>map $http_x_api_key $api_client_name {
    default "";
    "abc123def456" "client_app_1";
    "xyz789uvw012" "client_app_2";
    "mno345pqr678" "client_app_3";
}

server {
    location /api/ {
        # 检查API Key
        if ($api_client_name = "") {
            return 401 '{"error": "Invalid or missing API key"}';
        }

        # 设置客户端信息
        proxy_set_header X-Client-Name $api_client_name;
        proxy_set_header X-API-Key $http_x_api_key;

        proxy_pass http://backend;
    }
}</code></pre>
                    </div>

                    <h4><i class="fas fa-user-check"></i> 2. OAuth2集成</h4>
                    <div class="warning-box">
                        <p>集成OAuth2认证服务：</p>
                        <pre><code>server {
    location /api/protected {
        auth_request /auth;

        # 传递认证信息到后端
        auth_request_set $user_id $upstream_http_x_user_id;
        auth_request_set $user_role $upstream_http_x_user_role;

        proxy_set_header X-User-ID $user_id;
        proxy_set_header X-User-Role $user_role;

        proxy_pass http://backend;
    }

    # 内部认证端点
    location = /auth {
        internal;

        proxy_pass http://auth_service/validate;
        proxy_pass_request_body off;
        proxy_set_header Content-Length "";
        proxy_set_header X-Original-URI $request_uri;
        proxy_set_header Authorization $http_authorization;
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-tachometer-alt"></i> 限流与熔断</h3>

                    <h4><i class="fas fa-stopwatch"></i> 1. 多级限流</h4>
                    <div class="success-box">
                        <pre><code>http {
    # 定义限流区域
    limit_req_zone $binary_remote_addr zone=global:10m rate=100r/s;
    limit_req_zone $api_client_name zone=client:10m rate=50r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/s;

    # 限制连接数
    limit_conn_zone $binary_remote_addr zone=conn_per_ip:10m;
    limit_conn_zone $api_client_name zone=conn_per_client:10m;
}

server {
    # 全局限流
    limit_req zone=global burst=200 nodelay;
    limit_conn conn_per_ip 20;

    location /api/ {
        # 客户端级别限流
        limit_req zone=client burst=100 nodelay;
        limit_conn conn_per_client 10;

        proxy_pass http://backend;
    }

    location /api/auth/login {
        # 登录接口特殊限流
        limit_req zone=login burst=10 nodelay;

        proxy_pass http://auth_service;
    }

    # 限流错误处理
    error_page 429 /rate_limit_exceeded;

    location /rate_limit_exceeded {
        internal;
        return 429 '{"error": "Rate limit exceeded", "retry_after": 60}';
        add_header Content-Type application/json;
        add_header Retry-After 60;
    }
}</code></pre>
                    </div>

                    <h4><i class="fas fa-bolt"></i> 2. 熔断器模式</h4>
                    <div class="danger-box">
                        <p>使用Lua实现熔断器：</p>
                        <pre><code>init_by_lua_block {
    -- 熔断器状态
    circuit_breakers = {}

    function get_circuit_breaker(service_name)
        if not circuit_breakers[service_name] then
            circuit_breakers[service_name] = {
                state = "CLOSED",  -- CLOSED, OPEN, HALF_OPEN
                failure_count = 0,
                last_failure_time = 0,
                failure_threshold = 5,
                timeout = 60  -- 60秒后尝试半开
            }
        end
        return circuit_breakers[service_name]
    end
}

server {
    location /api/users {
        access_by_lua_block {
            local cb = get_circuit_breaker("user-service")
            local now = ngx.time()

            -- 检查熔断器状态
            if cb.state == "OPEN" then
                if now - cb.last_failure_time < cb.timeout then
                    ngx.log(ngx.WARN, "Circuit breaker OPEN for user-service")
                    ngx.status = 503
                    ngx.say('{"error": "Service temporarily unavailable"}')
                    return ngx.exit(503)
                else
                    cb.state = "HALF_OPEN"
                end
            end
        }

        proxy_pass http://user_service;

        # 记录响应状态
        log_by_lua_block {
            local cb = get_circuit_breaker("user-service")

            if ngx.status >= 500 then
                cb.failure_count = cb.failure_count + 1
                cb.last_failure_time = ngx.time()

                if cb.failure_count >= cb.failure_threshold then
                    cb.state = "OPEN"
                    ngx.log(ngx.ERR, "Circuit breaker OPENED for user-service")
                end
            else
                if cb.state == "HALF_OPEN" then
                    cb.state = "CLOSED"
                    cb.failure_count = 0
                    ngx.log(ngx.INFO, "Circuit breaker CLOSED for user-service")
                end
            end
        }
    }
}</code></pre>
                    </div>
                </section>

                <section id="caching-strategies">
                    <h2><span class="step-number">6</span>缓存策略</h2>

                    <div class="advanced-box">
                        <h3><i class="fas fa-memory"></i> 多层缓存架构</h3>
                        <p>高性能Web应用需要多层缓存策略：浏览器缓存、CDN缓存、Nginx缓存、应用缓存、数据库缓存。合理的缓存策略可以显著提升性能并减少服务器负载。</p>
                    </div>

                    <h3><i class="fas fa-hdd"></i> Nginx缓存配置</h3>

                    <h4><i class="fas fa-server"></i> 1. 反向代理缓存</h4>
                    <div class="info-box">
                        <pre><code>http {
    # 定义缓存路径和配置
    proxy_cache_path /var/cache/nginx/api
                     levels=1:2
                     keys_zone=api_cache:100m
                     max_size=10g
                     inactive=60m
                     use_temp_path=off;

    proxy_cache_path /var/cache/nginx/static
                     levels=1:2
                     keys_zone=static_cache:50m
                     max_size=5g
                     inactive=30d;
}

server {
    location /api/ {
        proxy_cache api_cache;
        proxy_cache_key "$scheme$request_method$host$request_uri";

        # 缓存时间设置
        proxy_cache_valid 200 302 10m;
        proxy_cache_valid 404 1m;
        proxy_cache_valid any 5m;

        # 缓存控制
        proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
        proxy_cache_background_update on;
        proxy_cache_lock on;

        # 添加缓存状态头部
        add_header X-Cache-Status $upstream_cache_status;

        proxy_pass http://backend;
    }

    location ~* \.(jpg|jpeg|png|gif|css|js)$ {
        proxy_cache static_cache;
        proxy_cache_valid 200 30d;
        proxy_cache_valid 404 1h;

        # 忽略客户端缓存控制
        proxy_ignore_headers Cache-Control Expires;

        add_header X-Cache-Status $upstream_cache_status;
        expires 30d;

        proxy_pass http://static_backend;
    }
}</code></pre>
                    </div>

                    <h4><i class="fas fa-code"></i> 2. 智能缓存清理</h4>
                    <div class="warning-box">
                        <p>使用Lua实现智能缓存管理：</p>
                        <pre><code>server {
    # 缓存清理接口
    location /cache/purge {
        access_by_lua_block {
            -- 验证权限
            local auth_header = ngx.var.http_authorization
            if auth_header ~= "Bearer admin-token" then
                return ngx.exit(401)
            end

            local cache_key = ngx.var.arg_key
            local cache_zone = ngx.var.arg_zone or "api_cache"

            if not cache_key then
                ngx.status = 400
                ngx.say('{"error": "Missing cache key"}')
                return ngx.exit(400)
            end

            -- 清理缓存
            local success = ngx.shared[cache_zone]:delete(cache_key)

            if success then
                ngx.say('{"message": "Cache cleared successfully"}')
            else
                ngx.say('{"message": "Cache key not found"}')
            end
        }
    }

    # 批量缓存清理
    location /cache/purge/pattern {
        content_by_lua_block {
            local pattern = ngx.var.arg_pattern
            if not pattern then
                ngx.status = 400
                ngx.say('{"error": "Missing pattern"}')
                return
            end

            -- 这里需要使用外部工具如nginx-cache-purge
            local cmd = "find /var/cache/nginx -name '*" .. pattern .. "*' -delete"
            local handle = io.popen(cmd)
            local result = handle:read("*a")
            handle:close()

            ngx.say('{"message": "Pattern cache cleared"}')
        }
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-globe"></i> CDN集成</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-cloud"></i> CDN回源配置</h4>
                        <pre><code>server {
    listen 80;
    server_name origin.example.com;

    # CDN回源优化
    location / {
        # 设置适当的缓存头部
        expires 1h;
        add_header Cache-Control "public, max-age=3600";

        # 支持条件请求
        if_modified_since exact;

        # 启用ETag
        etag on;

        # 压缩传输
        gzip on;
        gzip_vary on;

        root /var/www/html;
    }

    # API接口的CDN配置
    location /api/ {
        # 短时间缓存
        expires 5m;
        add_header Cache-Control "public, max-age=300";

        # 添加CDN标识
        add_header X-Origin-Server $hostname;

        proxy_pass http://backend;
    }

    # 静态资源长时间缓存
    location ~* \.(jpg|jpeg|png|gif|css|js|woff|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, max-age=31536000, immutable";

        # 支持跨域
        add_header Access-Control-Allow-Origin "*";

        root /var/www/static;
    }
}</code></pre>
                    </div>
                </section>

                <section id="streaming">
                    <h2><span class="step-number">7</span>流媒体服务</h2>

                    <div class="advanced-box">
                        <h3><i class="fas fa-stream"></i> 流媒体架构</h3>
                        <p>Nginx可以作为强大的流媒体服务器，支持RTMP、HLS、DASH等协议。通过nginx-rtmp-module模块，可以构建完整的直播和点播系统。</p>
                    </div>

                    <h3><i class="fas fa-download"></i> 安装RTMP模块</h3>
                    <div class="info-box">
                        <pre><code># 下载nginx-rtmp-module
git clone https://github.com/arut/nginx-rtmp-module.git

# 编译Nginx时添加RTMP模块
./configure --add-module=./nginx-rtmp-module
make && make install

# 或使用预编译版本
# Ubuntu: apt install libnginx-mod-rtmp
# CentOS: yum install nginx-mod-http-rtmp</code></pre>
                    </div>

                    <h3><i class="fas fa-broadcast-tower"></i> 直播配置</h3>

                    <h4><i class="fas fa-video"></i> 1. RTMP直播服务器</h4>
                    <div class="success-box">
                        <pre><code># nginx.conf
load_module modules/ngx_rtmp_module.so;

events {
    worker_connections 1024;
}

# RTMP配置块
rtmp {
    server {
        listen 1935;
        chunk_size 4096;

        application live {
            live on;

            # 允许推流
            allow publish all;

            # 允许播放
            allow play all;

            # 录制配置
            record all;
            record_path /var/recordings;
            record_unique on;
            record_suffix .flv;

            # HLS输出
            hls on;
            hls_path /var/hls;
            hls_fragment 3;
            hls_playlist_length 60;

            # DASH输出
            dash on;
            dash_path /var/dash;
            dash_fragment 3;
            dash_playlist_length 60;

            # 推流认证
            on_publish http://localhost/auth;

            # 推流回调
            on_publish_done http://localhost/publish_done;
            on_play http://localhost/play;
            on_play_done http://localhost/play_done;
        }
    }
}

# HTTP配置
http {
    server {
        listen 80;

        # HLS播放
        location /hls {
            types {
                application/vnd.apple.mpegurl m3u8;
                video/mp2t ts;
            }
            root /var;
            add_header Cache-Control no-cache;
            add_header Access-Control-Allow-Origin *;
        }

        # DASH播放
        location /dash {
            root /var;
            add_header Cache-Control no-cache;
            add_header Access-Control-Allow-Origin *;
        }

        # 推流认证接口
        location /auth {
            return 200;
        }
    }
}</code></pre>
                    </div>

                    <h4><i class="fas fa-mobile-alt"></i> 2. 多码率自适应</h4>
                    <div class="warning-box">
                        <p>配置多码率输出，支持自适应播放：</p>
                        <pre><code>rtmp {
    server {
        listen 1935;

        application live {
            live on;

            # 转码配置
            exec ffmpeg -i rtmp://localhost/live/$name
                -c:v libx264 -b:v 1000k -s 1280x720 -f flv rtmp://localhost/hls/$name_720p
                -c:v libx264 -b:v 500k -s 854x480 -f flv rtmp://localhost/hls/$name_480p
                -c:v libx264 -b:v 200k -s 640x360 -f flv rtmp://localhost/hls/$name_360p;
        }

        application hls {
            live on;
            hls on;
            hls_path /var/hls;
            hls_fragment 3;
            hls_playlist_length 60;

            # 多码率HLS
            hls_variant _720p BANDWIDTH=1000000;
            hls_variant _480p BANDWIDTH=500000;
            hls_variant _360p BANDWIDTH=200000;
        }
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-play-circle"></i> 点播服务</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-film"></i> 视频点播配置</h4>
                        <pre><code>http {
    server {
        listen 80;

        # 视频文件目录
        location /videos/ {
            root /var/www;

            # MP4伪流支持
            mp4;
            mp4_buffer_size 1m;
            mp4_max_buffer_size 5m;

            # 支持范围请求
            add_header Accept-Ranges bytes;

            # 缓存配置
            expires 1d;
            add_header Cache-Control "public, no-transform";
        }

        # FLV伪流支持
        location /flv/ {
            root /var/www;
            flv;
        }

        # 视频缩略图
        location ~ /thumb/(.+)\.jpg$ {
            set $video_path /var/www/videos/$1.mp4;

            # 使用ffmpeg生成缩略图
            content_by_lua_block {
                local video = ngx.var.video_path
                local thumb_cmd = "ffmpeg -i " .. video .. " -ss 00:00:01 -vframes 1 -f image2pipe -"

                local handle = io.popen(thumb_cmd)
                local thumb_data = handle:read("*a")
                handle:close()

                ngx.header.content_type = "image/jpeg"
                ngx.print(thumb_data)
            }
        }
    }
}</code></pre>
                    </div>
                </section>

                <section id="containerization">
                    <h2><span class="step-number">8</span>容器化部署</h2>

                    <div class="advanced-box">
                        <h3><i class="fab fa-docker"></i> Docker化Nginx</h3>
                        <p>容器化部署是现代应用的标准做法。通过Docker，可以实现Nginx的快速部署、版本管理和环境一致性。</p>
                    </div>

                    <h3><i class="fas fa-file-code"></i> Dockerfile最佳实践</h3>

                    <h4><i class="fas fa-layer-group"></i> 1. 多阶段构建</h4>
                    <div class="info-box">
                        <pre><code># Dockerfile
# 构建阶段
FROM nginx:alpine as builder

# 安装构建依赖
RUN apk add --no-cache \
    gcc \
    libc-dev \
    make \
    openssl-dev \
    pcre-dev \
    zlib-dev \
    linux-headers \
    curl \
    gnupg \
    libxslt-dev \
    gd-dev \
    geoip-dev

# 下载Nginx源码
WORKDIR /usr/src
RUN curl -fSL https://nginx.org/download/nginx-1.24.0.tar.gz -o nginx.tar.gz \
    && tar -zxC . -f nginx.tar.gz \
    && rm nginx.tar.gz

# 下载第三方模块
RUN git clone https://github.com/openresty/lua-nginx-module.git \
    && git clone https://github.com/arut/nginx-rtmp-module.git

# 编译Nginx
WORKDIR /usr/src/nginx-1.24.0
RUN ./configure \
    --prefix=/etc/nginx \
    --sbin-path=/usr/sbin/nginx \
    --modules-path=/usr/lib/nginx/modules \
    --conf-path=/etc/nginx/nginx.conf \
    --with-http_ssl_module \
    --with-http_v2_module \
    --with-http_realip_module \
    --with-http_gzip_static_module \
    --with-http_secure_link_module \
    --with-http_stub_status_module \
    --add-module=../lua-nginx-module \
    --add-module=../nginx-rtmp-module \
    && make \
    && make install

# 运行阶段
FROM alpine:latest

# 安装运行时依赖
RUN apk add --no-cache \
    pcre \
    zlib \
    openssl \
    luajit

# 复制编译好的Nginx
COPY --from=builder /usr/sbin/nginx /usr/sbin/nginx
COPY --from=builder /etc/nginx /etc/nginx
COPY --from=builder /usr/lib/nginx /usr/lib/nginx

# 创建nginx用户
RUN addgroup -g 101 -S nginx \
    && adduser -S -D -H -u 101 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx

# 创建必要目录
RUN mkdir -p /var/log/nginx /var/cache/nginx /var/run \
    && chown -R nginx:nginx /var/log/nginx /var/cache/nginx /var/run

# 复制配置文件
COPY nginx.conf /etc/nginx/nginx.conf
COPY conf.d/ /etc/nginx/conf.d/

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

EXPOSE 80 443 1935

STOPSIGNAL SIGQUIT

CMD ["nginx", "-g", "daemon off;"]</code></pre>
                    </div>

                    <h4><i class="fas fa-cogs"></i> 2. 配置管理</h4>
                    <div class="warning-box">
                        <p>使用环境变量和配置模板：</p>
                        <pre><code># docker-compose.yml
version: '3.8'

services:
  nginx:
    build: .
    ports:
      - "80:80"
      - "443:443"
      - "1935:1935"
    environment:
      - NGINX_WORKER_PROCESSES=auto
      - NGINX_WORKER_CONNECTIONS=1024
      - BACKEND_HOST=backend
      - BACKEND_PORT=3000
    volumes:
      - ./nginx.conf.template:/etc/nginx/nginx.conf.template
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs:/var/log/nginx
      - ./cache:/var/cache/nginx
    depends_on:
      - backend
    networks:
      - app-network
    restart: unless-stopped

  backend:
    image: node:alpine
    working_dir: /app
    volumes:
      - ./app:/app
    command: npm start
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

# nginx.conf.template
worker_processes ${NGINX_WORKER_PROCESSES};

events {
    worker_connections ${NGINX_WORKER_CONNECTIONS};
}

http {
    upstream backend {
        server ${BACKEND_HOST}:${BACKEND_PORT};
    }

    server {
        listen 80;

        location / {
            proxy_pass http://backend;
        }
    }
}</code></pre>
                    </div>

                    <h3><i class="fas fa-rocket"></i> 部署策略</h3>

                    <h4><i class="fas fa-sync-alt"></i> 1. 滚动更新</h4>
                    <div class="success-box">
                        <pre><code># deploy.sh
#!/bin/bash

# 构建新镜像
docker build -t nginx-app:latest .

# 滚动更新
docker service update \
    --image nginx-app:latest \
    --update-parallelism 1 \
    --update-delay 30s \
    --update-failure-action rollback \
    nginx-service

# 或使用docker-compose
docker-compose up -d --no-deps --build nginx

# 健康检查
for i in {1..30}; do
    if curl -f http://localhost/health; then
        echo "Deployment successful"
        break
    fi
    sleep 2
done</code></pre>
                    </div>

                    <h4><i class="fas fa-balance-scale"></i> 2. 蓝绿部署</h4>
                    <div class="info-box">
                        <pre><code># blue-green-deploy.sh
#!/bin/bash

CURRENT_COLOR=$(docker ps --filter "name=nginx-" --format "table {{.Names}}" | grep -o "blue\|green" | head -1)

if [ "$CURRENT_COLOR" = "blue" ]; then
    NEW_COLOR="green"
else
    NEW_COLOR="blue"
fi

echo "Deploying to $NEW_COLOR environment"

# 启动新环境
docker-compose -f docker-compose.$NEW_COLOR.yml up -d

# 等待新环境就绪
sleep 30

# 健康检查
if curl -f http://localhost:8080/health; then
    echo "New environment healthy, switching traffic"

    # 切换流量
    docker-compose -f docker-compose.lb.yml up -d

    # 停止旧环境
    docker-compose -f docker-compose.$CURRENT_COLOR.yml down

    echo "Deployment completed successfully"
else
    echo "Health check failed, rolling back"
    docker-compose -f docker-compose.$NEW_COLOR.yml down
    exit 1
fi</code></pre>
                    </div>
                </section>

                <section id="kubernetes">
                    <h2><span class="step-number">9</span>Kubernetes集成</h2>

                    <div class="advanced-box">
                        <h3><i class="fas fa-dharmachakra"></i> Nginx在K8s中的角色</h3>
                        <p>在Kubernetes环境中，Nginx可以作为Ingress Controller、Sidecar代理或独立的负载均衡器。掌握K8s中的Nginx部署是云原生架构的重要技能。</p>
                    </div>

                    <h3><i class="fas fa-network-wired"></i> Nginx Ingress Controller</h3>

                    <h4><i class="fas fa-download"></i> 1. 安装配置</h4>
                    <div class="info-box">
                        <pre><code># 安装Nginx Ingress Controller
kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.8.1/deploy/static/provider/cloud/deploy.yaml

# 或使用Helm
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo update
helm install ingress-nginx ingress-nginx/ingress-nginx \
    --namespace ingress-nginx \
    --create-namespace \
    --set controller.service.type=LoadBalancer

# 验证安装
kubectl get pods -n ingress-nginx
kubectl get svc -n ingress-nginx</code></pre>
                    </div>

                    <h4><i class="fas fa-route"></i> 2. Ingress资源配置</h4>
                    <div class="success-box">
                        <pre><code># ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: app-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.example.com
    - app.example.com
    secretName: example-tls
  rules:
  - host: api.example.com
    http:
      paths:
      - path: /v1/users
        pathType: Prefix
        backend:
          service:
            name: user-service
            port:
              number: 80
      - path: /v1/orders
        pathType: Prefix
        backend:
          service:
            name: order-service
            port:
              number: 80
  - host: app.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: frontend-service
            port:
              number: 80

---
# 高级配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: ingress-nginx
data:
  proxy-connect-timeout: "30"
  proxy-send-timeout: "30"
  proxy-read-timeout: "30"
  proxy-body-size: "10m"
  use-gzip: "true"
  gzip-types: "text/plain text/css application/json application/javascript text/xml application/xml"</code></pre>
                    </div>

                    <h3><i class="fas fa-shield-alt"></i> 高级功能配置</h3>

                    <h4><i class="fas fa-key"></i> 1. 认证和授权</h4>
                    <div class="warning-box">
                        <pre><code># auth-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: protected-app
  annotations:
    # 基础认证
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required'

    # OAuth认证
    nginx.ingress.kubernetes.io/auth-url: "https://auth.example.com/oauth2/auth"
    nginx.ingress.kubernetes.io/auth-signin: "https://auth.example.com/oauth2/start"

    # 白名单
    nginx.ingress.kubernetes.io/whitelist-source-range: "10.0.0.0/8,***********/16"
spec:
  rules:
  - host: admin.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: admin-service
            port:
              number: 80

---
# 创建认证密钥
apiVersion: v1
kind: Secret
metadata:
  name: basic-auth
type: Opaque
data:
  auth: YWRtaW46JGFwcjEkSDY1dnBkJE8vbGpxd0IyUUNkUDFLNTJRdVBkLi4= # admin:admin123</code></pre>
                    </div>

                    <h4><i class="fas fa-tachometer-alt"></i> 2. 性能优化</h4>
                    <div class="info-box">
                        <pre><code># performance-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: high-performance-app
  annotations:
    # 缓存配置
    nginx.ingress.kubernetes.io/proxy-cache-valid: "200 302 10m"
    nginx.ingress.kubernetes.io/proxy-cache-valid: "404 1m"

    # 连接池
    nginx.ingress.kubernetes.io/upstream-keepalive-connections: "32"
    nginx.ingress.kubernetes.io/upstream-keepalive-requests: "100"
    nginx.ingress.kubernetes.io/upstream-keepalive-timeout: "60s"

    # 压缩
    nginx.ingress.kubernetes.io/enable-brotli: "true"
    nginx.ingress.kubernetes.io/brotli-level: "6"

    # HTTP/2
    nginx.ingress.kubernetes.io/http2-push-preload: "true"

    # 自定义配置
    nginx.ingress.kubernetes.io/configuration-snippet: |
      more_set_headers "X-Frame-Options: SAMEORIGIN";
      more_set_headers "X-Content-Type-Options: nosniff";
      more_set_headers "X-XSS-Protection: 1; mode=block";
spec:
  rules:
  - host: fast.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: fast-service
            port:
              number: 80</code></pre>
                    </div>
                </section>

                <section id="automation">
                    <h2><span class="step-number">10</span>自动化运维</h2>

                    <div class="advanced-box">
                        <h3><i class="fas fa-robot"></i> DevOps自动化</h3>
                        <p>自动化运维是现代IT基础设施的核心。通过自动化工具和脚本，可以实现Nginx的自动部署、配置管理、监控告警和故障恢复。</p>
                    </div>

                    <h3><i class="fas fa-code-branch"></i> CI/CD集成</h3>

                    <h4><i class="fab fa-gitlab"></i> 1. GitLab CI配置</h4>
                    <div class="info-box">
                        <pre><code># .gitlab-ci.yml
stages:
  - test
  - build
  - deploy

variables:
  DOCKER_REGISTRY: registry.example.com
  IMAGE_NAME: nginx-app

# 配置测试
test_config:
  stage: test
  image: nginx:alpine
  script:
    - nginx -t -c nginx.conf
    - echo "Configuration test passed"

# 构建镜像
build_image:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker build -t $DOCKER_REGISTRY/$IMAGE_NAME:latest .
    - docker push $DOCKER_REGISTRY/$IMAGE_NAME:latest

# 部署到生产环境
deploy_production:
  stage: deploy
  image: bitnami/kubectl:latest
  script:
    - kubectl set image deployment/nginx-app nginx=$DOCKER_REGISTRY/$IMAGE_NAME:latest
    - kubectl rollout status deployment/nginx-app
  when: manual
  only:
    - main</code></pre>
                    </div>

                    <h3><i class="fas fa-cogs"></i> 配置管理</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-book"></i> Ansible自动化</h4>
                        <pre><code># nginx-playbook.yml
---
- name: Deploy Nginx
  hosts: nginx_servers
  become: yes
  tasks:
    - name: Install Nginx
      package:
        name: nginx
        state: present

    - name: Template configuration
      template:
        src: nginx.conf.j2
        dest: /etc/nginx/nginx.conf
      notify: restart nginx

    - name: Start Nginx
      systemd:
        name: nginx
        state: started
        enabled: yes

  handlers:
    - name: restart nginx
      systemd:
        name: nginx
        state: restarted</code></pre>
                    </div>
                </section>

                <section id="monitoring-advanced">
                    <h2><span class="step-number">11</span>高级监控</h2>

                    <div class="advanced-box">
                        <h3><i class="fas fa-chart-area"></i> 可观测性体系</h3>
                        <p>现代监控不仅仅是收集指标，而是构建完整的可观测性体系：指标监控、日志分析、链路追踪。</p>
                    </div>

                    <h3><i class="fas fa-chart-line"></i> Prometheus监控</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-download"></i> Nginx Exporter配置</h4>
                        <pre><code># nginx.conf - 启用状态页面
server {
    listen 8080;
    location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        deny all;
    }
}

# prometheus.yml
scrape_configs:
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 5s</code></pre>
                    </div>

                    <h3><i class="fas fa-search"></i> 日志分析</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-layer-group"></i> ELK Stack</h4>
                        <pre><code># filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/nginx/*.log
  fields:
    service: nginx

output.logstash:
  hosts: ["logstash:5044"]

# logstash.conf
input {
  beats { port => 5044 }
}

filter {
  grok {
    match => { "message" => "%{COMBINEDAPACHELOG}" }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "nginx-logs-%{+YYYY.MM.dd}"
  }
}</code></pre>
                    </div>
                </section>

                <section id="best-practices">
                    <h2><span class="step-number">12</span>最佳实践</h2>

                    <div class="advanced-box">
                        <h3><i class="fas fa-star"></i> 企业级最佳实践</h3>
                        <p>经过多年生产环境验证的最佳实践，涵盖性能、安全、可维护性和可扩展性等各个方面。</p>
                    </div>

                    <h3><i class="fas fa-rocket"></i> 性能最佳实践</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-tachometer-alt"></i> 高性能配置</h4>
                        <pre><code># nginx.conf - 生产环境配置
user nginx;
worker_processes auto;
worker_cpu_affinity auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;

    # 压缩配置
    gzip on;
    gzip_vary on;
    gzip_comp_level 6;
    gzip_min_length 1024;

    # 缓冲区优化
    client_body_buffer_size 128k;
    client_max_body_size 10m;

    include /etc/nginx/conf.d/*.conf;
}</code></pre>
                    </div>

                    <h3><i class="fas fa-shield-alt"></i> 安全最佳实践</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-lock"></i> 安全加固</h4>
                        <pre><code># 安全头部配置
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Strict-Transport-Security "max-age=31536000" always;

# 限制请求
limit_req_zone $binary_remote_addr zone=req_limit:10m rate=5r/s;
limit_conn_zone $binary_remote_addr zone=conn_limit:10m;

server {
    limit_req zone=req_limit burst=10 nodelay;
    limit_conn conn_limit 20;

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
    }
}</code></pre>
                    </div>

                    <div class="info-box">
                        <h4><i class="fas fa-graduation-cap"></i> 学习建议</h4>
                        <ul>
                            <li><strong>持续学习：</strong>关注Nginx官方动态和社区最佳实践</li>
                            <li><strong>实践为主：</strong>在测试环境中验证所有配置</li>
                            <li><strong>监控驱动：</strong>基于监控数据进行优化决策</li>
                            <li><strong>安全第一：</strong>定期更新和安全审计</li>
                            <li><strong>文档记录：</strong>维护详细的配置文档</li>
                        </ul>
                    </div>
                </section>

                <div class="success-box">
                    <h3><i class="fas fa-graduation-cap"></i> 恭喜完成Nginx进阶学习！</h3>
                    <p>通过这个进阶教程，你已经掌握了Nginx的高级功能和企业级应用技能。现在你可以：</p>
                    <ul>
                        <li><i class="fas fa-check"></i> 开发和使用高级Nginx模块</li>
                        <li><i class="fas fa-check"></i> 使用Lua脚本扩展Nginx功能</li>
                        <li><i class="fas fa-check"></i> 设计微服务架构和API网关</li>
                        <li><i class="fas fa-check"></i> 实现高级缓存和性能优化策略</li>
                        <li><i class="fas fa-check"></i> 构建企业级高可用Web架构</li>
                    </ul>

                    <h4><i class="fas fa-rocket"></i> 继续深入学习</h4>
                    <div class="path-steps">
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-cloud"></i></div>
                            <div class="step-content">
                                <h5>云原生架构</h5>
                                <p>学习Service Mesh、Istio等云原生技术</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-chart-line"></i></div>
                            <div class="step-content">
                                <h5>可观测性</h5>
                                <p>深入学习分布式追踪、指标监控</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-shield-alt"></i></div>
                            <div class="step-content">
                                <h5>安全专家</h5>
                                <p>掌握零信任架构、安全网关</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-cogs"></i></div>
                            <div class="step-content">
                                <h5>平台工程</h5>
                                <p>构建开发者平台和工具链</p>
                            </div>
                        </div>
                    </div>

                    <p><strong>记住：</strong>技术在不断发展，保持学习的热情，关注新技术趋势，在实践中不断提升自己的技能！</p>
                </div>

            </div>
        </div>
    </div>

    <!-- 返回顶部按钮 -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // 移动端菜单切换
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const sidebar = document.getElementById('sidebar');

        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', () => {
                sidebar.classList.toggle('active');
            });
        }

        // 返回顶部功能
        const backToTop = document.getElementById('backToTop');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'flex';
            } else {
                backToTop.style.display = 'none';
            }
        });

        backToTop.addEventListener('click', (e) => {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 侧边栏导航高亮
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.sidebar a[href^="#"]');

        window.addEventListener('scroll', () => {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // 平滑滚动
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href');
                const targetSection = document.querySelector(targetId);
                if (targetSection) {
                    targetSection.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
                // 移动端关闭菜单
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                }
            });
        });
    </script>

</body>

</html>