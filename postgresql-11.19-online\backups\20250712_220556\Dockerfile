# ========================================
# 第一阶段：构建阶段（包含所有编译工具）
# ========================================
FROM centos:7 AS builder

# 设置构建环境变量
ENV PG_VERSION=11.19

# 复制repo源文件和PostgreSQL源码包
COPY CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo
COPY postgresql-${PG_VERSION}.tar.gz /tmp/

# 更新repo缓存
RUN yum clean all && yum makecache

# 安装编译依赖和运行时工具
RUN yum install -y \
        gcc \
        gcc-c++ \
        make \
        readline-devel \
        zlib-devel \
        openssl-devel \
        git && \
    yum clean all

# 编译安装su-exec（用于权限切换）
RUN cd /tmp && \
    git clone https://github.com/ncopa/su-exec.git && \
    cd su-exec && \
    make && \
    cp su-exec /usr/local/bin/ && \
    cd / && \
    rm -rf /tmp/su-exec

# 编译PostgreSQL 11.19（完整功能）
RUN cd /tmp && \
    tar -xzf postgresql-${PG_VERSION}.tar.gz && \
    cd postgresql-${PG_VERSION} && \
    ./configure \
        --prefix=/usr/local/pgsql \
        --with-openssl \
        --enable-thread-safety && \
    make -j$(nproc) && \
    make install && \
    # 创建符号链接，方便后续复制
    ln -sf /usr/local/pgsql/bin/* /usr/local/bin/ && \
    # 清理源码
    cd /tmp && \
    rm -rf postgresql-${PG_VERSION}*

# ========================================
# 第二阶段：运行时阶段（最小化镜像）
# ========================================
FROM centos:7 AS runtime

# 设置运行时环境变量
ENV PG_VERSION=11.19
ENV PG_USER=postgres
ENV PG_PASSWORD=postgres
ENV PG_DB=postgres
ENV PG_PORT=3433
ENV PGDATA=/var/lib/postgresql/data
ENV PATH="/usr/local/pgsql/bin:$PATH"

# 复制repo源文件（用于安装运行时依赖）
COPY CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo

# 更新repo缓存
RUN yum clean all && yum makecache

# 只安装运行时必需的库（最小化）
RUN yum install -y \
        openssl \
        readline \
        zlib \
        which && \
    # 清理yum缓存和不必要文件
    yum clean all && \
    rm -rf /var/cache/yum/* && \
    rm -rf /tmp/* && \
    rm -rf /var/tmp/*

# 从构建阶段复制PostgreSQL安装文件和su-exec
COPY --from=builder /usr/local/pgsql /usr/local/pgsql
COPY --from=builder /usr/local/bin/su-exec /usr/local/bin/su-exec

# 创建postgres用户
RUN useradd -m -s /bin/bash postgres

# 创建必要的目录
RUN mkdir -p ${PGDATA} && \
    mkdir -p /var/log/postgresql && \
    mkdir -p /etc/postgresql && \
    mkdir -p /docker-entrypoint-initdb.d && \
    chown -R postgres:postgres ${PGDATA} && \
    chown -R postgres:postgres /var/log/postgresql && \
    chown -R postgres:postgres /etc/postgresql

# 复制配置文件
COPY postgresql.conf /etc/postgresql/postgresql.conf
COPY pg_hba.conf /etc/postgresql/pg_hba.conf
COPY docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh

# 设置权限
RUN chmod +x /usr/local/bin/docker-entrypoint.sh && \
    chown postgres:postgres /etc/postgresql/postgresql.conf && \
    chown postgres:postgres /etc/postgresql/pg_hba.conf

# 验证PostgreSQL安装
RUN /usr/local/pgsql/bin/postgres --version

# 切换到postgres用户
USER postgres

# 暴露端口
EXPOSE ${PG_PORT}

# 设置数据卷
VOLUME ["${PGDATA}", "/var/log/postgresql", "/etc/postgresql"]

# 设置入口点
ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
CMD ["postgres"]
