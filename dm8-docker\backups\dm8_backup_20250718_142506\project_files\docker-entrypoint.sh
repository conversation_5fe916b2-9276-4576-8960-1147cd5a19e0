#!/bin/bash
set -e

# 达梦数据库Docker容器启动脚本

# 环境变量默认值
DM_HOME=${DM_HOME:-/opt/dmdbms}
DM_DATA=${DM_DATA:-/opt/dmdbms/data}
DM_DB_NAME=${DM_DB_NAME:-DAMENG}
DM_INSTANCE_NAME=${DM_INSTANCE_NAME:-DMSERVER}
DM_PORT=${DM_PORT:-5236}
DM_PWD=${DM_PWD:-GDYtd@2025}
DM_PAGE_SIZE=${DM_PAGE_SIZE:-32}
DM_CHARSET=${DM_CHARSET:-0}
DM_CASE_SENSITIVE=${DM_CASE_SENSITIVE:-N}

echo "=== 达梦数据库容器启动 ==="
echo "DM_HOME: $DM_HOME"
echo "DM_DATA: $DM_DATA"
echo "DM_DB_NAME: $DM_DB_NAME"
echo "DM_INSTANCE_NAME: $DM_INSTANCE_NAME"
echo "DM_PORT: $DM_PORT"

# 确保数据目录存在
mkdir -p "$DM_DATA"

# 检查是否已经初始化
if [ ! -f "$DM_DATA/DAMENG.ini" ] && [ ! -f "$DM_DATA/dm.ini" ] && [ ! -f "$DM_DATA/$DM_DB_NAME/dm.ini" ] && [ ! -f "$DM_DATA/$DM_DB_NAME/$DM_DB_NAME.ini" ]; then
    echo "=== 首次启动，初始化数据库 ==="

    # 初始化数据库
    cd "$DM_HOME/bin"
    ./dminit \
        PATH="$DM_DATA" \
        DB_NAME="$DM_DB_NAME" \
        INSTANCE_NAME="$DM_INSTANCE_NAME" \
        PORT_NUM="$DM_PORT" \
        SYSDBA_PWD="$DM_PWD" \
        SYSAUDITOR_PWD="$DM_PWD" \
        PAGE_SIZE="$DM_PAGE_SIZE" \
        CHARSET="$DM_CHARSET" \
        CASE_SENSITIVE="$DM_CASE_SENSITIVE" \
        AUTO_OVERWRITE=1

    if [ $? -eq 0 ]; then
        echo "✓ 数据库初始化成功"
    else
        echo "✗ 数据库初始化失败"
        exit 1
    fi
else
    echo "=== 数据库已存在，跳过初始化 ==="
fi

# 查找配置文件
INI_FILE=""
if [ -f "$DM_DATA/dm.ini" ]; then
    INI_FILE="$DM_DATA/dm.ini"
elif [ -f "$DM_DATA/DAMENG.ini" ]; then
    INI_FILE="$DM_DATA/DAMENG.ini"
elif [ -f "$DM_DATA/${DM_DB_NAME}.ini" ]; then
    INI_FILE="$DM_DATA/${DM_DB_NAME}.ini"
elif [ -f "$DM_DATA/$DM_DB_NAME/dm.ini" ]; then
    INI_FILE="$DM_DATA/$DM_DB_NAME/dm.ini"
elif [ -f "$DM_DATA/$DM_DB_NAME/$DM_DB_NAME.ini" ]; then
    INI_FILE="$DM_DATA/$DM_DB_NAME/$DM_DB_NAME.ini"
else
    echo "✗ 找不到配置文件"
    echo "查找路径："
    echo "  $DM_DATA/dm.ini"
    echo "  $DM_DATA/DAMENG.ini"
    echo "  $DM_DATA/${DM_DB_NAME}.ini"
    echo "  $DM_DATA/$DM_DB_NAME/dm.ini"
    echo "  $DM_DATA/$DM_DB_NAME/$DM_DB_NAME.ini"
    echo "实际文件："
    ls -la "$DM_DATA"
    if [ -d "$DM_DATA/$DM_DB_NAME" ]; then
        echo "子目录 $DM_DB_NAME 内容："
        ls -la "$DM_DATA/$DM_DB_NAME"
    fi
    exit 1
fi

echo "=== 使用配置文件: $INI_FILE ==="

# 启动数据库服务器
echo "=== 启动达梦数据库服务器 ==="
cd "$DM_HOME/bin"

# 如果是交互式启动，直接运行
if [ "$1" = "dmserver" ] || [ "$1" = "" ]; then
    echo "启动命令: ./dmserver path=$INI_FILE"
    exec ./dmserver "path=$INI_FILE"
else
    # 执行传入的命令
    exec "$@"
fi
