# PostgreSQL 11.19 Docker

基于CentOS 7构建的PostgreSQL 11.19 Docker镜像

## 🚀 快速开始

### 方式一：Docker Compose（推荐）
```bash
# 一键启动PostgreSQL + pgAdmin
docker-compose up -d
```

### 方式二：直接Docker命令
```bash
# 1. 构建镜像（标准版本）
docker build -t postgresql:11.19-centos7 .

# 1. 构建镜像（体积优化版本，推荐）
docker build -f Dockerfile.multistage -t postgresql:11.19-optimized .

# 2. 启动容器
docker run -d --name postgresql-11.19 \
  -p 3433:3433 \
  -e PG_PASSWORD=postgres \
  postgresql:11.19-optimized

# 3. 连接数据库
docker exec -it postgresql-11.19 psql -U postgres -d postgres -p 3433
```

## 📋 连接信息

### PostgreSQL数据库
- **主机**: localhost
- **端口**: 3433
- **用户名**: postgres
- **密码**: postgres（Docker Compose）或通过环境变量PG_PASSWORD设置
- **数据库**: postgres

### pgAdmin Web管理界面（仅Docker Compose）
- **地址**: http://localhost:8080
- **邮箱**: <EMAIL>
- **密码**: admin123

## 📁 文件说明

- `Dockerfile` - Docker构建文件
- `docker-compose.yml` - Docker Compose配置
- `docker-entrypoint.sh` - 容器启动脚本
- `postgresql.conf` - PostgreSQL主配置文件
- `pg_hba.conf` - 认证配置文件
- `CentOS-Base.repo` - CentOS镜像源配置
- `postgresql-11.19.tar.gz` - PostgreSQL源码包
- `init-scripts/` - 数据库初始化脚本目录
- `使用指导.html` - 详细使用指导

## 🔧 常用命令

```bash
# 构建镜像
docker build -t postgresql:11.19-centos7 .

# 启动容器（基本）
docker run -d --name postgresql-11.19 -p 3433:3433 -e PG_PASSWORD=password postgresql:11.19-centos7

# 启动容器（数据持久化）
docker run -d --name postgresql-11.19 \
  -p 3433:3433 \
  -v postgres-data:/var/lib/postgresql/data \
  -e PG_PASSWORD=password \
  postgresql:11.19-centos7

# 查看容器状态
docker ps

# 查看日志
docker logs postgresql-11.19

# 连接数据库
docker exec -it postgresql-11.19 psql -U postgres -d postgres -p 3433

# 备份数据库
docker exec postgresql-11.19 pg_dumpall -U postgres -p 3433 > backup.sql

# 恢复数据库
docker exec -i postgresql-11.19 psql -U postgres -p 3433 < backup.sql

# 停止容器
docker stop postgresql-11.19

# 删除容器
docker rm postgresql-11.19
```

## 🐳 使用Docker Compose

```bash
# 启动服务（PostgreSQL + pgAdmin）
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs postgresql

# 重新构建镜像
docker-compose build

# 完全清理（包括数据卷）
docker-compose down -v
```

## 💾 数据持久化

### 使用数据卷
```bash
docker volume create postgres-data
docker run -d --name postgresql-11.19 \
  -p 3433:3433 \
  -v postgres-data:/var/lib/postgresql/data \
  -e PG_PASSWORD=password \
  postgresql:11.19-centos7
```

### 使用主机目录
```bash
# Windows
docker run -d --name postgresql-11.19 \
  -p 3433:3433 \
  -v D:\postgresql-data:/var/lib/postgresql/data \
  -e PG_PASSWORD=password \
  postgresql:11.19-centos7

# Linux/Mac
docker run -d --name postgresql-11.19 \
  -p 3433:3433 \
  -v /path/to/data:/var/lib/postgresql/data \
  -e PG_PASSWORD=password \
  postgresql:11.19-centos7
```

## 🔒 数据安全说明

### Windows系统重启后的数据保护
- **✅ 数据安全**: Docker数据卷独立于容器，系统重启不会影响数据
- **✅ 智能启动**: PostgreSQL会检测现有数据，跳过重复初始化
- **✅ 自动恢复**: 重启后直接启动，无需手动干预

```bash
# 验证数据完整性
docker exec postgresql-11.19 psql -U postgres -d sample_db -p 3433 -c "SELECT COUNT(*) FROM users;"

# 查看启动日志（应显示"跳过初始化"）
docker-compose logs postgresql
```

## 🔍 故障排除

### 端口被占用
```bash
# 检查端口占用
netstat -an | findstr 3433

# 使用其他端口
docker run -d --name postgresql-11.19 -p 5432:3433 -e PG_PASSWORD=postgres postgresql:11.19-centos7
```

### 容器启动失败
```bash
# 查看日志
docker logs postgresql-11.19

# 检查容器状态
docker ps -a

# 重新启动
docker restart postgresql-11.19
```

### 系统重启后数据问题
```bash
# 检查数据卷是否存在
docker volume ls | findstr postgresql

# 重新启动服务
docker-compose up -d

# 验证数据完整性
docker exec postgresql-11.19 psql -U postgres -d sample_db -p 3433 -c "SELECT * FROM users LIMIT 3;"
```

## 📊 版本信息

- **PostgreSQL版本**: 11.19
- **基础镜像**: CentOS 7
- **编译器**: GCC 4.8.5
- **端口**: 3433

## 🎯 镜像体积优化

| 版本 | 体积 | 优化幅度 | 构建文件 | 推荐场景 |
|------|------|----------|----------|----------|
| 标准版本 | 916MB | - | `Dockerfile` | 开发调试 |
| 多阶段优化版本 | 787MB | ↓ 14.1% | `Dockerfile.multistage` | **生产推荐** |
| 离线版本 | 613MB | ↓ 33.1% | `postgresql-11.19/Dockerfile` | 离线环境 |

### 构建优化版本
```bash
# 多阶段优化版本（推荐）
docker build -f Dockerfile.multistage -t postgresql:11.19-optimized .

# 查看体积对比
docker images | grep postgresql
```

详细的体积优化分析请查看：[体积优化对比.md](体积优化对比.md)

## 📖 详细文档

请查看 `使用指导.html` 获取完整的使用指导和故障排除信息。
