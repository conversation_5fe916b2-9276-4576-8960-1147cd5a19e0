<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K8s集群对象存储部署完整教程</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 13px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 机器标识样式 */
        .machine-tag {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            margin: 0 8px 12px 0;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .machine-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .machine-tag:hover::before {
            left: 100%;
        }

        .machine-master {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
        }

        .machine-node {
            background: linear-gradient(135deg, #45b7d1 0%, #96c93d 100%);
            color: white;
        }

        .machine-client {
            background: linear-gradient(135deg, #a55eea 0%, #8e44ad 100%);
            color: white;
        }

        .machine-storage {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .machine-all {
            background: linear-gradient(135deg, #f9ca24 0%, #f0932b 100%);
            color: white;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: var(--light-surface);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        th,
        td {
            padding: 18px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-decoration: none;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            z-index: 999;
            font-size: 20px;
        }

        .back-to-top:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .content-wrapper {
                padding: 30px;
            }

            .container {
                padding: 40px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
                border-radius: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 16px;
                margin-right: 15px;
            }
        }

        /* 打印样式 */
        @media print {

            .sidebar,
            .back-to-top,
            .mobile-menu-btn {
                display: none;
            }

            .main-content {
                margin-left: 0;
            }

            body {
                background: white;
                font-size: 12px;
            }

            .container {
                box-shadow: none;
                padding: 0;
            }

            pre {
                background: #f8f9fa;
                color: #333;
                border: 1px solid #ddd;
            }

            .info-box,
            .warning-box,
            .success-box,
            .danger-box {
                border: 1px solid #ddd;
                background: #f8f9fa;
            }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
        }
    </style>
</head>

<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-database"></i> K8s对象存储</h2>
            <p>MinIO集群部署指南</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#overview"><i class="fas fa-eye"></i>1. 概述</a></li>
                <li><a href="#environment-preparation"><i class="fas fa-cog"></i>2. 环境准备</a></li>
                <li><a href="#storage-preparation"><i class="fas fa-hdd"></i>3. 存储准备</a></li>
                <li><a href="#minio-deployment"><i class="fas fa-server"></i>4. MinIO部署</a></li>
                <li><a href="#service-configuration"><i class="fas fa-network-wired"></i>5. 服务配置</a></li>
                <li><a href="#ssl-configuration"><i class="fas fa-lock"></i>6. SSL配置</a></li>
                <li><a href="#client-configuration"><i class="fas fa-laptop"></i>7. 客户端配置</a></li>
                <li><a href="#bucket-operations"><i class="fas fa-folder"></i>8. 存储桶操作</a></li>
                <li><a href="#backup-restore"><i class="fas fa-database"></i>9. 备份恢复</a></li>
                <li><a href="#monitoring"><i class="fas fa-chart-line"></i>10. 监控维护</a></li>
                <li><a href="#troubleshooting"><i class="fas fa-bug"></i>11. 故障排查</a></li>
                <li><a href="#deployment-verification"><i class="fas fa-check-double"></i>12. 部署验证</a></li>
                <li><a href="#summary"><i class="fas fa-flag-checkered"></i>13. 总结</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-database"></i> K8s集群对象存储部署完整教程</h1>

                <div class="info-box">
                    <strong><i class="fas fa-info-circle"></i>
                        教程说明：</strong>本教程详细介绍了如何在Kubernetes集群中部署MinIO对象存储服务，包括集群模式部署、持久化存储配置、SSL证书配置、监控维护等完整功能。每个步骤都明确标注了执行的机器类型，请严格按照指示操作。
                </div>

                <div class="warning-box">
                    <strong><i class="fas fa-exclamation-triangle"></i> 机器标识说明：</strong>
                    <div style="margin-top: 15px;">
                        <span class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</span> -
                        K8s集群的控制平面节点<br>
                        <span class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</span> -
                        K8s集群的工作节点<br>
                        <span class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</span> -
                        执行kubectl命令的机器<br>
                        <span class="machine-tag machine-storage"><i class="fas fa-hdd"></i> 存储节点</span> -
                        提供持久化存储的节点<br>
                        <span class="machine-tag machine-all"><i class="fas fa-globe"></i> 所有节点</span> - 在所有相关节点上执行
                    </div>
                </div>

                <section id="overview">
                    <h2><span class="step-number">1</span>概述</h2>

                    <h3><i class="fas fa-question-circle"></i> 1.1 什么是对象存储</h3>
                    <p>对象存储是一种数据存储架构，它将数据作为对象进行管理，每个对象包含数据、元数据和唯一标识符。在Kubernetes环境中，对象存储主要用于：</p>
                    <ul>
                        <li><strong><i class="fas fa-backup"></i> 数据备份：</strong>应用数据、数据库备份的存储</li>
                        <li><strong><i class="fas fa-file-archive"></i> 静态资源：</strong>图片、视频、文档等静态文件存储</li>
                        <li><strong><i class="fas fa-chart-line"></i> 日志归档：</strong>应用日志的长期存储</li>
                        <li><strong><i class="fas fa-cloud"></i> 云原生应用：</strong>微服务架构中的数据持久化</li>
                    </ul>

                    <h3><i class="fas fa-star"></i> 1.2 为什么选择MinIO</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-check"></i> 优势</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-cloud"></i> S3兼容</td>
                            <td>完全兼容Amazon S3 API，无需修改现有应用</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-rocket"></i> 高性能</td>
                            <td>专为云原生环境设计，读写性能优异</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-shield-alt"></i> 企业级</td>
                            <td>支持加密、版本控制、访问策略等企业功能</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-expand-arrows-alt"></i> 可扩展</td>
                            <td>支持水平扩展，可从单节点扩展到大规模集群</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-code"></i> 开源</td>
                            <td>Apache License 2.0，无厂商锁定</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-sitemap"></i> 1.3 架构设计</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-puzzle-piece"></i> 组件</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                            <th><i class="fas fa-server"></i> 部署位置</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-database"></i> MinIO Server</td>
                            <td>核心对象存储服务</td>
                            <td>Worker节点</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-hdd"></i> 持久化存储</td>
                            <td>PVC提供的持久化卷</td>
                            <td>存储节点</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-network-wired"></i> Service</td>
                            <td>K8s服务，提供负载均衡</td>
                            <td>集群内部</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-globe"></i> Ingress</td>
                            <td>外部访问入口</td>
                            <td>边缘节点</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-network-wired"></i> 1.4 集群规划示例</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 角色</th>
                            <th><i class="fas fa-server"></i> 主机名</th>
                            <th><i class="fas fa-globe"></i> IP地址</th>
                            <th><i class="fas fa-comment"></i> 说明</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-crown"></i> Master</td>
                            <td>k8s-master</td>
                            <td>*************</td>
                            <td>K8s控制平面</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-server"></i> Worker01</td>
                            <td>k8s-worker01</td>
                            <td>*************</td>
                            <td>MinIO实例1</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-server"></i> Worker02</td>
                            <td>k8s-worker02</td>
                            <td>*************</td>
                            <td>MinIO实例2</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-server"></i> Worker03</td>
                            <td>k8s-worker03</td>
                            <td>192.168.60.63</td>
                            <td>MinIO实例3</td>
                        </tr>
                    </table>
                </section>

                <section id="environment-preparation">
                    <h2><span class="step-number">2</span>环境准备</h2>

                    <h3><i class="fas fa-desktop"></i> 2.1 系统要求</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-list"></i> 项目</th>
                            <th><i class="fas fa-cogs"></i> 要求</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-dharmachakra"></i> Kubernetes</td>
                            <td>v1.20+</td>
                            <td>支持StatefulSet和PVC</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-memory"></i> 内存</td>
                            <td>每个MinIO实例至少2GB</td>
                            <td>生产环境建议4GB+</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-microchip"></i> CPU</td>
                            <td>每个MinIO实例至少1核</td>
                            <td>高并发场景建议2核+</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-hdd"></i> 存储</td>
                            <td>每个实例至少50GB</td>
                            <td>根据数据量规划，建议SSD</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-network-wired"></i> 网络</td>
                            <td>节点间网络互通</td>
                            <td>确保9000和9001端口可用</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-check-circle"></i> 2.2 验证K8s集群状态</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <p>首先验证Kubernetes集群是否正常运行：</p>

                    <pre><code># 检查集群节点状态
kubectl get nodes -o wide

# 检查集群组件状态
kubectl get pods -n kube-system

# 检查存储类
kubectl get storageclass

# 检查可用资源
kubectl top nodes</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>所有节点状态为Ready，系统Pod运行正常，存储类可用。
                    </div>

                    <h3><i class="fas fa-folder"></i> 2.3 创建命名空间</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <p>为MinIO创建专用的命名空间：</p>

                    <pre><code># 创建MinIO命名空间
kubectl create namespace minio

# 验证命名空间创建
kubectl get namespaces | grep minio

# 设置默认命名空间（可选）
kubectl config set-context --current --namespace=minio</code></pre>

                    <h3><i class="fas fa-tools"></i> 2.4 安装必要工具</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <p>安装MinIO客户端工具mc，用于管理对象存储：</p>

                    <pre><code># 下载MinIO客户端
wget https://dl.min.io/client/mc/release/linux-amd64/mc
chmod +x mc
sudo mv mc /usr/local/bin/

# 验证安装
mc --version

# 或者使用包管理器安装
# CentOS/RHEL/银河麒麟
sudo yum install -y wget
wget https://dl.min.io/client/mc/release/linux-amd64/mc
chmod +x mc
sudo mv mc /usr/local/bin/

# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y wget
wget https://dl.min.io/client/mc/release/linux-amd64/mc
chmod +x mc
sudo mv mc /usr/local/bin/</code></pre>

                    <h3><i class="fas fa-cog"></i> 2.5 环境准备脚本</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 说明：</strong>运行此脚本可以自动完成MinIO部署的环境准备工作
                    </div>

                    <pre><code>#!/bin/bash
# MinIO环境准备脚本

echo "=== MinIO环境准备开始 ==="

# 1. 检查kubectl命令
echo "1. 检查kubectl命令..."
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl命令未找到，请先安装kubectl"
    exit 1
fi
echo "✅ kubectl命令可用"

# 2. 检查K8s集群连接
echo "2. 检查K8s集群连接..."
kubectl cluster-info &> /dev/null
if [ $? -ne 0 ]; then
    echo "❌ 无法连接到K8s集群"
    exit 1
fi
echo "✅ K8s集群连接正常"

# 3. 检查节点状态
echo "3. 检查节点状态..."
ready_nodes=$(kubectl get nodes --no-headers | grep Ready | wc -l)
if [ $ready_nodes -lt 3 ]; then
    echo "❌ Ready状态的节点数量不足: $ready_nodes"
    kubectl get nodes
    exit 1
fi
echo "✅ 节点状态正常"

# 4. 创建配置目录
echo "4. 创建配置目录..."
mkdir -p /k8s/minio-config/certs
echo "✅ 配置目录创建完成"

# 5. 创建命名空间
echo "5. 创建MinIO命名空间..."
kubectl create namespace minio --dry-run=client -o yaml | kubectl apply -f -
echo "✅ MinIO命名空间准备完成"

# 6. 检查存储节点准备情况
echo "6. 检查存储节点准备情况..."
for node in k8s-worker01 k8s-worker02 k8s-worker03; do
    echo "检查节点 $node..."
    kubectl get node $node &> /dev/null
    if [ $? -ne 0 ]; then
        echo "❌ 节点 $node 不存在"
        exit 1
    fi
done
echo "✅ 所有存储节点都存在"

# 7. 下载MinIO客户端（如果不存在）
echo "7. 检查MinIO客户端..."
if ! command -v mc &> /dev/null; then
    echo "下载MinIO客户端..."
    wget -q https://dl.min.io/client/mc/release/linux-amd64/mc
    chmod +x mc
    sudo mv mc /usr/local/bin/
    echo "✅ MinIO客户端安装完成"
else
    echo "✅ MinIO客户端已存在"
fi

echo "=== MinIO环境准备完成 ==="
echo "🎉 环境准备成功，可以开始部署MinIO集群！"

# 显示下一步操作提示
echo ""
echo "=== 下一步操作 ==="
echo "1. 在所有Worker节点创建存储目录: /mnt/minio-data"
echo "2. 创建存储类和持久化卷"
echo "3. 部署MinIO集群"</code></pre>

                    <h3><i class="fas fa-play"></i> 2.6 运行环境准备脚本</h3>
                    <pre><code># 保存环境准备脚本
cat > /k8s/minio-config/prepare-env.sh << 'EOF'
# 将上面的脚本内容粘贴到这里
EOF

# 设置执行权限
chmod +x /k8s/minio-config/prepare-env.sh

# 运行环境准备脚本
/k8s/minio-config/prepare-env.sh</code></pre>
                </section>

                <section id="storage-preparation">
                    <h2><span class="step-number">3</span>存储准备</h2>

                    <h3><i class="fas fa-hdd"></i> 3.1 存储类配置</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <p>配置适合MinIO的存储类，确保高性能和可靠性：</p>

                    <h4><i class="fas fa-file-alt"></i> 3.1.1 创建存储类（本地存储示例）</h4>
                    <pre><code># 创建配置文件目录
mkdir -p /k8s/minio-config
cd /k8s/minio-config

# 创建本地存储类配置文件
cat > minio-storageclass.yaml << 'EOF'
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: minio-local-storage
provisioner: kubernetes.io/no-provisioner
volumeBindingMode: WaitForFirstConsumer
reclaimPolicy: Retain
allowVolumeExpansion: true
EOF

# 应用存储类配置
kubectl apply -f minio-storageclass.yaml

# 验证存储类
kubectl get storageclass minio-local-storage</code></pre>

                    <h4><i class="fas fa-folder"></i> 3.1.2 准备本地存储目录</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <p>在每个Worker节点上创建MinIO数据目录：</p>

                    <pre><code># 在每个Worker节点执行（k8s-worker01, k8s-worker02, k8s-worker03）
# 创建MinIO数据目录
sudo mkdir -p /mnt/minio-data

# 设置权限（MinIO容器使用UID 1000）
sudo chown -R 1000:1000 /mnt/minio-data
sudo chmod -R 755 /mnt/minio-data

# 验证目录创建
ls -la /mnt/minio-data/

# 检查磁盘空间
df -h /mnt/minio-data/</code></pre>

                    <h4><i class="fas fa-database"></i> 3.1.3 创建持久化卷</h4>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <pre><code># 创建持久化卷配置文件
cat > minio-pv.yaml << 'EOF'
apiVersion: v1
kind: PersistentVolume
metadata:
  name: minio-pv-0
spec:
  capacity:
    storage: 100Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: minio-local-storage
  local:
    path: /mnt/minio-data
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/hostname
          operator: In
          values:
          - k8s-worker01
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: minio-pv-1
spec:
  capacity:
    storage: 100Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: minio-local-storage
  local:
    path: /mnt/minio-data
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/hostname
          operator: In
          values:
          - k8s-worker02
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: minio-pv-2
spec:
  capacity:
    storage: 100Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Retain
  storageClassName: minio-local-storage
  local:
    path: /mnt/minio-data
  nodeAffinity:
    required:
      nodeSelectorTerms:
      - matchExpressions:
        - key: kubernetes.io/hostname
          operator: In
          values:
          - k8s-worker03
EOF

# 应用持久化卷配置
kubectl apply -f minio-pv.yaml

# 验证持久化卷
kubectl get pv | grep minio

# 检查PV状态详情
kubectl describe pv minio-pv-0 minio-pv-1 minio-pv-2</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 存储说明：</strong>
                        <ul>
                            <li><strong>本地存储：</strong>适合测试和小规模部署，性能好但不支持跨节点</li>
                            <li><strong>网络存储：</strong>如NFS、Ceph等，支持跨节点但性能相对较低</li>
                            <li><strong>云存储：</strong>如AWS EBS、Azure Disk等，适合云环境部署</li>
                        </ul>
                    </div>
                </section>

                <section id="minio-deployment">
                    <h2><span class="step-number">4</span>MinIO部署</h2>

                    <h3><i class="fas fa-key"></i> 4.1 创建访问凭证</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <p>首先创建MinIO的访问密钥，用于管理员登录：</p>

                    <pre><code># 创建MinIO访问凭证Secret
cat > minio-secret.yaml << 'EOF'
apiVersion: v1
kind: Secret
metadata:
  name: minio-secret
  namespace: minio
type: Opaque
data:
  # echo -n "minioadmin" | base64
  accesskey: bWluaW9hZG1pbg==
  # echo -n "minioadmin123" | base64
  secretkey: bWluaW9hZG1pbjEyMw==
EOF

# 应用Secret配置
kubectl apply -f minio-secret.yaml

# 验证Secret创建
kubectl get secret minio-secret -n minio

# 查看Secret详情
kubectl describe secret minio-secret -n minio</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 安全提醒：</strong>生产环境请使用强密码，并定期更换访问密钥。
                    </div>

                    <h3><i class="fas fa-server"></i> 4.2 部署MinIO StatefulSet</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <p>使用StatefulSet部署MinIO集群，确保数据持久化和有序部署：</p>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 启动顺序：</strong>
                        <ol style="margin-top: 10px;">
                            <li>确保所有Worker节点存储目录已创建</li>
                            <li>确保PV已创建并处于Available状态</li>
                            <li>确保Secret已创建</li>
                            <li>然后部署StatefulSet</li>
                        </ol>
                    </div>
                    <pre><code># 创建MinIO StatefulSet配置文件
cat > minio-statefulset.yaml << 'EOF'
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: minio
  namespace: minio
  labels:
    app: minio
spec:
  serviceName: minio-headless
  replicas: 3
  selector:
    matchLabels:
      app: minio
  template:
    metadata:
      labels:
        app: minio
    spec:
      containers:
      - name: minio
        image: minio/minio:RELEASE.2023-12-02T10-51-33Z
        args:
        - server
        - http://minio-{0...2}.minio-headless.minio.svc.cluster.local/data
        - --console-address
        - ":9001"
        env:
        - name: MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: minio-secret
              key: accesskey
        - name: MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: minio-secret
              key: secretkey
        - name: MINIO_PROMETHEUS_AUTH_TYPE
          value: "public"
        ports:
        - containerPort: 9000
          name: api
        - containerPort: 9001
          name: console
        volumeMounts:
        - name: data
          mountPath: /data
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /minio/health/live
            port: 9000
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /minio/health/ready
            port: 9000
          initialDelaySeconds: 10
          periodSeconds: 10
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: minio-local-storage
      resources:
        requests:
          storage: 100Gi
EOF

# 应用StatefulSet配置
kubectl apply -f minio-statefulset.yaml

# 检查部署状态
kubectl get statefulset minio -n minio
kubectl get pods -n minio -l app=minio

# 等待所有Pod启动
kubectl wait --for=condition=ready pod -l app=minio -n minio --timeout=300s</code></pre>

                    <h3><i class="fas fa-network-wired"></i> 4.3 创建服务</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <p>创建Headless Service和普通Service，提供集群内外访问：</p>

                    <pre><code># 创建MinIO服务配置文件
cat > minio-service.yaml << 'EOF'
# Headless Service for StatefulSet
apiVersion: v1
kind: Service
metadata:
  name: minio-headless
  namespace: minio
  labels:
    app: minio
spec:
  clusterIP: None
  selector:
    app: minio
  ports:
  - port: 9000
    name: api
  - port: 9001
    name: console
---
# Service for API access
apiVersion: v1
kind: Service
metadata:
  name: minio-api
  namespace: minio
  labels:
    app: minio
spec:
  type: ClusterIP
  selector:
    app: minio
  ports:
  - port: 9000
    targetPort: 9000
    name: api
---
# Service for Console access
apiVersion: v1
kind: Service
metadata:
  name: minio-console
  namespace: minio
  labels:
    app: minio
spec:
  type: ClusterIP
  selector:
    app: minio
  ports:
  - port: 9001
    targetPort: 9001
    name: console
EOF

# 应用服务配置
kubectl apply -f minio-service.yaml

# 验证服务创建
kubectl get svc -n minio

# 查看服务详情
kubectl describe svc minio-api minio-console -n minio</code></pre>

                    <h3><i class="fas fa-check-circle"></i> 4.4 验证部署</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <pre><code># 检查Pod状态
kubectl get pods -n minio -o wide

# 检查PVC绑定状态
kubectl get pvc -n minio

# 查看MinIO日志
kubectl logs minio-0 -n minio

# 检查集群状态
kubectl exec -it minio-0 -n minio -- mc admin info local</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 部署成功标志：</strong>
                        <ul>
                            <li>所有MinIO Pod状态为Running</li>
                            <li>PVC成功绑定到PV</li>
                            <li>日志显示集群启动成功</li>
                            <li>可以访问MinIO API和Console</li>
                        </ul>
                    </div>
                </section>

                <section id="service-configuration">
                    <h2><span class="step-number">5</span>服务配置</h2>

                    <h3><i class="fas fa-globe"></i> 5.1 配置Ingress访问</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <p>配置Ingress控制器，提供外部访问入口：</p>

                    <h4><i class="fas fa-file-alt"></i> 5.1.1 创建Ingress配置</h4>
                    <pre><code># 创建MinIO Ingress配置文件
cat > minio-ingress.yaml << 'EOF'
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: minio-ingress
  namespace: minio
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
spec:
  rules:
  - host: minio-api.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: minio-api
            port:
              number: 9000
  - host: minio-console.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: minio-console
            port:
              number: 9001
EOF

# 应用Ingress配置
kubectl apply -f minio-ingress.yaml

# 验证Ingress创建
kubectl get ingress -n minio</code></pre>

                    <h4><i class="fas fa-network-wired"></i> 5.1.2 配置域名解析</h4>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <pre><code># 获取Ingress控制器的外部IP
kubectl get svc -n ingress-nginx

# 配置本地hosts文件（替换为实际的Ingress IP）
echo "************* minio-api.local" | sudo tee -a /etc/hosts
echo "************* minio-console.local" | sudo tee -a /etc/hosts

# 验证域名解析
ping -c 3 minio-api.local
ping -c 3 minio-console.local</code></pre>

                    <h3><i class="fas fa-external-link-alt"></i> 5.2 NodePort访问（可选）</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <p>如果没有Ingress控制器，可以使用NodePort方式访问：</p>

                    <pre><code># 创建NodePort服务配置文件
cat > minio-nodeport.yaml << 'EOF'
apiVersion: v1
kind: Service
metadata:
  name: minio-api-nodeport
  namespace: minio
  labels:
    app: minio
spec:
  type: NodePort
  selector:
    app: minio
  ports:
  - port: 9000
    targetPort: 9000
    nodePort: 30900
    name: api
---
apiVersion: v1
kind: Service
metadata:
  name: minio-console-nodeport
  namespace: minio
  labels:
    app: minio
spec:
  type: NodePort
  selector:
    app: minio
  ports:
  - port: 9001
    targetPort: 9001
    nodePort: 30901
    name: console
EOF

# 应用NodePort配置
kubectl apply -f minio-nodeport.yaml

# 验证NodePort服务
kubectl get svc -n minio | grep nodeport</code></pre>

                    <h3><i class="fas fa-vial"></i> 5.3 测试访问</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <pre><code># 测试API访问
curl http://minio-api.local/minio/health/live

# 或使用NodePort访问
curl http://*************:30900/minio/health/live

# 测试Console访问（在浏览器中打开）
# http://minio-console.local
# 或 http://*************:30901

# 使用mc客户端测试
mc alias set myminio http://minio-api.local minioadmin minioadmin123
mc admin info myminio</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 访问方式说明：</strong>
                        <ul>
                            <li><strong>Ingress：</strong>推荐方式，支持域名访问和SSL终止</li>
                            <li><strong>NodePort：</strong>简单直接，适合测试环境</li>
                            <li><strong>LoadBalancer：</strong>云环境推荐，自动分配外部IP</li>
                            <li><strong>Port-forward：</strong>临时访问，适合调试</li>
                        </ul>
                    </div>
                </section>

                <section id="ssl-configuration">
                    <h2><span class="step-number">6</span>SSL配置</h2>

                    <h3><i class="fas fa-certificate"></i> 6.1 生成SSL证书</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <p>为MinIO配置HTTPS访问，提高安全性：</p>

                    <h4><i class="fas fa-key"></i> 6.1.1 生成自签名证书</h4>
                    <pre><code># 创建证书目录
mkdir -p /k8s/minio-config/certs

# 生成私钥
openssl genrsa -out /k8s/minio-config/certs/private.key 4096

# 生成证书签名请求
openssl req -new -key /k8s/minio-config/certs/private.key -out /k8s/minio-config/certs/cert.csr \
  -subj "/C=CN/ST=Beijing/L=Beijing/O=MyCompany/OU=IT/CN=minio-api.local"

# 生成自签名证书
openssl x509 -req -days 365 -in /k8s/minio-config/certs/cert.csr \
  -signkey /k8s/minio-config/certs/private.key -out /k8s/minio-config/certs/public.crt

# 验证证书
openssl x509 -in /k8s/minio-config/certs/public.crt -text -noout</code></pre>

                    <h4><i class="fas fa-lock"></i> 6.1.2 创建TLS Secret</h4>
                    <pre><code># 创建TLS Secret
kubectl create secret tls minio-tls-secret \
  --cert=/k8s/minio-config/certs/public.crt \
  --key=/k8s/minio-config/certs/private.key \
  -n minio

# 验证Secret创建
kubectl get secret minio-tls-secret -n minio

# 查看Secret详情
kubectl describe secret minio-tls-secret -n minio</code></pre>

                    <h3><i class="fas fa-shield-alt"></i> 6.2 配置HTTPS Ingress</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <pre><code># 更新Ingress配置支持HTTPS
cat > minio-ingress-tls.yaml << 'EOF'
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: minio-ingress-tls
  namespace: minio
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
spec:
  tls:
  - hosts:
    - minio-api.local
    - minio-console.local
    secretName: minio-tls-secret
  rules:
  - host: minio-api.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: minio-api
            port:
              number: 9000
  - host: minio-console.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: minio-console
            port:
              number: 9001
EOF

# 应用HTTPS Ingress配置
kubectl apply -f minio-ingress-tls.yaml

# 验证HTTPS访问
curl -k https://minio-api.local/minio/health/live</code></pre>
                </section>

                <section id="client-configuration">
                    <h2><span class="step-number">7</span>客户端配置</h2>

                    <h3><i class="fas fa-laptop"></i> 7.1 配置MinIO客户端</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <p>配置mc客户端连接到MinIO集群：</p>

                    <h4><i class="fas fa-link"></i> 7.1.1 添加MinIO别名</h4>
                    <pre><code># 添加MinIO服务别名
mc alias set myminio https://minio-api.local minioadmin minioadmin123

# 或使用HTTP（如果没有配置SSL）
mc alias set myminio http://minio-api.local minioadmin minioadmin123

# 验证连接
mc admin info myminio

# 查看集群状态
mc admin cluster info myminio</code></pre>

                    <h4><i class="fas fa-list"></i> 7.1.2 基本操作测试</h4>
                    <pre><code># 列出所有存储桶
mc ls myminio

# 创建测试存储桶
mc mb myminio/test-bucket

# 上传测试文件
echo "Hello MinIO" > test.txt
mc cp test.txt myminio/test-bucket/

# 下载文件
mc cp myminio/test-bucket/test.txt downloaded.txt

# 查看文件内容
cat downloaded.txt</code></pre>

                    <h3><i class="fas fa-code"></i> 7.2 应用程序集成</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <p>演示如何在应用程序中使用MinIO：</p>

                    <h4><i class="fas fa-python"></i> 7.2.1 Python示例</h4>
                    <pre><code># 安装MinIO Python SDK
pip install minio

# 创建Python测试脚本
cat > minio_test.py << 'EOF'
from minio import Minio
from minio.error import S3Error

# 创建MinIO客户端
client = Minio(
    "minio-api.local",
    access_key="minioadmin",
    secret_key="minioadmin123",
    secure=False  # 如果使用HTTPS，设置为True
)

try:
    # 检查存储桶是否存在
    bucket_name = "python-test-bucket"
    if not client.bucket_exists(bucket_name):
        client.make_bucket(bucket_name)
        print(f"Bucket '{bucket_name}' created successfully")

    # 上传文件
    client.fput_object(bucket_name, "test.txt", "test.txt")
    print("File uploaded successfully")

    # 列出对象
    objects = client.list_objects(bucket_name)
    for obj in objects:
        print(f"Object: {obj.object_name}, Size: {obj.size}")

except S3Error as e:
    print(f"Error: {e}")
EOF

# 运行Python测试
python minio_test.py</code></pre>

                    <h4><i class="fas fa-java"></i> 7.2.2 Java示例</h4>
                    <pre><code># 创建Java测试项目目录
mkdir -p java-test
cd java-test

# 创建Maven配置文件
cat > pom.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.example</groupId>
    <artifactId>minio-test</artifactId>
    <version>1.0.0</version>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    <dependencies>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId>
            <version>8.5.7</version>
        </dependency>
    </dependencies>
</project>
EOF

# 创建Java测试类
mkdir -p src/main/java/com/example
cat > src/main/java/com/example/MinIOTest.java << 'EOF'
package com.example;

import io.minio.*;
import io.minio.errors.*;
import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

public class MinIOTest {
    public static void main(String[] args) {
        try {
            MinioClient minioClient = MinioClient.builder()
                .endpoint("http://minio-api.local")
                .credentials("minioadmin", "minioadmin123")
                .build();

            String bucketName = "java-test-bucket";

            // 检查存储桶是否存在
            if (!minioClient.bucketExists(BucketExistsArgs.builder()
                    .bucket(bucketName).build())) {
                minioClient.makeBucket(MakeBucketArgs.builder()
                    .bucket(bucketName).build());
                System.out.println("Bucket created successfully");
            }

            System.out.println("MinIO connection successful!");

        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
        }
    }
}
EOF</code></pre>
                </section>

                <section id="bucket-operations">
                    <h2><span class="step-number">8</span>存储桶操作</h2>

                    <h3><i class="fas fa-folder-plus"></i> 8.1 存储桶管理</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>

                    <h4><i class="fas fa-plus"></i> 8.1.1 创建和配置存储桶</h4>
                    <pre><code># 创建存储桶
mc mb myminio/my-app-data
mc mb myminio/backup-data
mc mb myminio/logs

# 设置存储桶策略（公共读取）
mc policy set public myminio/my-app-data

# 设置存储桶策略（私有）
mc policy set private myminio/backup-data

# 查看存储桶策略
mc policy get myminio/my-app-data

# 列出所有存储桶
mc ls myminio</code></pre>

                    <h4><i class="fas fa-shield-alt"></i> 8.1.2 版本控制配置</h4>
                    <pre><code># 启用版本控制
mc version enable myminio/my-app-data

# 查看版本控制状态
mc version info myminio/my-app-data

# 上传同名文件测试版本控制
echo "Version 1" > version-test.txt
mc cp version-test.txt myminio/my-app-data/

echo "Version 2" > version-test.txt
mc cp version-test.txt myminio/my-app-data/

# 查看文件版本
mc ls --versions myminio/my-app-data/version-test.txt</code></pre>

                    <h3><i class="fas fa-upload"></i> 8.2 文件操作</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>

                    <h4><i class="fas fa-file-upload"></i> 8.2.1 批量上传</h4>
                    <pre><code># 创建测试文件
mkdir -p test-data
for i in {1..10}; do
    echo "Test file $i content" > test-data/file$i.txt
done

# 批量上传文件
mc cp --recursive test-data/ myminio/my-app-data/test-data/

# 同步目录（增量上传）
mc mirror test-data/ myminio/my-app-data/test-data/

# 查看上传结果
mc ls --recursive myminio/my-app-data/test-data/</code></pre>

                    <h4><i class="fas fa-download"></i> 8.2.2 批量下载</h4>
                    <pre><code># 下载整个目录
mc cp --recursive myminio/my-app-data/test-data/ downloaded-data/

# 同步下载（增量下载）
mc mirror myminio/my-app-data/test-data/ downloaded-data/

# 查看下载结果
ls -la downloaded-data/</code></pre>

                    <h3><i class="fas fa-search"></i> 8.3 高级功能</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>

                    <h4><i class="fas fa-clock"></i> 8.3.1 生命周期管理</h4>
                    <pre><code># 创建生命周期规则配置文件
cat > lifecycle.json << 'EOF'
{
    "Rules": [
        {
            "ID": "DeleteOldFiles",
            "Status": "Enabled",
            "Expiration": {
                "Days": 30
            }
        }
    ]
}
EOF

# 应用生命周期规则
mc ilm import myminio/my-app-data < lifecycle.json

# 查看生命周期规则
mc ilm ls myminio/my-app-data</code></pre>

                    <h4><i class="fas fa-link"></i> 8.3.2 预签名URL</h4>
                    <pre><code># 生成预签名下载URL（有效期1小时）
mc share download --expire=1h myminio/my-app-data/test-data/file1.txt

# 生成预签名上传URL
mc share upload --expire=1h myminio/my-app-data/upload-test.txt

# 使用预签名URL上传文件（示例）
# curl -X PUT -T "local-file.txt" "预签名URL"</code></pre>
                </section>

                <section id="backup-restore">
                    <h2><span class="step-number">9</span>备份恢复</h2>

                    <h3><i class="fas fa-database"></i> 9.1 数据备份策略</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>

                    <h4><i class="fas fa-copy"></i> 9.1.1 存储桶备份</h4>
                    <pre><code># 创建备份脚本
cat > backup-minio.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/backup/minio/$(date +%Y%m%d_%H%M%S)"
SOURCE_ALIAS="myminio"
BACKUP_ALIAS="backup-minio"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份所有存储桶
for bucket in $(mc ls $SOURCE_ALIAS | awk '{print $5}'); do
    echo "Backing up bucket: $bucket"
    mc mirror $SOURCE_ALIAS/$bucket $BACKUP_DIR/$bucket
done

echo "Backup completed: $BACKUP_DIR"
EOF

chmod +x backup-minio.sh
./backup-minio.sh</code></pre>

                    <h4><i class="fas fa-clock"></i> 9.1.2 定时备份</h4>
                    <pre><code># 设置定时备份任务
crontab -e

# 添加每日凌晨2点备份任务
0 2 * * * /k8s/minio-config/backup-minio.sh >> /var/log/minio-backup.log 2>&1

# 查看定时任务
crontab -l</code></pre>

                    <h3><i class="fas fa-undo"></i> 9.2 数据恢复</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>

                    <h4><i class="fas fa-upload"></i> 9.2.1 恢复数据</h4>
                    <pre><code># 创建恢复脚本
cat > restore-minio.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/backup/minio/20231201_020000"  # 替换为实际备份目录
TARGET_ALIAS="myminio"

if [ ! -d "$BACKUP_DIR" ]; then
    echo "Backup directory not found: $BACKUP_DIR"
    exit 1
fi

# 恢复所有存储桶
for bucket_dir in $BACKUP_DIR/*/; do
    bucket_name=$(basename "$bucket_dir")
    echo "Restoring bucket: $bucket_name"

    # 创建存储桶（如果不存在）
    mc mb $TARGET_ALIAS/$bucket_name 2>/dev/null || true

    # 恢复数据
    mc mirror $bucket_dir $TARGET_ALIAS/$bucket_name
done

echo "Restore completed"
EOF

chmod +x restore-minio.sh</code></pre>
                </section>

                <section id="monitoring">
                    <h2><span class="step-number">10</span>监控维护</h2>

                    <h3><i class="fas fa-chart-line"></i> 10.1 监控配置</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>

                    <h4><i class="fas fa-heartbeat"></i> 10.1.1 健康检查</h4>
                    <pre><code># 创建健康检查脚本
cat > health-check.sh << 'EOF'
#!/bin/bash

echo "MinIO集群健康检查 - $(date)"
echo "================================"

# 检查Pod状态
echo "1. Pod状态检查:"
kubectl get pods -n minio -l app=minio

# 检查服务状态
echo -e "\n2. 服务状态检查:"
kubectl get svc -n minio

# 检查存储状态
echo -e "\n3. 存储状态检查:"
kubectl get pvc -n minio

# 检查MinIO集群状态
echo -e "\n4. MinIO集群状态:"
mc admin info myminio

# 检查存储使用情况
echo -e "\n5. 存储使用情况:"
mc admin prometheus metrics myminio | grep minio_disk

echo "================================"
echo "检查完成"
EOF

chmod +x health-check.sh
./health-check.sh</code></pre>

                    <h4><i class="fas fa-chart-bar"></i> 10.1.2 性能监控</h4>
                    <pre><code># 查看MinIO性能指标
mc admin prometheus metrics myminio

# 查看集群信息
mc admin cluster info myminio

# 查看服务器信息
mc admin service status myminio</code></pre>

                    <h3><i class="fas fa-tools"></i> 10.2 维护任务</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>

                    <h4><i class="fas fa-broom"></i> 10.2.1 清理任务</h4>
                    <pre><code># 创建清理脚本
cat > cleanup-minio.sh << 'EOF'
#!/bin/bash

echo "开始MinIO清理任务..."

# 清理过期的多部分上传
mc admin heal --scan deep myminio

# 清理未完成的上传
mc rm --incomplete --recursive myminio/

# 清理旧的备份文件（保留最近7天）
find /backup/minio -type d -mtime +7 -exec rm -rf {} \;

echo "清理任务完成"
EOF

chmod +x cleanup-minio.sh</code></pre>
                </section>

                <section id="troubleshooting">
                    <h2><span class="step-number">11</span>故障排查</h2>

                    <h3><i class="fas fa-bug"></i> 11.1 常见问题</h3>

                    <h4><i class="fas fa-exclamation-triangle"></i> 11.1.1 Pod启动失败</h4>
                    <div class="danger-box">
                        <strong>问题：</strong>MinIO Pod无法启动或一直重启
                    </div>
                    <div class="info-box">
                        <strong>解决方案：</strong>
                        <ol>
                            <li>检查PVC绑定状态：<code>kubectl get pvc -n minio</code></li>
                            <li>查看Pod日志：<code>kubectl logs minio-0 -n minio</code></li>
                            <li>检查存储权限：<code>ls -la /mnt/minio-data/</code></li>
                            <li>验证Secret配置：<code>kubectl get secret minio-secret -n minio -o yaml</code></li>
                        </ol>
                    </div>

                    <h4><i class="fas fa-network-wired"></i> 11.1.2 网络连接问题</h4>
                    <div class="danger-box">
                        <strong>问题：</strong>无法访问MinIO服务
                    </div>
                    <div class="info-box">
                        <strong>解决方案：</strong>
                        <ol>
                            <li>检查Service状态：<code>kubectl get svc -n minio</code></li>
                            <li>验证Ingress配置：<code>kubectl get ingress -n minio</code></li>
                            <li>测试内部连接：<code>kubectl exec -it minio-0 -n minio -- curl localhost:9000/minio/health/live</code>
                            </li>
                            <li>检查防火墙设置</li>
                        </ol>
                    </div>

                    <h3><i class="fas fa-search"></i> 11.2 日志分析</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>

                    <pre><code># 查看MinIO Pod日志
kubectl logs -f minio-0 -n minio

# 查看所有MinIO Pod日志
kubectl logs -f -l app=minio -n minio

# 查看Ingress控制器日志
kubectl logs -f -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx

# 查看事件
kubectl get events -n minio --sort-by='.lastTimestamp'</code></pre>
                </section>

                <section id="deployment-verification">
                    <h2><span class="step-number">12</span>部署验证脚本</h2>

                    <h3><i class="fas fa-check-circle"></i> 12.1 完整验证脚本</h3>
                    <div class="machine-tag machine-client"><i class="fas fa-laptop"></i> 客户端机器</div>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 说明：</strong>运行此脚本可以全面验证MinIO集群的部署状态和功能
                    </div>

                    <pre><code>#!/bin/bash
# MinIO集群部署验证脚本

echo "=== MinIO集群部署验证开始 ==="

# 1. 检查命名空间
echo "1. 检查命名空间..."
kubectl get namespace minio
if [ $? -ne 0 ]; then
    echo "❌ MinIO命名空间不存在"
    exit 1
fi
echo "✅ MinIO命名空间存在"

# 2. 检查存储类
echo "2. 检查存储类..."
kubectl get storageclass minio-local-storage
if [ $? -ne 0 ]; then
    echo "❌ MinIO存储类不存在"
    exit 1
fi
echo "✅ MinIO存储类存在"

# 3. 检查PV状态
echo "3. 检查持久化卷..."
pv_count=$(kubectl get pv | grep minio | grep Available | wc -l)
if [ $pv_count -lt 3 ]; then
    echo "❌ 可用的MinIO PV数量不足: $pv_count/3"
    kubectl get pv | grep minio
    exit 1
fi
echo "✅ 所有MinIO PV都可用"

# 4. 检查Secret
echo "4. 检查访问凭证..."
kubectl get secret minio-secret -n minio
if [ $? -ne 0 ]; then
    echo "❌ MinIO访问凭证不存在"
    exit 1
fi
echo "✅ MinIO访问凭证存在"

# 5. 检查StatefulSet
echo "5. 检查StatefulSet..."
kubectl get statefulset minio -n minio
if [ $? -ne 0 ]; then
    echo "❌ MinIO StatefulSet不存在"
    exit 1
fi

# 检查副本数
ready_replicas=$(kubectl get statefulset minio -n minio -o jsonpath='{.status.readyReplicas}')
desired_replicas=$(kubectl get statefulset minio -n minio -o jsonpath='{.spec.replicas}')
if [ "$ready_replicas" != "$desired_replicas" ]; then
    echo "❌ MinIO StatefulSet副本数不匹配: $ready_replicas/$desired_replicas"
    kubectl get pods -n minio
    exit 1
fi
echo "✅ MinIO StatefulSet运行正常"

# 6. 检查Pod状态
echo "6. 检查Pod状态..."
pod_count=$(kubectl get pods -n minio -l app=minio --field-selector=status.phase=Running | grep -v NAME | wc -l)
if [ $pod_count -lt 3 ]; then
    echo "❌ 运行中的MinIO Pod数量不足: $pod_count/3"
    kubectl get pods -n minio -l app=minio
    exit 1
fi
echo "✅ 所有MinIO Pod运行正常"

# 7. 检查PVC绑定
echo "7. 检查PVC绑定..."
bound_pvc=$(kubectl get pvc -n minio --field-selector=status.phase=Bound | grep -v NAME | wc -l)
if [ $bound_pvc -lt 3 ]; then
    echo "❌ 绑定的PVC数量不足: $bound_pvc/3"
    kubectl get pvc -n minio
    exit 1
fi
echo "✅ 所有PVC绑定成功"

# 8. 检查服务
echo "8. 检查服务..."
kubectl get svc -n minio | grep minio
if [ $? -ne 0 ]; then
    echo "❌ MinIO服务不存在"
    exit 1
fi
echo "✅ MinIO服务存在"

# 9. 测试API健康检查
echo "9. 测试API健康检查..."
kubectl exec -it minio-0 -n minio -- curl -f http://localhost:9000/minio/health/live
if [ $? -ne 0 ]; then
    echo "❌ MinIO API健康检查失败"
    exit 1
fi
echo "✅ MinIO API健康检查通过"

# 10. 检查集群状态
echo "10. 检查集群状态..."
kubectl exec -it minio-0 -n minio -- mc config host add local http://localhost:9000 minioadmin minioadmin123
kubectl exec -it minio-0 -n minio -- mc admin info local
if [ $? -ne 0 ]; then
    echo "❌ MinIO集群状态检查失败"
    exit 1
fi
echo "✅ MinIO集群状态正常"

echo "=== MinIO集群部署验证完成 ==="
echo "🎉 所有检查项都通过，MinIO集群部署成功！"

# 显示访问信息
echo ""
echo "=== 访问信息 ==="
echo "API端点: http://minio-api.local:9000"
echo "Console端点: http://minio-console.local:9001"
echo "用户名: minioadmin"
echo "密码: minioadmin123"
echo ""
echo "NodePort访问（如果配置）:"
echo "API: http://*************:30900"
echo "Console: http://*************:30901"</code></pre>

                    <h3><i class="fas fa-play"></i> 12.2 运行验证脚本</h3>
                    <pre><code># 保存验证脚本
cat > verify-minio.sh << 'EOF'
# 将上面的脚本内容粘贴到这里
EOF

# 设置执行权限
chmod +x verify-minio.sh

# 运行验证脚本
./verify-minio.sh</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 验证成功标志：</strong>
                        <ul>
                            <li>所有检查项显示 ✅ 通过</li>
                            <li>显示MinIO集群访问信息</li>
                            <li>可以正常访问MinIO Console</li>
                            <li>API健康检查返回正常</li>
                        </ul>
                    </div>
                </section>

                <section id="summary">
                    <h2><span class="step-number">13</span>总结</h2>

                    <h3><i class="fas fa-check-circle"></i> 12.1 部署总结</h3>
                    <p>通过本教程，我们成功在Kubernetes集群中部署了MinIO对象存储服务，包括：</p>

                    <div class="success-box">
                        <strong><i class="fas fa-trophy"></i> 已完成的功能：</strong>
                        <ul>
                            <li><strong><i class="fas fa-server"></i> 集群部署：</strong>3节点MinIO集群，提供高可用性</li>
                            <li><strong><i class="fas fa-hdd"></i> 持久化存储：</strong>基于PVC的数据持久化</li>
                            <li><strong><i class="fas fa-lock"></i> SSL支持：</strong>HTTPS加密传输</li>
                            <li><strong><i class="fas fa-globe"></i> 外部访问：</strong>Ingress和NodePort访问方式</li>
                            <li><strong><i class="fas fa-folder"></i> 存储桶管理：</strong>完整的存储桶操作功能</li>
                            <li><strong><i class="fas fa-database"></i> 备份恢复：</strong>数据备份和恢复方案</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-lightbulb"></i> 12.2 最佳实践</h3>
                    <div class="info-box">
                        <strong><i class="fas fa-star"></i> 生产环境建议：</strong>
                        <ul>
                            <li><strong>高可用：</strong>至少3个节点，跨可用区部署</li>
                            <li><strong>存储：</strong>使用高性能SSD，配置RAID</li>
                            <li><strong>网络：</strong>使用专用网络，配置负载均衡</li>
                            <li><strong>安全：</strong>启用TLS，配置访问策略</li>
                            <li><strong>监控：</strong>集成Prometheus监控</li>
                            <li><strong>备份：</strong>定期备份，异地存储</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-road"></i> 12.3 扩展方向</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-plus"></i> 扩展功能</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                            <th><i class="fas fa-star"></i> 优先级</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-chart-line"></i> Prometheus监控</td>
                            <td>集成Prometheus和Grafana监控</td>
                            <td>高</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-users"></i> 多租户</td>
                            <td>配置多租户访问控制</td>
                            <td>中</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-sync-alt"></i> 跨区域复制</td>
                            <td>配置跨区域数据复制</td>
                            <td>中</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-shield-alt"></i> 数据加密</td>
                            <td>启用服务端加密</td>
                            <td>高</td>
                        </tr>
                    </table>

                    <div class="success-box">
                        <strong><i class="fas fa-graduation-cap"></i>
                            恭喜！</strong>您已经成功在Kubernetes集群中部署了MinIO对象存储服务。现在可以开始在您的应用中使用S3兼容的对象存储功能了。
                    </div>
                </section>
            </div>
        </div>
    </div>

    <!-- 返回顶部按钮 -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // 移动端菜单切换
        document.getElementById('mobileMenuBtn').addEventListener('click', function () {
            document.getElementById('sidebar').classList.toggle('active');
        });

        // 返回顶部功能
        window.addEventListener('scroll', function () {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'flex';
            } else {
                backToTop.style.display = 'none';
            }
        });

        document.getElementById('backToTop').addEventListener('click', function (e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 侧边栏导航高亮
        window.addEventListener('scroll', function () {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.sidebar a[href^="#"]');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // 平滑滚动
        document.querySelectorAll('.sidebar a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }

                // 移动端关闭菜单
                if (window.innerWidth <= 768) {
                    document.getElementById('sidebar').classList.remove('active');
                }
            });
        });
    </script>
</body>

</html>