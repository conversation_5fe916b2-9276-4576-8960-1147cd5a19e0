<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL 8.0绿色版Docker部署完整教程（Rocky8基础镜像）</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 13px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 机器标识样式 */
        .machine-tag {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            margin: 0 8px 12px 0;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .machine-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .machine-tag:hover::before {
            left: 100%;
        }

        .machine-host {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
        }

        .machine-docker {
            background: linear-gradient(135deg, #45b7d1 0%, #96c93d 100%);
            color: white;
        }

        .machine-mysql {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: var(--light-surface);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        th,
        td {
            padding: 18px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 下载链接 */
        .download-link {
            background: linear-gradient(135deg, var(--success-color) 0%, #38a169 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            display: inline-flex;
            align-items: center;
            margin: 8px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            font-size: 14px;
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
        }

        .download-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .download-link:hover::before {
            left: 100%;
        }

        .download-link:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
        }

        .download-link i {
            margin-right: 8px;
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-decoration: none;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            z-index: 999;
            font-size: 20px;
        }

        .back-to-top:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
                border-radius: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 16px;
                margin-right: 15px;
            }
        }
    </style>
</head>

<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-database"></i> MySQL部署教程</h2>
            <p>MySQL 8.0绿色版Docker部署指南（Rocky8基础镜像）</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#overview"><i class="fas fa-info-circle"></i>1. 教程概述</a></li>
                <li><a href="#environment-preparation"><i class="fas fa-cog"></i>2. 环境准备</a></li>
                <li><a href="#download-mysql"><i class="fas fa-download"></i>3. 下载MySQL包</a></li>
                <li><a href="#dockerfile-stage1"><i class="fas fa-layer-group"></i>4. 第一阶段：基础镜像</a></li>
                <li><a href="#dockerfile-stage2"><i class="fas fa-tools"></i>5. 第二阶段：MySQL安装</a></li>
                <li><a href="#dockerfile-stage3"><i class="fas fa-cogs"></i>6. 第三阶段：配置优化</a></li>
                <li><a href="#dockerfile-stage4"><i class="fas fa-rocket"></i>7. 第四阶段：最终镜像</a></li>
                <li><a href="#build-image"><i class="fas fa-hammer"></i>8. 构建镜像</a></li>
                <li><a href="#run-container"><i class="fas fa-play"></i>9. 运行容器</a></li>
                <li><a href="#configuration"><i class="fas fa-wrench"></i>10. 配置管理</a></li>
                <li><a href="#data-persistence"><i class="fas fa-save"></i>11. 数据持久化</a></li>
                <li><a href="#security-setup"><i class="fas fa-shield-alt"></i>12. 安全配置</a></li>
                <li><a href="#performance-tuning"><i class="fas fa-tachometer-alt"></i>13. 性能调优</a></li>
                <li><a href="#backup-restore"><i class="fas fa-archive"></i>14. 备份恢复</a></li>
                <li><a href="#monitoring"><i class="fas fa-chart-line"></i>15. 监控管理</a></li>
                <li><a href="#troubleshooting"><i class="fas fa-bug"></i>16. 故障排查</a></li>
                <li><a href="#summary"><i class="fas fa-flag-checkered"></i>17. 总结检查</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-database"></i> MySQL 8.0绿色版Docker部署完整教程（Rocky8基础镜像）</h1>

                <div class="success-box">
                    <strong><i class="fas fa-star"></i> 全新MySQL 8.0教程特色：</strong>
                    <ul style="margin-top: 15px;">
                        <li><strong>最新版本：</strong>基于MySQL 8.0.35 LTS版本，提供长期支持</li>
                        <li><strong>现代化架构：</strong>采用四阶段构建，更加模块化和可维护</li>
                        <li><strong>安全增强：</strong>集成MySQL 8.0的新安全特性和认证插件</li>
                        <li><strong>性能优化：</strong>针对MySQL 8.0的新特性进行专门优化</li>
                        <li><strong>容器化最佳实践：</strong>遵循Docker最佳实践和安全规范</li>
                        <li><strong>生产就绪：</strong>包含完整的监控、备份和故障恢复方案</li>
                        <li><strong>详细说明：</strong>每个步骤都有详细解释，适合小白学习</li>
                    </ul>
                </div>

                <div class="info-box">
                    <strong><i class="fas fa-info-circle"></i>
                        教程说明：</strong>本教程详细介绍了如何使用MySQL 8.0绿色tar.gz版本包在Rocky Linux 8基础镜像上构建自定义MySQL
                    Docker镜像的完整过程。采用四阶段构建方式，每个步骤都有详细的注释和说明，适合小白学习和生产环境部署。
                </div>

                <div class="warning-box">
                    <strong><i class="fas fa-exclamation-triangle"></i> 执行环境说明：</strong>
                    <div style="margin-top: 15px;">
                        <span class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</span> -
                        在Docker宿主机上执行<br>
                        <span class="machine-tag machine-docker"><i class="fas fa-whale"></i> Docker容器</span> -
                        在Docker容器内执行<br>
                        <span class="machine-tag machine-mysql"><i class="fas fa-database"></i> MySQL容器</span> -
                        在MySQL容器内执行
                    </div>
                </div>

                <section id="overview">
                    <h2><span class="step-number">1</span>教程概述</h2>

                    <h3><i class="fas fa-target"></i> 1.1 部署目标</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-bullseye"></i> 主要目标</h4>
                        <ul>
                            <li><strong><i class="fas fa-box"></i> 自定义镜像：</strong>基于Rocky Linux 8构建MySQL 8.0绿色版Docker镜像
                            </li>
                            <li><strong><i class="fas fa-layer-group"></i> 四阶段构建：</strong>采用多阶段构建方式，便于理解和维护</li>
                            <li><strong><i class="fas fa-cogs"></i> 完整配置：</strong>包含安全配置、性能优化、数据持久化等</li>
                            <li><strong><i class="fas fa-shield-alt"></i> 生产就绪：</strong>满足生产环境的安全和性能要求</li>
                            <li><strong><i class="fas fa-rocket"></i> 现代化特性：</strong>充分利用MySQL 8.0的新特性和改进</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-list-check"></i> 1.2 技术特点</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 特性</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                            <th><i class="fas fa-star"></i> 优势</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-download"></i> 绿色版安装</td>
                            <td>使用官方tar.gz包进行安装</td>
                            <td>无依赖冲突，版本可控</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-layer-group"></i> 四阶段构建</td>
                            <td>将构建过程分为四个阶段</td>
                            <td>便于理解、调试和维护</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-linux"></i> Rocky Linux 8基础</td>
                            <td>使用稳定的Rocky Linux 8作为基础镜像</td>
                            <td>兼容性好，长期支持</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-cog"></i> 自定义配置</td>
                            <td>完全自定义MySQL 8.0配置</td>
                            <td>灵活性高，可优化</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-shield-alt"></i> 安全增强</td>
                            <td>集成MySQL 8.0新安全特性</td>
                            <td>更高的安全性</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-route"></i> 1.3 构建流程</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-list-ol"></i> 四阶段构建说明</h4>
                        <ol>
                            <li><strong>第一阶段：</strong>准备基础环境，安装必要的系统依赖和工具</li>
                            <li><strong>第二阶段：</strong>下载并安装MySQL 8.0绿色版，配置基础环境</li>
                            <li><strong>第三阶段：</strong>配置MySQL服务、优化设置和安全配置</li>
                            <li><strong>第四阶段：</strong>最终镜像构建，添加启动脚本和健康检查</li>
                        </ol>
                    </div>

                    <h3><i class="fas fa-star"></i> 1.4 MySQL 8.0新特性</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-rocket"></i> 主要改进</h4>
                        <ul>
                            <li><strong>性能提升：</strong>查询性能提升2倍，写入性能提升1.5倍</li>
                            <li><strong>JSON支持：</strong>原生JSON数据类型和函数</li>
                            <li><strong>窗口函数：</strong>支持ROW_NUMBER()、RANK()等窗口函数</li>
                            <li><strong>CTE支持：</strong>公共表表达式（Common Table Expressions）</li>
                            <li><strong>角色管理：</strong>更灵活的用户权限管理</li>
                            <li><strong>数据字典：</strong>事务性数据字典，提高可靠性</li>
                            <li><strong>原子DDL：</strong>DDL操作的原子性保证</li>
                        </ul>
                    </div>
                </section>

                <section id="environment-preparation">
                    <h2><span class="step-number">2</span>环境准备</h2>

                    <h3><i class="fas fa-desktop"></i> 2.1 系统要求</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-list"></i> 项目</th>
                            <th><i class="fas fa-cogs"></i> 要求</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td><i class="fas fa-linux"></i> 操作系统</td>
                            <td>Linux (Rocky/RHEL/Ubuntu)</td>
                            <td>支持Docker运行的Linux发行版</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-whale"></i> Docker版本</td>
                            <td>Docker CE 20.10+</td>
                            <td>支持多阶段构建功能</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-memory"></i> 内存</td>
                            <td>至少6GB可用内存</td>
                            <td>MySQL 8.0需要更多内存</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-hdd"></i> 磁盘空间</td>
                            <td>至少15GB可用空间</td>
                            <td>存储镜像和数据文件</td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-network-wired"></i> 网络</td>
                            <td>可访问互联网</td>
                            <td>下载MySQL安装包</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-check-circle"></i> 2.2 环境检查</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>在开始构建之前，请先检查Docker环境是否正常：</p>
                    <pre><code># 检查Docker版本
docker --version

# 检查Docker服务状态
systemctl status docker

# 检查Docker信息
docker info

# 测试Docker运行
docker run hello-world

# 检查可用磁盘空间
df -h

# 检查可用内存
free -h

# 检查CPU信息
lscpu | grep -E "^CPU\(s\)|Model name"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 验证要点：</strong>
                        <ul style="margin-top: 10px;">
                            <li>Docker版本应为20.10或更高版本</li>
                            <li>Docker服务状态应为active (running)</li>
                            <li>hello-world容器能够正常运行</li>
                            <li>可用磁盘空间大于15GB</li>
                            <li>可用内存大于6GB</li>
                            <li>CPU支持64位架构</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-folder"></i> 2.3 创建工作目录</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建专门的工作目录来存放构建文件：</p>
                    <pre><code># 创建工作目录
mkdir -p /opt/mysql8-docker/{dockerfile,config,scripts,data,logs}

# 进入工作目录
cd /opt/mysql8-docker

# 查看目录结构
tree /opt/mysql8-docker || ls -la /opt/mysql8-docker

# 设置目录权限
chmod 755 /opt/mysql8-docker
chmod 755 /opt/mysql8-docker/*</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 目录说明：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>dockerfile/：</strong>存放各阶段Dockerfile文件</li>
                            <li><strong>config/：</strong>存放MySQL配置文件</li>
                            <li><strong>scripts/：</strong>存放初始化和启动脚本</li>
                            <li><strong>data/：</strong>存放数据文件（用于数据持久化）</li>
                            <li><strong>logs/：</strong>存放日志文件</li>
                        </ul>
                    </div>
                </section>

                <section id="download-mysql">
                    <h2><span class="step-number">3</span>下载MySQL包</h2>

                    <h3><i class="fas fa-download"></i> 3.1 获取MySQL 8.0绿色版</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>下载MySQL 8.0官方绿色版tar.gz包：</p>
                    <pre><code># 进入工作目录
cd /opt/mysql8-docker

# 下载MySQL 8.0.35绿色版（LTS版本）
wget https://dev.mysql.com/get/Downloads/MySQL-8.0/mysql-8.0.35-linux-glibc2.28-x86_64.tar.xz

# 验证下载文件
ls -lh mysql-8.0.35-linux-glibc2.28-x86_64.tar.xz

# 检查文件完整性（可选）
sha256sum mysql-8.0.35-linux-glibc2.28-x86_64.tar.xz</code></pre>

                    <a href="https://dev.mysql.com/get/Downloads/MySQL-8.0/mysql-8.0.35-linux-glibc2.28-x86_64.tar.xz"
                        class="download-link">
                        <i class="fas fa-download"></i> 下载 MySQL 8.0.35 LTS
                    </a>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 版本说明：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>MySQL 8.0.35：</strong>MySQL 8.0系列的LTS（长期支持）版本</li>
                            <li><strong>linux-glibc2.28：</strong>适用于Rocky Linux 8及以上版本</li>
                            <li><strong>x86_64：</strong>64位架构版本</li>
                            <li><strong>文件格式：</strong>tar.xz压缩格式，体积更小</li>
                            <li><strong>文件大小：</strong>约450MB</li>
                            <li><strong>支持周期：</strong>LTS版本提供长期支持和安全更新</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <strong><i class="fas fa-star"></i> MySQL 8.0优势：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>长期支持：</strong>MySQL 8.0是当前的LTS版本，提供长期支持</li>
                            <li><strong>性能提升：</strong>相比5.7版本有显著性能提升</li>
                            <li><strong>新特性：</strong>支持JSON、窗口函数、CTE等现代SQL特性</li>
                            <li><strong>安全增强：</strong>更强的密码验证和角色管理</li>
                            <li><strong>生产推荐：</strong>官方推荐用于新项目和生产环境</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-archive"></i> 3.2 验证安装包</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>验证下载的MySQL安装包是否完整：</p>
                    <pre><code># 查看文件详细信息
file mysql-8.0.35-linux-glibc2.28-x86_64.tar.xz

# 测试压缩包完整性
tar -tJf mysql-8.0.35-linux-glibc2.28-x86_64.tar.xz > /dev/null && echo "压缩包完整" || echo "压缩包损坏"

# 查看压缩包内容（前10行）
tar -tJf mysql-8.0.35-linux-glibc2.28-x86_64.tar.xz | head -10

# 查看压缩包大小和文件数量
echo "文件数量: $(tar -tJf mysql-8.0.35-linux-glibc2.28-x86_64.tar.xz | wc -l)"
echo "解压后大小: $(tar -tJf mysql-8.0.35-linux-glibc2.28-x86_64.tar.xz | xargs -I {} echo {} | wc -c) bytes"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 验证成功标志：</strong>
                        <ul style="margin-top: 10px;">
                            <li>文件类型显示为XZ compressed data</li>
                            <li>完整性检查输出"压缩包完整"</li>
                            <li>能够正常列出压缩包内容</li>
                            <li>包含mysql-8.0.35-linux-glibc2.28-x86_64目录</li>
                            <li>文件数量约为3000+个文件</li>
                        </ul>
                    </div>
                </section>

                <section id="dockerfile-stage1">
                    <h2><span class="step-number">4</span>第一阶段：基础镜像</h2>

                    <h3><i class="fas fa-file-code"></i> 4.1 创建健康检查脚本</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建健康检查脚本，用于监控MySQL 8.0服务状态：</p>
                    <pre><code># 创建健康检查脚本
cat > /opt/mysql8-docker/scripts/health-check.sh << 'EOF'
#!/bin/bash
# ==========================================
# MySQL 8.0 健康检查脚本
# 用于Docker容器健康状态检查
# 支持MySQL 8.0新特性
# ==========================================

# 设置错误处理
set -e

# MySQL连接参数
MYSQL_HOST="localhost"
MYSQL_PORT="3306"
MYSQL_USER="root"
MYSQL_PASSWORD="${MYSQL_ROOT_PASSWORD:-}"

# 检查MySQL进程是否运行
check_mysql_process() {
    if pgrep mysqld > /dev/null; then
        echo "✓ MySQL进程正在运行"
        return 0
    else
        echo "✗ MySQL进程未运行"
        return 1
    fi
}

# 检查MySQL端口是否监听
check_mysql_port() {
    # 尝试使用ss命令（推荐）
    if command -v ss > /dev/null; then
        if ss -ln | grep ":${MYSQL_PORT}" > /dev/null; then
            echo "✓ MySQL端口${MYSQL_PORT}正在监听"
            return 0
        fi
    # 备用netstat命令
    elif command -v netstat > /dev/null; then
        if netstat -ln | grep ":${MYSQL_PORT}" > /dev/null; then
            echo "✓ MySQL端口${MYSQL_PORT}正在监听"
            return 0
        fi
    # 使用nc命令测试端口
    elif command -v nc > /dev/null; then
        if nc -z localhost ${MYSQL_PORT} 2>/dev/null; then
            echo "✓ MySQL端口${MYSQL_PORT}正在监听"
            return 0
        fi
    fi

    echo "✗ MySQL端口${MYSQL_PORT}未监听"
    return 1
}

# 检查MySQL连接和基本功能
check_mysql_connection() {
    local mysql_cmd="mysql -h${MYSQL_HOST} -P${MYSQL_PORT} -u${MYSQL_USER}"

    if [ -n "$MYSQL_PASSWORD" ]; then
        mysql_cmd="$mysql_cmd -p${MYSQL_PASSWORD}"
    fi

    # 基本连接测试
    if $mysql_cmd -e "SELECT 1;" > /dev/null 2>&1; then
        echo "✓ MySQL连接正常"
    else
        echo "✗ MySQL连接失败"
        return 1
    fi

    # 检查MySQL版本
    local version=$($mysql_cmd -e "SELECT VERSION();" 2>/dev/null | tail -n 1)
    if [[ $version == 8.0* ]]; then
        echo "✓ MySQL版本: $version"
    else
        echo "✗ MySQL版本异常: $version"
        return 1
    fi

    # 检查数据库状态
    local status=$($mysql_cmd -e "SHOW STATUS LIKE 'Uptime';" 2>/dev/null | tail -n 1 | awk '{print $2}')
    if [ "$status" -gt 0 ] 2>/dev/null; then
        echo "✓ MySQL运行时间: ${status}秒"
    else
        echo "✗ 无法获取MySQL状态"
        return 1
    fi

    return 0
}

# 主检查函数
main() {
    echo "开始MySQL 8.0健康检查..."

    # 检查进程
    if ! check_mysql_process; then
        exit 1
    fi

    # 检查端口
    if ! check_mysql_port; then
        exit 1
    fi

    # 检查连接
    if ! check_mysql_connection; then
        exit 1
    fi

    echo "✓ MySQL 8.0健康检查通过"
    exit 0
}

# 执行主函数
main "$@"
EOF

# 设置脚本权限
chmod +x /opt/mysql8-docker/scripts/health-check.sh</code></pre>

                    <h3><i class="fas fa-layer-group"></i> 4.2 创建第一阶段Dockerfile</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建第一阶段的Dockerfile，主要用于准备基础环境：</p>
                    <pre><code># 创建第一阶段Dockerfile
cat > /opt/mysql8-docker/dockerfile/Dockerfile.stage1 << 'EOF'
# ==========================================
# MySQL 8.0 绿色版 Docker 镜像构建
# 第一阶段：基础环境准备
# 基础镜像：Rocky Linux 8
# ==========================================

FROM rockylinux:8

# 设置维护者信息
LABEL maintainer="MySQL 8.0 Docker Builder"
LABEL description="MySQL 8.0 Green Version - Stage 1: Base Environment"
LABEL version="1.0"
LABEL mysql.version="8.0.35"

# 设置环境变量
ENV LANG=en_US.UTF-8
ENV LC_ALL=en_US.UTF-8
ENV TZ=Asia/Shanghai

# 更新系统并安装基础依赖包
RUN dnf update -y && \
    dnf install -y \
        # 基础工具
        wget \
        curl \
        tar \
        xz \
        gzip \
        which \
        # 编译工具（MySQL 8.0可能需要）
        gcc \
        gcc-c++ \
        make \
        cmake \
        # 系统库
        libaio \
        libaio-devel \
        numactl \
        numactl-devel \
        # 网络工具
        net-tools \
        iproute \
        # 进程管理
        procps-ng \
        # 文本处理
        sed \
        grep \
        gawk \
        # SSL支持
        openssl \
        openssl-devel \
        # 其他依赖
        ncurses-compat-libs && \
    # 清理缓存
    dnf clean all && \
    rm -rf /var/cache/dnf/*

# 创建mysql用户和组
RUN groupadd -r mysql && \
    useradd -r -g mysql -s /bin/false mysql

# 创建MySQL相关目录
RUN mkdir -p /usr/local/mysql && \
    mkdir -p /var/lib/mysql && \
    mkdir -p /var/log/mysql && \
    mkdir -p /etc/mysql && \
    mkdir -p /var/run/mysqld && \
    mkdir -p /etc/mysql/conf.d && \
    # 设置目录权限
    chown -R mysql:mysql /var/lib/mysql && \
    chown -R mysql:mysql /var/log/mysql && \
    chown -R mysql:mysql /etc/mysql && \
    chown -R mysql:mysql /var/run/mysqld

# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo 'Asia/Shanghai' > /etc/timezone

# 创建工作目录
WORKDIR /tmp

# 添加健康检查脚本（第一阶段暂不启用）
COPY scripts/health-check.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/health-check.sh

# 暴露MySQL默认端口（预留）
EXPOSE 3306

# 设置默认命令（第一阶段仅用于测试）
CMD ["/bin/bash"]
EOF</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 第一阶段说明：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>基础镜像：</strong>使用官方Rocky Linux 8镜像</li>
                            <li><strong>系统更新：</strong>使用dnf更新所有系统包到最新版本</li>
                            <li><strong>依赖安装：</strong>安装MySQL 8.0运行所需的系统依赖</li>
                            <li><strong>用户创建：</strong>创建专用的mysql用户和组</li>
                            <li><strong>目录准备：</strong>创建MySQL相关的目录结构</li>
                            <li><strong>SSL支持：</strong>安装OpenSSL支持MySQL 8.0的加密特性</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-hammer"></i> 4.3 构建第一阶段镜像</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>构建第一阶段的基础镜像：</p>
                    <pre><code># 进入工作目录
cd /opt/mysql8-docker

# 构建第一阶段镜像
docker build -f dockerfile/Dockerfile.stage1 -t mysql80-base:stage1 .

# 查看构建的镜像
docker images | grep mysql80-base

# 测试第一阶段镜像
docker run --rm -it mysql80-base:stage1 /bin/bash -c "
    echo '测试基础环境...'
    echo '用户信息:' && id mysql
    echo 'MySQL目录:' && ls -la /usr/local/mysql /var/lib/mysql
    echo '系统信息:' && cat /etc/redhat-release
    echo '时区信息:' && date
    echo 'OpenSSL版本:' && openssl version
"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 第一阶段验证：</strong>
                        <ul style="margin-top: 10px;">
                            <li>镜像构建成功，无错误信息</li>
                            <li>mysql用户和组创建成功</li>
                            <li>MySQL相关目录创建完成</li>
                            <li>系统时区设置正确</li>
                            <li>基础依赖包安装完成</li>
                            <li>OpenSSL版本正常显示</li>
                        </ul>
                    </div>
                </section>

                <section id="dockerfile-stage2">
                    <h2><span class="step-number">5</span>第二阶段：MySQL安装</h2>

                    <h3><i class="fas fa-tools"></i> 5.1 创建第二阶段Dockerfile</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建第二阶段的Dockerfile，主要用于安装MySQL 8.0：</p>
                    <pre><code># 创建第二阶段Dockerfile
cat > /opt/mysql8-docker/dockerfile/Dockerfile.stage2 << 'EOF'
# ==========================================
# MySQL 8.0 绿色版 Docker 镜像构建
# 第二阶段：MySQL安装
# 基于第一阶段镜像
# ==========================================

FROM mysql80-base:stage1

# 设置维护者信息
LABEL maintainer="MySQL 8.0 Docker Builder"
LABEL description="MySQL 8.0 Green Version - Stage 2: MySQL Installation"
LABEL version="2.0"

# 设置MySQL环境变量
ENV MYSQL_VERSION=8.0.35
ENV MYSQL_HOME=/usr/local/mysql
ENV MYSQL_DATADIR=/var/lib/mysql
ENV MYSQL_USER=mysql
ENV PATH=$MYSQL_HOME/bin:$PATH

# 复制MySQL安装包到容器
COPY mysql-8.0.35-linux-glibc2.28-x86_64.tar.xz /tmp/

# 安装MySQL
RUN cd /tmp && \
    # 解压MySQL安装包
    echo "解压MySQL 8.0安装包..." && \
    tar -xJf mysql-8.0.35-linux-glibc2.28-x86_64.tar.xz && \
    # 移动到安装目录
    echo "安装MySQL到 $MYSQL_HOME ..." && \
    mv mysql-8.0.35-linux-glibc2.28-x86_64/* $MYSQL_HOME/ && \
    # 设置权限
    echo "设置MySQL目录权限..." && \
    chown -R mysql:mysql $MYSQL_HOME && \
    # 创建符号链接
    echo "创建MySQL命令符号链接..." && \
    ln -sf $MYSQL_HOME/bin/mysql /usr/local/bin/mysql && \
    ln -sf $MYSQL_HOME/bin/mysqld /usr/local/bin/mysqld && \
    ln -sf $MYSQL_HOME/bin/mysqladmin /usr/local/bin/mysqladmin && \
    ln -sf $MYSQL_HOME/bin/mysqldump /usr/local/bin/mysqldump && \
    ln -sf $MYSQL_HOME/bin/mysqlpump /usr/local/bin/mysqlpump && \
    ln -sf $MYSQL_HOME/bin/mysql_secure_installation /usr/local/bin/mysql_secure_installation && \
    # 清理临时文件
    echo "清理临时文件..." && \
    rm -rf /tmp/mysql-8.0.35-linux-glibc2.28-x86_64* && \
    # 验证安装
    echo "验证MySQL安装..." && \
    $MYSQL_HOME/bin/mysqld --version

# 创建MySQL配置文件目录
RUN mkdir -p /etc/mysql/conf.d && \
    chown -R mysql:mysql /etc/mysql

# 设置工作目录
WORKDIR $MYSQL_HOME

# 暴露MySQL端口
EXPOSE 3306

# 设置默认命令（第二阶段用于测试MySQL安装）
CMD ["/bin/bash", "-c", "mysqld --version && /bin/bash"]
EOF</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
                        <ul style="margin-top: 10px;">
                            <li>确保MySQL安装包已下载到工作目录</li>
                            <li>第二阶段依赖第一阶段镜像，需先构建第一阶段</li>
                            <li>安装过程会创建必要的符号链接</li>
                            <li>所有MySQL相关目录权限已正确设置</li>
                            <li>MySQL 8.0需要更多的系统依赖</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-hammer"></i> 5.2 构建第二阶段镜像</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>构建第二阶段的MySQL安装镜像：</p>
                    <pre><code># 确保在工作目录
cd /opt/mysql8-docker

# 验证MySQL安装包存在
ls -lh mysql-8.0.35-linux-glibc2.28-x86_64.tar.xz

# 构建第二阶段镜像
docker build -f dockerfile/Dockerfile.stage2 -t mysql80-install:stage2 .

# 查看构建的镜像
docker images | grep mysql80

# 测试第二阶段镜像
docker run --rm -it mysql80-install:stage2 /bin/bash -c "
    echo '测试MySQL安装...'
    echo 'MySQL版本:' && mysqld --version
    echo 'MySQL路径:' && which mysql mysqld
    echo 'MySQL目录:' && ls -la /usr/local/mysql/bin/ | head -5
    echo 'MySQL用户:' && id mysql
    echo '配置目录:' && ls -la /etc/mysql/
"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 第二阶段验证：</strong>
                        <ul style="margin-top: 10px;">
                            <li>MySQL 8.0.35安装成功</li>
                            <li>MySQL命令可正常执行</li>
                            <li>符号链接创建正确</li>
                            <li>目录权限设置正确</li>
                            <li>临时文件清理完成</li>
                            <li>配置目录创建成功</li>
                        </ul>
                    </div>
                </section>

                <section id="dockerfile-stage3">
                    <h2><span class="step-number">6</span>第三阶段：配置优化</h2>

                    <h3><i class="fas fa-cogs"></i> 6.1 创建MySQL 8.0配置文件</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建针对MySQL 8.0优化的配置文件：</p>
                    <pre><code># 创建MySQL 8.0主配置文件
cat > /opt/mysql8-docker/config/my.cnf << 'EOF'
# ==========================================
# MySQL 8.0 配置文件
# 适用于Docker容器环境
# 包含MySQL 8.0新特性和性能优化
# ==========================================

[client]
# 客户端配置
port = 3306
socket = /var/run/mysqld/mysqld.sock
default-character-set = utf8mb4

[mysql]
# MySQL客户端配置
default-character-set = utf8mb4
no-auto-rehash

[mysqld]
# ==========================================
# 基础配置
# ==========================================
user = mysql
port = 3306
bind-address = 0.0.0.0
socket = /var/run/mysqld/mysqld.sock
pid-file = /var/run/mysqld/mysqld.pid

# 数据目录
datadir = /var/lib/mysql
tmpdir = /tmp

# 字符集配置（MySQL 8.0默认utf8mb4）
character-set-server = utf8mb4
collation-server = utf8mb4_0900_ai_ci
init_connect = 'SET NAMES utf8mb4'

# ==========================================
# MySQL 8.0新特性配置
# ==========================================
# 默认认证插件（MySQL 8.0新特性）
default_authentication_plugin = mysql_native_password

# 密码验证组件
validate_password.policy = MEDIUM
validate_password.length = 8

# 角色管理（MySQL 8.0新特性）
activate_all_roles_on_login = ON

# ==========================================
# 连接配置
# ==========================================
max_connections = 300
max_connect_errors = 1000
connect_timeout = 10
wait_timeout = 28800
interactive_timeout = 28800

# ==========================================
# 缓存配置（MySQL 8.0优化）
# ==========================================
# InnoDB缓冲池大小（建议设置为可用内存的70-80%）
innodb_buffer_pool_size = 1G
innodb_buffer_pool_instances = 2

# 表缓存
table_open_cache = 4000
table_definition_cache = 2000

# ==========================================
# 日志配置
# ==========================================
# 错误日志
log-error = /var/log/mysql/error.log

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 二进制日志
log-bin = /var/log/mysql/mysql-bin
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# 通用查询日志（生产环境建议关闭）
general_log = 0
general_log_file = /var/log/mysql/general.log

# ==========================================
# InnoDB配置（MySQL 8.0优化）
# ==========================================
innodb_file_per_table = 1
innodb_flush_log_at_trx_commit = 2
innodb_log_buffer_size = 32M
innodb_log_file_size = 512M
innodb_log_files_in_group = 2
innodb_max_dirty_pages_pct = 75
innodb_lock_wait_timeout = 50

# MySQL 8.0 InnoDB新特性
innodb_dedicated_server = OFF
innodb_redo_log_capacity = 1G

# ==========================================
# 性能优化配置
# ==========================================
tmp_table_size = 128M
max_heap_table_size = 128M
sort_buffer_size = 4M
join_buffer_size = 4M
read_buffer_size = 2M
read_rnd_buffer_size = 8M

# 线程配置
thread_cache_size = 16
thread_stack = 512K

# SQL模式（MySQL 8.0默认）
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION

[mysqldump]
# mysqldump配置
quick
quote-names
max_allowed_packet = 128M

[mysql]
# mysql客户端配置
no-auto-rehash

[myisamchk]
# myisamchk配置
key_buffer_size = 256M
sort_buffer_size = 256M
read_buffer = 4M
write_buffer = 4M

[mysqlhotcopy]
# mysqlhotcopy配置
interactive-timeout
EOF</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> MySQL 8.0配置说明：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>认证插件：</strong>使用mysql_native_password保持兼容性</li>
                            <li><strong>字符集：</strong>默认utf8mb4_0900_ai_ci排序规则</li>
                            <li><strong>密码验证：</strong>启用密码验证组件提高安全性</li>
                            <li><strong>角色管理：</strong>自动激活所有角色</li>
                            <li><strong>InnoDB优化：</strong>针对MySQL 8.0的InnoDB改进</li>
                            <li><strong>日志配置：</strong>完整的日志记录和管理</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-play"></i> 6.2 创建MySQL 8.0启动脚本</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建MySQL 8.0服务启动和初始化脚本：</p>
                    <pre><code># 创建MySQL 8.0启动脚本
cat > /opt/mysql8-docker/scripts/mysql-entrypoint.sh << 'EOF'
#!/bin/bash
# ==========================================
# MySQL 8.0 Docker 容器启动脚本
# 负责MySQL初始化和启动
# 支持MySQL 8.0新特性
# ==========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# MySQL配置变量
MYSQL_HOME=${MYSQL_HOME:-/usr/local/mysql}
MYSQL_DATADIR=${MYSQL_DATADIR:-/var/lib/mysql}
MYSQL_USER=${MYSQL_USER:-mysql}
MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-}
MYSQL_DATABASE=${MYSQL_DATABASE:-}
MYSQL_USER_NAME=${MYSQL_USER_NAME:-}
MYSQL_USER_PASSWORD=${MYSQL_USER_PASSWORD:-}

# 检查MySQL数据目录是否已初始化
is_mysql_initialized() {
    if [ -d "$MYSQL_DATADIR/mysql" ]; then
        return 0
    else
        return 1
    fi
}

# 初始化MySQL数据目录
initialize_mysql() {
    log_info "开始初始化MySQL 8.0数据目录..."

    # 确保数据目录存在且权限正确
    mkdir -p "$MYSQL_DATADIR"
    chown -R mysql:mysql "$MYSQL_DATADIR"

    # 使用mysqld --initialize初始化数据目录
    if [ -n "$MYSQL_ROOT_PASSWORD" ]; then
        log_info "使用指定的root密码初始化..."
        mysqld --initialize --user=mysql --datadir="$MYSQL_DATADIR" --init-file=/tmp/init.sql
    else
        log_info "使用随机root密码初始化..."
        mysqld --initialize --user=mysql --datadir="$MYSQL_DATADIR"
        # 从错误日志中提取临时密码
        TEMP_PASSWORD=$(grep 'temporary password' /var/log/mysql/error.log | tail -1 | awk '{print $NF}')
        log_warning "临时root密码: $TEMP_PASSWORD"
        log_warning "请在首次登录后修改密码"
    fi

    log_success "MySQL数据目录初始化完成"
}

# 创建初始化SQL文件
create_init_sql() {
    if [ -n "$MYSQL_ROOT_PASSWORD" ]; then
        cat > /tmp/init.sql << SQL_EOF
-- 设置root密码
ALTER USER 'root'@'localhost' IDENTIFIED BY '$MYSQL_ROOT_PASSWORD';

-- 创建远程root用户（如果需要）
CREATE USER 'root'@'%' IDENTIFIED BY '$MYSQL_ROOT_PASSWORD';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;

-- 创建自定义数据库（如果指定）
$([ -n "$MYSQL_DATABASE" ] && echo "CREATE DATABASE IF NOT EXISTS \`$MYSQL_DATABASE\` CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;")

-- 创建自定义用户（如果指定）
$([ -n "$MYSQL_USER_NAME" ] && [ -n "$MYSQL_USER_PASSWORD" ] && echo "CREATE USER '$MYSQL_USER_NAME'@'%' IDENTIFIED BY '$MYSQL_USER_PASSWORD';")
$([ -n "$MYSQL_USER_NAME" ] && [ -n "$MYSQL_DATABASE" ] && echo "GRANT ALL PRIVILEGES ON \`$MYSQL_DATABASE\`.* TO '$MYSQL_USER_NAME'@'%';")

-- 刷新权限
FLUSH PRIVILEGES;
SQL_EOF
    fi
}

# 启动MySQL服务
start_mysql() {
    log_info "启动MySQL 8.0服务..."

    # 确保日志目录存在
    mkdir -p /var/log/mysql
    chown -R mysql:mysql /var/log/mysql

    # 确保运行目录存在
    mkdir -p /var/run/mysqld
    chown -R mysql:mysql /var/run/mysqld

    # 启动MySQL
    exec mysqld --user=mysql --datadir="$MYSQL_DATADIR" --socket=/var/run/mysqld/mysqld.sock
}

# 主函数
main() {
    log_info "MySQL 8.0 Docker容器启动..."

    # 检查是否已初始化
    if ! is_mysql_initialized; then
        log_info "检测到未初始化的数据目录，开始初始化..."
        create_init_sql
        initialize_mysql
    else
        log_info "检测到已初始化的数据目录，跳过初始化..."
    fi

    # 启动MySQL服务
    start_mysql
}

# 执行主函数
main "$@"
EOF

# 设置脚本权限
chmod +x /opt/mysql8-docker/scripts/mysql-entrypoint.sh</code></pre>

                    <h3><i class="fas fa-cogs"></i> 6.3 创建第三阶段Dockerfile</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建第三阶段的Dockerfile，主要用于配置优化：</p>
                    <pre><code># 创建第三阶段Dockerfile
cat > /opt/mysql8-docker/dockerfile/Dockerfile.stage3 << 'EOF'
# ==========================================
# MySQL 8.0 绿色版 Docker 镜像构建
# 第三阶段：配置优化
# 基于第二阶段镜像
# ==========================================

FROM mysql80-install:stage2

# 设置维护者信息
LABEL maintainer="MySQL 8.0 Docker Builder"
LABEL description="MySQL 8.0 Green Version - Stage 3: Configuration"
LABEL version="3.0"

# 复制配置文件
COPY config/my.cnf /etc/mysql/my.cnf
COPY scripts/mysql-entrypoint.sh /usr/local/bin/

# 设置权限
RUN chmod +x /usr/local/bin/mysql-entrypoint.sh && \
    chown mysql:mysql /etc/mysql/my.cnf

# 创建必要的目录
RUN mkdir -p /var/log/mysql && \
    mkdir -p /var/run/mysqld && \
    chown -R mysql:mysql /var/log/mysql && \
    chown -R mysql:mysql /var/run/mysqld

# 设置环境变量
ENV MYSQL_ROOT_PASSWORD=""
ENV MYSQL_DATABASE=""
ENV MYSQL_USER_NAME=""
ENV MYSQL_USER_PASSWORD=""

# 暴露MySQL端口
EXPOSE 3306

# 设置数据卷
VOLUME ["/var/lib/mysql", "/var/log/mysql"]

# 设置工作目录
WORKDIR /usr/local/mysql

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/health-check.sh

# 设置启动命令
ENTRYPOINT ["/usr/local/bin/mysql-entrypoint.sh"]
EOF</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 第三阶段说明：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>配置文件：</strong>复制优化的MySQL 8.0配置文件</li>
                            <li><strong>启动脚本：</strong>添加自动初始化和启动脚本</li>
                            <li><strong>环境变量：</strong>支持通过环境变量配置MySQL</li>
                            <li><strong>健康检查：</strong>集成健康检查机制</li>
                            <li><strong>数据卷：</strong>配置数据和日志持久化</li>
                            <li><strong>权限设置：</strong>确保所有文件权限正确</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-hammer"></i> 6.4 构建第三阶段镜像</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>构建第三阶段的配置优化镜像：</p>
                    <pre><code># 确保在工作目录
cd /opt/mysql8-docker

# 构建第三阶段镜像
docker build -f dockerfile/Dockerfile.stage3 -t mysql80-config:stage3 .

# 查看构建的镜像
docker images | grep mysql80

# 测试第三阶段镜像（不启动MySQL服务）
docker run --rm -it mysql80-config:stage3 /bin/bash -c "
    echo '测试配置文件...'
    echo '配置文件:' && ls -la /etc/mysql/
    echo '启动脚本:' && ls -la /usr/local/bin/mysql-entrypoint.sh
    echo '健康检查:' && ls -la /usr/local/bin/health-check.sh
    echo '目录权限:' && ls -ld /var/lib/mysql /var/log/mysql /var/run/mysqld
"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 第三阶段验证：</strong>
                        <ul style="margin-top: 10px;">
                            <li>配置文件复制成功</li>
                            <li>启动脚本权限正确</li>
                            <li>健康检查脚本可用</li>
                            <li>目录权限设置正确</li>
                            <li>环境变量配置完成</li>
                        </ul>
                    </div>
                </section>

                <section id="dockerfile-stage4">
                    <h2><span class="step-number">7</span>第四阶段：最终镜像</h2>

                    <h3><i class="fas fa-rocket"></i> 7.1 创建最终阶段Dockerfile</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建第四阶段的Dockerfile，构建最终的生产就绪镜像：</p>
                    <pre><code># 创建第四阶段Dockerfile（最终镜像）
cat > /opt/mysql8-docker/dockerfile/Dockerfile.final << 'EOF'
# ==========================================
# MySQL 8.0 绿色版 Docker 镜像构建
# 第四阶段：最终镜像
# 生产就绪的MySQL 8.0镜像
# ==========================================

FROM mysql80-config:stage3

# 设置维护者信息
LABEL maintainer="MySQL 8.0 Docker Builder"
LABEL description="MySQL 8.0 Green Version - Final Production Ready Image"
LABEL version="4.0"
LABEL mysql.version="8.0.35"
LABEL build.date="$(date -u +'%Y-%m-%dT%H:%M:%SZ')"

# 最终优化和清理
RUN dnf clean all && \
    rm -rf /var/cache/dnf/* && \
    rm -rf /tmp/* && \
    # 创建必要的符号链接
    ln -sf /dev/stdout /var/log/mysql/access.log && \
    ln -sf /dev/stderr /var/log/mysql/error.log && \
    # 设置最终权限
    chown -R mysql:mysql /usr/local/mysql && \
    chown -R mysql:mysql /var/lib/mysql && \
    chown -R mysql:mysql /var/log/mysql && \
    chown -R mysql:mysql /var/run/mysqld && \
    chown -R mysql:mysql /etc/mysql

# 设置默认环境变量
ENV MYSQL_ROOT_PASSWORD=""
ENV MYSQL_DATABASE=""
ENV MYSQL_USER_NAME=""
ENV MYSQL_USER_PASSWORD=""
ENV MYSQL_CHARSET="utf8mb4"
ENV MYSQL_COLLATION="utf8mb4_0900_ai_ci"

# 暴露MySQL端口
EXPOSE 3306

# 设置数据卷
VOLUME ["/var/lib/mysql", "/var/log/mysql", "/etc/mysql/conf.d"]

# 设置工作目录
WORKDIR /usr/local/mysql

# 切换到mysql用户
USER mysql

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /usr/local/bin/health-check.sh || exit 1

# 设置启动命令
ENTRYPOINT ["/usr/local/bin/mysql-entrypoint.sh"]
CMD ["mysqld"]
EOF</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 最终阶段特点：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>生产就绪：</strong>完整的生产环境配置</li>
                            <li><strong>安全优化：</strong>使用mysql用户运行服务</li>
                            <li><strong>日志重定向：</strong>日志输出到stdout/stderr</li>
                            <li><strong>环境变量：</strong>完整的配置环境变量</li>
                            <li><strong>健康检查：</strong>完善的健康检查机制</li>
                            <li><strong>数据卷：</strong>支持数据、日志和配置持久化</li>
                        </ul>
                    </div>
                </section>

                <section id="build-image">
                    <h2><span class="step-number">8</span>构建镜像</h2>

                    <h3><i class="fas fa-hammer"></i> 8.1 完整构建流程</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>按顺序构建所有阶段的镜像：</p>
                    <pre><code># 进入工作目录
cd /opt/mysql8-docker

# 第一阶段：基础环境
echo "构建第一阶段：基础环境..."
docker build -f dockerfile/Dockerfile.stage1 -t mysql80-base:stage1 .

# 第二阶段：MySQL安装
echo "构建第二阶段：MySQL安装..."
docker build -f dockerfile/Dockerfile.stage2 -t mysql80-install:stage2 .

# 第三阶段：配置优化
echo "构建第三阶段：配置优化..."
docker build -f dockerfile/Dockerfile.stage3 -t mysql80-config:stage3 .

# 第四阶段：最终镜像
echo "构建第四阶段：最终镜像..."
docker build -f dockerfile/Dockerfile.final -t mysql80:8.0.35 .
docker tag mysql80:8.0.35 mysql80:latest

# 查看所有构建的镜像
docker images | grep mysql80</code></pre>

                    <h3><i class="fas fa-chart-bar"></i> 8.2 镜像大小优化</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>检查镜像大小和优化建议：</p>
                    <pre><code># 查看镜像详细信息
docker images mysql80 --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"

# 查看镜像层信息
docker history mysql80:latest

# 检查镜像内容
docker run --rm mysql80:latest du -sh /usr/local/mysql
docker run --rm mysql80:latest du -sh /var/lib/mysql

# 清理中间镜像（可选）
# docker rmi mysql80-base:stage1 mysql80-install:stage2 mysql80-config:stage3</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 构建成功标志：</strong>
                        <ul style="margin-top: 10px;">
                            <li>所有四个阶段构建成功，无错误</li>
                            <li>最终镜像大小合理（通常1-2GB）</li>
                            <li>镜像标签正确设置</li>
                            <li>MySQL版本信息正确</li>
                            <li>健康检查配置正常</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-test-tube"></i> 8.3 镜像测试</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>测试构建的MySQL 8.0镜像：</p>
                    <pre><code># 快速测试镜像
docker run --rm mysql80:latest mysqld --version

# 测试配置文件
docker run --rm mysql80:latest cat /etc/mysql/my.cnf | head -20

# 测试启动脚本
docker run --rm mysql80:latest ls -la /usr/local/bin/mysql-entrypoint.sh

# 测试健康检查（需要MySQL运行）
# docker run -d --name mysql-test mysql80:latest
# sleep 60
# docker exec mysql-test /usr/local/bin/health-check.sh
# docker rm -f mysql-test</code></pre>
                </section>

                <section id="run-container">
                    <h2><span class="step-number">9</span>运行容器</h2>

                    <h3><i class="fas fa-play"></i> 9.1 基本运行方式</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>使用基本配置运行MySQL 8.0容器：</p>
                    <pre><code># 创建数据目录
mkdir -p /opt/mysql8-data/{data,logs,conf}
chmod 755 /opt/mysql8-data
chmod 755 /opt/mysql8-data/*

# 基本运行（开发环境）
docker run -d \
  --name mysql80-dev \
  -p 3306:3306 \
  -e MYSQL_ROOT_PASSWORD=MySecurePass123! \
  -v /opt/mysql8-data/data:/var/lib/mysql \
  -v /opt/mysql8-data/logs:/var/log/mysql \
  mysql80:latest

# 查看容器状态
docker ps | grep mysql80

# 查看容器日志
docker logs mysql80-dev

# 等待MySQL启动完成
sleep 30

# 测试连接
docker exec mysql80-dev mysql -uroot -pMySecurePass123! -e "SELECT VERSION();"</code></pre>

                    <h3><i class="fas fa-cogs"></i> 9.2 生产环境运行</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>使用完整配置运行生产环境MySQL容器：</p>
                    <pre><code># 创建生产环境目录结构
mkdir -p /opt/mysql8-prod/{data,logs,conf,backup}
chmod 755 /opt/mysql8-prod
chmod 755 /opt/mysql8-prod/*

# 创建自定义配置文件
cat > /opt/mysql8-prod/conf/custom.cnf << 'EOF'
[mysqld]
# 生产环境优化配置
max_connections = 500
innodb_buffer_pool_size = 2G
innodb_buffer_pool_instances = 4
innodb_log_file_size = 1G
EOF

# 生产环境运行
docker run -d \
  --name mysql80-prod \
  --restart=unless-stopped \
  -p 3306:3306 \
  -e MYSQL_ROOT_PASSWORD=VerySecurePassword123! \
  -e MYSQL_DATABASE=myapp \
  -e MYSQL_USER_NAME=appuser \
  -e MYSQL_USER_PASSWORD=AppUserPass123! \
  -v /opt/mysql8-prod/data:/var/lib/mysql \
  -v /opt/mysql8-prod/logs:/var/log/mysql \
  -v /opt/mysql8-prod/conf:/etc/mysql/conf.d \
  -v /opt/mysql8-prod/backup:/backup \
  --memory=4g \
  --cpus=2 \
  mysql80:latest

# 查看容器资源使用
docker stats mysql80-prod --no-stream</code></pre>

                    <h3><i class="fas fa-network-wired"></i> 9.3 网络配置</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>配置Docker网络和端口映射：</p>
                    <pre><code># 创建自定义网络
docker network create mysql-network --driver bridge

# 在自定义网络中运行MySQL
docker run -d \
  --name mysql80-network \
  --network mysql-network \
  -p 3306:3306 \
  -e MYSQL_ROOT_PASSWORD=NetworkPass123! \
  -v /opt/mysql8-data/data:/var/lib/mysql \
  mysql80:latest

# 运行应用容器连接MySQL
docker run -d \
  --name app-container \
  --network mysql-network \
  -e DB_HOST=mysql80-network \
  -e DB_PORT=3306 \
  -e DB_USER=root \
  -e DB_PASSWORD=NetworkPass123! \
  alpine:latest sleep 3600

# 测试网络连接
docker exec app-container ping mysql80-network</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 安全提醒：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>密码安全：</strong>使用强密码，避免在命令行中明文显示</li>
                            <li><strong>端口暴露：</strong>生产环境避免直接暴露3306端口到公网</li>
                            <li><strong>网络隔离：</strong>使用自定义网络隔离MySQL容器</li>
                            <li><strong>资源限制：</strong>设置合适的内存和CPU限制</li>
                            <li><strong>数据备份：</strong>定期备份数据目录</li>
                        </ul>
                    </div>
                </section>

                <section id="configuration">
                    <h2><span class="step-number">10</span>配置管理</h2>

                    <h3><i class="fas fa-wrench"></i> 10.1 环境变量配置</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-list"></i> 支持的环境变量</h4>
                        <table>
                            <tr>
                                <th>变量名</th>
                                <th>说明</th>
                                <th>默认值</th>
                            </tr>
                            <tr>
                                <td>MYSQL_ROOT_PASSWORD</td>
                                <td>root用户密码</td>
                                <td>随机生成</td>
                            </tr>
                            <tr>
                                <td>MYSQL_DATABASE</td>
                                <td>初始化时创建的数据库</td>
                                <td>无</td>
                            </tr>
                            <tr>
                                <td>MYSQL_USER_NAME</td>
                                <td>创建的普通用户名</td>
                                <td>无</td>
                            </tr>
                            <tr>
                                <td>MYSQL_USER_PASSWORD</td>
                                <td>普通用户密码</td>
                                <td>无</td>
                            </tr>
                            <tr>
                                <td>MYSQL_CHARSET</td>
                                <td>默认字符集</td>
                                <td>utf8mb4</td>
                            </tr>
                            <tr>
                                <td>MYSQL_COLLATION</td>
                                <td>默认排序规则</td>
                                <td>utf8mb4_0900_ai_ci</td>
                            </tr>
                        </table>
                    </div>

                    <h3><i class="fas fa-file-alt"></i> 10.2 配置文件管理</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>管理MySQL配置文件：</p>
                    <pre><code># 查看当前配置
docker exec mysql80-prod cat /etc/mysql/my.cnf

# 创建自定义配置
cat > /opt/mysql8-prod/conf/performance.cnf << 'EOF'
[mysqld]
# 性能优化配置
query_cache_type = 0
query_cache_size = 0
innodb_flush_method = O_DIRECT
innodb_io_capacity = 2000
innodb_io_capacity_max = 4000
EOF

# 重启容器应用新配置
docker restart mysql80-prod

# 验证配置生效
docker exec mysql80-prod mysql -uroot -pVerySecurePassword123! \
  -e "SHOW VARIABLES LIKE 'innodb_flush_method';"</code></pre>

                    <h3><i class="fas fa-database"></i> 10.3 数据库初始化</h3>
                    <div class="machine-tag machine-mysql"><i class="fas fa-database"></i> MySQL容器</div>
                    <p>连接到MySQL容器进行数据库操作：</p>
                    <pre><code># 连接到MySQL
docker exec -it mysql80-prod mysql -uroot -pVerySecurePassword123!

# 在MySQL中执行以下命令：
-- 查看MySQL版本和状态
SELECT VERSION();
SHOW STATUS LIKE 'Uptime';

-- 查看数据库列表
SHOW DATABASES;

-- 创建新数据库
CREATE DATABASE testdb CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- 创建新用户
CREATE USER 'testuser'@'%' IDENTIFIED BY 'TestPass123!';
GRANT ALL PRIVILEGES ON testdb.* TO 'testuser'@'%';
FLUSH PRIVILEGES;

-- 查看用户列表
SELECT user, host FROM mysql.user;

-- 退出MySQL
EXIT;</code></pre>
                </section>

                <section id="data-persistence">
                    <h2><span class="step-number">11</span>数据持久化</h2>

                    <h3><i class="fas fa-save"></i> 11.1 数据卷配置</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>配置数据持久化存储：</p>
                    <pre><code># 创建命名数据卷
docker volume create mysql8-data
docker volume create mysql8-logs
docker volume create mysql8-config

# 查看数据卷信息
docker volume ls | grep mysql8
docker volume inspect mysql8-data

# 使用命名数据卷运行容器
docker run -d \
  --name mysql80-volume \
  -p 3307:3306 \
  -e MYSQL_ROOT_PASSWORD=VolumePass123! \
  -v mysql8-data:/var/lib/mysql \
  -v mysql8-logs:/var/log/mysql \
  -v mysql8-config:/etc/mysql/conf.d \
  mysql80:latest

# 查看数据卷使用情况
docker exec mysql80-volume df -h /var/lib/mysql</code></pre>

                    <h3><i class="fas fa-folder-open"></i> 11.2 数据目录管理</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>管理MySQL数据目录：</p>
                    <pre><code># 查看数据目录结构
docker exec mysql80-prod ls -la /var/lib/mysql/

# 查看数据库文件
docker exec mysql80-prod find /var/lib/mysql -name "*.ibd" | head -10

# 查看日志文件
docker exec mysql80-prod ls -la /var/log/mysql/

# 备份数据目录
docker run --rm \
  -v mysql8-data:/source:ro \
  -v /opt/mysql8-backup:/backup \
  alpine:latest \
  tar czf /backup/mysql-data-$(date +%Y%m%d-%H%M%S).tar.gz -C /source .

# 恢复数据目录（谨慎操作）
# docker run --rm \
#   -v mysql8-data:/target \
#   -v /opt/mysql8-backup:/backup:ro \
#   alpine:latest \
#   tar xzf /backup/mysql-data-20231201-120000.tar.gz -C /target</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 数据安全提醒：</strong>
                        <ul style="margin-top: 10px;">
                            <li><strong>定期备份：</strong>建立定期备份机制，避免数据丢失</li>
                            <li><strong>权限管理：</strong>确保数据目录权限正确设置</li>
                            <li><strong>磁盘空间：</strong>监控磁盘空间使用情况</li>
                            <li><strong>数据恢复：</strong>定期测试数据恢复流程</li>
                            <li><strong>版本兼容：</strong>注意MySQL版本间的数据兼容性</li>
                        </ul>
                    </div>
                </section>

                <section id="security-setup">
                    <h2><span class="step-number">12</span>安全配置</h2>

                    <h3><i class="fas fa-shield-alt"></i> 12.1 密码安全</h3>
                    <div class="machine-tag machine-mysql"><i class="fas fa-database"></i> MySQL容器</div>
                    <p>配置MySQL 8.0安全特性：</p>
                    <pre><code># 连接到MySQL进行安全配置
docker exec -it mysql80-prod mysql -uroot -pVerySecurePassword123!

-- 查看密码验证插件状态
SHOW VARIABLES LIKE 'validate_password%';

-- 设置密码验证策略
SET GLOBAL validate_password.policy = 'STRONG';
SET GLOBAL validate_password.length = 12;
SET GLOBAL validate_password.mixed_case_count = 2;
SET GLOBAL validate_password.number_count = 2;
SET GLOBAL validate_password.special_char_count = 2;

-- 创建具有强密码的用户
CREATE USER 'secureuser'@'%' IDENTIFIED BY 'SecurePass123!@#';

-- 查看用户认证信息
SELECT user, host, plugin FROM mysql.user WHERE user = 'secureuser';

-- 退出MySQL
EXIT;</code></pre>

                    <h3><i class="fas fa-user-shield"></i> 12.2 用户权限管理</h3>
                    <div class="machine-tag machine-mysql"><i class="fas fa-database"></i> MySQL容器</div>
                    <p>配置用户角色和权限：</p>
                    <pre><code># 连接到MySQL
docker exec -it mysql80-prod mysql -uroot -pVerySecurePassword123!

-- 创建角色（MySQL 8.0新特性）
CREATE ROLE 'app_read', 'app_write', 'app_admin';

-- 为角色分配权限
GRANT SELECT ON myapp.* TO 'app_read';
GRANT SELECT, INSERT, UPDATE, DELETE ON myapp.* TO 'app_write';
GRANT ALL PRIVILEGES ON myapp.* TO 'app_admin';

-- 创建用户并分配角色
CREATE USER 'reader'@'%' IDENTIFIED BY 'ReadPass123!';
CREATE USER 'writer'@'%' IDENTIFIED BY 'WritePass123!';
CREATE USER 'admin'@'%' IDENTIFIED BY 'AdminPass123!';

GRANT 'app_read' TO 'reader'@'%';
GRANT 'app_write' TO 'writer'@'%';
GRANT 'app_admin' TO 'admin'@'%';

-- 设置默认角色
SET DEFAULT ROLE 'app_read' TO 'reader'@'%';
SET DEFAULT ROLE 'app_write' TO 'writer'@'%';
SET DEFAULT ROLE 'app_admin' TO 'admin'@'%';

-- 查看角色和权限
SHOW GRANTS FOR 'reader'@'%';
SELECT * FROM mysql.role_edges;

-- 退出MySQL
EXIT;</code></pre>

                    <h3><i class="fas fa-lock"></i> 12.3 SSL/TLS配置</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>配置MySQL SSL连接：</p>
                    <pre><code># 查看SSL状态
docker exec mysql80-prod mysql -uroot -pVerySecurePassword123! \
  -e "SHOW VARIABLES LIKE 'have_ssl';"

# 查看SSL证书信息
docker exec mysql80-prod mysql -uroot -pVerySecurePassword123! \
  -e "SHOW STATUS LIKE 'Ssl%';"

# 强制用户使用SSL连接
docker exec -it mysql80-prod mysql -uroot -pVerySecurePassword123!

-- 创建需要SSL的用户
CREATE USER 'ssluser'@'%' IDENTIFIED BY 'SSLPass123!' REQUIRE SSL;
GRANT SELECT ON myapp.* TO 'ssluser'@'%';

-- 查看用户SSL要求
SELECT user, host, ssl_type FROM mysql.user WHERE user = 'ssluser';

-- 退出MySQL
EXIT;</code></pre>
                </section>

                <section id="performance-tuning">
                    <h2><span class="step-number">13</span>性能调优</h2>

                    <h3><i class="fas fa-tachometer-alt"></i> 13.1 性能监控</h3>
                    <div class="machine-tag machine-mysql"><i class="fas fa-database"></i> MySQL容器</div>
                    <p>监控MySQL性能指标：</p>
                    <pre><code># 连接到MySQL查看性能状态
docker exec -it mysql80-prod mysql -uroot -pVerySecurePassword123!

-- 查看连接状态
SHOW STATUS LIKE 'Connections';
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Max_used_connections';

-- 查看查询统计
SHOW STATUS LIKE 'Questions';
SHOW STATUS LIKE 'Queries';
SHOW STATUS LIKE 'Slow_queries';

-- 查看InnoDB状态
SHOW ENGINE INNODB STATUS\G

-- 查看缓冲池使用情况
SELECT
  POOL_ID,
  POOL_SIZE,
  FREE_BUFFERS,
  DATABASE_PAGES,
  OLD_DATABASE_PAGES
FROM INFORMATION_SCHEMA.INNODB_BUFFER_POOL_STATS;

-- 查看慢查询
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 5;

-- 退出MySQL
EXIT;</code></pre>

                    <h3><i class="fas fa-chart-line"></i> 13.2 性能优化配置</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建性能优化配置文件：</p>
                    <pre><code># 创建高性能配置文件
cat > /opt/mysql8-prod/conf/performance-tuning.cnf << 'EOF'
[mysqld]
# ==========================================
# MySQL 8.0 高性能配置
# ==========================================

# 连接优化
max_connections = 1000
max_connect_errors = 100000
connect_timeout = 5
wait_timeout = 600
interactive_timeout = 600

# InnoDB优化
innodb_buffer_pool_size = 4G
innodb_buffer_pool_instances = 8
innodb_log_file_size = 2G
innodb_log_buffer_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_io_capacity = 4000
innodb_io_capacity_max = 8000
innodb_read_io_threads = 8
innodb_write_io_threads = 8

# 查询缓存（MySQL 8.0已移除，使用其他缓存策略）
# 表缓存
table_open_cache = 8000
table_definition_cache = 4000

# 排序和连接优化
sort_buffer_size = 8M
join_buffer_size = 8M
read_buffer_size = 4M
read_rnd_buffer_size = 16M

# 临时表优化
tmp_table_size = 256M
max_heap_table_size = 256M

# 线程优化
thread_cache_size = 32
thread_stack = 1M

# 二进制日志优化
binlog_cache_size = 4M
max_binlog_cache_size = 2G
sync_binlog = 0

# 其他优化
key_buffer_size = 256M
bulk_insert_buffer_size = 64M
EOF

# 重启容器应用新配置
docker restart mysql80-prod

# 等待启动完成
sleep 30

# 验证配置
docker exec mysql80-prod mysql -uroot -pVerySecurePassword123! \
  -e "SHOW VARIABLES LIKE 'innodb_buffer_pool_size';"</code></pre>

                    <h3><i class="fas fa-database"></i> 13.3 索引优化</h3>
                    <div class="machine-tag machine-mysql"><i class="fas fa-database"></i> MySQL容器</div>
                    <p>优化数据库索引：</p>
                    <pre><code># 连接到MySQL进行索引优化
docker exec -it mysql80-prod mysql -uroot -pVerySecurePassword123!

-- 使用测试数据库
USE myapp;

-- 创建测试表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive') DEFAULT 'active',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_status_created (status, created_at)
);

-- 查看表结构和索引
SHOW CREATE TABLE users;
SHOW INDEX FROM users;

-- 分析查询性能
EXPLAIN SELECT * FROM users WHERE username = 'testuser';
EXPLAIN SELECT * FROM users WHERE status = 'active' ORDER BY created_at DESC;

-- 查看索引使用统计
SELECT
    TABLE_SCHEMA,
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY
FROM INFORMATION_SCHEMA.STATISTICS
WHERE TABLE_SCHEMA = 'myapp';

-- 退出MySQL
EXIT;</code></pre>
                </section>

                <section id="backup-restore">
                    <h2><span class="step-number">14</span>备份恢复</h2>

                    <h3><i class="fas fa-archive"></i> 14.1 逻辑备份</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>使用mysqldump进行逻辑备份：</p>
                    <pre><code># 创建备份目录
mkdir -p /opt/mysql8-backup/{full,incremental,logs}
chmod 755 /opt/mysql8-backup
chmod 755 /opt/mysql8-backup/*

# 全库备份
docker exec mysql80-prod mysqldump \
  -uroot -pVerySecurePassword123! \
  --all-databases \
  --single-transaction \
  --routines \
  --triggers \
  --events \
  --master-data=2 \
  --flush-logs \
  > /opt/mysql8-backup/full/full-backup-$(date +%Y%m%d-%H%M%S).sql

# 单库备份
docker exec mysql80-prod mysqldump \
  -uroot -pVerySecurePassword123! \
  --single-transaction \
  --routines \
  --triggers \
  myapp \
  > /opt/mysql8-backup/full/myapp-backup-$(date +%Y%m%d-%H%M%S).sql

# 表结构备份
docker exec mysql80-prod mysqldump \
  -uroot -pVerySecurePassword123! \
  --no-data \
  --all-databases \
  > /opt/mysql8-backup/full/schema-backup-$(date +%Y%m%d-%H%M%S).sql

# 查看备份文件
ls -lh /opt/mysql8-backup/full/</code></pre>

                    <h3><i class="fas fa-undo"></i> 14.2 数据恢复</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>从备份文件恢复数据：</p>
                    <pre><code># 恢复全库（谨慎操作）
# docker exec -i mysql80-prod mysql -uroot -pVerySecurePassword123! \
#   < /opt/mysql8-backup/full/full-backup-20231201-120000.sql

# 恢复单库
docker exec mysql80-prod mysql -uroot -pVerySecurePassword123! \
  -e "CREATE DATABASE myapp_restore CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;"

docker exec -i mysql80-prod mysql -uroot -pVerySecurePassword123! myapp_restore \
  < /opt/mysql8-backup/full/myapp-backup-$(date +%Y%m%d)*.sql

# 验证恢复结果
docker exec mysql80-prod mysql -uroot -pVerySecurePassword123! \
  -e "SHOW DATABASES; USE myapp_restore; SHOW TABLES;"</code></pre>

                    <h3><i class="fas fa-clock"></i> 14.3 自动备份脚本</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>创建自动备份脚本：</p>
                    <pre><code># 创建自动备份脚本
cat > /opt/mysql8-backup/auto-backup.sh << 'EOF'
#!/bin/bash
# ==========================================
# MySQL 8.0 自动备份脚本
# ==========================================

# 配置变量
CONTAINER_NAME="mysql80-prod"
MYSQL_USER="root"
MYSQL_PASSWORD="VerySecurePassword123!"
BACKUP_DIR="/opt/mysql8-backup"
RETENTION_DAYS=7

# 创建备份目录
mkdir -p ${BACKUP_DIR}/{full,logs}

# 生成备份文件名
BACKUP_DATE=$(date +%Y%m%d-%H%M%S)
BACKUP_FILE="${BACKUP_DIR}/full/mysql-backup-${BACKUP_DATE}.sql"
LOG_FILE="${BACKUP_DIR}/logs/backup-${BACKUP_DATE}.log"

# 执行备份
echo "开始备份: $(date)" > ${LOG_FILE}
docker exec ${CONTAINER_NAME} mysqldump \
  -u${MYSQL_USER} -p${MYSQL_PASSWORD} \
  --all-databases \
  --single-transaction \
  --routines \
  --triggers \
  --events \
  --master-data=2 \
  --flush-logs \
  > ${BACKUP_FILE} 2>>${LOG_FILE}

if [ $? -eq 0 ]; then
    echo "备份成功: ${BACKUP_FILE}" >> ${LOG_FILE}
    # 压缩备份文件
    gzip ${BACKUP_FILE}
    echo "压缩完成: ${BACKUP_FILE}.gz" >> ${LOG_FILE}
else
    echo "备份失败" >> ${LOG_FILE}
    exit 1
fi

# 清理旧备份
find ${BACKUP_DIR}/full -name "*.sql.gz" -mtime +${RETENTION_DAYS} -delete
find ${BACKUP_DIR}/logs -name "*.log" -mtime +${RETENTION_DAYS} -delete

echo "备份完成: $(date)" >> ${LOG_FILE}
EOF

# 设置脚本权限
chmod +x /opt/mysql8-backup/auto-backup.sh

# 测试备份脚本
/opt/mysql8-backup/auto-backup.sh

# 添加到crontab（每天凌晨2点备份）
echo "0 2 * * * /opt/mysql8-backup/auto-backup.sh" | crontab -</code></pre>
                </section>

                <section id="monitoring">
                    <h2><span class="step-number">15</span>监控管理</h2>

                    <h3><i class="fas fa-chart-line"></i> 15.1 容器监控</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>监控MySQL容器状态：</p>
                    <pre><code># 查看容器状态
docker ps | grep mysql80

# 查看容器资源使用
docker stats mysql80-prod --no-stream

# 查看容器日志
docker logs mysql80-prod --tail 50

# 查看容器详细信息
docker inspect mysql80-prod | jq '.[0].State'

# 监控容器健康状态
docker inspect mysql80-prod | jq '.[0].State.Health'

# 实时监控资源使用
watch -n 5 'docker stats mysql80-prod --no-stream'</code></pre>

                    <h3><i class="fas fa-database"></i> 15.2 MySQL监控</h3>
                    <div class="machine-tag machine-mysql"><i class="fas fa-database"></i> MySQL容器</div>
                    <p>监控MySQL数据库状态：</p>
                    <pre><code># 创建监控脚本
cat > /opt/mysql8-backup/monitor-mysql.sh << 'EOF'
#!/bin/bash
# MySQL 8.0 监控脚本

CONTAINER_NAME="mysql80-prod"
MYSQL_USER="root"
MYSQL_PASSWORD="VerySecurePassword123!"

echo "=== MySQL 8.0 监控报告 $(date) ==="

# 连接数监控
echo "=== 连接状态 ==="
docker exec ${CONTAINER_NAME} mysql -u${MYSQL_USER} -p${MYSQL_PASSWORD} \
  -e "SHOW STATUS LIKE 'Threads_connected'; SHOW STATUS LIKE 'Max_used_connections';"

# 查询统计
echo "=== 查询统计 ==="
docker exec ${CONTAINER_NAME} mysql -u${MYSQL_USER} -p${MYSQL_PASSWORD} \
  -e "SHOW STATUS LIKE 'Questions'; SHOW STATUS LIKE 'Slow_queries';"

# InnoDB状态
echo "=== InnoDB缓冲池 ==="
docker exec ${CONTAINER_NAME} mysql -u${MYSQL_USER} -p${MYSQL_PASSWORD} \
  -e "SELECT POOL_SIZE, FREE_BUFFERS, DATABASE_PAGES FROM INFORMATION_SCHEMA.INNODB_BUFFER_POOL_STATS;"

# 磁盘使用
echo "=== 磁盘使用 ==="
docker exec ${CONTAINER_NAME} df -h /var/lib/mysql

# 进程列表
echo "=== 活跃进程 ==="
docker exec ${CONTAINER_NAME} mysql -u${MYSQL_USER} -p${MYSQL_PASSWORD} \
  -e "SHOW PROCESSLIST;"

echo "=== 监控完成 ==="
EOF

chmod +x /opt/mysql8-backup/monitor-mysql.sh

# 运行监控脚本
/opt/mysql8-backup/monitor-mysql.sh</code></pre>

                    <h3><i class="fas fa-bell"></i> 15.3 告警配置</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>配置监控告警：</p>
                    <pre><code># 创建告警脚本
cat > /opt/mysql8-backup/alert-check.sh << 'EOF'
#!/bin/bash
# MySQL 8.0 告警检查脚本

CONTAINER_NAME="mysql80-prod"
MYSQL_USER="root"
MYSQL_PASSWORD="VerySecurePassword123!"
ALERT_EMAIL="<EMAIL>"

# 检查容器是否运行
if ! docker ps | grep -q ${CONTAINER_NAME}; then
    echo "ALERT: MySQL容器未运行" | mail -s "MySQL Alert" ${ALERT_EMAIL}
    exit 1
fi

# 检查MySQL连接
if ! docker exec ${CONTAINER_NAME} mysql -u${MYSQL_USER} -p${MYSQL_PASSWORD} \
     -e "SELECT 1;" > /dev/null 2>&1; then
    echo "ALERT: MySQL连接失败" | mail -s "MySQL Alert" ${ALERT_EMAIL}
    exit 1
fi

# 检查连接数
CONNECTIONS=$(docker exec ${CONTAINER_NAME} mysql -u${MYSQL_USER} -p${MYSQL_PASSWORD} \
              -e "SHOW STATUS LIKE 'Threads_connected';" | awk 'NR==2 {print $2}')

if [ ${CONNECTIONS} -gt 400 ]; then
    echo "ALERT: MySQL连接数过高: ${CONNECTIONS}" | mail -s "MySQL Alert" ${ALERT_EMAIL}
fi

# 检查磁盘使用率
DISK_USAGE=$(docker exec ${CONTAINER_NAME} df /var/lib/mysql | awk 'NR==2 {print $5}' | sed 's/%//')

if [ ${DISK_USAGE} -gt 80 ]; then
    echo "ALERT: MySQL磁盘使用率过高: ${DISK_USAGE}%" | mail -s "MySQL Alert" ${ALERT_EMAIL}
fi

echo "MySQL健康检查通过"
EOF

chmod +x /opt/mysql8-backup/alert-check.sh

# 添加到crontab（每5分钟检查一次）
echo "*/5 * * * * /opt/mysql8-backup/alert-check.sh" | crontab -</code></pre>
                </section>

                <section id="troubleshooting">
                    <h2><span class="step-number">16</span>故障排查</h2>

                    <h3><i class="fas fa-bug"></i> 16.1 常见问题</h3>
                    <div class="danger-box">
                        <h4><i class="fas fa-exclamation-triangle"></i> 容器启动失败</h4>
                        <p><strong>问题：</strong>MySQL容器无法启动或立即退出</p>
                        <p><strong>排查步骤：</strong></p>
                        <pre><code># 查看容器日志
docker logs mysql80-prod

# 查看容器退出状态
docker ps -a | grep mysql80

# 检查数据目录权限
ls -la /opt/mysql8-prod/data/

# 手动启动容器进行调试
docker run --rm -it mysql80:latest /bin/bash</code></pre>
                        <p><strong>常见原因：</strong></p>
                        <ul>
                            <li>数据目录权限不正确</li>
                            <li>配置文件语法错误</li>
                            <li>内存不足</li>
                            <li>端口被占用</li>
                        </ul>
                    </div>

                    <div class="warning-box">
                        <h4><i class="fas fa-database"></i> 连接问题</h4>
                        <p><strong>问题：</strong>无法连接到MySQL数据库</p>
                        <p><strong>排查步骤：</strong></p>
                        <pre><code># 检查容器网络
docker port mysql80-prod

# 检查MySQL进程
docker exec mysql80-prod ps aux | grep mysql

# 检查MySQL端口监听
docker exec mysql80-prod netstat -tlnp | grep 3306

# 测试本地连接
docker exec mysql80-prod mysql -uroot -p -e "SELECT 1;"

# 检查防火墙设置
systemctl status firewalld
firewall-cmd --list-ports</code></pre>
                    </div>

                    <div class="info-box">
                        <h4><i class="fas fa-memory"></i> 性能问题</h4>
                        <p><strong>问题：</strong>MySQL性能缓慢</p>
                        <p><strong>排查步骤：</strong></p>
                        <pre><code># 检查系统资源
docker stats mysql80-prod

# 查看MySQL状态
docker exec mysql80-prod mysql -uroot -p \
  -e "SHOW ENGINE INNODB STATUS\G"

# 查看慢查询
docker exec mysql80-prod mysql -uroot -p \
  -e "SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;"

# 检查索引使用
docker exec mysql80-prod mysql -uroot -p \
  -e "SHOW PROCESSLIST;"</code></pre>
                    </div>

                    <h3><i class="fas fa-tools"></i> 16.2 调试技巧</h3>
                    <div class="machine-tag machine-host"><i class="fas fa-server"></i> 宿主机</div>
                    <p>MySQL 8.0调试和诊断技巧：</p>
                    <pre><code># 启用详细日志
docker exec mysql80-prod mysql -uroot -p \
  -e "SET GLOBAL general_log = 'ON';"

# 查看错误日志
docker exec mysql80-prod tail -f /var/log/mysql/error.log

# 检查MySQL配置
docker exec mysql80-prod mysql -uroot -p \
  -e "SHOW VARIABLES LIKE '%innodb%';" | head -20

# 查看MySQL版本和编译信息
docker exec mysql80-prod mysql -uroot -p \
  -e "SELECT VERSION(); SHOW VARIABLES LIKE 'version_compile%';"

# 检查插件状态
docker exec mysql80-prod mysql -uroot -p \
  -e "SHOW PLUGINS;"

# 性能诊断
docker exec mysql80-prod mysql -uroot -p \
  -e "SELECT * FROM performance_schema.events_statements_summary_by_digest ORDER BY sum_timer_wait DESC LIMIT 5;"</code></pre>

                    <h3><i class="fas fa-first-aid"></i> 16.3 紧急恢复</h3>
                    <div class="danger-box">
                        <h4><i class="fas fa-exclamation-circle"></i> 数据损坏恢复</h4>
                        <p><strong>紧急情况：</strong>MySQL数据文件损坏</p>
                        <pre><code># 停止MySQL容器
docker stop mysql80-prod

# 备份损坏的数据目录
cp -r /opt/mysql8-prod/data /opt/mysql8-prod/data.corrupted

# 尝试修复InnoDB表
docker run --rm \
  -v /opt/mysql8-prod/data:/var/lib/mysql \
  mysql80:latest \
  mysqld --innodb-force-recovery=1

# 如果修复失败，从备份恢复
# rm -rf /opt/mysql8-prod/data/*
# 恢复最新备份...</code></pre>
                    </div>
                </section>

                <section id="summary">
                    <h2><span class="step-number">17</span>总结检查</h2>

                    <h3><i class="fas fa-check-circle"></i> 17.1 部署验证清单</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-list-check"></i> 完整验证步骤</h4>
                        <ol>
                            <li><strong>镜像构建：</strong>四个阶段镜像全部构建成功</li>
                            <li><strong>容器运行：</strong>MySQL容器正常启动和运行</li>
                            <li><strong>数据库连接：</strong>可以正常连接和操作数据库</li>
                            <li><strong>数据持久化：</strong>数据卷配置正确，数据持久保存</li>
                            <li><strong>安全配置：</strong>用户权限和密码策略配置完成</li>
                            <li><strong>性能优化：</strong>配置文件优化，性能监控正常</li>
                            <li><strong>备份恢复：</strong>备份脚本工作正常，恢复测试通过</li>
                            <li><strong>监控告警：</strong>监控脚本和告警机制配置完成</li>
                        </ol>
                    </div>

                    <h3><i class="fas fa-rocket"></i> 17.2 生产环境建议</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-star"></i> 最佳实践</h4>
                        <ul>
                            <li><strong>资源配置：</strong>根据实际负载调整内存和CPU配置</li>
                            <li><strong>网络安全：</strong>使用防火墙和VPN保护数据库访问</li>
                            <li><strong>定期维护：</strong>定期更新镜像和安全补丁</li>
                            <li><strong>监控告警：</strong>集成专业监控系统如Prometheus</li>
                            <li><strong>高可用：</strong>考虑MySQL主从复制或集群部署</li>
                            <li><strong>备份策略：</strong>制定完整的备份和灾难恢复计划</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-graduation-cap"></i> 17.3 学习总结</h3>
                    <div class="success-box">
                        <p><strong><i class="fas fa-trophy"></i> 恭喜！</strong>您已经成功完成了MySQL 8.0绿色版Docker部署教程。通过本教程，您学会了：
                        </p>
                        <ul style="margin-top: 15px;">
                            <li>使用四阶段构建方式创建MySQL 8.0 Docker镜像</li>
                            <li>配置MySQL 8.0的新特性和安全功能</li>
                            <li>实现数据持久化和备份恢复</li>
                            <li>进行性能优化和监控管理</li>
                            <li>处理常见问题和故障排查</li>
                        </ul>
                        <p style="margin-top: 15px;">这个自定义的MySQL 8.0镜像已经可以用于生产环境，具备了完整的功能和安全特性。</p>
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 技术支持：</strong>
                        <p>如果在使用过程中遇到问题，建议：</p>
                        <ul style="margin-top: 10px;">
                            <li>查阅MySQL 8.0官方文档</li>
                            <li>检查Docker和容器日志</li>
                            <li>参考本教程的故障排查章节</li>
                            <li>在技术社区寻求帮助</li>
                        </ul>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <!-- 返回顶部按钮 -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // 移动端菜单切换
        document.getElementById('mobileMenuBtn').addEventListener('click', function () {
            document.getElementById('sidebar').classList.toggle('active');
        });

        // 返回顶部功能
        window.addEventListener('scroll', function () {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'flex';
            } else {
                backToTop.style.display = 'none';
            }
        });

        document.getElementById('backToTop').addEventListener('click', function (e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 侧边栏导航高亮
        window.addEventListener('scroll', function () {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.sidebar a[href^="#"]');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // 平滑滚动
        document.querySelectorAll('.sidebar a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
                // 移动端关闭菜单
                if (window.innerWidth <= 768) {
                    document.getElementById('sidebar').classList.remove('active');
                }
            });
        });
    </script>
</body>

</html>