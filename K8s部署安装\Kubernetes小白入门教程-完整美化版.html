<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kubernetes小白入门教程 - 从零开始学K8s</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 12px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 欢迎框样式 */
        .welcome-box {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            padding: 40px;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
        }

        .welcome-box::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 8s ease-in-out infinite;
        }

        .welcome-box h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .welcome-box p {
            font-size: 16px;
            line-height: 1.8;
            color: var(--text-primary);
            position: relative;
            z-index: 1;
        }

        /* 学习路径样式 */
        .learning-path {
            margin-top: 30px;
            position: relative;
            z-index: 1;
        }

        .learning-path h4 {
            color: var(--secondary-color);
            margin-bottom: 25px;
        }

        .path-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .path-step {
            background: var(--light-surface);
            border-radius: 15px;
            padding: 25px;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .path-step:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-color);
        }

        .step-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 20px;
            margin-bottom: 15px;
            box-shadow: var(--shadow-md);
        }

        .step-content h5 {
            color: var(--text-primary);
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .step-content p {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.6;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box,
        .beginner-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover,
        .beginner-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        .beginner-box {
            background: linear-gradient(135deg, rgba(128, 90, 213, 0.1) 0%, rgba(128, 90, 213, 0.05) 100%);
            border-left-color: #805ad5;
            color: #553c9a;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: var(--light-surface);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        th,
        td {
            padding: 18px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-decoration: none;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            z-index: 999;
            font-size: 20px;
        }

        .back-to-top:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .content-wrapper {
                padding: 30px;
            }

            .container {
                padding: 40px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
                border-radius: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 16px;
                margin-right: 15px;
            }

            .path-steps {
                grid-template-columns: 1fr;
            }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
        }
    </style>
</head>

<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-graduation-cap"></i> K8s小白入门</h2>
            <p>从零开始学习Kubernetes</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#what-is-k8s"><i class="fas fa-question-circle"></i>1. 什么是Kubernetes</a></li>
                <li><a href="#why-use-k8s"><i class="fas fa-lightbulb"></i>2. 为什么要用K8s</a></li>
                <li><a href="#basic-concepts"><i class="fas fa-cube"></i>3. 基础概念详解</a></li>
                <li><a href="#architecture"><i class="fas fa-sitemap"></i>4. 架构原理</a></li>
                <li><a href="#installation-guide"><i class="fas fa-download"></i>5. 安装指南</a></li>
                <li><a href="#first-app"><i class="fas fa-rocket"></i>6. 第一个应用</a></li>
                <li><a href="#pod-management"><i class="fas fa-box"></i>7. Pod管理</a></li>
                <li><a href="#service-networking"><i class="fas fa-network-wired"></i>8. 服务与网络</a></li>
                <li><a href="#storage"><i class="fas fa-hdd"></i>9. 存储管理</a></li>
                <li><a href="#config-secrets"><i class="fas fa-key"></i>10. 配置与密钥</a></li>
                <li><a href="#monitoring"><i class="fas fa-chart-line"></i>11. 监控与日志</a></li>
                <li><a href="#best-practices"><i class="fas fa-star"></i>12. 最佳实践</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-graduation-cap"></i> Kubernetes小白入门教程</h1>

                <div class="welcome-box">
                    <h3><i class="fas fa-heart"></i> 欢迎来到Kubernetes的世界！</h3>
                    <p>这是一份专门为初学者准备的Kubernetes入门教程。我们会用最通俗易懂的语言，带你从零开始学习Kubernetes。不需要任何基础，只要你有一颗学习的心！</p>

                    <div class="learning-path">
                        <h4><i class="fas fa-map"></i> 学习路径</h4>
                        <div class="path-steps">
                            <div class="path-step">
                                <div class="step-icon">1</div>
                                <div class="step-content">
                                    <h5>理解概念</h5>
                                    <p>先了解什么是K8s，为什么要用它</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">2</div>
                                <div class="step-content">
                                    <h5>学习基础</h5>
                                    <p>掌握Pod、Service等核心概念</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">3</div>
                                <div class="step-content">
                                    <h5>动手实践</h5>
                                    <p>部署第一个应用，体验K8s魅力</p>
                                </div>
                            </div>
                            <div class="path-step">
                                <div class="step-icon">4</div>
                                <div class="step-content">
                                    <h5>深入学习</h5>
                                    <p>掌握高级功能和最佳实践</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <section id="what-is-k8s">
                    <h2><span class="step-number">1</span>什么是Kubernetes？</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-baby"></i> 小白友好解释</h3>
                        <p>想象一下，你是一个餐厅的老板，有很多厨师（应用程序）需要管理。Kubernetes就像是一个超级智能的餐厅经理，它能够：</p>
                        <ul>
                            <li><i class="fas fa-users"></i> <strong>自动安排厨师工作</strong> - 决定哪个厨师做什么菜</li>
                            <li><i class="fas fa-heartbeat"></i> <strong>监控厨师状态</strong> - 如果有厨师生病了，立即找替补</li>
                            <li><i class="fas fa-balance-scale"></i> <strong>调节工作量</strong> - 客人多了就多安排厨师，客人少了就让厨师休息</li>
                            <li><i class="fas fa-network-wired"></i> <strong>协调团队合作</strong> - 让不同的厨师配合完成复杂的菜品</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-book"></i> 正式定义</h3>
                    <p>Kubernetes（简称K8s）是一个开源的<strong>容器编排平台</strong>，用于自动化部署、扩展和管理容器化应用程序。</p>

                    <div class="info-box">
                        <h4><i class="fas fa-lightbulb"></i> 为什么叫K8s？</h4>
                        <p>Kubernetes这个单词太长了（8个字母），所以大家简称为K8s（K + 8个字母 + s）。就像国际化叫i18n一样！</p>
                    </div>

                    <h3><i class="fas fa-history"></i> Kubernetes的历史</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-calendar"></i> 时间</th>
                            <th><i class="fas fa-landmark"></i> 事件</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td>2003-2004</td>
                            <td>Google开发Borg系统</td>
                            <td>Google内部的容器管理系统，K8s的前身</td>
                        </tr>
                        <tr>
                            <td>2014</td>
                            <td>Kubernetes诞生</td>
                            <td>Google基于Borg经验开源了Kubernetes</td>
                        </tr>
                        <tr>
                            <td>2015</td>
                            <td>CNCF成立</td>
                            <td>云原生计算基金会接管Kubernetes项目</td>
                        </tr>
                        <tr>
                            <td>2017</td>
                            <td>成为主流</td>
                            <td>各大云厂商都开始支持Kubernetes</td>
                        </tr>
                        <tr>
                            <td>现在</td>
                            <td>事实标准</td>
                            <td>容器编排的事实标准，被广泛采用</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-puzzle-piece"></i> 核心功能</h3>
                    <div class="path-steps">
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-rocket"></i></div>
                            <div class="step-content">
                                <h5>自动部署</h5>
                                <p>自动将应用部署到合适的服务器上，无需手动操作</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-expand-arrows-alt"></i></div>
                            <div class="step-content">
                                <h5>弹性伸缩</h5>
                                <p>根据负载自动增加或减少应用实例数量</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-shield-alt"></i></div>
                            <div class="step-content">
                                <h5>故障恢复</h5>
                                <p>应用出问题时自动重启或替换，保证服务不中断</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-balance-scale"></i></div>
                            <div class="step-content">
                                <h5>负载均衡</h5>
                                <p>智能分配用户请求，避免某台服务器过载</p>
                            </div>
                        </div>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-check-circle"></i> 小结</h4>
                        <p>Kubernetes就是一个超级智能的"应用管家"，它能帮你自动管理所有的应用程序，让你不用担心服务器宕机、流量激增等问题。有了它，你就可以专注于开发业务功能，而不用操心基础设施的管理！
                        </p>
                    </div>
                </section>

                <section id="why-use-k8s">
                    <h2><span class="step-number">2</span>为什么要使用Kubernetes？</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-question"></i> 没有Kubernetes的痛苦</h3>
                        <p>想象一下，你开了一家网店，生意越来越好：</p>
                        <ul>
                            <li><i class="fas fa-times"></i> <strong>双11来了</strong> - 流量暴增，服务器扛不住，网站崩了</li>
                            <li><i class="fas fa-times"></i> <strong>服务器坏了</strong> - 半夜被电话吵醒，要紧急修复</li>
                            <li><i class="fas fa-times"></i> <strong>更新应用</strong> - 需要一台台服务器手动更新，容易出错</li>
                            <li><i class="fas fa-times"></i> <strong>资源浪费</strong> - 为了应对高峰期，平时服务器大部分时间闲置</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-star"></i> Kubernetes的优势</h3>

                    <h4><i class="fas fa-magic"></i> 1. 自动化管理</h4>
                    <div class="info-box">
                        <p><strong>传统方式：</strong>手动部署应用到每台服务器，手动监控，手动处理故障</p>
                        <p><strong>K8s方式：</strong>告诉K8s你想要什么，它自动帮你实现和维护</p>

                        <pre><code># 传统方式：需要登录每台服务器执行
ssh server1 "docker run myapp"
ssh server2 "docker run myapp"
ssh server3 "docker run myapp"

# K8s方式：一条命令搞定
kubectl create deployment myapp --image=myapp --replicas=3</code></pre>
                    </div>

                    <h4><i class="fas fa-expand-arrows-alt"></i> 2. 弹性伸缩</h4>
                    <div class="success-box">
                        <p>就像魔法一样，K8s可以根据流量自动调整应用数量：</p>
                        <ul>
                            <li><strong>流量少时：</strong>只运行1个应用实例，节省资源</li>
                            <li><strong>流量增加：</strong>自动启动更多实例（比如10个）</li>
                            <li><strong>流量减少：</strong>自动减少实例，释放资源</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-shield-alt"></i> 3. 高可用性</h4>
                    <div class="warning-box">
                        <p><strong>场景：</strong>你的应用运行在3台服务器上，其中1台突然坏了</p>
                        <p><strong>K8s的反应：</strong></p>
                        <ol>
                            <li>立即检测到服务器故障</li>
                            <li>在其他健康的服务器上启动新的应用实例</li>
                            <li>更新负载均衡，确保用户请求不会发到坏掉的服务器</li>
                            <li>整个过程自动完成，用户几乎感觉不到</li>
                        </ol>
                    </div>
                </section>

                <section id="basic-concepts">
                    <h2><span class="step-number">3</span>基础概念详解</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-lightbulb"></i> 学习提示</h3>
                        <p>学习Kubernetes就像学习一门新语言，需要先掌握基本词汇。这些概念刚开始可能有点抽象，但我们会用生活中的例子来帮你理解！</p>
                    </div>

                    <h3><i class="fas fa-cube"></i> 1. Pod（豆荚）</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-home"></i> 生活比喻：Pod就像一个小房间</h4>
                        <p>想象Pod是一个小房间，里面可以住一个或几个密切相关的室友（容器）。这些室友：</p>
                        <ul>
                            <li><i class="fas fa-wifi"></i> <strong>共享网络</strong> - 使用同一个IP地址，就像共用一个门牌号</li>
                            <li><i class="fas fa-hdd"></i> <strong>共享存储</strong> - 可以访问相同的文件夹</li>
                            <li><i class="fas fa-users"></i> <strong>生死与共</strong> - 一起创建，一起销毁</li>
                        </ul>
                    </div>

                    <div class="warning-box">
                        <h4><i class="fas fa-exclamation-triangle"></i> 重要概念</h4>
                        <p>Pod是Kubernetes中<strong>最小的部署单位</strong>，你不能直接部署容器，只能部署Pod。大多数情况下，一个Pod只包含一个容器。</p>
                    </div>

                    <pre><code># 创建一个简单的Pod
apiVersion: v1
kind: Pod
metadata:
  name: my-first-pod
spec:
  containers:
  - name: nginx-container
    image: nginx:1.20
    ports:
    - containerPort: 80</code></pre>

                    <h3><i class="fas fa-layer-group"></i> 2. Deployment（部署）</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-factory"></i> 生活比喻：Deployment就像一个工厂管理员</h4>
                        <p>如果Pod是工人，那么Deployment就是工厂管理员，它负责：</p>
                        <ul>
                            <li><i class="fas fa-plus"></i> <strong>招聘工人</strong> - 创建指定数量的Pod</li>
                            <li><i class="fas fa-heartbeat"></i> <strong>监督工人</strong> - 确保Pod正常工作</li>
                            <li><i class="fas fa-redo"></i> <strong>替换工人</strong> - Pod挂了就创建新的</li>
                            <li><i class="fas fa-sync"></i> <strong>更新换代</strong> - 滚动更新应用版本</li>
                        </ul>
                    </div>

                    <pre><code># 创建一个Deployment，管理3个nginx Pod
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
spec:
  replicas: 3  # 我要3个Pod
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.20
        ports:
        - containerPort: 80</code></pre>

                    <h3><i class="fas fa-network-wired"></i> 3. Service（服务）</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-phone"></i> 生活比喻：Service就像一个总机</h4>
                        <p>想象你要给某个公司打电话，但你不知道具体分机号：</p>
                        <ul>
                            <li><i class="fas fa-phone"></i> <strong>统一入口</strong> - 你只需要拨打总机号码</li>
                            <li><i class="fas fa-random"></i> <strong>智能转接</strong> - 总机会把你转接到空闲的客服</li>
                            <li><i class="fas fa-shield-alt"></i> <strong>故障处理</strong> - 如果某个客服不在，会转接给其他人</li>
                        </ul>
                        <p>Service就是这个总机，它为一组Pod提供统一的访问入口。</p>
                    </div>

                    <h4><i class="fas fa-list"></i> Service的类型</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 类型</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                            <th><i class="fas fa-lightbulb"></i> 使用场景</th>
                        </tr>
                        <tr>
                            <td><strong>ClusterIP</strong></td>
                            <td>只能在集群内部访问</td>
                            <td>内部服务通信，如数据库</td>
                        </tr>
                        <tr>
                            <td><strong>NodePort</strong></td>
                            <td>通过节点端口对外暴露</td>
                            <td>简单的对外服务</td>
                        </tr>
                        <tr>
                            <td><strong>LoadBalancer</strong></td>
                            <td>使用云厂商的负载均衡器</td>
                            <td>生产环境的对外服务</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-tags"></i> 4. Label（标签）和 Selector（选择器）</h3>
                    <div class="beginner-box">
                        <h4><i class="fas fa-tag"></i> 生活比喻：Label就像贴纸标签</h4>
                        <p>想象你在整理房间，给每样东西贴上标签：</p>
                        <ul>
                            <li><i class="fas fa-tshirt"></i> 衣服贴上"衣物"、"夏装"、"红色"</li>
                            <li><i class="fas fa-book"></i> 书籍贴上"书籍"、"技术"、"编程"</li>
                            <li><i class="fas fa-gamepad"></i> 游戏贴上"娱乐"、"单机"、"RPG"</li>
                        </ul>
                        <p>当你想找"红色的夏装"时，就用选择器来筛选标签！</p>
                    </div>

                    <pre><code># Pod带标签
apiVersion: v1
kind: Pod
metadata:
  name: web-pod
  labels:
    app: web
    version: v1.0
    environment: production

---
# Service通过选择器找到对应的Pod
apiVersion: v1
kind: Service
metadata:
  name: web-service
spec:
  selector:
    app: web  # 选择所有app=web的Pod
  ports:
  - port: 80
    targetPort: 8080</code></pre>

                    <h3><i class="fas fa-sitemap"></i> 5. Namespace（命名空间）</h3>
                    <div class="success-box">
                        <h4><i class="fas fa-building"></i> 生活比喻：Namespace就像大楼的不同楼层</h4>
                        <p>想象一栋办公大楼：</p>
                        <ul>
                            <li><i class="fas fa-briefcase"></i> <strong>1楼：</strong>财务部门（default命名空间）</li>
                            <li><i class="fas fa-code"></i> <strong>2楼：</strong>技术部门（development命名空间）</li>
                            <li><i class="fas fa-chart-line"></i> <strong>3楼：</strong>销售部门（production命名空间）</li>
                        </ul>
                        <p>每个楼层相对独立，有自己的资源和管理规则，但都在同一栋大楼里。</p>
                    </div>

                    <div class="warning-box">
                        <h4><i class="fas fa-exclamation-triangle"></i> 注意事项</h4>
                        <ul>
                            <li>不同命名空间的资源是隔离的</li>
                            <li>相同命名空间内的资源名称不能重复</li>
                            <li>如果不指定命名空间，默认使用"default"</li>
                        </ul>
                    </div>

                    <pre><code># 创建命名空间
kubectl create namespace my-app

# 在指定命名空间创建资源
kubectl create deployment nginx --image=nginx -n my-app

# 查看指定命名空间的资源
kubectl get pods -n my-app</code></pre>

                    <div class="success-box">
                        <h4><i class="fas fa-graduation-cap"></i> 概念总结</h4>
                        <p>现在你已经掌握了Kubernetes的核心概念：</p>
                        <ul>
                            <li><strong>Pod：</strong>最小部署单位，像一个小房间</li>
                            <li><strong>Deployment：</strong>管理Pod的工厂管理员</li>
                            <li><strong>Service：</strong>提供统一访问入口的总机</li>
                            <li><strong>Label/Selector：</strong>给资源贴标签和筛选的方式</li>
                            <li><strong>Namespace：</strong>资源隔离的楼层</li>
                        </ul>
                        <p>这些概念就像积木块，组合起来就能构建复杂的应用系统！</p>
                    </div>
                </section>

                <section id="architecture">
                    <h2><span class="step-number">4</span>Kubernetes架构原理</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-building"></i> 架构比喻：Kubernetes就像一个智能城市</h3>
                        <p>想象Kubernetes是一个智能城市，有政府大楼（Master节点）和居民区（Worker节点）：</p>
                        <ul>
                            <li><i class="fas fa-university"></i> <strong>政府大楼</strong> - 制定政策，统一管理</li>
                            <li><i class="fas fa-home"></i> <strong>居民区</strong> - 执行政策，提供服务</li>
                            <li><i class="fas fa-road"></i> <strong>交通网络</strong> - 连接各个区域</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-crown"></i> Master节点（控制平面）</h3>
                    <p>Master节点就像城市的政府大楼，负责整个集群的管理和决策：</p>

                    <h4><i class="fas fa-server"></i> 1. API Server（政府窗口）</h4>
                    <div class="info-box">
                        <p><strong>作用：</strong>就像政府的服务窗口，所有请求都要通过这里</p>
                        <ul>
                            <li><i class="fas fa-door-open"></i> 接收所有的API请求（kubectl命令、其他组件的请求）</li>
                            <li><i class="fas fa-shield-alt"></i> 验证身份和权限（你有资格办这个业务吗？）</li>
                            <li><i class="fas fa-database"></i> 与etcd数据库交互（查询和存储信息）</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-database"></i> 2. etcd（政府档案馆）</h4>
                    <div class="success-box">
                        <p><strong>作用：</strong>存储集群的所有重要信息，就像政府的档案馆</p>
                        <ul>
                            <li><i class="fas fa-file-alt"></i> 存储集群配置信息</li>
                            <li><i class="fas fa-users"></i> 记录所有节点和Pod的状态</li>
                            <li><i class="fas fa-key"></i> 保存密钥和配置数据</li>
                            <li><i class="fas fa-history"></i> 支持数据版本控制和回滚</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-calendar-check"></i> 3. Scheduler（城市规划师）</h4>
                    <div class="warning-box">
                        <p><strong>作用：</strong>决定新的Pod应该放在哪个节点上，就像城市规划师</p>
                        <ul>
                            <li><i class="fas fa-search"></i> 监听新创建的Pod</li>
                            <li><i class="fas fa-balance-scale"></i> 评估各个节点的资源情况</li>
                            <li><i class="fas fa-map-marker-alt"></i> 选择最合适的节点来运行Pod</li>
                            <li><i class="fas fa-puzzle-piece"></i> 考虑亲和性、反亲和性等约束条件</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-cogs"></i> 4. Controller Manager（城市管理员）</h4>
                    <div class="info-box">
                        <p><strong>作用：</strong>确保城市按照规划运行，就像各种管理员</p>
                        <ul>
                            <li><i class="fas fa-eye"></i> <strong>Deployment Controller：</strong>确保Pod数量符合要求</li>
                            <li><i class="fas fa-heartbeat"></i> <strong>Node Controller：</strong>监控节点健康状态</li>
                            <li><i class="fas fa-network-wired"></i> <strong>Service Controller：</strong>管理服务的负载均衡</li>
                            <li><i class="fas fa-sync"></i> <strong>ReplicaSet Controller：</strong>维护Pod副本数量</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-home"></i> Worker节点（工作节点）</h3>
                    <p>Worker节点就像城市的居民区，负责实际运行应用：</p>

                    <h4><i class="fas fa-robot"></i> 1. kubelet（社区管理员）</h4>
                    <div class="success-box">
                        <p><strong>作用：</strong>每个节点的管理员，负责本节点的所有事务</p>
                        <ul>
                            <li><i class="fas fa-download"></i> 从API Server接收Pod规格</li>
                            <li><i class="fas fa-play"></i> 启动和停止容器</li>
                            <li><i class="fas fa-heartbeat"></i> 监控Pod和容器的健康状态</li>
                            <li><i class="fas fa-upload"></i> 向API Server报告节点和Pod状态</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-network-wired"></i> 2. kube-proxy（网络管理员）</h4>
                    <div class="info-box">
                        <p><strong>作用：</strong>管理网络流量，就像社区的网络管理员</p>
                        <ul>
                            <li><i class="fas fa-route"></i> 维护网络规则</li>
                            <li><i class="fas fa-random"></i> 实现Service的负载均衡</li>
                            <li><i class="fas fa-shield-alt"></i> 处理网络代理和转发</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-box"></i> 3. Container Runtime（房屋建造者）</h4>
                    <div class="warning-box">
                        <p><strong>作用：</strong>实际运行容器的组件，就像建造和维护房屋的工人</p>
                        <ul>
                            <li><i class="fas fa-download"></i> 拉取容器镜像</li>
                            <li><i class="fas fa-play"></i> 启动和停止容器</li>
                            <li><i class="fas fa-cog"></i> 管理容器生命周期</li>
                        </ul>
                        <p><strong>常见的容器运行时：</strong>Docker、containerd、CRI-O</p>
                    </div>

                    <h3><i class="fas fa-flow-chart"></i> 工作流程示例</h3>
                    <div class="beginner-box">
                        <h4><i class="fas fa-rocket"></i> 部署一个应用的完整流程</h4>
                        <ol>
                            <li><i class="fas fa-terminal"></i>
                                <strong>你执行命令：</strong><code>kubectl create deployment nginx --image=nginx</code>
                            </li>
                            <li><i class="fas fa-server"></i> <strong>API Server：</strong>接收请求，验证权限，存储到etcd</li>
                            <li><i class="fas fa-eye"></i> <strong>Controller
                                    Manager：</strong>发现新的Deployment，创建ReplicaSet和Pod</li>
                            <li><i class="fas fa-calendar-check"></i> <strong>Scheduler：</strong>为Pod选择合适的节点</li>
                            <li><i class="fas fa-robot"></i> <strong>kubelet：</strong>在选定节点上启动容器</li>
                            <li><i class="fas fa-box"></i> <strong>Container Runtime：</strong>拉取nginx镜像，启动容器</li>
                            <li><i class="fas fa-check-circle"></i> <strong>完成：</strong>nginx应用开始运行！</li>
                        </ol>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-lightbulb"></i> 架构总结</h4>
                        <p>Kubernetes的架构设计非常巧妙：</p>
                        <ul>
                            <li><strong>分工明确：</strong>每个组件都有专门的职责</li>
                            <li><strong>高可用：</strong>Master节点可以部署多个，避免单点故障</li>
                            <li><strong>可扩展：</strong>可以随时添加更多Worker节点</li>
                            <li><strong>声明式：</strong>你只需要告诉K8s想要什么，它会自动实现</li>
                        </ul>
                    </div>
                </section>

                <section id="installation-guide">
                    <h2><span class="step-number">5</span>安装指南</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-graduation-cap"></i> 学习建议</h3>
                        <p>对于初学者，我们推荐从简单的方式开始学习Kubernetes，等熟悉了基本概念后再考虑生产环境的部署。</p>
                    </div>

                    <h3><i class="fas fa-list"></i> 安装方式对比</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-tools"></i> 安装方式</th>
                            <th><i class="fas fa-star"></i> 难度</th>
                            <th><i class="fas fa-clock"></i> 时间</th>
                            <th><i class="fas fa-lightbulb"></i> 适用场景</th>
                        </tr>
                        <tr>
                            <td><strong>minikube</strong></td>
                            <td>⭐</td>
                            <td>10分钟</td>
                            <td>本地学习和开发</td>
                        </tr>
                        <tr>
                            <td><strong>Docker Desktop</strong></td>
                            <td>⭐</td>
                            <td>5分钟</td>
                            <td>Windows/Mac本地开发</td>
                        </tr>
                        <tr>
                            <td><strong>kubeadm</strong></td>
                            <td>⭐⭐⭐</td>
                            <td>1小时</td>
                            <td>测试环境和小规模生产</td>
                        </tr>
                        <tr>
                            <td><strong>云厂商托管</strong></td>
                            <td>⭐⭐</td>
                            <td>30分钟</td>
                            <td>生产环境推荐</td>
                        </tr>
                        <tr>
                            <td><strong>二进制安装</strong></td>
                            <td>⭐⭐⭐⭐⭐</td>
                            <td>半天</td>
                            <td>深度定制和学习</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-rocket"></i> 推荐：使用minikube开始学习</h3>
                    <div class="info-box">
                        <h4><i class="fas fa-lightbulb"></i> 为什么选择minikube？</h4>
                        <ul>
                            <li><i class="fas fa-download"></i> 安装简单，一条命令搞定</li>
                            <li><i class="fas fa-laptop"></i> 在本地运行，不需要云服务器</li>
                            <li><i class="fas fa-cog"></i> 功能完整，包含所有K8s特性</li>
                            <li><i class="fas fa-trash"></i> 可以随时删除重建，不怕搞坏</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-windows"></i> Windows安装minikube</h4>
                    <pre><code># 1. 安装Chocolatey包管理器（如果没有的话）
# 以管理员身份运行PowerShell，执行：
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# 2. 安装minikube
choco install minikube

# 3. 启动minikube
minikube start

# 4. 验证安装
kubectl get nodes</code></pre>

                    <h4><i class="fas fa-apple-alt"></i> macOS安装minikube</h4>
                    <pre><code># 1. 安装Homebrew（如果没有的话）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 2. 安装minikube
brew install minikube

# 3. 启动minikube
minikube start

# 4. 验证安装
kubectl get nodes</code></pre>

                    <h4><i class="fas fa-linux"></i> Linux安装minikube</h4>
                    <pre><code># 1. 下载minikube
curl -LO https://storage.googleapis.com/minikube/releases/latest/minikube-linux-amd64

# 2. 安装minikube
sudo install minikube-linux-amd64 /usr/local/bin/minikube

# 3. 启动minikube
minikube start

# 4. 验证安装
kubectl get nodes</code></pre>

                    <div class="success-box">
                        <h4><i class="fas fa-check-circle"></i> 安装成功标志</h4>
                        <p>如果看到类似下面的输出，说明安装成功：</p>
                        <pre><code>NAME       STATUS   ROLES           AGE   VERSION
minikube   Ready    control-plane   1m    v1.28.3</code></pre>
                    </div>

                    <h3><i class="fas fa-tools"></i> 常用minikube命令</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-terminal"></i> 命令</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td><code>minikube start</code></td>
                            <td>启动minikube集群</td>
                        </tr>
                        <tr>
                            <td><code>minikube stop</code></td>
                            <td>停止minikube集群</td>
                        </tr>
                        <tr>
                            <td><code>minikube delete</code></td>
                            <td>删除minikube集群</td>
                        </tr>
                        <tr>
                            <td><code>minikube status</code></td>
                            <td>查看集群状态</td>
                        </tr>
                        <tr>
                            <td><code>minikube dashboard</code></td>
                            <td>打开Web管理界面</td>
                        </tr>
                        <tr>
                            <td><code>minikube ip</code></td>
                            <td>获取集群IP地址</td>
                        </tr>
                    </table>

                    <div class="warning-box">
                        <h4><i class="fas fa-exclamation-triangle"></i> 常见问题</h4>
                        <ul>
                            <li><strong>启动失败：</strong>确保Docker已安装并运行</li>
                            <li><strong>网络问题：</strong>可能需要配置代理或使用国内镜像</li>
                            <li><strong>资源不足：</strong>确保至少有2GB内存和2个CPU核心</li>
                        </ul>
                    </div>
                </section>

                <section id="first-app">
                    <h2><span class="step-number">6</span>部署第一个应用</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-rocket"></i> 激动人心的时刻！</h3>
                        <p>现在我们要部署第一个应用了！就像学会骑自行车一样，第一次成功部署应用会让你对Kubernetes有全新的认识。我们会一步步详细解释每个操作。</p>
                    </div>

                    <h3><i class="fas fa-check-circle"></i> 前提条件</h3>
                    <div class="warning-box">
                        <p>在开始之前，请确保：</p>
                        <ul>
                            <li><i class="fas fa-check"></i> minikube已经安装并启动（<code>minikube status</code>显示Running）</li>
                            <li><i class="fas fa-check"></i> kubectl命令可以正常使用（<code>kubectl get nodes</code>能看到节点）</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-play"></i> 第一步：部署一个Web服务器</h3>
                    <p>我们选择nginx作为第一个应用，因为它简单、稳定，而且容易验证是否成功。</p>

                    <h4><i class="fas fa-terminal"></i> 1. 创建Deployment</h4>
                    <div class="info-box">
                        <p><strong>解释：</strong>Deployment会帮我们管理Pod，确保nginx始终运行。</p>
                    </div>
                    <pre><code># 创建一个nginx deployment
kubectl create deployment my-nginx --image=nginx:1.20

# 查看deployment状态
kubectl get deployments

# 查看pod状态
kubectl get pods</code></pre>

                    <div class="success-box">
                        <h4><i class="fas fa-lightbulb"></i> 命令解释</h4>
                        <ul>
                            <li><code>kubectl create deployment</code> - 创建一个新的部署</li>
                            <li><code>my-nginx</code> - 给这个部署起个名字</li>
                            <li><code>--image=nginx:1.20</code> - 使用nginx 1.20版本的镜像</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-eye"></i> 2. 查看应用状态</h4>
                    <pre><code># 详细查看pod信息
kubectl get pods -o wide

# 查看pod的详细描述
kubectl describe pod &lt;pod-name&gt;

# 查看pod日志
kubectl logs &lt;pod-name&gt;</code></pre>

                    <div class="info-box">
                        <h4><i class="fas fa-question-circle"></i> 看到什么了？</h4>
                        <p>你应该能看到类似这样的输出：</p>
                        <pre><code>NAME                        READY   STATUS    RESTARTS   AGE
my-nginx-7d8b49557f-abc123   1/1     Running   0          2m</code></pre>
                        <ul>
                            <li><strong>READY 1/1：</strong>Pod中的1个容器已经准备就绪</li>
                            <li><strong>STATUS Running：</strong>Pod正在正常运行</li>
                            <li><strong>RESTARTS 0：</strong>没有重启过，说明很稳定</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-network-wired"></i> 3. 暴露服务</h4>
                    <p>现在nginx在运行，但我们还不能从外部访问它。需要创建一个Service来暴露服务。</p>

                    <pre><code># 创建Service，暴露nginx服务
kubectl expose deployment my-nginx --port=80 --type=NodePort

# 查看服务
kubectl get services

# 获取服务的访问地址
minikube service my-nginx --url</code></pre>

                    <div class="beginner-box">
                        <h4><i class="fas fa-lightbulb"></i> 这里发生了什么？</h4>
                        <ol>
                            <li><strong>expose deployment：</strong>为我们的nginx deployment创建一个服务</li>
                            <li><strong>--port=80：</strong>nginx监听80端口</li>
                            <li><strong>--type=NodePort：</strong>通过节点端口对外暴露服务</li>
                            <li><strong>minikube service：</strong>获取在minikube中访问服务的URL</li>
                        </ol>
                    </div>

                    <h4><i class="fas fa-globe"></i> 4. 访问你的应用</h4>
                    <div class="success-box">
                        <p>复制上一步得到的URL，在浏览器中打开，你应该能看到nginx的欢迎页面！</p>
                        <p><strong>恭喜！🎉 你已经成功在Kubernetes上部署了第一个应用！</strong></p>
                    </div>

                    <h3><i class="fas fa-expand-arrows-alt"></i> 第二步：体验弹性伸缩</h3>
                    <p>现在让我们体验Kubernetes最酷的功能之一：自动伸缩！</p>

                    <h4><i class="fas fa-plus"></i> 1. 扩展应用实例</h4>
                    <pre><code># 将nginx扩展到3个实例
kubectl scale deployment my-nginx --replicas=3

# 查看pod数量变化
kubectl get pods

# 实时观察pod状态变化
kubectl get pods -w</code></pre>

                    <div class="info-box">
                        <h4><i class="fas fa-magic"></i> 神奇的事情发生了！</h4>
                        <p>你会看到Kubernetes自动创建了2个新的nginx Pod，现在总共有3个nginx实例在运行！这就是弹性伸缩的威力。</p>
                    </div>

                    <h4><i class="fas fa-minus"></i> 2. 缩减应用实例</h4>
                    <pre><code># 缩减到1个实例
kubectl scale deployment my-nginx --replicas=1

# 观察pod被删除的过程
kubectl get pods -w</code></pre>

                    <h3><i class="fas fa-sync"></i> 第三步：更新应用</h3>
                    <p>让我们体验一下滚动更新，这是生产环境中非常重要的功能。</p>

                    <h4><i class="fas fa-upload"></i> 1. 更新nginx版本</h4>
                    <pre><code># 更新nginx到最新版本
kubectl set image deployment/my-nginx nginx=nginx:latest

# 查看更新状态
kubectl rollout status deployment/my-nginx

# 查看更新历史
kubectl rollout history deployment/my-nginx</code></pre>

                    <div class="beginner-box">
                        <h4><i class="fas fa-cogs"></i> 滚动更新的过程</h4>
                        <ol>
                            <li>Kubernetes创建一个新的Pod（使用新版本镜像）</li>
                            <li>等待新Pod启动并准备就绪</li>
                            <li>删除旧的Pod</li>
                            <li>整个过程中服务不会中断！</li>
                        </ol>
                    </div>

                    <h4><i class="fas fa-undo"></i> 2. 回滚到之前版本</h4>
                    <pre><code># 如果新版本有问题，可以快速回滚
kubectl rollout undo deployment/my-nginx

# 查看回滚状态
kubectl rollout status deployment/my-nginx</code></pre>

                    <h3><i class="fas fa-trash"></i> 第四步：清理资源</h3>
                    <p>实验完成后，让我们清理创建的资源：</p>

                    <pre><code># 删除service
kubectl delete service my-nginx

# 删除deployment（这会自动删除相关的pod）
kubectl delete deployment my-nginx

# 验证资源已删除
kubectl get all</code></pre>

                    <h3><i class="fas fa-file-alt"></i> 第五步：使用YAML文件部署</h3>
                    <p>命令行很方便，但在实际工作中，我们更多使用YAML文件来定义资源。</p>

                    <h4><i class="fas fa-edit"></i> 1. 创建YAML文件</h4>
                    <p>创建一个名为<code>nginx-app.yaml</code>的文件：</p>

                    <pre><code># nginx-app.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
  labels:
    app: nginx
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.20
        ports:
        - containerPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
spec:
  selector:
    app: nginx
  ports:
  - port: 80
    targetPort: 80
  type: NodePort</code></pre>

                    <h4><i class="fas fa-rocket"></i> 2. 部署YAML文件</h4>
                    <pre><code># 应用YAML文件
kubectl apply -f nginx-app.yaml

# 查看创建的资源
kubectl get all

# 获取服务访问地址
minikube service nginx-service --url</code></pre>

                    <div class="success-box">
                        <h4><i class="fas fa-graduation-cap"></i> 恭喜你完成了第一个应用的部署！</h4>
                        <p>通过这个练习，你已经学会了：</p>
                        <ul>
                            <li><i class="fas fa-check"></i> 使用kubectl命令部署应用</li>
                            <li><i class="fas fa-check"></i> 创建Service暴露服务</li>
                            <li><i class="fas fa-check"></i> 弹性伸缩应用实例</li>
                            <li><i class="fas fa-check"></i> 滚动更新和回滚</li>
                            <li><i class="fas fa-check"></i> 使用YAML文件管理资源</li>
                        </ul>
                        <p>这些都是Kubernetes的核心功能，你已经迈出了成为K8s专家的第一步！</p>
                    </div>

                    <div class="warning-box">
                        <h4><i class="fas fa-lightbulb"></i> 下一步建议</h4>
                        <p>现在你可以：</p>
                        <ul>
                            <li>尝试部署其他应用（如Redis、MySQL等）</li>
                            <li>学习更多kubectl命令</li>
                            <li>探索Kubernetes Dashboard</li>
                            <li>继续学习后面的章节</li>
                        </ul>
                    </div>
                </section>

                <section id="pod-management">
                    <h2><span class="step-number">7</span>Pod管理进阶</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-box"></i> Pod是Kubernetes的核心</h3>
                        <p>现在你已经部署了第一个应用，让我们深入了解Pod的管理技巧。</p>
                    </div>

                    <h3><i class="fas fa-heartbeat"></i> 健康检查</h3>
                    <div class="info-box">
                        <p>Kubernetes可以自动检查你的应用是否健康，就像医生定期体检一样：</p>
                        <ul>
                            <li><strong>存活探针（Liveness Probe）：</strong>检查应用是否还活着</li>
                            <li><strong>就绪探针（Readiness Probe）：</strong>检查应用是否准备好接收请求</li>
                        </ul>
                    </div>

                    <pre><code># 带健康检查的Pod示例
apiVersion: v1
kind: Pod
metadata:
  name: healthy-pod
spec:
  containers:
  - name: app
    image: nginx:1.20
    livenessProbe:
      httpGet:
        path: /
        port: 80
      initialDelaySeconds: 30
      periodSeconds: 10
    readinessProbe:
      httpGet:
        path: /
        port: 80
      initialDelaySeconds: 5
      periodSeconds: 5</code></pre>

                    <h3><i class="fas fa-cog"></i> 资源限制</h3>
                    <div class="warning-box">
                        <p>就像给孩子零花钱一样，我们需要给Pod设置资源限制：</p>
                        <ul>
                            <li><strong>requests：</strong>Pod需要的最少资源（保证能分配到）</li>
                            <li><strong>limits：</strong>Pod能使用的最大资源（防止占用过多）</li>
                        </ul>
                    </div>
                </section>

                <section id="service-networking">
                    <h2><span class="step-number">8</span>服务与网络</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-network-wired"></i> 网络是连接的桥梁</h3>
                        <p>在Kubernetes中，网络让不同的应用能够相互通信，就像城市的道路系统。</p>
                    </div>

                    <h3><i class="fas fa-route"></i> Service类型详解</h3>
                    <div class="path-steps">
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-home"></i></div>
                            <div class="step-content">
                                <h5>ClusterIP</h5>
                                <p>内部通信专用，就像家里的内线电话</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-door-open"></i></div>
                            <div class="step-content">
                                <h5>NodePort</h5>
                                <p>通过节点端口对外开放，像开了一扇门</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-balance-scale"></i></div>
                            <div class="step-content">
                                <h5>LoadBalancer</h5>
                                <p>云厂商提供的负载均衡器</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon"><i class="fas fa-globe"></i></div>
                            <div class="step-content">
                                <h5>Ingress</h5>
                                <p>HTTP/HTTPS路由，像网站的导航</p>
                            </div>
                        </div>
                    </div>
                </section>

                <section id="storage">
                    <h2><span class="step-number">9</span>存储管理</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-hdd"></i> 数据需要持久化</h3>
                        <p>就像我们需要把重要文件保存在硬盘里一样，应用的数据也需要持久化存储。</p>
                    </div>

                    <h3><i class="fas fa-folder"></i> 存储类型</h3>
                    <div class="info-box">
                        <ul>
                            <li><strong>Volume：</strong>临时存储，Pod删除后数据丢失</li>
                            <li><strong>PersistentVolume：</strong>持久存储，数据永久保存</li>
                            <li><strong>ConfigMap：</strong>存储配置文件</li>
                            <li><strong>Secret：</strong>存储敏感信息（密码、证书等）</li>
                        </ul>
                    </div>
                </section>

                <section id="config-secrets">
                    <h2><span class="step-number">10</span>配置与密钥管理</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-key"></i> 安全地管理配置</h3>
                        <p>应用需要各种配置信息，Kubernetes提供了安全的方式来管理这些信息。</p>
                    </div>

                    <h3><i class="fas fa-file-alt"></i> ConfigMap示例</h3>
                    <pre><code># 创建ConfigMap
kubectl create configmap app-config --from-literal=database_url=mysql://localhost:3306

# 在Pod中使用ConfigMap
apiVersion: v1
kind: Pod
metadata:
  name: app-pod
spec:
  containers:
  - name: app
    image: myapp:latest
    env:
    - name: DATABASE_URL
      valueFrom:
        configMapKeyRef:
          name: app-config
          key: database_url</code></pre>
                </section>

                <section id="monitoring">
                    <h2><span class="step-number">11</span>监控与日志</h2>

                    <div class="beginner-box">
                        <h3><i class="fas fa-chart-line"></i> 监控应用健康状态</h3>
                        <p>就像医生需要监控病人的生命体征一样，我们也需要监控应用的运行状态。</p>
                    </div>

                    <h3><i class="fas fa-eye"></i> 基本监控命令</h3>
                    <pre><code># 查看Pod资源使用情况
kubectl top pods

# 查看节点资源使用情况
kubectl top nodes

# 查看Pod日志
kubectl logs -f &lt;pod-name&gt;

# 进入Pod内部调试
kubectl exec -it &lt;pod-name&gt; -- /bin/bash</code></pre>
                </section>

                <section id="best-practices">
                    <h2><span class="step-number">12</span>最佳实践与总结</h2>

                    <div class="success-box">
                        <h3><i class="fas fa-trophy"></i> 恭喜！你已经完成了Kubernetes入门之旅！</h3>
                        <p>通过这个教程，你已经从一个完全的小白成长为了Kubernetes的初级用户。让我们回顾一下你学到的知识：</p>
                    </div>

                    <h3><i class="fas fa-graduation-cap"></i> 你已经掌握的技能</h3>
                    <div class="path-steps">
                        <div class="path-step">
                            <div class="step-icon">✅</div>
                            <div class="step-content">
                                <h5>基础概念</h5>
                                <p>Pod、Deployment、Service、Namespace等核心概念</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon">✅</div>
                            <div class="step-content">
                                <h5>实际操作</h5>
                                <p>部署应用、扩缩容、滚动更新、故障排查</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon">✅</div>
                            <div class="step-content">
                                <h5>YAML配置</h5>
                                <p>使用声明式配置管理Kubernetes资源</p>
                            </div>
                        </div>
                        <div class="path-step">
                            <div class="step-icon">✅</div>
                            <div class="step-content">
                                <h5>最佳实践</h5>
                                <p>健康检查、资源限制、安全配置</p>
                            </div>
                        </div>
                    </div>

                    <h3><i class="fas fa-star"></i> 生产环境最佳实践</h3>
                    <div class="info-box">
                        <ul>
                            <li><i class="fas fa-shield-alt"></i> <strong>安全第一：</strong>使用RBAC、网络策略、Pod安全策略</li>
                            <li><i class="fas fa-chart-line"></i> <strong>监控告警：</strong>部署Prometheus、Grafana等监控系统</li>
                            <li><i class="fas fa-backup"></i> <strong>备份策略：</strong>定期备份etcd和重要数据</li>
                            <li><i class="fas fa-cog"></i> <strong>资源管理：</strong>合理设置资源请求和限制</li>
                            <li><i class="fas fa-rocket"></i> <strong>CI/CD：</strong>建立自动化部署流水线</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-road"></i> 继续学习的路径</h3>
                    <div class="warning-box">
                        <h4><i class="fas fa-map"></i> 进阶学习建议</h4>
                        <ol>
                            <li><strong>深入学习：</strong>StatefulSet、DaemonSet、Job等高级资源</li>
                            <li><strong>网络进阶：</strong>CNI插件、网络策略、服务网格</li>
                            <li><strong>存储进阶：</strong>CSI驱动、存储类、数据备份</li>
                            <li><strong>安全加固：</strong>RBAC、Pod安全策略、镜像扫描</li>
                            <li><strong>运维实践：</strong>集群升级、故障排查、性能调优</li>
                            <li><strong>生态工具：</strong>Helm、Istio、Prometheus等</li>
                        </ol>
                    </div>

                    <h3><i class="fas fa-heart"></i> 结语</h3>
                    <div class="beginner-box">
                        <p>Kubernetes的学习之路虽然有挑战，但也充满乐趣。记住：</p>
                        <ul>
                            <li><i class="fas fa-hands-helping"></i> <strong>多实践：</strong>理论结合实践，动手操作是最好的学习方式</li>
                            <li><i class="fas fa-users"></i> <strong>多交流：</strong>加入Kubernetes社区，与其他开发者交流经验</li>
                            <li><i class="fas fa-book"></i> <strong>多阅读：</strong>关注官方文档和最新技术动态</li>
                            <li><i class="fas fa-lightbulb"></i> <strong>多思考：</strong>理解背后的设计原理，而不仅仅是记住命令</li>
                        </ul>
                        <p><strong>祝你在Kubernetes的世界里探索愉快！🚀</strong></p>
                    </div>

                    <div class="success-box">
                        <h4><i class="fas fa-gift"></i> 学习资源推荐</h4>
                        <ul>
                            <li><strong>官方文档：</strong> <a href="https://kubernetes.io/docs/"
                                    target="_blank">https://kubernetes.io/docs/</a></li>
                            <li><strong>在线练习：</strong> <a href="https://katacoda.com/courses/kubernetes"
                                    target="_blank">Katacoda Kubernetes</a></li>
                            <li><strong>中文社区：</strong> <a href="https://kubernetes.org.cn/"
                                    target="_blank">Kubernetes中文社区</a></li>
                            <li><strong>实战项目：</strong>尝试部署真实的应用到Kubernetes集群</li>
                        </ul>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <a href="#" class="back-to-top" id="backToTop"><i class="fas fa-arrow-up"></i></a>

    <script>
        // 移动端菜单切换
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const sidebar = document.getElementById('sidebar');

        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', function () {
                sidebar.classList.toggle('active');
            });
        }

        // 侧边栏导航高亮
        function updateActiveNav() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.sidebar a');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                if (window.pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        }

        // 返回顶部功能
        function toggleBackToTop() {
            const backToTop = document.getElementById("backToTop");
            if (document.body.scrollTop > 300 || document.documentElement.scrollTop > 300) {
                backToTop.style.display = "flex";
            } else {
                backToTop.style.display = "none";
            }
        }

        // 滚动事件监听
        window.addEventListener('scroll', function () {
            updateActiveNav();
            toggleBackToTop();
        });

        // 返回顶部点击事件
        const backToTopBtn = document.getElementById("backToTop");
        if (backToTopBtn) {
            backToTopBtn.addEventListener('click', function (e) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // 平滑滚动
        document.querySelectorAll('.sidebar a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
                // 移动端关闭菜单
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                }
            });
        });

        // 点击外部关闭移动端菜单
        document.addEventListener('click', function (e) {
            if (window.innerWidth <= 768 && !sidebar.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                sidebar.classList.remove('active');
            }
        });

        // 初始化导航高亮
        updateActiveNav();

        // 页面加载动画
        window.addEventListener('load', function () {
            document.body.style.opacity = '1';
        });
    </script>
</body>

</html>