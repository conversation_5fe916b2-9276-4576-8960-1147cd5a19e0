<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银河麒麟v10 sp3 2403服务器K8s v1.24.17二进制部署教程</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 12px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 机器标识样式 */
        .machine-tag {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            margin: 0 8px 12px 0;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .machine-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .machine-tag:hover::before {
            left: 100%;
        }

        .machine-master {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
        }

        .machine-node {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
        }

        .machine-all {
            background: linear-gradient(135deg, #f9ca24 0%, #f0932b 100%);
            color: white;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: var(--light-surface);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        th,
        td {
            padding: 18px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-decoration: none;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            z-index: 999;
            font-size: 20px;
        }

        .back-to-top:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .content-wrapper {
                padding: 30px;
            }

            .container {
                padding: 40px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
                border-radius: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 16px;
                margin-right: 15px;
            }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 12px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            transform: scale(1.1);
        }

        ::-webkit-scrollbar-thumb:active {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
        }

        /* 主内容区域滚动条 */
        .main-content::-webkit-scrollbar {
            width: 8px;
        }

        .main-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .main-content::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.3);
            border-radius: 4px;
        }

        .main-content::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.6);
        }

        /* 平滑滚动效果 */
        html {
            scroll-behavior: smooth;
        }
    </style>
</head>

<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-cubes"></i> K8s v1.24.17二进制部署</h2>
            <p>银河麒麟v10 sp3 2403服务器</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#overview"><i class="fas fa-eye"></i>1. 概述</a></li>
                <li><a href="#environment-preparation"><i class="fas fa-cog"></i>2. 环境准备</a></li>
                <li><a href="#certificate-preparation"><i class="fas fa-key"></i>3. 证书准备</a></li>
                <li><a href="#etcd-deployment"><i class="fas fa-database"></i>4. ETCD集群部署</a></li>
                <li><a href="#master-deployment"><i class="fas fa-crown"></i>5. Master组件部署</a></li>
                <li><a href="#worker-deployment"><i class="fas fa-server"></i>6. Worker组件部署</a></li>
                <li><a href="#network-deployment"><i class="fas fa-network-wired"></i>7. 网络组件部署</a></li>
                <li><a href="#coredns-deployment"><i class="fas fa-globe"></i>8. CoreDNS部署</a></li>
                <li><a href="#dashboard-deployment"><i class="fas fa-chart-line"></i>9. Dashboard部署</a></li>
                <li><a href="#verify-deployment"><i class="fas fa-check-circle"></i>10. 验证部署</a></li>
                <li><a href="#rbac-enhancement"><i class="fas fa-shield-alt"></i>11. RBAC权限完善</a></li>
                <li><a href="#application-deployment"><i class="fas fa-rocket"></i>12. 应用部署验证</a></li>
                <li><a href="#troubleshooting"><i class="fas fa-bug"></i>13. 故障排查</a></li>
                <li><a href="#monitoring-logging"><i class="fas fa-chart-line"></i>14. 监控和日志</a></li>
                <li><a href="#summary"><i class="fas fa-flag-checkered"></i>15. 总结</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-cubes"></i> 银河麒麟v10 sp3 2403服务器K8s v1.24.17二进制部署教程</h1>

                <div class="info-box">
                    <strong><i class="fas fa-info-circle"></i>
                        教程说明：</strong>本教程详细介绍了如何在银河麒麟v10 sp3 2403服务器上使用二进制方式部署Kubernetes v1.24.17集群。K8s 1.24.17是一个重要的LTS版本，具有出色的稳定性和兼容性，特别适合生产环境使用。每个步骤都配有详细解释和预期效果，适合初学者按步骤完成部署。
                </div>

                <div class="warning-box">
                    <strong><i class="fas fa-exclamation-triangle"></i> 服务器配置说明：</strong>
                    <div style="margin-top: 15px;">
                        <span class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</span> -
                        IP：*************，4核8G内存<br>
                        <span class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</span> -
                        IP：*************，4核8G内存<br>
                        <span class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</span> -
                        需要执行的命令在所有节点上执行<br>
                    </div>
                    <div style="margin-top: 15px; padding: 10px; background: rgba(255, 193, 7, 0.1); border-left: 4px solid #ffc107;">
                        <strong><i class="fas fa-info-circle"></i> K8s v1.24.17特别说明：</strong><br>
                        本教程使用Kubernetes v1.24.17版本，这是最后一个包含dockershim的版本，同时完全支持containerd作为容器运行时。已针对银河麒麟v10sp3 2403系统进行优化，包含系统特殊配置和兼容性处理。
                    </div>
                </div>

                <!-- 概述部分 -->
                <section id="overview">
                    <h2><span class="step-number">1</span>概述</h2>

                    <h3><i class="fas fa-bookmark"></i> 1.1 什么是Kubernetes v1.24.17</h3>
                    <p>Kubernetes v1.24.17是Kubernetes的一个重要LTS（长期支持）版本，具有以下特点：</p>
                    <ul style="list-style-type: none; padding-left: 20px;">
                        <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 最后一个包含dockershim的版本，向后兼容性强</li>
                        <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 完全支持containerd作为容器运行时</li>
                        <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 稳定性和安全性经过长期验证</li>
                        <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 广泛的生态系统支持和社区维护</li>
                        <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 适合生产环境长期使用</li>
                    </ul>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> v1.24.17版本特性：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- <strong>容器运行时：</strong> 支持containerd 1.6.x系列，同时保留dockershim支持</li>
                            <li>- <strong>API版本：</strong> 使用稳定的API版本，兼容性好</li>
                            <li>- <strong>网络插件：</strong> 完全支持CNI 1.0规范</li>
                            <li>- <strong>存储：</strong> 支持CSI 1.6，存储功能完善</li>
                            <li>- <strong>安全性：</strong> 包含最新的安全补丁和修复</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-sitemap"></i> 1.2 K8s v1.24.17集群架构</h3>
                    <p>Kubernetes v1.24.17集群主要由以下部分组成：</p>
                    <table>
                        <tr>
                            <th><i class="fas fa-layer-group"></i> 组件类型</th>
                            <th><i class="fas fa-box"></i> 组件名称</th>
                            <th><i class="fas fa-info-circle"></i> 功能描述</th>
                        </tr>
                        <tr>
                            <td rowspan="4">Master组件</td>
                            <td>kube-apiserver</td>
                            <td>提供集群管理的REST API接口，是集群控制的入口</td>
                        </tr>
                        <tr>
                            <td>kube-controller-manager</td>
                            <td>负责维护集群的状态，如故障检测、自动扩展、滚动更新等</td>
                        </tr>
                        <tr>
                            <td>kube-scheduler</td>
                            <td>负责资源的调度，按照预定的调度策略将Pod调度到相应的节点上</td>
                        </tr>
                        <tr>
                            <td>etcd</td>
                            <td>键值对数据库，保存了整个集群的状态</td>
                        </tr>
                        <tr>
                            <td rowspan="2">Node组件</td>
                            <td>kubelet</td>
                            <td>在每个节点上运行的代理，保证容器都运行在Pod中</td>
                        </tr>
                        <tr>
                            <td>kube-proxy</td>
                            <td>负责为Service提供集群内部的服务发现和负载均衡</td>
                        </tr>
                        <tr>
                            <td>容器运行时</td>
                            <td>containerd</td>
                            <td>负责容器的生命周期管理</td>
                        </tr>
                        <tr>
                            <td>网络插件</td>
                            <td>Calico</td>
                            <td>实现容器之间的网络通信</td>
                        </tr>
                    </table>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 部署方式说明：</strong>本教程使用"二进制方式"部署K8s v1.24.17集群，相比kubeadm等工具化部署方式，二进制部署具有更高的灵活性和可控性，适合理解K8s组件工作原理，同时方便进行深度的定制化配置。
                    </div>

                    <h3><i class="fas fa-tasks"></i> 1.3 部署计划</h3>
                    <p>本次部署采用1个Master节点和1个Worker节点的最小化集群配置：</p>
                    <table>
                        <tr>
                            <th><i class="fas fa-network-wired"></i> 角色</th>
                            <th><i class="fas fa-server"></i> 主机名</th>
                            <th><i class="fas fa-project-diagram"></i> IP地址</th>
                            <th><i class="fas fa-boxes"></i> 部署组件</th>
                        </tr>
                        <tr>
                            <td>Master</td>
                            <td>k8s-master</td>
                            <td>*************</td>
                            <td>etcd、kube-apiserver、kube-controller-manager、kube-scheduler、kubectl</td>
                        </tr>
                        <tr>
                            <td>Worker</td>
                            <td>k8s-node1</td>
                            <td>*************</td>
                            <td>kubelet、kube-proxy、容器运行时(containerd)</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-exclamation-triangle"></i> 1.4 版本兼容性说明</h3>
                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> K8s v1.24.17版本兼容性矩阵：</strong>
                        <table style="margin-top: 15px;">
                            <tr>
                                <th>组件</th>
                                <th>推荐版本</th>
                                <th>兼容性说明</th>
                            </tr>
                            <tr>
                                <td>etcd</td>
                                <td>v3.5.6</td>
                                <td>与K8s v1.24.17完全兼容</td>
                            </tr>
                            <tr>
                                <td>containerd</td>
                                <td>v1.6.15</td>
                                <td>推荐版本，稳定性好</td>
                            </tr>
                            <tr>
                                <td>runc</td>
                                <td>v1.1.4</td>
                                <td>与containerd 1.6.15兼容</td>
                            </tr>
                            <tr>
                                <td>CNI plugins</td>
                                <td>v1.1.1</td>
                                <td>支持CNI 1.0规范</td>
                            </tr>
                            <tr>
                                <td>Calico</td>
                                <td>v3.24.5</td>
                                <td>与K8s v1.24.17兼容</td>
                            </tr>
                            <tr>
                                <td>CoreDNS</td>
                                <td>v1.8.6</td>
                                <td>K8s v1.24.17默认DNS版本</td>
                            </tr>
                        </table>
                    </div>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 银河麒麟v10sp3 2403兼容性：</strong>
                        经过测试验证，Kubernetes v1.24.17在银河麒麟v10sp3 2403系统上运行稳定，所有组件都能正常工作。本教程已针对该系统进行了特殊优化。
                    </div>
                </section>

                <!-- 环境准备部分 -->
                <section id="environment-preparation">
                    <h2><span class="step-number">2</span>环境准备</h2>

                    <h3><i class="fas fa-server"></i> 2.1 硬件要求</h3>
                    <p>在银河麒麟v10 sp3 2403服务器上部署Kubernetes v1.24.17，推荐的硬件配置如下：</p>
                    <table>
                        <tr>
                            <th><i class="fas fa-microchip"></i> 组件</th>
                            <th><i class="fas fa-info-circle"></i> 最低配置</th>
                            <th><i class="fas fa-thumbs-up"></i> 建议配置</th>
                            <th><i class="fas fa-comment"></i> 说明</th>
                        </tr>
                        <tr>
                            <td>CPU</td>
                            <td>2核</td>
                            <td>4核</td>
                            <td>Master节点CPU消耗较大</td>
                        </tr>
                        <tr>
                            <td>内存</td>
                            <td>4GB</td>
                            <td>8GB</td>
                            <td>建议Master节点至少8GB</td>
                        </tr>
                        <tr>
                            <td>硬盘</td>
                            <td>50GB</td>
                            <td>100GB+</td>
                            <td>系统盘建议SSD以提高性能</td>
                        </tr>
                        <tr>
                            <td>网卡</td>
                            <td>千兆网卡</td>
                            <td>万兆网卡</td>
                            <td>节点间通信较频繁</td>
                        </tr>
                    </table>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 本次部署硬件配置：</strong>
                        我们使用2台配置为4核8G内存的银河麒麟v10 sp3 2403服务器，满足K8s v1.24.17集群的部署要求。
                    </div>

                    <h3><i class="fas fa-sync-alt"></i> 2.1.1 系统更新和依赖环境准备</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <p>在开始Kubernetes部署之前，必须先更新系统并安装必要的依赖环境，这是确保部署成功的关键步骤。</p>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
                        系统更新和依赖安装是部署的第一步，跳过此步骤可能导致后续安装失败或运行异常。建议在网络状况良好时进行此操作。
                    </div>

                    <h4><i class="fas fa-download"></i> 更新系统软件包</h4>
                    <pre><code># 更新软件包索引
yum clean all
yum makecache

# 查看可更新的软件包
yum check-update

# 更新所有软件包（推荐）
yum update -y

# 或者只更新安全补丁（保守方式）
# yum update --security -y

# 验证系统版本
cat /etc/kylin-release
uname -r</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 更新说明：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- <strong>完整更新：</strong> 推荐使用 <code>yum update -y</code> 获得最新的系统补丁和驱动</li>
                            <li>- <strong>保守更新：</strong> 如果是生产环境，可以只更新安全补丁</li>
                            <li>- <strong>重启建议：</strong> 如果内核有更新，建议重启系统</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-tools"></i> 安装基础依赖工具</h4>
                    <pre><code># 安装基础开发工具和依赖
yum groupinstall -y "Development Tools"
yum install -y \
    wget \
    curl \
    vim \
    git \
    unzip \
    tar \
    gzip \
    net-tools \
    telnet \
    nmap \
    lsof \
    htop \
    iotop \
    sysstat \
    tcpdump \
    bind-utils \
    iputils \
    iproute \
    iptables \
    iptables-services \
    firewalld \
    chrony \
    rsync \
    tree \
    jq \
    yum-utils \
    device-mapper-persistent-data \
    lvm2

# 验证关键工具安装
which wget curl vim git
echo "基础工具安装完成"</code></pre>

                    <h4><i class="fas fa-puzzle-piece"></i> 安装容器相关依赖</h4>
                    <pre><code># 安装容器运行时依赖
yum install -y \
    container-selinux \
    libseccomp \
    libseccomp-devel

# 安装网络相关工具
yum install -y \
    bridge-utils \
    conntrack-tools \
    ipvsadm \
    ipset \
    socat \
    ebtables \
    ethtool

# 加载必要的内核模块
modprobe br_netfilter
modprobe ip_vs
modprobe ip_vs_rr
modprobe ip_vs_wrr
modprobe ip_vs_sh
modprobe nf_conntrack
modprobe overlay

# 确保开机自动加载
cat > /etc/modules-load.d/k8s.conf << EOF
br_netfilter
ip_vs
ip_vs_rr
ip_vs_wrr
ip_vs_sh
nf_conntrack
overlay
EOF

# 检查模块加载
lsmod | grep -E "br_netfilter|ip_vs|nf_conntrack|overlay"</code></pre>

                    <h4><i class="fas fa-clock"></i> 配置时间同步</h4>
                    <pre><code># 安装和配置chrony时间同步
yum install -y chrony

# 启动并设置开机启动
systemctl start chronyd
systemctl enable chronyd

# 配置时间服务器（使用国内NTP服务器）
cat > /etc/chrony.conf << EOF
# 使用国内NTP服务器
server ntp.aliyun.com iburst
server cn.pool.ntp.org iburst
server ntp.tuna.tsinghua.edu.cn iburst

# 其他配置保持默认
driftfile /var/lib/chrony/drift
makestep 1.0 3
rtcsync
logdir /var/log/chrony
EOF

# 重启chrony服务
systemctl restart chronyd

# 验证时间同步
chrony sources -v
timedatectl status</code></pre>

                    <h3 id="system-config"><i class="fas fa-cog"></i> 2.1.2 银河麒麟系统检查与配置</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <p>在开始部署前，需要先检查和配置银河麒麟系统的特殊设置：</p>

                    <div class="danger-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 银河麒麟v10sp3 2403 + K8s v1.24.17 关键兼容性要求：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>✓ 内核版本必须 >= 4.18（支持cgroup v1/v2）</li>
                            <li>✓ 必须支持overlay和br_netfilter模块</li>
                            <li>✓ iptables版本必须兼容（建议 >= 1.6.0）</li>
                            <li>✓ 系统必须支持systemd cgroup驱动</li>
                            <li>✓ 确保runc版本 >= 1.1.0</li>
                            <li>✓ 支持dockershim和containerd双运行时</li>
                            <li><strong>⚠️ 安全要求：禁用不安全的端口和匿名访问</strong></li>
                            <li><strong>⚠️ 证书要求：所有组件必须使用TLS加密通信</strong></li>
                        </ul>
                    </div>

                    <pre><code># 银河麒麟v10sp3 2403 + K8s v1.24.17 兼容性检查脚本
cat > /tmp/kylin_k8s_v1.24.17_check.sh << 'EOF'
#!/bin/bash
echo "=== 银河麒麟v10sp3 2403 + Kubernetes v1.24.17 兼容性检查 ==="
echo

# 1. 检查系统版本
echo "1. 系统版本检查："
cat /etc/kylin-release 2>/dev/null || echo "警告：无法读取银河麒麟版本信息"
echo "内核版本: $(uname -r)"
KERNEL_VERSION=$(uname -r | cut -d. -f1,2)
if [ "$(echo "$KERNEL_VERSION >= 4.18" | bc 2>/dev/null)" = "1" ]; then
    echo "✓ 内核版本符合要求 (>= 4.18)"
else
    echo "✗ 内核版本过低，可能不支持容器功能"
fi
echo

# 2. 检查必要的内核模块
echo "2. 内核模块检查："
modprobe overlay 2>/dev/null && echo "✓ overlay模块加载成功" || echo "✗ overlay模块加载失败"
modprobe br_netfilter 2>/dev/null && echo "✓ br_netfilter模块加载成功" || echo "✗ br_netfilter模块加载失败"
modprobe ip_vs 2>/dev/null && echo "✓ ip_vs模块加载成功" || echo "✗ ip_vs模块加载失败"
echo

# 3. 检查cgroup支持
echo "3. cgroup支持检查："
if mount | grep -q cgroup2; then
    echo "✓ 系统支持cgroup v2"
elif mount | grep -q cgroup; then
    echo "✓ 系统支持cgroup v1（K8s v1.24.17兼容）"
else
    echo "✗ 未检测到cgroup支持"
fi
echo

# 4. 检查iptables
echo "4. iptables检查："
IPTABLES_VERSION=$(iptables --version 2>/dev/null | grep -o 'v[0-9]\+\.[0-9]\+' | sed 's/v//')
if [ -n "$IPTABLES_VERSION" ]; then
    echo "✓ iptables版本: $IPTABLES_VERSION"
    if [ "$(echo "$IPTABLES_VERSION >= 1.6" | bc 2>/dev/null)" = "1" ]; then
        echo "✓ iptables版本符合要求"
    else
        echo "⚠ iptables版本较低，建议升级"
    fi
else
    echo "✗ 无法获取iptables版本"
fi
echo

# 5. 检查systemd
echo "5. systemd检查："
if systemctl --version >/dev/null 2>&1; then
    echo "✓ systemd可用"
    SYSTEMD_VERSION=$(systemctl --version | head -1 | grep -o '[0-9]\+')
    echo "systemd版本: $SYSTEMD_VERSION"
else
    echo "✗ systemd不可用"
fi
echo

# 6. 检查容器运行时依赖
echo "6. 容器运行时依赖检查："
which runc >/dev/null 2>&1 && echo "✓ runc已安装" || echo "⚠ runc未安装"
which containerd >/dev/null 2>&1 && echo "✓ containerd已安装" || echo "⚠ containerd未安装"
which docker >/dev/null 2>&1 && echo "✓ docker已安装（K8s v1.24.17支持dockershim）" || echo "⚠ docker未安装"
echo

# 7. 检查K8s v1.24.17特殊要求
echo "7. K8s v1.24.17特殊要求检查："
if [ -f /proc/sys/net/bridge/bridge-nf-call-iptables ]; then
    echo "✓ bridge-nf-call-iptables支持可用"
else
    echo "✗ bridge-nf-call-iptables不可用"
fi

if [ -f /proc/sys/net/ipv4/ip_forward ]; then
    echo "✓ ip_forward支持可用"
else
    echo "✗ ip_forward不可用"
fi
echo

echo "=== 检查完成 ==="
echo "如果有✗标记的项目，请先解决相关问题再继续部署"
EOF

chmod +x /tmp/kylin_k8s_v1.24.17_check.sh
/tmp/kylin_k8s_v1.24.17_check.sh

# 如果检查通过，继续配置
echo "继续银河麒麟系统配置..."

# 检查银河麒麟系统版本
cat /etc/kylin-release
cat /proc/version

# 检查系统架构
uname -m

# 检查内核版本（确保支持容器功能）
uname -r

# 配置银河麒麟的包管理器
yum clean all
yum makecache fast

# 检查并启用必要的内核模块
modprobe overlay
modprobe br_netfilter

# 确保开机自动加载
cat > /etc/modules-load.d/k8s.conf << EOF
overlay
br_netfilter
EOF

# 验证模块加载
lsmod | grep overlay
lsmod | grep br_netfilter

# 银河麒麟特殊检查：确保iptables工作正常
iptables --version
which iptables-legacy && echo "iptables-legacy可用" || echo "需要安装iptables-legacy"

# 检查cgroup支持（K8s v1.24.17支持cgroup v1和v2）
mount | grep cgroup
ls -la /sys/fs/cgroup/</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 银河麒麟特殊说明：</strong>
                        银河麒麟系统基于CentOS/RHEL，但可能有一些特殊的安全策略和配置。如果遇到权限问题，请检查系统的安全策略设置。<br>
                        <strong>K8s v1.24.17特别提醒：</strong>此版本同时支持cgroup v1和v2，兼容性更好。
                    </div>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 4核8G配置特别优化建议：</strong>
                        <div style="margin-bottom: 15px; padding: 10px; background: rgba(245, 101, 101, 0.1); border-left: 4px solid #f56565;">
                            <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong><br>
                            以下优化参数已针对4核8G配置进行调整。如果您的服务器配置不同，请根据实际情况修改参数值。过大的参数值可能导致系统不稳定。
                        </div>
                        <pre><code># 针对4核8G配置的系统优化
cat > /tmp/4c8g_optimization.sh << 'EOF'
#!/bin/bash
echo "=== 4核8G配置优化脚本 ==="

# 1. 调整系统参数（已针对4核8G优化）
cat >> /etc/sysctl.d/k8s-4c8g.conf << EOL
# 4核8G优化参数
vm.max_map_count = 262144
vm.swappiness = 1
net.core.somaxconn = 32768
net.ipv4.tcp_max_syn_backlog = 8192
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_keepalive_probes = 3
net.ipv4.tcp_keepalive_intvl = 15
# 注意：nf_conntrack_max 已调整为适合4G内存的值
net.netfilter.nf_conntrack_max = 1048576
EOL

# 2. 应用参数
sysctl -p /etc/sysctl.d/k8s-4c8g.conf

# 3. 调整文件描述符限制
cat >> /etc/security/limits.conf << EOL
# 4核8G配置优化
* soft nofile 65536
* hard nofile 65536
* soft nproc 32768
* hard nproc 32768
EOL

echo "4核8G优化配置完成"
EOF

chmod +x /tmp/4c8g_optimization.sh
/tmp/4c8g_optimization.sh</code></pre>
                    </div>

                    <h3><i class="fas fa-network-wired"></i> 2.2 主机名和网络配置</h3>
                    <p>配置集群节点的主机名和网络设置，确保节点间能够正常通信。</p>

                    <h4><i class="fas fa-tag"></i> 设置主机名</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点 (*************)</div>
                    <pre><code># 在Master节点设置主机名
hostnamectl set-hostname k8s-master

# 验证设置
hostnamectl status
hostname</code></pre>

                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点 (*************)</div>
                    <pre><code># 在Worker节点设置主机名
hostnamectl set-hostname k8s-node1

# 验证设置
hostnamectl status
hostname</code></pre>

                    <h4><i class="fas fa-list"></i> 配置hosts文件</h4>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <pre><code># 配置hosts文件，确保节点间可以通过主机名访问
cat >> /etc/hosts << EOF
************* k8s-master
************* k8s-node1
EOF

# 验证hosts配置
cat /etc/hosts | grep k8s

# 测试节点间连通性
ping -c 3 k8s-master
ping -c 3 k8s-node1</code></pre>

                    <h4><i class="fas fa-shield-alt"></i> 关闭防火墙和SELinux</h4>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <pre><code># 关闭防火墙
systemctl stop firewalld
systemctl disable firewalld

# 关闭SELinux
setenforce 0
sed -i 's/^SELINUX=enforcing$/SELINUX=disabled/' /etc/selinux/config

# 验证SELinux状态
getenforce

# 关闭swap分区（Kubernetes要求）
swapoff -a
sed -i '/ swap / s/^\(.*\)$/#\1/g' /etc/fstab

# 验证swap已关闭
free -h</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 安全提醒：</strong>
                        在生产环境中，建议配置防火墙规则而不是完全关闭防火墙。本教程为了简化部署过程选择关闭防火墙，实际生产环境请根据安全要求配置相应的防火墙规则。
                    </div>

                    <h4><i class="fas fa-cogs"></i> 配置内核参数</h4>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <pre><code># 配置K8s v1.24.17所需的内核参数
cat > /etc/sysctl.d/k8s.conf << EOF
# Kubernetes v1.24.17 内核参数配置
net.bridge.bridge-nf-call-ip6tables = 1
net.bridge.bridge-nf-call-iptables = 1
net.ipv4.ip_forward = 1

# 针对4核8G配置的优化参数
vm.swappiness = 1
vm.overcommit_memory = 1
vm.panic_on_oom = 0
fs.inotify.max_user_instances = 8192
fs.inotify.max_user_watches = 1048576
fs.file-max = 1048576
fs.nr_open = 1048576
net.netfilter.nf_conntrack_max = 1048576

# 网络优化参数
net.ipv4.tcp_keepalive_time = 600
net.ipv4.tcp_keepalive_probes = 3
net.ipv4.tcp_keepalive_intvl = 15
net.ipv4.tcp_max_tw_buckets = 36000
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_max_orphans = 327680
net.ipv4.tcp_orphan_retries = 3
net.ipv4.tcp_syncookies = 1
net.ipv4.tcp_max_syn_backlog = 16384
net.core.netdev_max_backlog = 16384
net.core.rmem_default = 262144
net.core.wmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
EOF

# 应用内核参数
sysctl -p /etc/sysctl.d/k8s.conf

# 验证关键参数
sysctl net.bridge.bridge-nf-call-iptables
sysctl net.ipv4.ip_forward</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 内核参数说明：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- <strong>bridge-nf-call-iptables：</strong> 允许iptables处理桥接流量</li>
                            <li>- <strong>ip_forward：</strong> 启用IP转发，Pod间通信必需</li>
                            <li>- <strong>vm.swappiness：</strong> 降低swap使用，提高性能</li>
                            <li>- <strong>其他参数：</strong> 针对4核8G配置进行的网络和文件系统优化</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-download"></i> 2.3 下载Kubernetes v1.24.17二进制文件</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <p>下载Kubernetes v1.24.17版本的二进制文件和相关组件。</p>

                    <h4><i class="fas fa-folder"></i> 创建工作目录</h4>
                    <pre><code># 创建工作目录
mkdir -p /opt/kubernetes/{bin,cfg,ssl,logs}
mkdir -p /etc/kubernetes
mkdir -p /var/log/kubernetes

# 设置环境变量
export K8S_VERSION="v1.24.17"
export ETCD_VERSION="v3.5.6"
export CONTAINERD_VERSION="1.6.15"
export RUNC_VERSION="v1.1.4"
export CNI_VERSION="v1.1.1"

echo "工作目录创建完成"</code></pre>

                    <h4><i class="fas fa-download"></i> 下载Kubernetes二进制文件</h4>
                    <pre><code># 下载Kubernetes v1.24.17 server包
cd /opt/kubernetes

# 方法1：从官方源下载（推荐）
wget https://dl.k8s.io/v1.24.17/kubernetes-server-linux-amd64.tar.gz

# 如果官方源下载失败，使用备用源
if [ ! -f kubernetes-server-linux-amd64.tar.gz ]; then
    echo "官方源下载失败，尝试备用源..."
    wget https://github.com/kubernetes/kubernetes/releases/download/v1.24.17/kubernetes-server-linux-amd64.tar.gz
fi

# 验证下载文件
if [ ! -f kubernetes-server-linux-amd64.tar.gz ]; then
    echo "错误：无法下载Kubernetes二进制文件，请检查网络连接"
    exit 1
fi

# 解压并安装
tar -xzf kubernetes-server-linux-amd64.tar.gz
cp kubernetes/server/bin/{kube-apiserver,kube-controller-manager,kube-scheduler,kubectl,kubelet,kube-proxy} /opt/kubernetes/bin/

# 设置执行权限
chmod +x /opt/kubernetes/bin/*

# 创建软链接到系统PATH
ln -sf /opt/kubernetes/bin/* /usr/local/bin/

# 验证安装
kubectl version --client
kubelet --version

# 检查所有组件是否正确安装
echo "检查Kubernetes组件安装..."
for component in kube-apiserver kube-controller-manager kube-scheduler kubectl kubelet kube-proxy; do
    if command -v $component >/dev/null 2>&1; then
        echo "✓ $component 安装成功"
    else
        echo "✗ $component 安装失败"
        exit 1
    fi
done</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期效果：</strong>
                        执行 <code>kubectl version --client</code> 应该显示：<br>
                        <code>Client Version: version.Info{Major:"1", Minor:"24", GitVersion:"v1.24.17"...}</code>
                    </div>

                    <h3><i class="fas fa-box"></i> 2.4 安装容器运行时</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <p>Kubernetes v1.24.17支持多种容器运行时，本教程使用containerd作为主要容器运行时，同时保留对dockershim的支持。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> K8s v1.24.17容器运行时说明：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- <strong>containerd：</strong> 推荐的容器运行时，性能好，资源占用低</li>
                            <li>- <strong>dockershim：</strong> K8s v1.24.17是最后支持dockershim的版本</li>
                            <li>- <strong>兼容性：</strong> 本教程配置支持两种运行时，可根据需要选择</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-download"></i> 安装containerd</h4>
                    <pre><code># 下载containerd v1.6.15
cd /opt/kubernetes

# 下载containerd（添加重试机制）
echo "下载containerd v1.6.15..."
for i in {1..3}; do
    wget https://github.com/containerd/containerd/releases/download/v1.6.15/containerd-1.6.15-linux-amd64.tar.gz && break
    echo "下载失败，重试 $i/3..."
    sleep 5
done

# 验证下载
if [ ! -f containerd-1.6.15-linux-amd64.tar.gz ]; then
    echo "错误：containerd下载失败"
    exit 1
fi

# 解压并安装
tar -xzf containerd-1.6.15-linux-amd64.tar.gz -C /usr/local/

# 下载并安装runc
echo "下载runc v1.1.4..."
for i in {1..3}; do
    wget https://github.com/opencontainers/runc/releases/download/v1.1.4/runc.amd64 && break
    echo "下载失败，重试 $i/3..."
    sleep 5
done

if [ ! -f runc.amd64 ]; then
    echo "错误：runc下载失败"
    exit 1
fi

chmod +x runc.amd64
mv runc.amd64 /usr/local/sbin/runc

# 下载CNI插件
echo "下载CNI插件 v1.1.1..."
mkdir -p /opt/cni/bin
for i in {1..3}; do
    wget https://github.com/containernetworking/plugins/releases/download/v1.1.1/cni-plugins-linux-amd64-v1.1.1.tgz && break
    echo "下载失败，重试 $i/3..."
    sleep 5
done

if [ ! -f cni-plugins-linux-amd64-v1.1.1.tgz ]; then
    echo "错误：CNI插件下载失败"
    exit 1
fi

tar -xzf cni-plugins-linux-amd64-v1.1.1.tgz -C /opt/cni/bin/

# 验证安装
echo "验证containerd组件安装..."
containerd --version || { echo "containerd安装失败"; exit 1; }
runc --version || { echo "runc安装失败"; exit 1; }
ls /opt/cni/bin/ | head -5 || { echo "CNI插件安装失败"; exit 1; }

echo "✓ containerd组件安装成功"</code></pre>

                    <h4><i class="fas fa-cog"></i> 配置containerd</h4>
                    <pre><code># 创建containerd配置目录
mkdir -p /etc/containerd

# 生成默认配置
containerd config default > /etc/containerd/config.toml

# 备份原始配置
cp /etc/containerd/config.toml /etc/containerd/config.toml.bak

# 修改配置以支持systemd cgroup驱动（K8s v1.24.17推荐）
# 注意：需要确保配置文件中正确设置了systemd cgroup驱动
if grep -q "SystemdCgroup" /etc/containerd/config.toml; then
    sed -i 's/SystemdCgroup = false/SystemdCgroup = true/' /etc/containerd/config.toml
else
    echo "警告：未找到SystemdCgroup配置项，请手动检查配置文件"
    # 尝试在正确位置添加配置
    sed -i '/\[plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc.options\]/a \ \ \ \ \ \ \ \ \ \ SystemdCgroup = true' /etc/containerd/config.toml
fi

# 验证配置是否正确
echo "验证SystemdCgroup配置..."
if grep -q "SystemdCgroup = true" /etc/containerd/config.toml; then
    echo "✓ SystemdCgroup配置正确"
else
    echo "✗ SystemdCgroup配置错误，请手动修改配置文件"
    echo "参考位置：[plugins.\"io.containerd.grpc.v1.cri\".containerd.runtimes.runc.options]"
fi

# 显示配置上下文
grep -A 5 -B 5 "SystemdCgroup" /etc/containerd/config.toml || echo "未找到SystemdCgroup配置项"

# 配置镜像加速器（使用阿里云镜像）
cat > /etc/containerd/config.toml << EOF
version = 2
root = "/var/lib/containerd"
state = "/run/containerd"
plugin_dir = ""
disabled_plugins = []
required_plugins = []
oom_score = 0

[grpc]
  address = "/run/containerd/containerd.sock"
  tcp_address = ""
  tcp_tls_cert = ""
  tcp_tls_key = ""
  uid = 0
  gid = 0
  max_recv_message_size = 16777216
  max_send_message_size = 16777216

[ttrpc]
  address = ""
  uid = 0
  gid = 0

[debug]
  address = ""
  uid = 0
  gid = 0
  level = ""

[metrics]
  address = ""
  grpc_histogram = false

[cgroup]
  path = ""

[timeouts]
  "io.containerd.timeout.shim.cleanup" = "5s"
  "io.containerd.timeout.shim.load" = "5s"
  "io.containerd.timeout.shim.shutdown" = "3s"
  "io.containerd.timeout.task.state" = "2s"

[plugins]
  [plugins."io.containerd.gc.v1.scheduler"]
    pause_threshold = 0.02
    deletion_threshold = 0
    mutation_threshold = 100
    schedule_delay = "0s"
    startup_delay = "100ms"
  [plugins."io.containerd.grpc.v1.cri"]
    disable_tcp_service = true
    stream_server_address = "127.0.0.1"
    stream_server_port = "0"
    stream_idle_timeout = "4h0m0s"
    enable_selinux = false
    selinux_category_range = 1024
    sandbox_image = "registry.aliyuncs.com/google_containers/pause:3.7"
    stats_collect_period = 10
    systemd_cgroup = true
    enable_tls_streaming = false
    max_container_log_line_size = 16384
    disable_cgroup = false
    disable_apparmor = false
    restrict_oom_score_adj = false
    max_concurrent_downloads = 3
    disable_proc_mount = false
    unset_seccomp_profile = ""
    tolerate_missing_hugetlb_controller = true
    disable_hugetlb_controller = true
    ignore_image_defined_volumes = false
    [plugins."io.containerd.grpc.v1.cri".containerd]
      snapshotter = "overlayfs"
      default_runtime_name = "runc"
      no_pivot = false
      disable_snapshot_annotations = true
      discard_unpacked_layers = false
      [plugins."io.containerd.grpc.v1.cri".containerd.default_runtime]
        runtime_type = ""
        runtime_engine = ""
        runtime_path = ""
        runtime_root = ""
        privileged_without_host_devices = false
        base_runtime_spec = ""
      [plugins."io.containerd.grpc.v1.cri".containerd.untrusted_workload_runtime]
        runtime_type = ""
        runtime_engine = ""
        runtime_path = ""
        runtime_root = ""
        privileged_without_host_devices = false
        base_runtime_spec = ""
      [plugins."io.containerd.grpc.v1.cri".containerd.runtimes]
        [plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc]
          runtime_type = "io.containerd.runc.v2"
          runtime_engine = ""
          runtime_path = ""
          runtime_root = ""
          privileged_without_host_devices = false
          base_runtime_spec = ""
          [plugins."io.containerd.grpc.v1.cri".containerd.runtimes.runc.options]
            SystemdCgroup = true
    [plugins."io.containerd.grpc.v1.cri".cni]
      bin_dir = "/opt/cni/bin"
      conf_dir = "/etc/cni/net.d"
      max_conf_num = 1
      conf_template = ""
    [plugins."io.containerd.grpc.v1.cri".registry]
      [plugins."io.containerd.grpc.v1.cri".registry.mirrors]
        [plugins."io.containerd.grpc.v1.cri".registry.mirrors."docker.io"]
          endpoint = ["https://docker.mirrors.ustc.edu.cn", "https://hub-mirror.c.163.com"]
        [plugins."io.containerd.grpc.v1.cri".registry.mirrors."k8s.gcr.io"]
          endpoint = ["https://registry.aliyuncs.com/google_containers"]
        [plugins."io.containerd.grpc.v1.cri".registry.mirrors."gcr.io"]
          endpoint = ["https://registry.aliyuncs.com/google_containers"]
    [plugins."io.containerd.grpc.v1.cri".image_decryption]
      key_model = ""
    [plugins."io.containerd.grpc.v1.cri".x509_key_pair_streaming]
      tls_cert_file = ""
      tls_private_key_file = ""
  [plugins."io.containerd.internal.v1.opt"]
    path = "/opt/containerd"
  [plugins."io.containerd.internal.v1.restart"]
    interval = "10s"
  [plugins."io.containerd.metadata.v1.bolt"]
    content_sharing_policy = "shared"
  [plugins."io.containerd.monitor.v1.cgroups"]
    no_prometheus = false
  [plugins."io.containerd.runtime.v1.linux"]
    shim = "containerd-shim"
    runtime = "runc"
    runtime_root = ""
    no_shim = false
    shim_debug = false
  [plugins."io.containerd.runtime.v2.task"]
    platforms = ["linux/amd64"]
  [plugins."io.containerd.service.v1.diff-service"]
    default = ["walking"]
  [plugins."io.containerd.service.v1.tasks-service"]
    rdt_config_file = ""
EOF

# 创建containerd systemd服务文件
cat > /etc/systemd/system/containerd.service << EOF
[Unit]
Description=containerd container runtime
Documentation=https://containerd.io
After=network.target local-fs.target

[Service]
ExecStartPre=-/sbin/modprobe overlay
ExecStart=/usr/local/bin/containerd
Type=notify
Delegate=yes
KillMode=process
Restart=always
RestartSec=5
LimitNPROC=infinity
LimitCORE=infinity
LimitNOFILE=infinity
TasksMax=infinity
OOMScoreAdjust=-999

[Install]
WantedBy=multi-user.target
EOF

# 启动并设置开机启动
systemctl daemon-reload
systemctl enable containerd
systemctl start containerd

# 验证containerd状态
systemctl status containerd
ctr version

# 验证containerd配置
echo "检查containerd配置..."
containerd config dump | grep -A 5 -B 5 "SystemdCgroup"

# 测试containerd功能
echo "测试containerd基本功能..."
ctr images pull registry.aliyuncs.com/google_containers/pause:3.7
ctr images list | grep pause

# 验证CNI插件
echo "验证CNI插件..."
ls -la /opt/cni/bin/ | head -10</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期效果：</strong>
                        执行 <code>systemctl status containerd</code> 应该显示 <code>Active: active (running)</code><br>
                        执行 <code>ctr version</code> 应该显示containerd的版本信息
                    </div>
                </section>

                <!-- 证书准备部分 -->
                <section id="certificate-preparation">
                    <h2><span class="step-number">3</span>证书准备</h2>
                    <p>Kubernetes v1.24.17集群使用TLS证书来保证各组件间通信的安全性。我们需要创建CA证书和各组件的证书。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 证书说明：</strong>
                        Kubernetes v1.24.17使用PKI（公钥基础设施）来保证集群安全。本教程将创建一个自签名的CA，然后为各个组件签发证书。
                    </div>

                    <h3><i class="fas fa-download"></i> 3.1 安装cfssl工具</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>cfssl是CloudFlare开源的PKI工具，用于生成证书。</p>

                    <pre><code># 下载cfssl工具
cd /opt/kubernetes
wget https://github.com/cloudflare/cfssl/releases/download/v1.6.4/cfssl_1.6.4_linux_amd64
wget https://github.com/cloudflare/cfssl/releases/download/v1.6.4/cfssljson_1.6.4_linux_amd64
wget https://github.com/cloudflare/cfssl/releases/download/v1.6.4/cfssl-certinfo_1.6.4_linux_amd64

# 设置执行权限并移动到PATH
chmod +x cfssl*
mv cfssl_1.6.4_linux_amd64 /usr/local/bin/cfssl
mv cfssljson_1.6.4_linux_amd64 /usr/local/bin/cfssljson
mv cfssl-certinfo_1.6.4_linux_amd64 /usr/local/bin/cfssl-certinfo

# 验证安装
cfssl version
cfssljson --version</code></pre>

                    <h3><i class="fas fa-key"></i> 3.2 创建CA证书</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>创建集群的根CA证书，用于签发其他组件的证书。</p>

                    <pre><code># 创建证书存放目录
mkdir -p /opt/kubernetes/ssl
cd /opt/kubernetes/ssl

# 创建CA配置文件
cat > ca-config.json << EOF
{
  "signing": {
    "default": {
      "expiry": "87600h"
    },
    "profiles": {
      "kubernetes": {
        "expiry": "87600h",
        "usages": [
          "signing",
          "key encipherment",
          "server auth",
          "client auth"
        ]
      }
    }
  }
}
EOF

# 创建CA证书签名请求
cat > ca-csr.json << EOF
{
  "CN": "Kubernetes",
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "L": "Beijing",
      "ST": "Beijing",
      "O": "k8s",
      "OU": "System"
    }
  ]
}
EOF

# 生成CA证书
cfssl gencert -initca ca-csr.json | cfssljson -bare ca

# 验证CA证书
ls -la ca*
cfssl-certinfo -cert ca.pem</code></pre>

                    <h3><i class="fas fa-server"></i> 3.3 创建kube-apiserver证书</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>为kube-apiserver组件创建服务端证书。</p>

                    <pre><code># 创建kube-apiserver证书签名请求
cat > server-csr.json << EOF
{
  "CN": "kubernetes",
  "hosts": [
    "********",
    "127.0.0.1",
    "*************",
    "k8s-master",
    "kubernetes",
    "kubernetes.default",
    "kubernetes.default.svc",
    "kubernetes.default.svc.cluster",
    "kubernetes.default.svc.cluster.local"
  ],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "L": "Beijing",
      "ST": "Beijing",
      "O": "k8s",
      "OU": "System"
    }
  ]
}
EOF

# 生成kube-apiserver证书
cfssl gencert -ca=ca.pem -ca-key=ca-key.pem -config=ca-config.json -profile=kubernetes server-csr.json | cfssljson -bare server

# 验证证书
ls -la server*</code></pre>

                    <h3><i class="fas fa-users"></i> 3.4 创建kube-controller-manager证书</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建kube-controller-manager证书签名请求
cat > kube-controller-manager-csr.json << EOF
{
  "CN": "system:kube-controller-manager",
  "hosts": [],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "L": "Beijing",
      "ST": "Beijing",
      "O": "system:kube-controller-manager",
      "OU": "System"
    }
  ]
}
EOF

# 生成证书
cfssl gencert -ca=ca.pem -ca-key=ca-key.pem -config=ca-config.json -profile=kubernetes kube-controller-manager-csr.json | cfssljson -bare kube-controller-manager</code></pre>

                    <h3><i class="fas fa-calendar-alt"></i> 3.5 创建kube-scheduler证书</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建kube-scheduler证书签名请求
cat > kube-scheduler-csr.json << EOF
{
  "CN": "system:kube-scheduler",
  "hosts": [],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "L": "Beijing",
      "ST": "Beijing",
      "O": "system:kube-scheduler",
      "OU": "System"
    }
  ]
}
EOF

# 生成证书
cfssl gencert -ca=ca.pem -ca-key=ca-key.pem -config=ca-config.json -profile=kubernetes kube-scheduler-csr.json | cfssljson -bare kube-scheduler</code></pre>

                    <h3><i class="fas fa-user-cog"></i> 3.6 创建admin证书</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建admin证书签名请求
cat > admin-csr.json << EOF
{
  "CN": "admin",
  "hosts": [],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "L": "Beijing",
      "ST": "Beijing",
      "O": "system:masters",
      "OU": "System"
    }
  ]
}
EOF

# 生成证书
cfssl gencert -ca=ca.pem -ca-key=ca-key.pem -config=ca-config.json -profile=kubernetes admin-csr.json | cfssljson -bare admin</code></pre>

                    <h3><i class="fas fa-network-wired"></i> 3.7 创建kube-proxy证书</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建kube-proxy证书签名请求
cat > kube-proxy-csr.json << EOF
{
  "CN": "system:kube-proxy",
  "hosts": [],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "L": "Beijing",
      "ST": "Beijing",
      "O": "k8s",
      "OU": "System"
    }
  ]
}
EOF

# 生成证书
cfssl gencert -ca=ca.pem -ca-key=ca-key.pem -config=ca-config.json -profile=kubernetes kube-proxy-csr.json | cfssljson -bare kube-proxy</code></pre>

                    <h3><i class="fas fa-copy"></i> 3.8 分发证书到各节点</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>将生成的证书分发到各个节点。</p>

                    <pre><code># 复制证书到Master节点的配置目录
cp ca*.pem server*.pem kube-controller-manager*.pem kube-scheduler*.pem admin*.pem /etc/kubernetes/

# 分发证书到Worker节点
scp ca.pem ca-key.pem kube-proxy*.pem root@*************:/etc/kubernetes/

# 验证证书分发
ls -la /etc/kubernetes/*.pem
ssh root@************* "ls -la /etc/kubernetes/*.pem"

# 验证证书有效性
echo "验证CA证书..."
openssl x509 -in /etc/kubernetes/ca.pem -text -noout | grep -E "Subject:|Not After:"

echo "验证API Server证书..."
openssl x509 -in /etc/kubernetes/server.pem -text -noout | grep -E "Subject:|DNS:|IP:|Not After:"

echo "验证证书权限..."
ls -la /etc/kubernetes/*.pem | awk '{print $1, $9}'</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 证书创建完成：</strong>
                        所有必要的TLS证书已经创建完成并分发到相应节点。这些证书将用于保证Kubernetes v1.24.17集群各组件间通信的安全性。
                    </div>
                </section>

                <!-- ETCD集群部署部分 -->
                <section id="etcd-deployment">
                    <h2><span class="step-number">4</span>ETCD集群部署</h2>
                    <p>ETCD是Kubernetes的数据存储后端，存储了集群的所有状态信息。本教程部署单节点ETCD，适合测试和小规模环境。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> ETCD v3.5.6说明：</strong>
                        ETCD v3.5.6与Kubernetes v1.24.17完全兼容，提供了稳定的数据存储服务。在生产环境中建议部署3节点或5节点的ETCD集群以保证高可用性。
                    </div>

                    <h3><i class="fas fa-download"></i> 4.1 下载和安装ETCD</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 下载ETCD v3.5.6
cd /opt/kubernetes

echo "下载ETCD v3.5.6..."
for i in {1..3}; do
    wget https://github.com/etcd-io/etcd/releases/download/v3.5.6/etcd-v3.5.6-linux-amd64.tar.gz && break
    echo "下载失败，重试 $i/3..."
    sleep 5
done

# 验证下载
if [ ! -f etcd-v3.5.6-linux-amd64.tar.gz ]; then
    echo "错误：ETCD下载失败，请检查网络连接"
    exit 1
fi

# 解压并安装
tar -xzf etcd-v3.5.6-linux-amd64.tar.gz
cp etcd-v3.5.6-linux-amd64/etcd* /opt/kubernetes/bin/

# 创建软链接
ln -sf /opt/kubernetes/bin/etcd* /usr/local/bin/

# 验证安装
echo "验证ETCD安装..."
etcd --version || { echo "ETCD安装失败"; exit 1; }
etcdctl version || { echo "etcdctl安装失败"; exit 1; }

echo "✓ ETCD安装成功"</code></pre>

                    <h3><i class="fas fa-key"></i> 4.2 创建ETCD证书</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 进入证书目录
cd /opt/kubernetes/ssl

# 创建ETCD证书签名请求
cat > etcd-csr.json << EOF
{
  "CN": "etcd",
  "hosts": [
    "127.0.0.1",
    "*************",
    "k8s-master"
  ],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "L": "Beijing",
      "ST": "Beijing",
      "O": "k8s",
      "OU": "System"
    }
  ]
}
EOF

# 生成ETCD证书
cfssl gencert -ca=ca.pem -ca-key=ca-key.pem -config=ca-config.json -profile=kubernetes etcd-csr.json | cfssljson -bare etcd

# 复制证书到配置目录
cp etcd*.pem /etc/kubernetes/

# 验证证书
ls -la /etc/kubernetes/etcd*</code></pre>

                    <h3><i class="fas fa-cog"></i> 4.3 配置ETCD</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建ETCD数据目录
mkdir -p /var/lib/etcd/default.etcd
mkdir -p /etc/etcd

# 创建ETCD配置文件
cat > /etc/etcd/etcd.conf << EOF
#[Member]
ETCD_NAME="etcd-01"
ETCD_DATA_DIR="/var/lib/etcd/default.etcd"
ETCD_LISTEN_PEER_URLS="https://*************:2380"
ETCD_LISTEN_CLIENT_URLS="https://*************:2379,http://127.0.0.1:2379"

#[Clustering]
ETCD_INITIAL_ADVERTISE_PEER_URLS="https://*************:2380"
ETCD_ADVERTISE_CLIENT_URLS="https://*************:2379"
ETCD_INITIAL_CLUSTER="etcd-01=https://*************:2380"
ETCD_INITIAL_CLUSTER_TOKEN="etcd-cluster"
ETCD_INITIAL_CLUSTER_STATE="new"
EOF

# 创建ETCD systemd服务文件
cat > /etc/systemd/system/etcd.service << EOF
[Unit]
Description=Etcd Server
After=network.target
After=network-online.target
Wants=network-online.target

[Service]
Type=notify
EnvironmentFile=/etc/etcd/etcd.conf
ExecStart=/opt/kubernetes/bin/etcd \\
  --cert-file=/etc/kubernetes/etcd.pem \\
  --key-file=/etc/kubernetes/etcd-key.pem \\
  --peer-cert-file=/etc/kubernetes/etcd.pem \\
  --peer-key-file=/etc/kubernetes/etcd-key.pem \\
  --trusted-ca-file=/etc/kubernetes/ca.pem \\
  --peer-trusted-ca-file=/etc/kubernetes/ca.pem \\
  --logger=zap
Restart=on-failure
RestartSec=5
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF</code></pre>

                    <h3><i class="fas fa-play"></i> 4.4 启动ETCD服务</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 设置数据目录权限
chown -R etcd:etcd /var/lib/etcd/ 2>/dev/null || chown -R root:root /var/lib/etcd/
chmod 700 /var/lib/etcd/

# 验证证书文件存在
echo "检查ETCD证书文件..."
for cert in ca.pem etcd.pem etcd-key.pem; do
    if [ ! -f "/etc/kubernetes/$cert" ]; then
        echo "错误：证书文件 $cert 不存在"
        exit 1
    fi
done
echo "✓ 证书文件检查通过"

# 重新加载systemd配置
systemctl daemon-reload

# 启动并设置开机启动
systemctl enable etcd

# 启动ETCD服务
echo "启动ETCD服务..."
systemctl start etcd

# 等待服务启动
sleep 10

# 检查服务状态
echo "检查ETCD服务状态..."
if systemctl is-active --quiet etcd; then
    echo "✓ ETCD服务启动成功"
    systemctl status etcd --no-pager
else
    echo "✗ ETCD服务启动失败"
    echo "查看错误日志："
    journalctl -u etcd --no-pager -l
    exit 1
fi

# 验证ETCD集群状态
echo "验证ETCD集群健康状态..."
ETCDCTL_API=3 etcdctl --cacert=/etc/kubernetes/ca.pem --cert=/etc/kubernetes/etcd.pem --key=/etc/kubernetes/etcd-key.pem --endpoints="https://*************:2379" endpoint health

# 查看ETCD成员列表
echo "查看ETCD成员列表..."
ETCDCTL_API=3 etcdctl --cacert=/etc/kubernetes/ca.pem --cert=/etc/kubernetes/etcd.pem --key=/etc/kubernetes/etcd-key.pem --endpoints="https://*************:2379" member list

echo "✓ ETCD部署完成"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期效果：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- <code>systemctl status etcd</code> 显示 <code>Active: active (running)</code></li>
                            <li>- <code>endpoint health</code> 显示 <code>is healthy</code></li>
                            <li>- <code>member list</code> 显示ETCD成员信息</li>
                        </ul>
                    </div>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 故障排查：</strong>
                        如果ETCD启动失败，请检查：
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>1. 证书文件是否存在且权限正确</li>
                            <li>2. 端口2379和2380是否被占用</li>
                            <li>3. 数据目录权限是否正确</li>
                            <li>4. 查看日志：<code>journalctl -u etcd -f</code></li>
                        </ul>
                    </div>
                </section>

                <!-- Master组件部署部分 -->
                <section id="master-deployment">
                    <h2><span class="step-number">5</span>Master组件部署</h2>
                    <p>部署Kubernetes v1.24.17 Master节点的核心组件：kube-apiserver、kube-controller-manager和kube-scheduler。</p>

                    <h3><i class="fas fa-server"></i> 5.1 部署kube-apiserver</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>kube-apiserver是Kubernetes集群的前端，提供HTTP API服务。</p>

                    <h4><i class="fas fa-cog"></i> 创建kube-apiserver配置</h4>
                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> K8s v1.24.17兼容性说明：</strong>
                        在Kubernetes v1.24.17中，以下参数已被移除：<br>
                        - <code>--insecure-port</code> (已在v1.20中移除)<br>
                        - <code>--kubelet-https</code> (已在v1.24中移除)<br>
                        本配置已针对v1.24.17进行优化，移除了不兼容的参数。
                    </div>
                    <pre><code># 创建日志目录（必须先创建）
mkdir -p /opt/kubernetes/logs
chmod 755 /opt/kubernetes/logs

# 创建kube-apiserver配置文件（兼容K8s v1.24.17）
cat > /etc/kubernetes/kube-apiserver.conf << EOF
KUBE_APISERVER_OPTS="--logtostderr=false \\
--v=2 \\
--log-dir=/opt/kubernetes/logs \\
--etcd-servers=https://*************:2379 \\
--bind-address=************* \\
--secure-port=6443 \\
--advertise-address=************* \\
--allow-privileged=true \\
--service-cluster-ip-range=10.0.0.0/24 \\
--enable-admission-plugins=NamespaceLifecycle,LimitRanger,ServiceAccount,ResourceQuota,NodeRestriction,PodSecurity \\
--authorization-mode=RBAC,Node \\
--enable-bootstrap-token-auth=true \\
--token-auth-file=/etc/kubernetes/token.csv \\
--service-node-port-range=30000-32767 \\
--kubelet-client-certificate=/etc/kubernetes/server.pem \\
--kubelet-client-key=/etc/kubernetes/server-key.pem \\
--tls-cert-file=/etc/kubernetes/server.pem \\
--tls-private-key-file=/etc/kubernetes/server-key.pem \\
--client-ca-file=/etc/kubernetes/ca.pem \\
--service-account-key-file=/etc/kubernetes/ca-key.pem \\
--service-account-issuer=https://kubernetes.default.svc.cluster.local \\
--service-account-signing-key-file=/etc/kubernetes/server-key.pem \\
--etcd-cafile=/etc/kubernetes/ca.pem \\
--etcd-certfile=/etc/kubernetes/server.pem \\
--etcd-keyfile=/etc/kubernetes/server-key.pem \\
--requestheader-client-ca-file=/etc/kubernetes/ca.pem \\
--proxy-client-cert-file=/etc/kubernetes/server.pem \\
--proxy-client-key-file=/etc/kubernetes/server-key.pem \\
--requestheader-allowed-names=kubernetes \\
--requestheader-extra-headers-prefix=X-Remote-Extra- \\
--requestheader-group-headers=X-Remote-Group \\
--requestheader-username-headers=X-Remote-User \\
--enable-aggregator-routing=true \\
--audit-log-maxage=30 \\
--audit-log-maxbackup=3 \\
--audit-log-maxsize=100 \\
--audit-log-path=/opt/kubernetes/logs/k8s-audit.log \\
--anonymous-auth=false \\
--profiling=false \\
--disable-admission-plugins=AlwaysAdmit \\
--kubelet-certificate-authority=/etc/kubernetes/ca.pem \\
--kubelet-timeout=5s"
EOF</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 4核8G配置优化建议：</strong>
                        对于4核8G的配置，建议在kube-apiserver配置中添加以下资源限制参数：<br>
                        <code>--max-requests-inflight=400</code> （默认400，适合小集群）<br>
                        <code>--max-mutating-requests-inflight=200</code> （默认200，适合小集群）<br>
                        <strong>注意：</strong>K8s v1.24.17中PodSecurity准入控制器默认启用，在小规模集群中运行良好。
                    </div>

                    <h4><i class="fas fa-key"></i> 创建token文件</h4>
                    <div class="danger-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 安全警告：</strong>
                        以下token仅用于演示，生产环境中必须生成随机的强token！
                    </div>
                    <pre><code># 生成随机bootstrap token（生产环境必须使用）
BOOTSTRAP_TOKEN=$(openssl rand -hex 16)
echo "生成的Bootstrap Token: $BOOTSTRAP_TOKEN"

# 创建bootstrap token文件
cat > /etc/kubernetes/token.csv << EOF
${BOOTSTRAP_TOKEN:-c47ffb939f5ca36231d9e3121a252940},kubelet-bootstrap,10001,"system:node-bootstrapper"
EOF

# 设置文件权限（重要安全措施）
chmod 600 /etc/kubernetes/token.csv
chown root:root /etc/kubernetes/token.csv</code></pre>

                    <h4><i class="fas fa-cogs"></i> 创建systemd服务</h4>
                    <pre><code># 创建kube-apiserver systemd服务文件
cat > /etc/systemd/system/kube-apiserver.service << EOF
[Unit]
Description=Kubernetes API Server
Documentation=https://github.com/kubernetes/kubernetes

[Service]
EnvironmentFile=/etc/kubernetes/kube-apiserver.conf
ExecStart=/opt/kubernetes/bin/kube-apiserver \$KUBE_APISERVER_OPTS
Restart=on-failure

[Install]
WantedBy=multi-user.target
EOF

# 验证必要文件存在
echo "检查kube-apiserver必要文件..."
required_files=(
    "/etc/kubernetes/ca.pem"
    "/etc/kubernetes/server.pem"
    "/etc/kubernetes/server-key.pem"
    "/etc/kubernetes/token.csv"
)

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "错误：必要文件 $file 不存在"
        exit 1
    fi
done
echo "✓ 必要文件检查通过"

# 启动kube-apiserver
systemctl daemon-reload
systemctl enable kube-apiserver

echo "启动kube-apiserver服务..."
systemctl start kube-apiserver

# 等待服务启动
sleep 10

# 检查服务状态
echo "检查kube-apiserver服务状态..."
if systemctl is-active --quiet kube-apiserver; then
    echo "✓ kube-apiserver服务启动成功"
    systemctl status kube-apiserver --no-pager
else
    echo "✗ kube-apiserver服务启动失败，开始故障排查..."
    echo "1. 检查服务状态："
    systemctl status kube-apiserver --no-pager
    echo ""
    echo "2. 查看最近的错误日志："
    journalctl -u kube-apiserver --no-pager -n 20
    echo ""
    echo "3. 常见问题排查："
    echo "   - 检查配置文件语法：cat /etc/kubernetes/kube-apiserver.conf"
    echo "   - 检查证书文件权限：ls -la /etc/kubernetes/*.pem"
    echo "   - 检查ETCD连接：systemctl status etcd"
    echo "   - 检查端口占用：netstat -tlnp | grep 6443"
    echo ""
    echo "4. 如果遇到参数错误，请检查是否使用了K8s v1.24.17不兼容的参数"
    exit 1
fi

# 验证API服务器响应
echo "验证API服务器响应..."
for i in {1..30}; do
    if curl -k https://*************:6443/healthz >/dev/null 2>&1; then
        echo "✓ API服务器响应正常"
        break
    fi
    echo "等待API服务器启动... ($i/30)"
    sleep 2
done

# 验证端口监听
echo "验证端口监听状态..."
netstat -tlnp | grep 6443 && echo "✓ kube-apiserver端口6443监听正常"

echo "✓ kube-apiserver部署完成"</code></pre>

                    <h3><i class="fas fa-users"></i> 5.2 部署kube-controller-manager</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>kube-controller-manager运行控制器进程，负责集群状态管理。</p>

                    <h4><i class="fas fa-file-alt"></i> 创建kubeconfig文件</h4>
                    <pre><code># 创建kube-controller-manager的kubeconfig文件（简化方式）
cat > /etc/kubernetes/kube-controller-manager.kubeconfig << EOF
apiVersion: v1
clusters:
- cluster:
    certificate-authority: /etc/kubernetes/ca.pem
    server: https://*************:6443
  name: kubernetes
contexts:
- context:
    cluster: kubernetes
    user: kube-controller-manager
  name: default
current-context: default
kind: Config
preferences: {}
users:
- name: kube-controller-manager
  user:
    client-certificate: /etc/kubernetes/kube-controller-manager.pem
    client-key: /etc/kubernetes/kube-controller-manager-key.pem
EOF</code></pre>

                    <h4><i class="fas fa-cog"></i> 创建配置文件</h4>
                    <pre><code># 创建kube-controller-manager配置文件
cat > /etc/kubernetes/kube-controller-manager.conf << EOF
KUBE_CONTROLLER_MANAGER_OPTS="--logtostderr=false \\
--v=2 \\
--log-dir=/opt/kubernetes/logs \\
--leader-elect=true \\
--kubeconfig=/etc/kubernetes/kube-controller-manager.kubeconfig \\
--bind-address=127.0.0.1 \\
--allocate-node-cidrs=true \\
--cluster-cidr=**********/16 \\
--service-cluster-ip-range=10.0.0.0/24 \\
--cluster-signing-cert-file=/etc/kubernetes/ca.pem \\
--cluster-signing-key-file=/etc/kubernetes/ca-key.pem \\
--root-ca-file=/etc/kubernetes/ca.pem \\
--service-account-private-key-file=/etc/kubernetes/ca-key.pem \\
--cluster-signing-duration=87600h0m0s"
EOF</code></pre>

                    <h4><i class="fas fa-cogs"></i> 创建systemd服务</h4>
                    <pre><code># 创建kube-controller-manager systemd服务文件
cat > /etc/systemd/system/kube-controller-manager.service << EOF
[Unit]
Description=Kubernetes Controller Manager
Documentation=https://github.com/kubernetes/kubernetes

[Service]
EnvironmentFile=/etc/kubernetes/kube-controller-manager.conf
ExecStart=/opt/kubernetes/bin/kube-controller-manager \$KUBE_CONTROLLER_MANAGER_OPTS
Restart=on-failure

[Install]
WantedBy=multi-user.target
EOF

# 启动kube-controller-manager
systemctl daemon-reload
systemctl enable kube-controller-manager
systemctl start kube-controller-manager

# 检查服务状态
echo "检查kube-controller-manager服务状态..."
if systemctl is-active --quiet kube-controller-manager; then
    echo "✓ kube-controller-manager服务启动成功"
    systemctl status kube-controller-manager --no-pager
else
    echo "✗ kube-controller-manager服务启动失败，开始故障排查..."
    echo "1. 检查服务状态："
    systemctl status kube-controller-manager --no-pager
    echo ""
    echo "2. 查看错误日志："
    journalctl -u kube-controller-manager --no-pager -n 20
    echo ""
    echo "3. 检查kubeconfig文件："
    echo "   - 文件存在性：ls -la /etc/kubernetes/kube-controller-manager.kubeconfig"
    echo "   - 证书文件：ls -la /etc/kubernetes/kube-controller-manager*.pem"
    echo "   - API服务器连接：curl -k https://*************:6443/healthz"
    exit 1
fi

echo "✓ kube-controller-manager部署完成"</code></pre>

                    <h3><i class="fas fa-calendar-alt"></i> 5.3 部署kube-scheduler</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>kube-scheduler负责Pod的调度，将Pod分配到合适的节点上。</p>

                    <h4><i class="fas fa-file-alt"></i> 创建kubeconfig文件</h4>
                    <pre><code># 创建kube-scheduler的kubeconfig文件（简化方式）
cat > /etc/kubernetes/kube-scheduler.kubeconfig << EOF
apiVersion: v1
clusters:
- cluster:
    certificate-authority: /etc/kubernetes/ca.pem
    server: https://*************:6443
  name: kubernetes
contexts:
- context:
    cluster: kubernetes
    user: kube-scheduler
  name: default
current-context: default
kind: Config
preferences: {}
users:
- name: kube-scheduler
  user:
    client-certificate: /etc/kubernetes/kube-scheduler.pem
    client-key: /etc/kubernetes/kube-scheduler-key.pem
EOF</code></pre>

                    <h4><i class="fas fa-cog"></i> 创建配置文件</h4>
                    <pre><code># 创建kube-scheduler配置文件
cat > /etc/kubernetes/kube-scheduler.conf << EOF
KUBE_SCHEDULER_OPTS="--logtostderr=false \\
--v=2 \\
--log-dir=/opt/kubernetes/logs \\
--leader-elect=true \\
--kubeconfig=/etc/kubernetes/kube-scheduler.kubeconfig \\
--bind-address=127.0.0.1"
EOF</code></pre>

                    <h4><i class="fas fa-cogs"></i> 创建systemd服务</h4>
                    <pre><code># 创建kube-scheduler systemd服务文件
cat > /etc/systemd/system/kube-scheduler.service << EOF
[Unit]
Description=Kubernetes Scheduler
Documentation=https://github.com/kubernetes/kubernetes

[Service]
EnvironmentFile=/etc/kubernetes/kube-scheduler.conf
ExecStart=/opt/kubernetes/bin/kube-scheduler \$KUBE_SCHEDULER_OPTS
Restart=on-failure

[Install]
WantedBy=multi-user.target
EOF

# 启动kube-scheduler
systemctl daemon-reload
systemctl enable kube-scheduler
systemctl start kube-scheduler

# 检查服务状态
echo "检查kube-scheduler服务状态..."
if systemctl is-active --quiet kube-scheduler; then
    echo "✓ kube-scheduler服务启动成功"
    systemctl status kube-scheduler --no-pager
else
    echo "✗ kube-scheduler服务启动失败，开始故障排查..."
    echo "1. 检查服务状态："
    systemctl status kube-scheduler --no-pager
    echo ""
    echo "2. 查看错误日志："
    journalctl -u kube-scheduler --no-pager -n 20
    echo ""
    echo "3. 检查kubeconfig文件："
    echo "   - 文件存在性：ls -la /etc/kubernetes/kube-scheduler.kubeconfig"
    echo "   - 证书文件：ls -la /etc/kubernetes/kube-scheduler*.pem"
    exit 1
fi

echo "✓ kube-scheduler部署完成"</code></pre>

                    <h3><i class="fas fa-check-double"></i> 5.4 Master组件状态验证</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>验证所有Master组件是否正常运行。</p>

                    <pre><code># 验证所有Master组件状态
echo "🎉 Master组件部署完成！验证所有组件状态..."

echo "=== ETCD 状态 ==="
systemctl is-active etcd && echo "✅ ETCD: 运行正常" || echo "❌ ETCD: 运行异常"

echo "=== kube-apiserver 状态 ==="
systemctl is-active kube-apiserver && echo "✅ kube-apiserver: 运行正常" || echo "❌ kube-apiserver: 运行异常"

echo "=== kube-controller-manager 状态 ==="
systemctl is-active kube-controller-manager && echo "✅ kube-controller-manager: 运行正常" || echo "❌ kube-controller-manager: 运行异常"

echo "=== kube-scheduler 状态 ==="
systemctl is-active kube-scheduler && echo "✅ kube-scheduler: 运行正常" || echo "❌ kube-scheduler: 运行异常"

echo "=== 端口监听状态 ==="
echo "检查关键端口监听状态："
netstat -tlnp | grep -E "(2379|6443)" | head -10

echo "=== 进程状态 ==="
echo "检查Kubernetes进程："
ps aux | grep -E "(etcd|kube-)" | grep -v grep</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>✅ 所有组件状态显示为"运行正常"</li>
                            <li>✅ 端口6443和2379正常监听</li>
                            <li>✅ 所有kube-*进程正在运行</li>
                        </ul>
                    </div>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 故障排查提示：</strong>
                        如果某个组件状态异常，请按以下步骤排查：<br>
                        1. 查看服务状态：<code>systemctl status [服务名]</code><br>
                        2. 查看日志：<code>journalctl -u [服务名] -f</code><br>
                        3. 检查配置文件语法<br>
                        4. 检查证书文件权限和路径<br>
                        5. 确认没有使用K8s v1.24.17不兼容的参数
                    </div>

                    <h3><i class="fas fa-user-cog"></i> 5.5 配置kubectl</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>配置kubectl命令行工具，用于管理Kubernetes集群。</p>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
                        在配置kubectl之前，必须确保在Master节点的SSL目录中执行，并且admin证书已经正确生成。
                    </div>

                    <pre><code># 切换到SSL证书目录
cd /opt/kubernetes/ssl

# 创建admin的kubeconfig（使用正确的证书路径）
kubectl config set-cluster kubernetes \\
  --certificate-authority=ca.pem \\
  --embed-certs=true \\
  --server=https://*************:6443 \\
  --kubeconfig=admin.kubeconfig

kubectl config set-credentials admin \\
  --client-certificate=admin.pem \\
  --client-key=admin-key.pem \\
  --embed-certs=true \\
  --kubeconfig=admin.kubeconfig

kubectl config set-context kubernetes \\
  --cluster=kubernetes \\
  --user=admin \\
  --kubeconfig=admin.kubeconfig

kubectl config use-context kubernetes --kubeconfig=admin.kubeconfig

# 复制到系统配置目录
cp admin.kubeconfig /etc/kubernetes/

# 复制到用户目录
mkdir -p ~/.kube
cp admin.kubeconfig ~/.kube/config

# 设置环境变量（重要！）
export KUBECONFIG=/etc/kubernetes/admin.kubeconfig

# 验证集群状态
echo "验证kubectl配置..."
kubectl cluster-info
kubectl get componentstatuses

# 验证API服务器连接
echo "验证API服务器连接..."
kubectl get nodes</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期效果：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- <code>kubectl cluster-info</code> 显示集群信息</li>
                            <li>- <code>kubectl get componentstatuses</code> 显示所有组件状态为Healthy</li>
                            <li>- 所有Master组件服务状态为Active</li>
                        </ul>
                    </div>
                </section>

                <!-- Worker组件部署部分 -->
                <section id="worker-deployment">
                    <h2><span class="step-number">6</span>Worker组件部署</h2>
                    <p>部署Kubernetes v1.24.17 Worker节点的核心组件：kubelet和kube-proxy。</p>

                    <h3><i class="fas fa-server"></i> 6.1 部署kubelet</h3>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点 (*************)</div>
                    <p>kubelet是运行在每个节点上的代理，负责管理Pod和容器的生命周期。</p>

                    <h4><i class="fas fa-key"></i> 创建kubelet证书</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 在Master节点创建kubelet证书
cd /opt/kubernetes/ssl

# 创建kubelet证书签名请求
cat > kubelet-csr.json << EOF
{
  "CN": "system:node:k8s-node1",
  "hosts": [
    "127.0.0.1",
    "*************",
    "k8s-node1"
  ],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "L": "Beijing",
      "ST": "Beijing",
      "O": "system:nodes",
      "OU": "System"
    }
  ]
}
EOF

# 生成kubelet证书
cfssl gencert -ca=ca.pem -ca-key=ca-key.pem -config=ca-config.json -profile=kubernetes kubelet-csr.json | cfssljson -bare kubelet

# 分发证书到Worker节点
scp kubelet*.pem root@*************:/etc/kubernetes/</code></pre>

                    <h4><i class="fas fa-file-alt"></i> 创建kubelet kubeconfig</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 在Master节点创建kubelet的kubeconfig
kubectl config set-cluster kubernetes \\
  --certificate-authority=/etc/kubernetes/ca.pem \\
  --embed-certs=true \\
  --server=https://*************:6443 \\
  --kubeconfig=kubelet.kubeconfig

kubectl config set-credentials system:node:k8s-node1 \\
  --client-certificate=/opt/kubernetes/ssl/kubelet.pem \\
  --client-key=/opt/kubernetes/ssl/kubelet-key.pem \\
  --embed-certs=true \\
  --kubeconfig=kubelet.kubeconfig

kubectl config set-context default \\
  --cluster=kubernetes \\
  --user=system:node:k8s-node1 \\
  --kubeconfig=kubelet.kubeconfig

kubectl config use-context default --kubeconfig=kubelet.kubeconfig

# 分发kubeconfig到Worker节点
scp kubelet.kubeconfig root@*************:/etc/kubernetes/</code></pre>

                    <h4><i class="fas fa-cog"></i> 配置kubelet</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点 (*************)</div>

                    <div class="danger-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> K8s v1.24.17重要变更：</strong>
                        在Kubernetes v1.24.17中，<code>--network-plugin</code> 参数已被移除！CNI网络插件通过配置文件自动检测。使用此参数会导致kubelet启动失败。
                    </div>

                    <pre><code># 创建kubelet工作目录
mkdir -p /var/lib/kubelet
mkdir -p /opt/kubernetes/logs
mkdir -p /etc/kubernetes
mkdir -p /etc/cni/net.d

# 创建kubelet配置文件（兼容K8s v1.24.17）
cat > /etc/kubernetes/kubelet-config.yml << EOF
kind: KubeletConfiguration
apiVersion: kubelet.config.k8s.io/v1beta1
address: *************
port: 10250
readOnlyPort: 0
cgroupDriver: systemd
clusterDNS:
- ********
clusterDomain: cluster.local
failSwapOn: false
authentication:
  anonymous:
    enabled: false
  webhook:
    cacheTTL: 2m0s
    enabled: true
  x509:
    clientCAFile: /etc/kubernetes/ca.pem
authorization:
  mode: Webhook
  webhook:
    cacheAuthorizedTTL: 5m0s
    cacheUnauthorizedTTL: 30s
evictionHard:
  imagefs.available: 15%
  memory.available: 100Mi
  nodefs.available: 10%
  nodefs.inodesFree: 5%
maxOpenFiles: 1000000
maxPods: 110
containerRuntimeEndpoint: unix:///run/containerd/containerd.sock
EOF

# 创建kubelet启动配置文件（移除不兼容参数）
cat > /etc/kubernetes/kubelet.conf << EOF
KUBELET_OPTS="--logtostderr=false \\
--v=2 \\
--log-dir=/opt/kubernetes/logs \\
--hostname-override=k8s-node1 \\
--kubeconfig=/etc/kubernetes/kubelet.kubeconfig \\
--bootstrap-kubeconfig=/etc/kubernetes/kubelet.kubeconfig \\
--config=/etc/kubernetes/kubelet-config.yml \\
--cert-dir=/etc/kubernetes \\
--pod-infra-container-image=registry.aliyuncs.com/google_containers/pause:3.7"
EOF</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> K8s v1.24.17容器运行时配置：</strong>
                        Kubernetes v1.24.17推荐使用containerd作为容器运行时。本教程已配置containerd作为默认运行时，通过unix:///run/containerd/containerd.sock进行通信。确保containerd服务正常运行。
                    </div>

                    <h4><i class="fas fa-cogs"></i> 创建systemd服务</h4>
                    <pre><code># 创建kubelet systemd服务文件
cat > /etc/systemd/system/kubelet.service << EOF
[Unit]
Description=Kubernetes Kubelet
After=containerd.service
Requires=containerd.service

[Service]
EnvironmentFile=/etc/kubernetes/kubelet.conf
ExecStart=/opt/kubernetes/bin/kubelet \$KUBELET_OPTS
Restart=on-failure
KillMode=process

[Install]
WantedBy=multi-user.target
EOF

# 启动kubelet服务
systemctl daemon-reload
systemctl enable kubelet

echo "启动kubelet服务..."
systemctl start kubelet

# 等待服务启动
sleep 5

# 检查服务状态
echo "检查kubelet服务状态..."
if systemctl is-active --quiet kubelet; then
    echo "✓ kubelet服务启动成功"
    systemctl status kubelet --no-pager
else
    echo "✗ kubelet服务启动失败，开始故障排查..."
    echo "1. 检查服务状态："
    systemctl status kubelet --no-pager
    echo ""
    echo "2. 查看最近的错误日志："
    journalctl -u kubelet --no-pager -n 20
    echo ""
    echo "3. 常见问题排查："
    echo "   - 检查配置文件：cat /etc/kubernetes/kubelet.conf"
    echo "   - 检查证书文件：ls -la /etc/kubernetes/kubelet*.pem"
    echo "   - 检查containerd状态：systemctl status containerd"
    echo "   - 检查是否使用了不兼容的--network-plugin参数"
    echo ""
    echo "4. 如果遇到参数错误，请确认已移除K8s v1.24.17不兼容的参数"
    exit 1
fi

echo "✓ kubelet部署完成"</code></pre>

                    <h3><i class="fas fa-network-wired"></i> 6.2 部署kube-proxy</h3>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点 (*************)</div>
                    <p>kube-proxy负责为Service提供集群内部的服务发现和负载均衡。</p>

                    <h4><i class="fas fa-file-alt"></i> 创建kube-proxy kubeconfig</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 在Master节点创建kube-proxy的kubeconfig
kubectl config set-cluster kubernetes \\
  --certificate-authority=/etc/kubernetes/ca.pem \\
  --embed-certs=true \\
  --server=https://*************:6443 \\
  --kubeconfig=kube-proxy.kubeconfig

kubectl config set-credentials kube-proxy \\
  --client-certificate=/etc/kubernetes/kube-proxy.pem \\
  --client-key=/etc/kubernetes/kube-proxy-key.pem \\
  --embed-certs=true \\
  --kubeconfig=kube-proxy.kubeconfig

kubectl config set-context default \\
  --cluster=kubernetes \\
  --user=kube-proxy \\
  --kubeconfig=kube-proxy.kubeconfig

kubectl config use-context default --kubeconfig=kube-proxy.kubeconfig

# 分发kubeconfig到Worker节点
scp kube-proxy.kubeconfig root@*************:/etc/kubernetes/</code></pre>

                    <h4><i class="fas fa-cog"></i> 配置kube-proxy</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点 (*************)</div>
                    <pre><code># 创建kube-proxy配置文件
cat > /etc/kubernetes/kube-proxy-config.yml << EOF
kind: KubeProxyConfiguration
apiVersion: kubeproxy.config.k8s.io/v1alpha1
bindAddress: *************
metricsBindAddress: *************:10249
clientConnection:
  kubeconfig: /etc/kubernetes/kube-proxy.kubeconfig
hostnameOverride: k8s-node1
clusterCIDR: **********/16
mode: ipvs
ipvs:
  scheduler: "rr"
iptables:
  masqueradeAll: true
EOF

# 创建kube-proxy启动配置
cat > /etc/kubernetes/kube-proxy.conf << EOF
KUBE_PROXY_OPTS="--logtostderr=false \\
--v=2 \\
--log-dir=/var/log/kubernetes \\
--config=/etc/kubernetes/kube-proxy-config.yml"
EOF</code></pre>

                    <h4><i class="fas fa-cogs"></i> 创建systemd服务</h4>
                    <pre><code># 创建kube-proxy systemd服务文件
cat > /etc/systemd/system/kube-proxy.service << EOF
[Unit]
Description=Kubernetes Kube-Proxy Server
After=network.target

[Service]
EnvironmentFile=/etc/kubernetes/kube-proxy.conf
ExecStart=/opt/kubernetes/bin/kube-proxy \$KUBE_PROXY_OPTS
Restart=on-failure
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF

# 启动kube-proxy
systemctl daemon-reload
systemctl enable kube-proxy
systemctl start kube-proxy

# 检查服务状态
systemctl status kube-proxy</code></pre>

                    <h3><i class="fas fa-check-circle"></i> 6.3 验证Worker节点</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>在Master节点验证Worker节点是否成功加入集群。</p>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
                        在验证节点之前，确保已设置正确的KUBECONFIG环境变量：<code>export KUBECONFIG=/etc/kubernetes/admin.kubeconfig</code>
                    </div>

                    <pre><code># 设置KUBECONFIG环境变量
export KUBECONFIG=/etc/kubernetes/admin.kubeconfig

# 查看节点状态
echo "检查节点状态..."
kubectl get nodes

# 查看节点详细信息
echo "查看节点详细信息..."
kubectl get nodes -o wide

# 查看节点标签
echo "查看节点标签..."
kubectl get nodes --show-labels

# 检查节点是否存在污点
echo "检查节点污点..."
kubectl describe node k8s-node1 | grep -A 5 -B 5 Taints

# 如果节点状态为NotReady，查看kubelet日志
echo "如果节点状态异常，可以查看kubelet日志："
echo "ssh root@************* \"journalctl -u kubelet --no-pager -n 20\""

# 验证Worker节点服务状态
echo "验证Worker节点服务状态..."
ssh root@************* "systemctl is-active kubelet && echo '✓ kubelet: 运行正常' || echo '✗ kubelet: 运行异常'"
ssh root@************* "systemctl is-active kube-proxy && echo '✓ kube-proxy: 运行正常' || echo '✗ kube-proxy: 运行异常'"
ssh root@************* "systemctl is-active containerd && echo '✓ containerd: 运行正常' || echo '✗ containerd: 运行异常'"</code></pre>

                    <div class="danger-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 常见问题及解决方案：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li><strong>问题1：</strong>节点显示NotReady且有污点 <code>node.kubernetes.io/not-ready:NoSchedule</code></li>
                            <li><strong>解决：</strong><code>kubectl taint nodes k8s-node1 node.kubernetes.io/not-ready:NoSchedule-</code></li>
                            <li><strong>问题2：</strong>kubelet启动失败，提示 <code>unknown flag: --network-plugin</code></li>
                            <li><strong>解决：</strong>从kubelet.conf中移除 <code>--network-plugin=cni</code> 参数</li>
                            <li><strong>问题3：</strong>容器运行时连接失败</li>
                            <li><strong>解决：</strong>检查containerd服务状态，确保CRI插件正确配置</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期效果：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>✓ <code>kubectl get nodes</code> 显示k8s-node1节点</li>
                            <li>✓ 节点状态可能为NotReady（需要安装网络插件后才会Ready）</li>
                            <li>✓ kubelet、kube-proxy、containerd服务状态为Active</li>
                            <li>✓ 节点污点已清理（如果存在的话）</li>
                        </ul>
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 节点状态说明：</strong>
                        Worker节点加入集群后，状态可能显示为NotReady，这是正常的。需要安装网络插件（如Flannel）后，节点状态才会变为Ready。如果存在污点，需要手动清理。
                    </div>
                </section>

                <!-- 网络组件部署部分 -->
                <section id="network-deployment">
                    <h2><span class="step-number">7</span>网络组件部署</h2>
                    <p>部署Flannel网络插件，为Kubernetes v1.24.17集群提供Pod间网络通信能力。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> Flannel网络插件说明：</strong>
                        Flannel是一个简单易用的网络插件，与Kubernetes v1.24.17完全兼容。它使用VXLAN模式为Pod提供跨节点的网络连通性，配置简单，适合小规模集群。
                    </div>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
                        在部署网络插件之前，确保已设置正确的KUBECONFIG环境变量：<code>export KUBECONFIG=/etc/kubernetes/admin.kubeconfig</code>
                    </div>

                    <h3><i class="fas fa-download"></i> 7.1 下载并部署Flannel</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 设置KUBECONFIG环境变量
export KUBECONFIG=/etc/kubernetes/admin.kubeconfig

# 创建工作目录
cd /opt/kubernetes

echo "下载Flannel网络插件配置文件..."
# 下载Flannel配置文件（使用稳定版本）
for i in {1..3}; do
    wget https://github.com/flannel-io/flannel/releases/latest/download/kube-flannel.yml && break
    echo "下载失败，重试 $i/3..."
    sleep 5
done

# 验证下载
if [ ! -f kube-flannel.yml ]; then
    echo "错误：Flannel配置文件下载失败"
    echo "请手动下载或检查网络连接"
    echo "备用下载地址：https://raw.githubusercontent.com/flannel-io/flannel/master/Documentation/kube-flannel.yml"
    exit 1
fi

# 备份原始配置
cp kube-flannel.yml kube-flannel.yml.bak

# 检查并修改网络CIDR配置（确保与kube-controller-manager配置一致）
echo "检查Flannel网络CIDR配置..."
if grep -q "**********/16" kube-flannel.yml; then
    echo "✓ Flannel网络CIDR配置正确 (**********/16)"
else
    echo "修改Flannel网络CIDR为**********/16..."
    sed -i 's|"Network": ".*"|"Network": "**********/16"|g' kube-flannel.yml
fi

# 修改镜像地址为国内镜像（可选，提高下载速度）
echo "配置国内镜像源..."
sed -i 's|docker.io/flannel/flannel:|registry.aliyuncs.com/google_containers/flannel:|g' kube-flannel.yml
sed -i 's|docker.io/flannel/flannel-cni-plugin:|registry.aliyuncs.com/google_containers/flannel-cni-plugin:|g' kube-flannel.yml

# 部署Flannel
echo "部署Flannel网络插件..."
kubectl apply -f kube-flannel.yml

# 等待Flannel Pod启动
echo "等待Flannel Pod启动..."
sleep 10

# 检查Flannel Pod状态
echo "检查Flannel Pod状态..."
kubectl get pods -n kube-flannel

# 等待所有Pod运行
echo "等待所有Flannel Pod运行..."
kubectl wait --for=condition=Ready pod -l app=flannel -n kube-flannel --timeout=300s</code></pre>

                    <h3><i class="fas fa-check-circle"></i> 7.2 验证网络组件</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 设置KUBECONFIG环境变量
export KUBECONFIG=/etc/kubernetes/admin.kubeconfig

# 检查节点状态（应该变为Ready）
echo "检查节点状态..."
kubectl get nodes

# 检查Flannel Pod状态
echo "检查Flannel Pod状态..."
kubectl get pods -n kube-flannel
kubectl get pods -n kube-system | grep flannel

# 检查网络接口和路由
echo "检查网络路由..."
ip route show | grep -E "(10\.|flannel)"

# 检查是否存在节点污点
echo "检查节点污点..."
kubectl describe node k8s-node1 | grep -A 5 -B 5 Taints

# 如果存在not-ready污点，需要清理
echo "清理节点污点（如果存在）..."
kubectl taint nodes k8s-node1 node.kubernetes.io/not-ready:NoSchedule- 2>/dev/null || echo "无需清理污点"

# 等待节点状态更新
echo "等待节点状态更新..."
sleep 10

# 再次检查节点状态
echo "再次检查节点状态..."
kubectl get nodes

# 测试Pod网络连通性
echo "测试Pod创建和网络连通性..."
kubectl run test-pod --image=nginx:alpine --restart=Never

# 等待Pod启动
echo "等待Pod启动..."
kubectl wait --for=condition=Ready pod test-pod --timeout=300s

# 检查Pod状态和IP分配
echo "检查Pod状态和IP分配..."
kubectl get pods -o wide

# 测试Pod网络连通性
echo "测试Pod网络连通性..."
POD_IP=$(kubectl get pod test-pod -o jsonpath='{.status.podIP}')
echo "Pod IP: $POD_IP"

# 从Master节点ping Pod IP
if [ ! -z "$POD_IP" ]; then
    echo "从Master节点ping Pod IP..."
    ping -c 3 $POD_IP && echo "✓ Pod网络连通性正常" || echo "✗ Pod网络连通性异常"
fi

# 清理测试Pod
echo "清理测试Pod..."
kubectl delete pod test-pod</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期效果：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>✓ 所有节点状态变为Ready</li>
                            <li>✓ Flannel相关Pod状态为Running</li>
                            <li>✓ 节点污点已清理</li>
                            <li>✓ 可以成功创建和调度Pod</li>
                            <li>✓ Pod获得正确的IP地址（10.88.x.x网段）</li>
                            <li>✓ Pod网络连通性正常</li>
                        </ul>
                    </div>

                    <div class="danger-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 故障排查：</strong>
                        如果网络组件部署失败，请检查：<br>
                        1. Flannel Pod是否正常运行：<code>kubectl get pods -n kube-flannel</code><br>
                        2. 节点是否存在污点：<code>kubectl describe node k8s-node1</code><br>
                        3. 网络路由是否正确：<code>ip route show</code><br>
                        4. containerd服务是否正常：<code>systemctl status containerd</code><br>
                        5. 检查Flannel日志：<code>kubectl logs -n kube-flannel -l app=flannel</code>
                    </div>
                </section>

                <!-- CoreDNS部署部分 -->
                <section id="coredns-deployment">
                    <h2><span class="step-number">8</span>CoreDNS部署</h2>
                    <p>部署CoreDNS为Kubernetes v1.24.17集群提供DNS服务。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> CoreDNS v1.8.6说明：</strong>
                        CoreDNS v1.8.6是Kubernetes v1.24.17的默认DNS版本，提供了稳定的集群内DNS解析服务。
                    </div>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
                        在部署CoreDNS之前，需要先修复kube-controller-manager的RBAC权限问题，否则CoreDNS Deployment无法创建ReplicaSet。
                    </div>

                    <h3><i class="fas fa-key"></i> 8.1 修复RBAC权限问题</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 设置KUBECONFIG环境变量
export KUBECONFIG=/etc/kubernetes/admin.kubeconfig

# 切换到工作目录
cd /opt/kubernetes

# 创建kube-controller-manager RBAC权限修复文件
cat > controller-manager-rbac.yaml << 'EOF'
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: system:kube-controller-manager
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: system:kube-controller-manager
subjects:
- kind: User
  name: system:kube-controller-manager
  apiGroup: rbac.authorization.k8s.io
EOF

# 应用RBAC权限修复
kubectl apply -f controller-manager-rbac.yaml

echo "RBAC权限修复完成"</code></pre>

                    <h3><i class="fas fa-download"></i> 8.2 部署CoreDNS</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建CoreDNS配置文件
cat > coredns.yaml << EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: coredns
  namespace: kube-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    kubernetes.io/bootstrapping: rbac-defaults
  name: system:coredns
rules:
- apiGroups:
  - ""
  resources:
  - endpoints
  - services
  - pods
  - namespaces
  verbs:
  - list
  - watch
- apiGroups:
  - discovery.k8s.io
  resources:
  - endpointslices
  verbs:
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  annotations:
    rbac.authorization.kubernetes.io/autoupdate: "true"
  labels:
    kubernetes.io/bootstrapping: rbac-defaults
  name: system:coredns
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: system:coredns
subjects:
- kind: ServiceAccount
  name: coredns
  namespace: kube-system
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: coredns
  namespace: kube-system
data:
  Corefile: |
    .:53 {
        errors
        health {
           lameduck 5s
        }
        ready
        kubernetes cluster.local in-addr.arpa ip6.arpa {
           pods insecure
           fallthrough in-addr.arpa ip6.arpa
           ttl 30
        }
        prometheus :9153
        forward . /etc/resolv.conf {
           max_concurrent 1000
        }
        cache 30
        loop
        reload
        loadbalance
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: coredns
  namespace: kube-system
  labels:
    k8s-app: kube-dns
    kubernetes.io/name: "CoreDNS"
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
  selector:
    matchLabels:
      k8s-app: kube-dns
  template:
    metadata:
      labels:
        k8s-app: kube-dns
    spec:
      priorityClassName: system-cluster-critical
      serviceAccountName: coredns
      tolerations:
        - key: "CriticalAddonsOnly"
          operator: "Exists"
      nodeSelector:
        kubernetes.io/os: linux
      containers:
      - name: coredns
        image: registry.aliyuncs.com/google_containers/coredns:v1.9.3
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            memory: 170Mi
          requests:
            cpu: 100m
            memory: 70Mi
        args: [ "-conf", "/etc/coredns/Corefile" ]
        volumeMounts:
        - name: config-volume
          mountPath: /etc/coredns
          readOnly: true
        ports:
        - containerPort: 53
          name: dns
          protocol: UDP
        - containerPort: 53
          name: dns-tcp
          protocol: TCP
        - containerPort: 9153
          name: metrics
          protocol: TCP
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
            scheme: HTTP
          initialDelaySeconds: 60
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /ready
            port: 8181
            scheme: HTTP
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            add:
            - NET_BIND_SERVICE
            drop:
            - all
          readOnlyRootFilesystem: true
      dnsPolicy: Default
      volumes:
        - name: config-volume
          configMap:
            name: coredns
            items:
            - key: Corefile
              path: Corefile
---
apiVersion: v1
kind: Service
metadata:
  name: kube-dns
  namespace: kube-system
  annotations:
    prometheus.io/port: "9153"
    prometheus.io/scrape: "true"
  labels:
    k8s-app: kube-dns
    kubernetes.io/cluster-service: "true"
    kubernetes.io/name: "CoreDNS"
spec:
  selector:
    k8s-app: kube-dns
  clusterIP: ********
  ports:
  - name: dns
    port: 53
    protocol: UDP
  - name: dns-tcp
    port: 53
    protocol: TCP
  - name: metrics
    port: 9153
    protocol: TCP
EOF

# 部署CoreDNS
kubectl apply -f coredns.yaml

# 等待CoreDNS Pod启动
echo "等待CoreDNS Pod启动..."
kubectl wait --for=condition=Ready pod -l k8s-app=kube-dns -n kube-system --timeout=300s

# 检查CoreDNS状态
kubectl get pods -n kube-system -l k8s-app=kube-dns
kubectl get svc -n kube-system kube-dns

# 如果Pod无法启动，检查Deployment状态
kubectl get deployment -n kube-system coredns
kubectl describe deployment coredns -n kube-system</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 常见问题排查：</strong>
                        如果CoreDNS Pod无法启动，可能的原因包括：<br>
                        1. RBAC权限问题：检查kube-controller-manager是否有创建ReplicaSet的权限<br>
                        2. 镜像拉取问题：检查节点是否能正常拉取CoreDNS镜像<br>
                        3. 资源不足：检查节点资源是否充足<br>
                        4. 网络问题：检查Pod网络是否正常
                    </div>

                    <h3><i class="fas fa-check-circle"></i> 8.3 测试DNS解析</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建测试Pod
kubectl run dns-test --image=busybox --restart=Never -- sleep 3600

# 测试DNS解析
kubectl exec dns-test -- nslookup kubernetes.default.svc.cluster.local
kubectl exec dns-test -- nslookup kube-dns.kube-system.svc.cluster.local

# 清理测试Pod
kubectl delete pod dns-test</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期效果：</strong>
                        DNS解析测试应该能够成功解析集群内的服务名称，返回正确的IP地址。
                    </div>
                </section>

                <!-- Dashboard部署部分 -->
                <section id="dashboard-deployment">
                    <h2><span class="step-number">9</span>Dashboard部署</h2>
                    <p>部署Kubernetes Dashboard为集群提供Web管理界面。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> Dashboard说明：</strong>
                        Kubernetes Dashboard提供了一个基于Web的用户界面，可以方便地管理和监控集群资源。
                    </div>

                    <h3><i class="fas fa-download"></i> 9.1 部署Dashboard</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 下载Dashboard v2.6.1配置文件（与K8s v1.24.17兼容）
cd /opt/kubernetes
wget https://raw.githubusercontent.com/kubernetes/dashboard/v2.6.1/aio/deploy/recommended.yaml

# 修改Service类型为NodePort以便外部访问
sed -i '/type: ClusterIP/c\  type: NodePort' recommended.yaml

# 修改镜像地址为国内镜像（可选）
sed -i 's|kubernetesui/dashboard:|registry.aliyuncs.com/google_containers/dashboard:|g' recommended.yaml
sed -i 's|kubernetesui/metrics-scraper:|registry.aliyuncs.com/google_containers/metrics-scraper:|g' recommended.yaml

# 部署Dashboard
kubectl apply -f recommended.yaml

# 查看Dashboard服务
kubectl get svc -n kubernetes-dashboard
kubectl get pods -n kubernetes-dashboard</code></pre>

                    <h3><i class="fas fa-user-plus"></i> 9.2 创建管理员用户</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建管理员用户
cat > dashboard-admin.yaml << EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: admin-user
  namespace: kubernetes-dashboard
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: admin-user
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: admin-user
  namespace: kubernetes-dashboard
EOF

kubectl apply -f dashboard-admin.yaml

# 获取访问token
kubectl -n kubernetes-dashboard create token admin-user</code></pre>

                    <h3><i class="fas fa-globe"></i> 9.3 访问Dashboard</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 查看Dashboard访问端口
kubectl get svc kubernetes-dashboard -n kubernetes-dashboard

# 通过浏览器访问
# https://*************:NodePort
# 使用上面获取的token登录</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 访问方式：</strong>
                        通过浏览器访问 https://*************:NodePort，使用生成的token登录Dashboard。
                    </div>
                </section>

                <!-- 验证部署部分 -->
                <section id="verify-deployment">
                    <h2><span class="step-number">10</span>验证部署</h2>
                    <p>基于实际部署经验的全面验证Kubernetes v1.24.17集群功能和性能。</p>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 验证前准备：</strong>
                        确保已设置正确的KUBECONFIG环境变量：<code>export KUBECONFIG=/etc/kubernetes/admin.kubeconfig</code>
                    </div>

                    <h3><i class="fas fa-check-circle"></i> 10.1 基础集群状态验证</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 设置KUBECONFIG环境变量
export KUBECONFIG=/etc/kubernetes/admin.kubeconfig

echo "=== 基础集群状态验证 ==="

# 检查集群信息
echo "1. 检查集群信息："
kubectl cluster-info

# 检查节点状态
echo "2. 检查节点状态："
kubectl get nodes -o wide

# 检查节点详细信息
echo "3. 检查节点详细信息："
kubectl describe node k8s-node1

# 检查系统组件状态
echo "4. 检查系统组件状态："
systemctl is-active etcd && echo "✓ ETCD: 运行正常" || echo "✗ ETCD: 运行异常"
systemctl is-active kube-apiserver && echo "✓ kube-apiserver: 运行正常" || echo "✗ kube-apiserver: 运行异常"
systemctl is-active kube-controller-manager && echo "✓ kube-controller-manager: 运行正常" || echo "✗ kube-controller-manager: 运行异常"
systemctl is-active kube-scheduler && echo "✓ kube-scheduler: 运行正常" || echo "✗ kube-scheduler: 运行异常"

# 检查Worker节点组件状态
echo "5. 检查Worker节点组件状态："
ssh root@************* "systemctl is-active kubelet && echo '✓ kubelet: 运行正常' || echo '✗ kubelet: 运行异常'"
ssh root@************* "systemctl is-active kube-proxy && echo '✓ kube-proxy: 运行正常' || echo '✗ kube-proxy: 运行异常'"
ssh root@************* "systemctl is-active containerd && echo '✓ containerd: 运行正常' || echo '✗ containerd: 运行异常'"

# 检查系统Pod状态
echo "6. 检查系统Pod状态："
kubectl get pods -n kube-system
kubectl get pods -n kube-flannel

# 检查服务状态
echo "7. 检查服务状态："
kubectl get svc -A</code></pre>

                    <h3><i class="fas fa-rocket"></i> 10.2 Pod创建和调度测试</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code>echo "=== Pod创建和调度测试 ==="

# 创建测试Pod
echo "1. 创建测试Pod..."
kubectl run test-nginx --image=nginx:alpine --restart=Never

# 等待Pod启动
echo "2. 等待Pod启动..."
kubectl wait --for=condition=Ready pod test-nginx --timeout=300s

# 检查Pod状态和IP分配
echo "3. 检查Pod状态和IP分配..."
kubectl get pods -o wide

# 获取Pod IP
POD_IP=$(kubectl get pod test-nginx -o jsonpath='{.status.podIP}')
echo "Pod IP: $POD_IP"

# 检查Pod是否正确调度到Worker节点
echo "4. 检查Pod调度情况..."
kubectl describe pod test-nginx | grep -E "(Node:|Status:|IP:)"

# 测试Pod网络连通性
echo "5. 测试Pod网络连通性..."
if [ ! -z "$POD_IP" ]; then
    echo "从Master节点ping Pod IP..."
    ping -c 3 $POD_IP && echo "✓ Pod网络连通性正常" || echo "✗ Pod网络连通性异常"

    echo "从Worker节点ping Pod IP..."
    ssh root@************* "ping -c 3 $POD_IP" && echo "✓ Worker节点到Pod网络正常" || echo "✗ Worker节点到Pod网络异常"
fi

# 测试Pod内部功能
echo "6. 测试Pod内部功能..."
kubectl exec test-nginx -- nginx -t && echo "✓ Nginx配置正常" || echo "✗ Nginx配置异常"

# 测试Pod HTTP服务
echo "7. 测试Pod HTTP服务..."
if [ ! -z "$POD_IP" ]; then
    curl -s http://$POD_IP | grep -q "Welcome to nginx" && echo "✓ Nginx HTTP服务正常" || echo "✗ Nginx HTTP服务异常"
fi

# 清理测试Pod
echo "8. 清理测试Pod..."
kubectl delete pod test-nginx</code></pre>

                    <h3><i class="fas fa-network-wired"></i> 10.3 Service和网络功能测试</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code>echo "=== Service和网络功能测试 ==="

# 创建测试Deployment和Service
echo "1. 创建测试Deployment和Service..."
cat > test-deployment.yaml << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
  labels:
    app: nginx
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:alpine
        ports:
        - containerPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
spec:
  selector:
    app: nginx
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
      nodePort: 30080
  type: NodePort
EOF

kubectl apply -f test-deployment.yaml

# 等待Deployment就绪
echo "2. 等待Deployment就绪..."
kubectl wait --for=condition=Available deployment nginx-deployment --timeout=300s

# 检查部署状态
echo "3. 检查部署状态..."
kubectl get deployments
kubectl get pods -l app=nginx -o wide
kubectl get svc nginx-service

# 测试Service访问
echo "4. 测试Service访问..."
SERVICE_IP=$(kubectl get svc nginx-service -o jsonpath='{.spec.clusterIP}')
echo "Service ClusterIP: $SERVICE_IP"

# 测试ClusterIP访问
curl -s http://$SERVICE_IP | grep -q "Welcome to nginx" && echo "✓ ClusterIP访问正常" || echo "✗ ClusterIP访问异常"

# 测试NodePort访问
echo "5. 测试NodePort访问..."
curl -s http://*************:30080 | grep -q "Welcome to nginx" && echo "✓ Master NodePort访问正常" || echo "✗ Master NodePort访问异常"
curl -s http://*************:30080 | grep -q "Welcome to nginx" && echo "✓ Worker NodePort访问正常" || echo "✗ Worker NodePort访问异常"

# 清理测试资源
echo "6. 清理测试资源..."
kubectl delete -f test-deployment.yaml
rm -f test-deployment.yaml</code></pre>

                    <h3><i class="fas fa-network-wired"></i> 10.4 DNS和跨Pod网络测试</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code>echo "=== DNS和跨Pod网络测试 ==="

# 创建网络测试Pod
echo "1. 创建网络测试Pod..."
kubectl run test-pod1 --image=busybox --restart=Never -- sleep 3600
kubectl run test-pod2 --image=busybox --restart=Never -- sleep 3600

# 等待Pod启动
echo "2. 等待Pod启动..."
kubectl wait --for=condition=Ready pod/test-pod1 --timeout=300s
kubectl wait --for=condition=Ready pod/test-pod2 --timeout=300s

# 获取Pod IP
POD1_IP=$(kubectl get pod test-pod1 -o jsonpath='{.status.podIP}')
POD2_IP=$(kubectl get pod test-pod2 -o jsonpath='{.status.podIP}')

echo "Pod1 IP: $POD1_IP"
echo "Pod2 IP: $POD2_IP"

# 检查Pod分布
echo "3. 检查Pod分布..."
kubectl get pods -o wide

# 测试Pod间连通性
echo "4. 测试Pod间连通性..."
kubectl exec test-pod1 -- ping -c 3 $POD2_IP && echo "✓ Pod1到Pod2连通正常" || echo "✗ Pod1到Pod2连通异常"
kubectl exec test-pod2 -- ping -c 3 $POD1_IP && echo "✓ Pod2到Pod1连通正常" || echo "✗ Pod2到Pod1连通异常"

# 测试DNS解析
echo "5. 测试DNS解析..."
kubectl exec test-pod1 -- nslookup kubernetes.default.svc.cluster.local && echo "✓ DNS解析正常" || echo "✗ DNS解析异常"

# 测试外网连通性
echo "6. 测试外网连通性..."
kubectl exec test-pod1 -- ping -c 3 ******* && echo "✓ 外网连通正常" || echo "✗ 外网连通异常"

# 清理测试资源
echo "7. 清理测试资源..."
kubectl delete pod test-pod1 test-pod2</code></pre>

                    <h3><i class="fas fa-chart-line"></i> 10.5 系统性能和资源验证</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <pre><code>echo "=== 系统性能和资源验证 ==="

# 检查Master节点资源使用情况
echo "1. Master节点资源使用情况："
echo "内存使用："
free -h
echo ""
echo "磁盘使用："
df -h | grep -E "(/$|/var|/opt)"
echo ""
echo "CPU负载："
uptime
echo ""

# 检查Worker节点资源使用情况
echo "2. Worker节点资源使用情况："
ssh root@************* "echo '内存使用：'; free -h; echo ''; echo '磁盘使用：'; df -h | grep -E '(/$|/var|/opt)'; echo ''; echo 'CPU负载：'; uptime"

# 检查容器运行时状态
echo "3. 检查容器运行时状态："
echo "Master节点containerd状态："
systemctl is-active containerd && echo "✓ containerd运行正常" || echo "✗ containerd运行异常"
echo ""
echo "Worker节点containerd状态："
ssh root@************* "systemctl is-active containerd && echo '✓ containerd运行正常' || echo '✗ containerd运行异常'"

# 检查网络状态
echo "4. 检查网络状态："
echo "网络路由："
ip route show | grep -E "(10\.|flannel)"
echo ""
echo "网络接口："
ip addr show | grep -E "(flannel|cni)"

# 检查端口监听状态
echo "5. 检查关键端口监听状态："
netstat -tlnp | grep -E "(6443|2379|10250|10249)" | head -10

# 检查进程状态
echo "6. 检查Kubernetes进程状态："
ps aux | grep -E "(etcd|kube-|kubelet)" | grep -v grep | head -10</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 完整验证通过标准：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>✓ 所有节点状态为Ready</li>
                            <li>✓ 所有系统组件服务状态为Active</li>
                            <li>✓ 所有系统Pod状态为Running</li>
                            <li>✓ Pod可以正常创建和调度到Worker节点</li>
                            <li>✓ Pod间网络连通正常</li>
                            <li>✓ DNS解析功能正常</li>
                            <li>✓ Service ClusterIP和NodePort访问正常</li>
                            <li>✓ 外网连通性正常</li>
                            <li>✓ 系统资源使用合理</li>
                            <li>✓ 关键端口正常监听</li>
                        </ul>
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 验证完成提示：</strong>
                        如果以上所有验证项目都通过，说明您的Kubernetes v1.24.17集群已经成功部署并正常运行。集群现在可以用于生产环境或进一步的应用部署。
                    </div>
                </section>

                <!-- RBAC权限完善部分 -->
                <section id="rbac-enhancement">
                    <h2><span class="step-number">11</span>RBAC权限完善</h2>
                    <p>解决kube-controller-manager的RBAC权限问题，确保集群组件能够正常创建和管理资源。</p>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 权限问题说明：</strong>
                        在部署CoreDNS等系统组件时，可能会遇到kube-controller-manager权限不足的问题，导致无法创建ReplicaSet等资源。本章节将解决这些权限问题。
                    </div>

                    <h3><i class="fas fa-shield-alt"></i> 11.1 kube-controller-manager权限问题</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>

                    <h4><i class="fas fa-bug"></i> 问题现象</h4>
                    <p>部署CoreDNS时出现以下错误：</p>
                    <div class="code-block">
                        <pre><code># 查看Deployment状态
kubectl describe deployment coredns -n kube-system

# 错误信息
Warning  ReplicaSetCreateError  Failed to create new replica set "coredns-688c6bb6d":
replicasets.apps is forbidden: User "system:kube-controller-manager" cannot create
resource "replicasets" in API group "apps" in the namespace "kube-system"</code></pre>
                    </div>

                    <h4><i class="fas fa-tools"></i> 解决方案</h4>
                    <p>创建并应用kube-controller-manager的RBAC权限配置：</p>
                    <div class="code-block">
                        <pre><code># 创建controller-manager RBAC配置
cat > /opt/kubernetes/controller-manager-rbac.yaml << 'EOF'
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: system:kube-controller-manager
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: system:kube-controller-manager
subjects:
- kind: User
  name: system:kube-controller-manager
  apiGroup: rbac.authorization.k8s.io
EOF

# 应用RBAC配置
kubectl apply -f /opt/kubernetes/controller-manager-rbac.yaml</code></pre>
                    </div>

                    <h4><i class="fas fa-check-circle"></i> 验证权限修复</h4>
                    <div class="code-block">
                        <pre><code># 检查ClusterRoleBinding
kubectl get clusterrolebinding system:kube-controller-manager

# 验证权限
kubectl auth can-i create replicasets --as=system:kube-controller-manager -n kube-system

# 重新部署CoreDNS验证
kubectl delete -f coredns.yaml
kubectl apply -f coredns.yaml

# 检查Deployment状态
kubectl get deployment -n kube-system
kubectl get pods -n kube-system</code></pre>
                    </div>

                    <h3><i class="fas fa-network-wired"></i> 11.2 kube-proxy权限和配置</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-server"></i> 所有节点</div>

                    <h4><i class="fas fa-certificate"></i> 生成kube-proxy证书</h4>
                    <p>为kube-proxy组件生成专用的TLS证书：</p>
                    <div class="code-block">
                        <pre><code># 在Master节点创建kube-proxy证书请求
cd /opt/kubernetes/ssl

cat > kube-proxy-csr.json << 'EOF'
{
  "CN": "system:kube-proxy",
  "hosts": [],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "L": "BeiJing",
      "ST": "BeiJing",
      "O": "system:node-proxier",
      "OU": "System"
    }
  ]
}
EOF

# 生成kube-proxy证书
cfssl gencert -ca=ca.pem -ca-key=ca-key.pem -config=ca-config.json -profile=kubernetes kube-proxy-csr.json | cfssljson -bare kube-proxy

# 验证证书生成
ls -la kube-proxy*</code></pre>
                    </div>

                    <h4><i class="fas fa-cog"></i> 配置kube-proxy</h4>
                    <p>创建kube-proxy的kubeconfig和配置文件：</p>
                    <div class="code-block">
                        <pre><code># 创建kube-proxy kubeconfig
kubectl config set-cluster kubernetes \
  --certificate-authority=/opt/kubernetes/ssl/ca.pem \
  --embed-certs=true \
  --server=https://*************:6443 \
  --kubeconfig=/opt/kubernetes/cfg/kube-proxy.kubeconfig

kubectl config set-credentials kube-proxy \
  --client-certificate=/opt/kubernetes/ssl/kube-proxy.pem \
  --client-key=/opt/kubernetes/ssl/kube-proxy-key.pem \
  --embed-certs=true \
  --kubeconfig=/opt/kubernetes/cfg/kube-proxy.kubeconfig

kubectl config set-context default \
  --cluster=kubernetes \
  --user=kube-proxy \
  --kubeconfig=/opt/kubernetes/cfg/kube-proxy.kubeconfig

kubectl config use-context default --kubeconfig=/opt/kubernetes/cfg/kube-proxy.kubeconfig</code></pre>
                    </div>

                    <div class="code-block">
                        <pre><code># 创建kube-proxy配置文件
cat > /opt/kubernetes/cfg/kube-proxy.conf << 'EOF'
KUBE_PROXY_OPTS="--logtostderr=false \
--v=2 \
--log-dir=/opt/kubernetes/logs \
--config=/opt/kubernetes/cfg/kube-proxy-config.yml"
EOF

# 创建kube-proxy详细配置
cat > /opt/kubernetes/cfg/kube-proxy-config.yml << 'EOF'
kind: KubeProxyConfiguration
apiVersion: kubeproxy.config.k8s.io/v1alpha1
bindAddress: 0.0.0.0
metricsBindAddress: 0.0.0.0:10249
clientConnection:
  kubeconfig: /opt/kubernetes/cfg/kube-proxy.kubeconfig
hostnameOverride: k8s-master
clusterCIDR: *********/16
mode: iptables
ipvs:
  scheduler: "rr"
iptables:
  masqueradeAll: true
EOF</code></pre>
                    </div>

                    <h4><i class="fas fa-copy"></i> 分发配置到Worker节点</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <div class="code-block">
                        <pre><code># 复制配置文件和证书到Worker节点
scp /opt/kubernetes/cfg/kube-proxy.conf root@*************:/opt/kubernetes/cfg/
scp /opt/kubernetes/cfg/kube-proxy.kubeconfig root@*************:/opt/kubernetes/cfg/
scp /opt/kubernetes/ssl/kube-proxy*.pem root@*************:/opt/kubernetes/ssl/
scp /opt/kubernetes/ssl/ca.pem root@*************:/opt/kubernetes/ssl/

# 在Worker节点创建kube-proxy配置
ssh root@************* "cat > /opt/kubernetes/cfg/kube-proxy-config.yml << 'EOF'
kind: KubeProxyConfiguration
apiVersion: kubeproxy.config.k8s.io/v1alpha1
bindAddress: 0.0.0.0
metricsBindAddress: 0.0.0.0:10249
clientConnection:
  kubeconfig: /opt/kubernetes/cfg/kube-proxy.kubeconfig
hostnameOverride: k8s-node1
clusterCIDR: *********/16
mode: iptables
ipvs:
  scheduler: \"rr\"
iptables:
  masqueradeAll: true
EOF"</code></pre>
                    </div>

                    <h4><i class="fas fa-play"></i> 启动kube-proxy服务</h4>
                    <div class="code-block">
                        <pre><code># 在Master节点启动kube-proxy
cd /opt/kubernetes
nohup ./bin/kube-proxy --config=./cfg/kube-proxy.conf --v=2 > ./logs/kube-proxy.log 2>&1 &

# 在Worker节点启动kube-proxy
ssh root@************* "cd /opt/kubernetes && nohup ./bin/kube-proxy --config=./cfg/kube-proxy.conf --v=2 > ./logs/kube-proxy.log 2>&1 &"

# 验证kube-proxy运行状态
ps aux | grep kube-proxy | grep -v grep
ssh root@************* "ps aux | grep kube-proxy | grep -v grep"</code></pre>
                    </div>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 权限完善完成：</strong>
                        kube-controller-manager和kube-proxy的RBAC权限已经正确配置，集群组件现在可以正常创建和管理所有类型的资源。
                    </div>
                </section>

                <!-- 应用部署验证部分 -->
                <section id="application-deployment">
                    <h2><span class="step-number">12</span>应用部署验证</h2>
                    <p>通过部署实际应用来验证Kubernetes v1.24.17集群的完整功能，包括Pod调度、服务发现、网络通信等。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 验证目标：</strong>
                        本章节将部署Nginx应用，验证Pod调度、Service服务、NodePort外部访问等核心功能，确保集群完全可用。
                    </div>

                    <h3><i class="fas fa-rocket"></i> 12.1 部署Nginx应用</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>

                    <h4><i class="fas fa-file-code"></i> 创建Nginx Deployment</h4>
                    <div class="code-block">
                        <pre><code># 创建Nginx部署配置
cat > /opt/kubernetes/nginx-deployment.yaml << 'EOF'
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
  labels:
    app: nginx
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:1.20.2
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "64Mi"
            cpu: "250m"
          limits:
            memory: "128Mi"
            cpu: "500m"
EOF

# 部署Nginx应用
kubectl apply -f /opt/kubernetes/nginx-deployment.yaml</code></pre>
                    </div>

                    <h4><i class="fas fa-network-wired"></i> 创建Service服务</h4>
                    <div class="code-block">
                        <pre><code># 创建NodePort服务配置
cat > /opt/kubernetes/nginx-service.yaml << 'EOF'
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
  labels:
    app: nginx
spec:
  type: NodePort
  ports:
  - port: 80
    targetPort: 80
    nodePort: 30080
  selector:
    app: nginx
EOF

# 创建服务
kubectl apply -f /opt/kubernetes/nginx-service.yaml</code></pre>
                    </div>

                    <h4><i class="fas fa-eye"></i> 验证应用部署</h4>
                    <div class="code-block">
                        <pre><code># 检查Pod状态
kubectl get pods -o wide

# 检查Service状态
kubectl get svc

# 检查Deployment状态
kubectl get deployment

# 查看Pod详细信息
kubectl describe pods</code></pre>
                    </div>

                    <h3><i class="fas fa-globe"></i> 12.2 网络连通性测试</h3>

                    <h4><i class="fas fa-network-wired"></i> 内部网络测试</h4>
                    <div class="code-block">
                        <pre><code># 测试Pod间网络通信
kubectl exec -it $(kubectl get pods -l app=nginx -o jsonpath='{.items[0].metadata.name}') -- ping -c 3 $(kubectl get pods -l app=nginx -o jsonpath='{.items[1].status.podIP}')

# 测试Service ClusterIP访问
kubectl exec -it $(kubectl get pods -l app=nginx -o jsonpath='{.items[0].metadata.name}') -- curl -I $(kubectl get svc nginx-service -o jsonpath='{.spec.clusterIP}')

# 测试DNS解析
kubectl exec -it $(kubectl get pods -l app=nginx -o jsonpath='{.items[0].metadata.name}') -- nslookup nginx-service</code></pre>
                    </div>

                    <h4><i class="fas fa-external-link-alt"></i> 外部访问测试</h4>
                    <div class="code-block">
                        <pre><code># 测试NodePort访问 - Master节点
curl -I http://*************:30080

# 测试NodePort访问 - Worker节点
curl -I http://*************:30080

# 检查iptables规则
iptables -t nat -L | grep 30080

# 检查端口监听
netstat -tlnp | grep 30080</code></pre>
                    </div>

                    <h3><i class="fas fa-chart-line"></i> 12.3 负载均衡测试</h3>
                    <div class="code-block">
                        <pre><code># 创建测试脚本验证负载均衡
cat > /tmp/test_loadbalance.sh << 'EOF'
#!/bin/bash
echo "测试负载均衡..."
for i in {1..10}; do
    echo "请求 $i:"
    curl -s http://*************:30080 | grep -o "nginx/[0-9.]*"
    sleep 1
done
EOF

chmod +x /tmp/test_loadbalance.sh
/tmp/test_loadbalance.sh</code></pre>
                    </div>

                    <h3><i class="fas fa-expand-arrows-alt"></i> 12.4 扩缩容测试</h3>
                    <div class="code-block">
                        <pre><code># 扩容到4个副本
kubectl scale deployment nginx-deployment --replicas=4

# 检查扩容结果
kubectl get pods -l app=nginx

# 缩容到1个副本
kubectl scale deployment nginx-deployment --replicas=1

# 检查缩容结果
kubectl get pods -l app=nginx

# 恢复到2个副本
kubectl scale deployment nginx-deployment --replicas=2</code></pre>
                    </div>

                    <h3><i class="fas fa-sync-alt"></i> 12.5 滚动更新测试</h3>
                    <div class="code-block">
                        <pre><code># 更新镜像版本
kubectl set image deployment/nginx-deployment nginx=nginx:1.21.6

# 查看滚动更新状态
kubectl rollout status deployment/nginx-deployment

# 查看更新历史
kubectl rollout history deployment/nginx-deployment

# 回滚到上一个版本
kubectl rollout undo deployment/nginx-deployment

# 验证回滚结果
kubectl describe deployment nginx-deployment</code></pre>
                    </div>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 应用验证完成：</strong>
                        Nginx应用已成功部署并通过所有功能测试，包括Pod调度、服务发现、网络通信、负载均衡、扩缩容和滚动更新。集群功能完全正常。
                    </div>
                </section>

                <!-- 故障排查部分 -->
                <section id="troubleshooting">
                    <h2><span class="step-number">13</span>故障排查</h2>
                    <p>基于实际部署经验的常见问题诊断和解决方法。</p>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
                        本故障排查部分基于实际部署过程中遇到的真实问题，包含了详细的解决步骤和验证方法。建议在遇到问题时按照顺序逐一排查。
                    </div>

                    <h3><i class="fas fa-exclamation-triangle"></i> 11.1 kubelet启动失败问题</h3>

                    <h4><i class="fas fa-bug"></i> 问题1：kubelet启动失败 - 不兼容参数错误</h4>
                    <div class="danger-box">
                        <strong>问题现象：</strong>kubelet服务启动失败，日志显示 <code>unknown flag: --network-plugin</code><br>
                        <strong>根本原因：</strong>K8s v1.24.17已移除 <code>--network-plugin</code> 参数，CNI网络插件通过配置文件自动检测<br>
                        <strong>解决方法：</strong>
                        <pre><code># 1. 检查当前kubelet配置
cat /etc/kubernetes/kubelet.conf

# 2. 移除不兼容的参数，修正配置文件
cat > /etc/kubernetes/kubelet.conf << EOF
KUBELET_OPTS="--logtostderr=false \\
--v=2 \\
--log-dir=/opt/kubernetes/logs \\
--hostname-override=k8s-node1 \\
--kubeconfig=/etc/kubernetes/kubelet.kubeconfig \\
--bootstrap-kubeconfig=/etc/kubernetes/kubelet.kubeconfig \\
--config=/etc/kubernetes/kubelet-config.yml \\
--cert-dir=/etc/kubernetes \\
--pod-infra-container-image=registry.aliyuncs.com/google_containers/pause:3.7"
EOF

# 3. 重新启动kubelet
systemctl daemon-reload
systemctl restart kubelet
systemctl status kubelet</code></pre>
                    </div>

                    <h4><i class="fas fa-bug"></i> 问题2：kubelet容器运行时连接失败</h4>
                    <div class="danger-box">
                        <strong>问题现象：</strong>kubelet日志显示 <code>rpc error: code = Unimplemented desc = unknown service runtime.v1alpha2.RuntimeService</code><br>
                        <strong>根本原因：</strong>containerd CRI插件未正确配置或版本不兼容<br>
                        <strong>解决方法：</strong>
                        <pre><code># 1. 检查containerd状态
systemctl status containerd

# 2. 检查containerd配置中的CRI插件
grep -A 10 -B 5 "cri" /etc/containerd/config.toml

# 3. 重新配置containerd（确保CRI插件启用）
# 在kubelet配置中指定容器运行时端点
cat > /etc/kubernetes/kubelet-config.yml << EOF
kind: KubeletConfiguration
apiVersion: kubelet.config.k8s.io/v1beta1
address: *************
port: 10250
readOnlyPort: 0
cgroupDriver: systemd
clusterDNS:
- ********
clusterDomain: cluster.local
failSwapOn: false
authentication:
  anonymous:
    enabled: false
  webhook:
    cacheTTL: 2m0s
    enabled: true
  x509:
    clientCAFile: /etc/kubernetes/ca.pem
authorization:
  mode: Webhook
  webhook:
    cacheAuthorizedTTL: 5m0s
    cacheUnauthorizedTTL: 30s
evictionHard:
  imagefs.available: 15%
  memory.available: 100Mi
  nodefs.available: 10%
  nodefs.inodesFree: 5%
maxOpenFiles: 1000000
maxPods: 110
containerRuntimeEndpoint: unix:///run/containerd/containerd.sock
EOF

# 4. 重启服务
systemctl restart containerd
systemctl restart kubelet</code></pre>
                    </div>

                    <h4><i class="fas fa-bug"></i> 问题3：节点NotReady - 污点问题</h4>
                    <div class="danger-box">
                        <strong>问题现象：</strong>节点状态显示Ready，但Pod无法调度，提示 <code>node(s) had untolerated taint</code><br>
                        <strong>根本原因：</strong>节点存在 <code>node.kubernetes.io/not-ready:NoSchedule</code> 污点<br>
                        <strong>解决方法：</strong>
                        <pre><code># 1. 检查节点污点
kubectl describe node k8s-node1 | grep -A 5 -B 5 Taints

# 2. 移除not-ready污点
kubectl taint nodes k8s-node1 node.kubernetes.io/not-ready:NoSchedule-

# 3. 验证污点已移除
kubectl describe node k8s-node1 | grep Taints

# 4. 测试Pod调度
kubectl run test-pod --image=nginx:alpine
kubectl get pods -o wide</code></pre>
                    </div>

                    <h3><i class="fas fa-network-wired"></i> 11.2 Pod网络和调度问题</h3>

                    <h4><i class="fas fa-bug"></i> 问题4：Pod一直处于ContainerCreating状态</h4>
                    <div class="danger-box">
                        <strong>问题现象：</strong>Pod创建后一直显示 <code>ContainerCreating</code> 状态<br>
                        <strong>根本原因：</strong>缺少必要的ConfigMap或网络配置问题<br>
                        <strong>解决方法：</strong>
                        <pre><code># 1. 检查Pod详细状态
kubectl describe pod test-pod

# 2. 检查是否缺少kube-root-ca.crt ConfigMap
kubectl get configmap -n default | grep kube-root-ca.crt
kubectl get configmap -n kube-system | grep kube-root-ca.crt

# 3. 如果缺少，创建必要的ConfigMap
kubectl create configmap kube-root-ca.crt --from-file=ca.crt=/etc/kubernetes/ca.pem -n kube-system
kubectl create configmap kube-root-ca.crt --from-file=ca.crt=/etc/kubernetes/ca.pem -n default

# 4. 检查网络插件状态
kubectl get pods -n kube-system | grep -E "(calico|flannel|cni)"

# 5. 验证Pod能否正常创建
kubectl delete pod test-pod
kubectl run test-pod --image=nginx:alpine
kubectl get pods -o wide</code></pre>
                    </div>

                    <h4><i class="fas fa-bug"></i> 问题5：kubectl exec/logs命令失败</h4>
                    <div class="danger-box">
                        <strong>问题现象：</strong>执行 <code>kubectl exec</code> 或 <code>kubectl logs</code> 时提示证书错误<br>
                        <strong>错误信息：</strong><code>tls: failed to verify certificate: x509: certificate signed by unknown authority</code><br>
                        <strong>根本原因：</strong>kubelet证书配置问题或API服务器无法验证kubelet证书<br>
                        <strong>解决方法：</strong>
                        <pre><code># 1. 检查kubelet证书配置
ls -la /etc/kubernetes/kubelet*.pem

# 2. 验证kubelet证书是否正确
openssl x509 -in /etc/kubernetes/kubelet.pem -text -noout | grep -A 5 Subject

# 3. 检查kubelet配置中的证书路径
grep -E "(client-ca-file|tls-cert-file|tls-private-key-file)" /etc/kubernetes/kubelet-config.yml

# 4. 如果证书有问题，重新生成kubelet证书
cd /opt/kubernetes/ssl
cat > kubelet-csr.json << EOF
{
  "CN": "system:node:k8s-node1",
  "hosts": [
    "127.0.0.1",
    "*************",
    "k8s-node1"
  ],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "L": "Beijing",
      "ST": "Beijing",
      "O": "system:nodes",
      "OU": "System"
    }
  ]
}
EOF

cfssl gencert -ca=ca.pem -ca-key=ca-key.pem -config=ca-config.json -profile=kubernetes kubelet-csr.json | cfssljson -bare kubelet

# 5. 复制新证书到配置目录
cp kubelet*.pem /etc/kubernetes/
systemctl restart kubelet</code></pre>
                    </div>

                    <h4><i class="fas fa-bug"></i> 问题6：Pod网络连通性问题</h4>
                    <div class="danger-box">
                        <strong>问题现象：</strong>Pod可以创建但无法访问外部网络或其他Pod<br>
                        <strong>解决方法：</strong>
                        <pre><code># 1. 检查Pod网络分配
kubectl get pods -o wide

# 2. 检查节点网络路由
ip route show | grep -E "(10\.|172\.|192\.)"

# 3. 检查iptables规则
iptables -t nat -L -n | grep -E "(KUBE|CNI)"

# 4. 检查网络插件状态
kubectl get pods -n kube-system -o wide | grep -E "(calico|flannel)"

# 5. 测试Pod间网络连通性
kubectl run net-test-1 --image=busybox --restart=Never -- sleep 3600
kubectl run net-test-2 --image=busybox --restart=Never -- sleep 3600
kubectl exec net-test-1 -- ping -c 3 $(kubectl get pod net-test-2 -o jsonpath='{.status.podIP}')

# 6. 清理测试Pod
kubectl delete pod net-test-1 net-test-2</code></pre>
                    </div>

                    <h3><i class="fas fa-key"></i> 11.3 证书和认证问题</h3>

                    <h4><i class="fas fa-bug"></i> 问题7：证书权限和路径问题</h4>
                    <div class="danger-box">
                        <strong>问题现象：</strong>组件无法启动，提示证书文件无法读取<br>
                        <strong>解决方法：</strong>
                        <pre><code># 1. 检查证书文件权限
ls -la /etc/kubernetes/*.pem

# 2. 修正证书文件权限
chmod 644 /etc/kubernetes/*.pem
chown root:root /etc/kubernetes/*.pem

# 3. 检查证书有效期
for cert in /etc/kubernetes/*.pem; do
    echo "检查证书: $cert"
    openssl x509 -in "$cert" -text -noout | grep -E "(Subject|Not After)" 2>/dev/null || echo "非证书文件"
    echo "---"
done

# 4. 验证证书链
openssl verify -CAfile /etc/kubernetes/ca.pem /etc/kubernetes/server.pem</code></pre>
                    </div>

                    <h4><i class="fas fa-bug"></i> 问题8：kubeconfig文件配置错误</h4>
                    <div class="danger-box">
                        <strong>问题现象：</strong>kubectl命令执行失败或组件无法连接API服务器<br>
                        <strong>解决方法：</strong>
                        <pre><code># 1. 检查kubeconfig文件格式
kubectl config view --kubeconfig=/etc/kubernetes/admin.kubeconfig

# 2. 测试kubeconfig连接
kubectl --kubeconfig=/etc/kubernetes/admin.kubeconfig get nodes

# 3. 验证证书配置
kubectl config view --kubeconfig=/etc/kubernetes/admin.kubeconfig --raw | grep -A 5 -B 5 certificate-authority-data

# 4. 如果配置有问题，重新生成kubeconfig
cd /opt/kubernetes/ssl
kubectl config set-cluster kubernetes \
  --certificate-authority=ca.pem \
  --embed-certs=true \
  --server=https://*************:6443 \
  --kubeconfig=admin.kubeconfig

kubectl config set-credentials admin \
  --client-certificate=admin.pem \
  --client-key=admin-key.pem \
  --embed-certs=true \
  --kubeconfig=admin.kubeconfig

kubectl config set-context kubernetes \
  --cluster=kubernetes \
  --user=admin \
  --kubeconfig=admin.kubeconfig

kubectl config use-context kubernetes --kubeconfig=admin.kubeconfig

# 5. 复制到正确位置
cp admin.kubeconfig /etc/kubernetes/
cp admin.kubeconfig ~/.kube/config</code></pre>
                    </div>

                    <h3><i class="fas fa-tools"></i> 11.4 系统服务和日志诊断</h3>

                    <h4><i class="fas fa-search"></i> 详细日志查看和分析</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 系统组件日志查看：</strong>
                        <pre><code># 查看系统组件日志（实时）
journalctl -u etcd -f
journalctl -u kube-apiserver -f
journalctl -u kube-controller-manager -f
journalctl -u kube-scheduler -f
journalctl -u kubelet -f
journalctl -u kube-proxy -f
journalctl -u containerd -f

# 查看最近的错误日志
journalctl -u kubelet --no-pager -n 50 | grep -i error
journalctl -u containerd --no-pager -n 50 | grep -i error

# 查看Pod日志
kubectl logs -n kube-system pod-name
kubectl logs -n kube-system pod-name -c container-name --previous

# 查看集群事件
kubectl get events --sort-by=.metadata.creationTimestamp
kubectl get events --field-selector type=Warning

# 查看节点详细信息
kubectl describe node k8s-node1
kubectl top nodes  # 需要metrics-server</code></pre>
                    </div>

                    <h3><i class="fas fa-chart-line"></i> 11.5 性能调优和监控</h3>

                    <h4><i class="fas fa-tachometer-alt"></i> 4核8G配置专项优化</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 基于实际测试的优化参数：</strong>
                        <pre><code># 1. kubelet性能优化
cat >> /etc/kubernetes/kubelet-config.yml << EOF
# 4核8G优化参数
maxPods: 110
kubeReserved:
  cpu: 500m
  memory: 1Gi
  ephemeral-storage: 1Gi
systemReserved:
  cpu: 500m
  memory: 1Gi
  ephemeral-storage: 1Gi
evictionHard:
  memory.available: "200Mi"
  nodefs.available: "10%"
  imagefs.available: "15%"
EOF

# 2. kube-apiserver性能优化
# 在kube-apiserver配置中添加：
--max-requests-inflight=400
--max-mutating-requests-inflight=200
--default-watch-cache-size=100
--watch-cache-sizes=nodes#100,pods#1000

# 3. etcd性能优化
# 在etcd配置中添加：
--quota-backend-bytes=2147483648  # 2GB
--max-txn-ops=1024
--max-request-bytes=1572864

# 4. containerd性能优化
# 在/etc/containerd/config.toml中调整：
[plugins."io.containerd.grpc.v1.cri"]
  max_concurrent_downloads = 3
  max_container_log_line_size = 16384</code></pre>
                    </div>

                    <h4><i class="fas fa-monitor"></i> 集群健康监控脚本</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 实时监控脚本：</strong>
                        <pre><code># 创建集群健康监控脚本
cat > /tmp/k8s_health_monitor.sh << 'EOF'
#!/bin/bash
echo "=== Kubernetes集群健康监控 ==="
echo "时间: $(date)"
echo

# 1. 检查节点状态
echo "1. 节点状态："
kubectl get nodes -o wide
echo

# 2. 检查系统Pod状态
echo "2. 系统Pod状态："
kubectl get pods -n kube-system --no-headers | awk '{print $1, $3}' | grep -v Running | head -10
echo

# 3. 检查资源使用情况
echo "3. 节点资源使用："
kubectl top nodes 2>/dev/null || echo "需要安装metrics-server"
echo

# 4. 检查存储使用
echo "4. 存储使用情况："
df -h | grep -E "(/$|/var|/opt)"
echo

# 5. 检查网络连通性
echo "5. 网络连通性测试："
ping -c 2 ************* >/dev/null 2>&1 && echo "✓ Master节点网络正常" || echo "✗ Master节点网络异常"
ping -c 2 ************* >/dev/null 2>&1 && echo "✓ Worker节点网络正常" || echo "✗ Worker节点网络异常"
echo

# 6. 检查关键服务状态
echo "6. 关键服务状态："
for service in etcd kube-apiserver kube-controller-manager kube-scheduler kubelet containerd; do
    if systemctl is-active --quiet $service 2>/dev/null; then
        echo "✓ $service 运行正常"
    else
        echo "✗ $service 状态异常"
    fi
done
echo

echo "=== 监控完成 ==="
EOF

chmod +x /tmp/k8s_health_monitor.sh
echo "运行监控脚本: /tmp/k8s_health_monitor.sh"</code></pre>
                    </div>
                </section>

                <!-- 监控和日志部分 -->
                <section id="monitoring-logging">
                    <h2><span class="step-number">14</span>监控和日志</h2>
                    <p>部署Prometheus、Grafana等监控工具，为Kubernetes v1.24.17集群提供完整的监控和日志解决方案。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 监控架构说明：</strong>
                        本章节将部署完整的监控栈，包括Prometheus用于指标收集、Grafana用于可视化展示、以及日志收集系统，为集群提供全方位的可观测性。
                    </div>

                    <h3><i class="fas fa-chart-line"></i> 14.1 部署Prometheus监控</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>

                    <h4><i class="fas fa-download"></i> 下载Prometheus</h4>
                    <div class="code-block">
                        <pre><code># 下载Prometheus
cd /opt
wget https://github.com/prometheus/prometheus/releases/download/v2.40.7/prometheus-2.40.7.linux-amd64.tar.gz
tar -xzf prometheus-2.40.7.linux-amd64.tar.gz
mv prometheus-2.40.7.linux-amd64 prometheus

# 创建Prometheus用户和目录
useradd -r -s /bin/false prometheus
mkdir -p /opt/prometheus/{data,config}
chown -R prometheus:prometheus /opt/prometheus</code></pre>
                    </div>

                    <h4><i class="fas fa-cog"></i> 配置Prometheus</h4>
                    <div class="code-block">
                        <pre><code># 创建Prometheus配置文件
cat > /opt/prometheus/config/prometheus.yml << 'EOF'
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "k8s_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
            - default
    scheme: https
    tls_config:
      ca_file: /opt/kubernetes/ssl/ca.pem
      cert_file: /opt/kubernetes/ssl/server.pem
      key_file: /opt/kubernetes/ssl/server-key.pem
    relabel_configs:
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: default;kubernetes;https

  - job_name: 'kubernetes-nodes'
    kubernetes_sd_configs:
      - role: node
    scheme: https
    tls_config:
      ca_file: /opt/kubernetes/ssl/ca.pem
      cert_file: /opt/kubernetes/ssl/server.pem
      key_file: /opt/kubernetes/ssl/server-key.pem
    relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)

  - job_name: 'kubernetes-cadvisor'
    kubernetes_sd_configs:
      - role: node
    scheme: https
    metrics_path: /metrics/cadvisor
    tls_config:
      ca_file: /opt/kubernetes/ssl/ca.pem
      cert_file: /opt/kubernetes/ssl/server.pem
      key_file: /opt/kubernetes/ssl/server-key.pem
    relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)

  - job_name: 'kubernetes-service-endpoints'
    kubernetes_sd_configs:
      - role: endpoints
    relabel_configs:
      - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_service_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_service_label_(.+)
      - source_labels: [__meta_kubernetes_namespace]
        action: replace
        target_label: kubernetes_namespace
      - source_labels: [__meta_kubernetes_service_name]
        action: replace
        target_label: kubernetes_name
EOF</code></pre>
                    </div>

                    <h4><i class="fas fa-play"></i> 启动Prometheus</h4>
                    <div class="code-block">
                        <pre><code># 创建Prometheus systemd服务
cat > /etc/systemd/system/prometheus.service << 'EOF'
[Unit]
Description=Prometheus
Wants=network-online.target
After=network-online.target

[Service]
User=prometheus
Group=prometheus
Type=simple
ExecStart=/opt/prometheus/prometheus \
  --config.file=/opt/prometheus/config/prometheus.yml \
  --storage.tsdb.path=/opt/prometheus/data \
  --web.console.templates=/opt/prometheus/consoles \
  --web.console.libraries=/opt/prometheus/console_libraries \
  --web.listen-address=0.0.0.0:9090 \
  --web.enable-lifecycle

[Install]
WantedBy=multi-user.target
EOF

# 启动Prometheus
systemctl daemon-reload
systemctl enable prometheus
systemctl start prometheus

# 检查状态
systemctl status prometheus</code></pre>
                    </div>

                    <h3><i class="fas fa-chart-bar"></i> 14.2 部署Grafana</h3>

                    <h4><i class="fas fa-download"></i> 安装Grafana</h4>
                    <div class="code-block">
                        <pre><code># 下载并安装Grafana
cd /opt
wget https://dl.grafana.com/oss/release/grafana-9.3.2.linux-amd64.tar.gz
tar -xzf grafana-9.3.2.linux-amd64.tar.gz
mv grafana-9.3.2 grafana

# 创建Grafana用户和目录
useradd -r -s /bin/false grafana
mkdir -p /opt/grafana/{data,logs}
chown -R grafana:grafana /opt/grafana</code></pre>
                    </div>

                    <h4><i class="fas fa-cog"></i> 配置Grafana</h4>
                    <div class="code-block">
                        <pre><code># 创建Grafana配置文件
cat > /opt/grafana/conf/custom.ini << 'EOF'
[server]
http_port = 3000
domain = *************

[database]
type = sqlite3
path = /opt/grafana/data/grafana.db

[security]
admin_user = admin
admin_password = admin123

[users]
allow_sign_up = false

[auth.anonymous]
enabled = false
EOF</code></pre>
                    </div>

                    <h4><i class="fas fa-play"></i> 启动Grafana</h4>
                    <div class="code-block">
                        <pre><code># 创建Grafana systemd服务
cat > /etc/systemd/system/grafana.service << 'EOF'
[Unit]
Description=Grafana
After=network.target

[Service]
User=grafana
Group=grafana
Type=simple
ExecStart=/opt/grafana/bin/grafana-server \
  --config=/opt/grafana/conf/custom.ini \
  --homepath=/opt/grafana
WorkingDirectory=/opt/grafana

[Install]
WantedBy=multi-user.target
EOF

# 启动Grafana
systemctl daemon-reload
systemctl enable grafana
systemctl start grafana

# 检查状态
systemctl status grafana</code></pre>
                    </div>

                    <h3><i class="fas fa-file-alt"></i> 14.3 部署日志收集</h3>

                    <h4><i class="fas fa-download"></i> 部署Filebeat</h4>
                    <div class="code-block">
                        <pre><code># 下载Filebeat
cd /opt
wget https://artifacts.elastic.co/downloads/beats/filebeat/filebeat-8.5.3-linux-x86_64.tar.gz
tar -xzf filebeat-8.5.3-linux-x86_64.tar.gz
mv filebeat-8.5.3-linux-x86_64 filebeat

# 创建Filebeat配置
cat > /opt/filebeat/filebeat.yml << 'EOF'
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /opt/kubernetes/logs/*.log
    - /var/log/containers/*.log
  fields:
    logtype: kubernetes
  fields_under_root: true

output.file:
  path: "/opt/filebeat/logs"
  filename: kubernetes-logs

logging.level: info
logging.to_files: true
logging.files:
  path: /opt/filebeat/logs
  name: filebeat
  keepfiles: 7
  permissions: 0644
EOF

# 创建日志目录
mkdir -p /opt/filebeat/logs</code></pre>
                    </div>

                    <h4><i class="fas fa-play"></i> 启动Filebeat</h4>
                    <div class="code-block">
                        <pre><code># 创建Filebeat systemd服务
cat > /etc/systemd/system/filebeat.service << 'EOF'
[Unit]
Description=Filebeat
After=network.target

[Service]
Type=simple
ExecStart=/opt/filebeat/filebeat -c /opt/filebeat/filebeat.yml
WorkingDirectory=/opt/filebeat
Restart=always

[Install]
WantedBy=multi-user.target
EOF

# 启动Filebeat
systemctl daemon-reload
systemctl enable filebeat
systemctl start filebeat

# 检查状态
systemctl status filebeat</code></pre>
                    </div>

                    <h3><i class="fas fa-eye"></i> 14.4 监控验证</h3>

                    <h4><i class="fas fa-globe"></i> 访问监控界面</h4>
                    <div class="code-block">
                        <pre><code># 检查Prometheus
curl http://*************:9090/metrics

# 检查Grafana
curl http://*************:3000/api/health

# 检查端口监听
netstat -tlnp | grep -E "(9090|3000)"</code></pre>
                    </div>

                    <h4><i class="fas fa-chart-line"></i> 配置Grafana数据源</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> Grafana配置步骤：</strong>
                        <ol>
                            <li>访问 http://*************:3000</li>
                            <li>使用 admin/admin123 登录</li>
                            <li>添加Prometheus数据源：http://*************:9090</li>
                            <li>导入Kubernetes监控Dashboard</li>
                        </ol>
                    </div>

                    <h4><i class="fas fa-download"></i> 导入监控Dashboard</h4>
                    <div class="code-block">
                        <pre><code># 下载Kubernetes监控Dashboard
curl -o /tmp/k8s-dashboard.json https://grafana.com/api/dashboards/315/revisions/3/download

# 或者手动创建简单的Dashboard配置
cat > /tmp/k8s-simple-dashboard.json << 'EOF'
{
  "dashboard": {
    "title": "Kubernetes Cluster Monitoring",
    "panels": [
      {
        "title": "Node CPU Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "100 - (avg by (instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)"
          }
        ]
      },
      {
        "title": "Node Memory Usage",
        "type": "graph",
        "targets": [
          {
            "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100"
          }
        ]
      }
    ]
  }
}
EOF</code></pre>
                    </div>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 监控部署完成：</strong>
                        Prometheus、Grafana和日志收集系统已成功部署。可以通过Web界面监控集群状态、查看指标和日志信息。
                    </div>
                </section>

                <!-- 总结部分 -->
                <section id="summary">
                    <h2><span class="step-number">15</span>总结</h2>
                    <p>恭喜！您已经成功在银河麒麟v10 sp3 2403服务器上部署了Kubernetes v1.24.17集群。</p>

                    <h3><i class="fas fa-check-circle"></i> 15.1 部署成果</h3>
                    <div class="success-box">
                        <strong><i class="fas fa-trophy"></i> 已完成的部署：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>✓ 1个Master节点（*************）</li>
                            <li>✓ 1个Worker节点（*************）</li>
                            <li>✓ ETCD v3.5.6数据存储</li>
                            <li>✓ containerd v1.6.15容器运行时</li>
                            <li>✓ Flannel网络插件</li>
                            <li>✓ CoreDNS v1.9.3 DNS服务</li>
                            <li>✓ Kubernetes Dashboard Web界面</li>
                            <li>✓ 完整的RBAC权限控制</li>
                            <li>✓ kube-proxy网络代理服务</li>
                            <li>✓ Nginx应用部署验证</li>
                            <li>✓ Prometheus + Grafana监控系统</li>
                            <li>✓ Filebeat日志收集系统</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-lightbulb"></i> 15.2 K8s v1.24.17特性</h3>
                    <div class="info-box">
                        <strong><i class="fas fa-star"></i> 版本特性总结：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- <strong>dockershim支持：</strong> 最后一个支持dockershim的版本</li>
                            <li>- <strong>containerd优先：</strong> 推荐使用containerd作为容器运行时</li>
                            <li>- <strong>稳定性：</strong> 经过长期验证的LTS版本</li>
                            <li>- <strong>兼容性：</strong> 与银河麒麟v10sp3 2403完全兼容</li>
                            <li>- <strong>安全性：</strong> 包含最新的安全补丁和修复</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-rocket"></i> 15.3 实际部署经验总结</h3>
                    <div class="success-box">
                        <strong><i class="fas fa-trophy"></i> 关键成功要素：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>✓ <strong>RBAC权限配置：</strong> 正确配置kube-controller-manager权限</li>
                            <li>✓ <strong>网络组件部署：</strong> kube-proxy在所有节点正常运行</li>
                            <li>✓ <strong>应用验证测试：</strong> 通过实际应用验证集群功能</li>
                            <li>✓ <strong>监控系统部署：</strong> 完整的可观测性解决方案</li>
                            <li>✓ <strong>故障排查能力：</strong> 基于实际问题的解决方案</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-rocket"></i> 15.4 后续建议</h3>
                    <div class="warning-box">
                        <strong><i class="fas fa-forward"></i> 生产环境建议：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>1. <strong>高可用部署：</strong> 部署多Master节点实现高可用</li>
                            <li>2. <strong>存储方案：</strong> 配置持久化存储（如Ceph、NFS）</li>
                            <li>3. <strong>监控告警：</strong> 已部署Prometheus+Grafana，配置告警规则</li>
                            <li>4. <strong>日志收集：</strong> 已部署Filebeat，可扩展ELK栈</li>
                            <li>5. <strong>备份策略：</strong> 制定ETCD和应用数据备份策略</li>
                            <li>6. <strong>安全加固：</strong> 配置网络策略和Pod安全策略</li>
                            <li>7. <strong>资源管理：</strong> 配置资源配额和限制</li>
                            <li>8. <strong>自动化运维：</strong> 基于监控数据实现自动扩缩容</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-book"></i> 15.5 学习资源</h3>
                    <div class="info-box">
                        <strong><i class="fas fa-graduation-cap"></i> 推荐学习资源：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- <strong>官方文档：</strong> https://kubernetes.io/docs/</li>
                            <li>- <strong>API参考：</strong> https://kubernetes.io/docs/reference/</li>
                            <li>- <strong>最佳实践：</strong> https://kubernetes.io/docs/concepts/</li>
                            <li>- <strong>社区资源：</strong> https://github.com/kubernetes/kubernetes</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <strong><i class="fas fa-heart"></i> 感谢使用本教程！</strong><br>
                        本教程基于实际部署经验，详细介绍了在银河麒麟v10 sp3 2403服务器上部署Kubernetes v1.24.17的完整过程。
                        教程已根据实际部署中遇到的问题进行了全面修正和优化，确保部署成功率。
                        如果您在使用过程中遇到问题，请参考故障排查部分或查阅官方文档。
                        祝您在Kubernetes的学习和使用中取得成功！
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 教程更新说明（基于实际部署经验）：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>✓ 修正了kubelet配置中的K8s v1.24.17不兼容参数问题</li>
                            <li>✓ 更新了网络插件从Calico改为Flannel，提供更简单的配置</li>
                            <li>✓ 添加了详细的故障排查步骤和解决方案</li>
                            <li>✓ 完善了kubectl配置和KUBECONFIG环境变量设置</li>
                            <li>✓ 增加了节点污点清理的处理步骤</li>
                            <li>✓ 提供了完整的验证流程和测试用例</li>
                            <li>✓ 所有步骤都经过实际验证，确保可重现性</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-check-double"></i> 12.5 最终部署验证脚本</h3>
                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 一键验证脚本：</strong>
                        运行以下脚本可以全面验证集群部署是否成功。
                    </div>
                    <pre><code># 创建集群验证脚本
cat > /tmp/k8s_cluster_check.sh << 'EOF'
#!/bin/bash
echo "=== Kubernetes v1.24.17 集群部署验证 ==="
echo

# 1. 检查节点状态
echo "1. 检查节点状态："
kubectl get nodes -o wide
echo

# 2. 检查系统Pod状态
echo "2. 检查系统Pod状态："
kubectl get pods -n kube-system
echo

# 3. 检查服务状态
echo "3. 检查服务状态："
kubectl get svc -A
echo

# 4. 检查组件健康状态
echo "4. 检查组件健康状态："
kubectl get componentstatuses 2>/dev/null || echo "componentstatuses API已弃用"
echo

# 5. 测试DNS解析
echo "5. 测试DNS解析："
kubectl run dns-test --image=busybox --restart=Never --rm -it -- nslookup kubernetes.default.svc.cluster.local 2>/dev/null || echo "DNS测试需要手动执行"
echo

# 6. 检查网络连通性
echo "6. 检查网络连通性："
kubectl run net-test --image=busybox --restart=Never --rm -it -- ping -c 3 ******** 2>/dev/null || echo "网络测试需要手动执行"
echo

# 7. 检查存储
echo "7. 检查存储类："
kubectl get storageclass 2>/dev/null || echo "未配置存储类"
echo

# 8. 检查RBAC
echo "8. 检查RBAC配置："
kubectl auth can-i create pods --as=system:serviceaccount:default:default
echo

echo "=== 验证完成 ==="
echo "如果所有检查都通过，说明集群部署成功！"
EOF

chmod +x /tmp/k8s_cluster_check.sh
echo "运行验证脚本："
echo "/tmp/k8s_cluster_check.sh"</code></pre>
                </section>

                <!-- 返回顶部按钮 -->
                <a href="#" class="back-to-top" id="backToTop">
                    <i class="fas fa-arrow-up"></i>
                </a>
            </div>
        </div>
    </div>

    <script>
        // 移动端菜单切换
        document.getElementById('mobileMenuBtn').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('active');
        });

        // 返回顶部功能
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'flex';
            } else {
                backToTop.style.display = 'none';
            }
        });

        document.getElementById('backToTop').addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 侧边栏导航高亮
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.sidebar a[href^="#"]');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });

        // 平滑滚动
        document.querySelectorAll('.sidebar a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }

                // 移动端关闭菜单
                if (window.innerWidth <= 768) {
                    document.getElementById('sidebar').classList.remove('active');
                }
            });
        });
    </script>
</body>
</html>
