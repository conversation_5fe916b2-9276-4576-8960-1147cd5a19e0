﻿[2025-07-12 23:16:38] [INFO] 开始PostgreSQL完整恢复
[2025-07-12 23:16:38] [INFO] 备份路径: D:\Code\MicrosoftCode\postgresql-11.19-offline\backups\20250712_231429
[2025-07-12 23:16:38] [INFO] 临时目录: D:\Code\MicrosoftCode\postgresql-11.19-offline\temp_restore_20250712_231638
[2025-07-12 23:16:38] [SUCCESS] 临时目录创建成功
[2025-07-12 23:16:38] [INFO] 检查Docker环境...
[2025-07-12 23:16:38] [SUCCESS] Docker版本: Docker version 28.0.4, build b8034c0
[2025-07-12 23:16:38] [INFO] 验证备份文件...
[2025-07-12 23:16:38] [SUCCESS] 找到备份文件: postgres_data_volume.zip (5.14MB)
[2025-07-12 23:16:38] [SUCCESS] 找到备份文件: postgres_logs_volume.zip (0MB)
[2025-07-12 23:16:38] [INFO] 检测容器用户ID...
[2025-07-12 23:16:38] [SUCCESS] 从现有容器获取PostgreSQL用户ID: 1000
[2025-07-12 23:16:38] [INFO] 停止现有服务...
[2025-07-12 23:16:47] [SUCCESS] 现有服务已停止
[2025-07-12 23:16:47] [INFO] 删除现有数据卷...
[2025-07-12 23:16:47] [SUCCESS] 已删除数据卷: postgresql_11_19_data_offline
[2025-07-12 23:16:47] [SUCCESS] 已删除数据卷: postgresql_11_19_logs_offline
[2025-07-12 23:16:47] [SUCCESS] 已删除数据卷: pgadmin_data_offline
[2025-07-12 23:16:47] [INFO] 恢复配置文件...
[2025-07-12 23:16:47] [SUCCESS] 已恢复: docker-compose.yml
[2025-07-12 23:16:47] [SUCCESS] 已恢复: Dockerfile
[2025-07-12 23:16:47] [SUCCESS] 已恢复: postgresql.conf
[2025-07-12 23:16:47] [SUCCESS] 已恢复: pg_hba.conf
[2025-07-12 23:16:47] [SUCCESS] 已恢复: docker-entrypoint.sh
[2025-07-12 23:16:47] [SUCCESS] 已恢复: init-scripts目录
[2025-07-12 23:16:47] [SUCCESS] 配置文件恢复完成，已恢复 6 个文件/目录
[2025-07-12 23:16:48] [INFO] 创建新数据卷...
[2025-07-12 23:16:48] [SUCCESS] 已创建数据卷: postgresql_11_19_data_offline
[2025-07-12 23:16:48] [SUCCESS] 已创建数据卷: postgresql_11_19_logs_offline
[2025-07-12 23:16:48] [SUCCESS] 已创建数据卷: pgadmin_data_offline
[2025-07-12 23:16:48] [INFO] 恢复PostgreSQL主数据卷...
[2025-07-12 23:17:01] [INFO] 修复数据目录权限...
[2025-07-12 23:17:02] [SUCCESS] PostgreSQL主数据卷恢复完成
[2025-07-12 23:17:03] [INFO] 恢复PostgreSQL日志卷...
[2025-07-12 23:17:03] [INFO] 修复日志目录权限...
[2025-07-12 23:17:04] [SUCCESS] PostgreSQL日志卷恢复完成
[2025-07-12 23:17:04] [INFO] 恢复pgAdmin数据卷...
[2025-07-12 23:17:04] [INFO] 修复pgAdmin权限...
[2025-07-12 23:17:05] [SUCCESS] pgAdmin数据卷恢复完成
[2025-07-12 23:17:05] [INFO] 启动PostgreSQL服务...
[2025-07-12 23:17:12] [INFO] 等待PostgreSQL启动...
[2025-07-12 23:17:20] [INFO] 检查PostgreSQL状态 (尝试 1/15)...
[2025-07-12 23:17:20] [SUCCESS] PostgreSQL容器运行正常
[2025-07-12 23:17:20] [SUCCESS] PostgreSQL服务启动成功
[2025-07-12 23:17:20] [INFO] 恢复SQL数据...
[2025-07-12 23:17:20] [INFO] 从以下文件导入SQL数据: postgresql_all_databases.sql
[2025-07-12 23:17:21] [WARN] SQL数据导入完成，但验证失败
[2025-07-12 23:17:21] [INFO] 验证恢复结果...
[2025-07-12 23:17:21] [SUCCESS] 容器状态: 运行中
[2025-07-12 23:17:21] [SUCCESS] 数据库连接: 正常
[2025-07-12 23:17:21] [SUCCESS] PostgreSQL服务恢复成功
[2025-07-12 23:17:21] [INFO] 已清理临时目录
[2025-07-12 23:17:21] [SUCCESS] 恢复成功完成
[2025-07-12 23:17:21] [SUCCESS] PostgreSQL服务可在以下地址访问: localhost:3433
[2025-07-12 23:17:21] [SUCCESS] 用户名: postgres, 密码: postgres
