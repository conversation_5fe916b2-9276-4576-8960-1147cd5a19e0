# PostgreSQL 11.19 离线版权限修复脚本
# 版本: 1.0
# 作者: AI Assistant
# 日期: 2025-07-12
#
# 用法:
#   .\fix-postgresql-permissions.ps1
#   .\fix-postgresql-permissions.ps1 -Restart

param(
    [switch]$Restart
)

# 日志记录函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogContent = "[$Time] [$Level] $Message"

    switch ($Level) {
        "ERROR" { Write-Host $LogContent -ForegroundColor Red }
        "WARN"  { Write-Host $LogContent -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $LogContent -ForegroundColor Green }
        default { Write-Host $LogContent -ForegroundColor White }
    }
}

# 获取容器用户ID函数
function Get-ContainerUserId {
    param([string]$ImageName, [string]$UserName)
    try {
        $UserId = docker run --rm --entrypoint="" $ImageName id -u $UserName 2>$null
        if ($LASTEXITCODE -eq 0) {
            return $UserId.Trim()
        }
    } catch {}
    return $null
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "PostgreSQL 11.19 离线版权限修复脚本 v1.0" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

try {
    # 检查Docker环境
    Write-Log "检查Docker环境..."
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker未运行或未安装"
    }
    Write-Log "Docker版本: $dockerVersion" "SUCCESS"

    # 检查PostgreSQL容器状态
    Write-Log "检查PostgreSQL容器状态..."
    $PostgresStatus = docker inspect postgresql-11.19 --format='{{.State.Status}}' 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Log "PostgreSQL容器状态: $PostgresStatus"
        
        if ($PostgresStatus -eq "restarting") {
            Write-Log "检测到PostgreSQL正在重启，这通常是权限或配置问题" "WARN"
        }
    } else {
        Write-Log "PostgreSQL容器不存在" "WARN"
        exit 1
    }

    # 获取PostgreSQL用户ID
    Write-Log "获取PostgreSQL用户ID..."
    $PostgresUserId = $null
    
    # 尝试从现有容器获取
    if ($PostgresStatus -eq "running") {
        try {
            $PostgresUserId = docker exec postgresql-11.19 id -u postgres 2>$null
            if ($LASTEXITCODE -eq 0 -and $PostgresUserId) {
                Write-Log "从运行容器获取用户ID: $PostgresUserId" "SUCCESS"
            }
        } catch {}
    }
    
    # 如果无法从容器获取，尝试从镜像获取
    if (-not $PostgresUserId) {
        $PostgresUserId = Get-ContainerUserId "postgresql:11.19" "postgres"
        if ($PostgresUserId) {
            Write-Log "从镜像获取用户ID: $PostgresUserId" "SUCCESS"
        } else {
            $PostgresUserId = "26"  # CentOS默认postgres用户ID
            Write-Log "使用默认用户ID: $PostgresUserId" "WARN"
        }
    }

    # 停止PostgreSQL容器
    if ($Restart -or $PostgresStatus -eq "restarting") {
        Write-Log "停止PostgreSQL容器..."
        docker stop postgresql-11.19 2>&1 | Out-Null
        Start-Sleep -Seconds 5
    }

    # 修复PostgreSQL数据卷权限
    Write-Log "修复PostgreSQL数据卷权限..."
    docker run --rm -v postgresql_11_19_data_offline:/data alpine sh -c "
        # 设置基本权限
        chown -R ${PostgresUserId}:${PostgresUserId} /data 2>/dev/null || chown -R ${PostgresUserId}:0 /data
        
        # 设置数据目录权限
        chmod 750 /data 2>/dev/null || chmod 755 /data
        
        # 设置子目录权限
        find /data -type d -exec chmod 750 {} \; 2>/dev/null || find /data -type d -exec chmod 755 {} \;
        
        # 设置文件权限
        find /data -type f -exec chmod 640 {} \; 2>/dev/null || find /data -type f -exec chmod 644 {} \;
        
        # 特殊文件权限
        find /data -name 'postgresql.conf' -exec chmod 600 {} \; 2>/dev/null || true
        find /data -name 'pg_hba.conf' -exec chmod 600 {} \; 2>/dev/null || true
        find /data -name 'pg_ident.conf' -exec chmod 600 {} \; 2>/dev/null || true
        
        echo 'PostgreSQL数据目录权限修复完成'
    " 2>&1 | Out-Null

    if ($LASTEXITCODE -eq 0) {
        Write-Log "PostgreSQL数据卷权限修复成功" "SUCCESS"
    } else {
        Write-Log "PostgreSQL数据卷权限修复失败" "ERROR"
    }

    # 修复PostgreSQL日志卷权限
    Write-Log "修复PostgreSQL日志卷权限..."
    docker run --rm -v postgresql_11_19_logs_offline:/data alpine sh -c "
        chown -R ${PostgresUserId}:${PostgresUserId} /data 2>/dev/null || chown -R ${PostgresUserId}:0 /data
        chmod 755 /data
        find /data -type f -name '*.log' -exec chmod 644 {} \; 2>/dev/null || true
        find /data -type d -exec chmod 755 {} \; 2>/dev/null || true
        echo 'PostgreSQL日志目录权限修复完成'
    " 2>&1 | Out-Null

    # 重启PostgreSQL容器
    if ($Restart -or $PostgresStatus -eq "restarting") {
        Write-Log "启动PostgreSQL容器..."
        docker start postgresql-11.19 2>&1 | Out-Null
        
        # 等待PostgreSQL启动
        Write-Log "等待PostgreSQL启动..."
        $Count = 0
        $MaxRetries = 10
        $Started = $false
        
        do {
            Start-Sleep -Seconds 8
            $Count++
            Write-Log "检查PostgreSQL状态 (尝试 $Count/$MaxRetries)..."
            
            $Status = docker inspect postgresql-11.19 --format='{{.State.Status}}' 2>$null
            if ($Status -eq "running") {
                # 检查PostgreSQL服务
                $PgReady = docker exec postgresql-11.19 pg_isready -U postgres -p 3433 2>$null
                if ($LASTEXITCODE -eq 0) {
                    Write-Log "PostgreSQL启动成功，服务正常" "SUCCESS"
                    $Started = $true
                    break
                } else {
                    Write-Log "PostgreSQL容器运行中，但服务尚未就绪..." "INFO"
                }
            } elseif ($Status -eq "restarting") {
                Write-Log "PostgreSQL仍在重启中..." "WARN"
            }
        } while ($Count -lt $MaxRetries)

        if (-not $Started) {
            Write-Log "PostgreSQL启动检查超时" "WARN"
            Write-Log "请检查容器日志: docker logs postgresql-11.19" "INFO"
        }
    }

    # 最终状态检查
    Write-Log "最终状态检查..."
    $FinalStatus = docker inspect postgresql-11.19 --format='{{.State.Status}}' 2>$null
    Write-Log "PostgreSQL容器最终状态: $FinalStatus"
    
    if ($FinalStatus -eq "running") {
        Write-Log "PostgreSQL权限修复完成，服务正常运行" "SUCCESS"
        Write-Log "PostgreSQL访问地址: localhost:3433" "SUCCESS"
    } else {
        Write-Log "PostgreSQL可能仍有问题，建议查看日志: docker logs postgresql-11.19" "WARN"
    }

} catch {
    Write-Log "权限修复失败: $($_.Exception.Message)" "ERROR"
    exit 1
}

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "PostgreSQL权限修复脚本执行完成" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
