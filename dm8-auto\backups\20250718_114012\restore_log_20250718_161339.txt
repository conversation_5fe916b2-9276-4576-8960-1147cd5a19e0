[2025-07-18 16:13:39] [INFO] 开始DM8数据卷恢复
[2025-07-18 16:13:39] [INFO] 备份路径: D:\Code\MicrosoftCode\dm8-auto\backups\20250718_114012
[2025-07-18 16:13:39] [INFO] 恢复模式: 数据卷恢复 (包含完整数据库数据)
[2025-07-18 16:13:39] [INFO] 临时目录: D:\Code\MicrosoftCode\dm8-auto\temp_restore_20250718_161339
[2025-07-18 16:13:39] [SUCCESS] 临时目录创建成功
[2025-07-18 16:13:39] [INFO] 检查Docker环境...
[2025-07-18 16:13:39] [SUCCESS] Docker版本: Docker version 28.0.4, build b8034c0
[2025-07-18 16:13:39] [INFO] 验证备份文件...
[2025-07-18 16:13:39] [SUCCESS] 找到备份文件: dm8_data_volume.zip (5.13MB)
[2025-07-18 16:13:39] [SUCCESS] 找到备份文件: dm8_config_backup.zip (740.38MB)
[2025-07-18 16:13:39] [INFO] 停止现有服务...
[2025-07-18 16:13:45] [SUCCESS] 现有服务已停止
[2025-07-18 16:13:45] [INFO] 删除现有数据卷...
[2025-07-18 16:13:45] [SUCCESS] 已删除数据卷: dm8_database_data
[2025-07-18 16:13:45] [SUCCESS] 已删除数据卷: dm8_database_logs
[2025-07-18 16:13:45] [INFO] 恢复配置文件...
[2025-07-18 16:13:45] [SUCCESS] 已恢复: docker-compose.yml
[2025-07-18 16:13:45] [SUCCESS] 已恢复: Dockerfile
[2025-07-18 16:13:45] [SUCCESS] 配置文件恢复完成，已恢复 2 个文件
[2025-07-18 16:13:45] [INFO] 创建新数据卷...
[2025-07-18 16:13:45] [SUCCESS] 已创建数据卷: dm8_database_data
[2025-07-18 16:13:45] [SUCCESS] 已创建数据卷: dm8_database_logs
[2025-07-18 16:13:45] [INFO] 恢复DM8主数据卷...
[2025-07-18 16:14:06] [INFO] 修复数据目录权限...
[2025-07-18 16:14:07] [SUCCESS] DM8主数据卷恢复完成
[2025-07-18 16:14:07] [INFO] 恢复DM8日志数据卷...
[2025-07-18 16:14:07] [INFO] 修复日志目录权限...
[2025-07-18 16:14:08] [SUCCESS] DM8日志数据卷恢复完成
[2025-07-18 16:14:08] [INFO] 解压日志卷文件...
[2025-07-18 16:14:08] [INFO] 修复日志目录权限...
[2025-07-18 16:14:09] [SUCCESS] DM8日志数据卷恢复完成
[2025-07-18 16:14:09] [INFO] 启动DM8服务...
[2025-07-18 16:14:09] [INFO] 等待DM8启动...
[2025-07-18 16:14:09] [INFO] 等待60秒让DM8完全启动...
[2025-07-18 16:15:09] [SUCCESS] DM8容器启动成功
[2025-07-18 16:15:09] [SUCCESS] 数据卷恢复完成 - 包含完整数据库数据，无需额外SQL导入
[2025-07-18 16:15:09] [INFO] 验证恢复结果...
[2025-07-18 16:15:09] [SUCCESS] 容器状态: 运行中
[2025-07-18 16:15:09] [SUCCESS] 容器健康状态: 健康
[2025-07-18 16:15:09] [INFO] 测试数据库连接...
[2025-07-18 16:15:13] [WARN] 数据库连接: 暂时无法连接 (数据库可能仍在启动中)
[2025-07-18 16:15:13] [INFO] 建议等待几分钟后手动验证数据库状态
[2025-07-18 16:15:13] [INFO] 已清理临时目录
[2025-07-18 16:15:13] [SUCCESS] 恢复成功完成
[2025-07-18 16:15:13] [SUCCESS] DM8服务可在以下地址访问: localhost:5236
[2025-07-18 16:15:13] [SUCCESS] 用户名: SYSDBA, 密码: GDYtd@2025
[2025-07-18 16:15:13] [SUCCESS] docker exec -it dm8-database /home/<USER>/dmdbms/bin/disql SYSDBA/'"GDYtd@2025"'@localhost:5236
