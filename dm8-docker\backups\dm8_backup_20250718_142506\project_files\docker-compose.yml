services:
  dm8:
    image: dm8:latest
    container_name: dm8-server
    restart: unless-stopped
    ports:
      - "5236:5236"
    environment:
      - DM_PWD=GDYtd@2025
      - DM_DB_NAME=DAMENG
      - DM_INSTANCE_NAME=DMSERVER
      - DM_PORT=5236
    volumes:
      - dm8-data:/opt/dmdbms/data
      - dm8-logs:/opt/dmdbms/log
    healthcheck:
      test: ["CMD", "netstat", "-tlnp", "|", "grep", ":5236"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

volumes:
  dm8-data:
    driver: local
    name: dm8_data_volumes
  dm8-logs:
    driver: local
    name: dm8_logs_volumes


networks:
  default:
    name: dm8_docker_network
    driver: bridge
