services:
  postgresql:
    build:
      context: .
      dockerfile: Dockerfile
    image: postgresql:11.19
    container_name: postgresql-11.19
    restart: unless-stopped
    
    # 环境变量配置
    environment:
      - PG_USER=postgres
      - PG_PASSWORD=postgres
      - PG_DB=postgres
      - PG_PORT=3433
      - PGDATA=/var/lib/postgresql/data
    
    # 端口映射
    ports:
      - "3433:3433"
    
    # 数据卷挂载（使用Docker数据卷 - 最稳定方案）
    volumes:
      # 数据目录：使用Docker数据卷
      - postgres_data_online:/var/lib/postgresql/data
      # 日志目录：使用Docker数据卷
      - postgres_logs_online:/var/log/postgresql
      # 初始化脚本：只读挂载（主机目录）
      - ./init-scripts:/docker-entrypoint-initdb.d:ro
      # 备份目录：读写挂载（主机目录）
      - ./backups:/var/backups/postgresql
    
    # 健康检查
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -p 3433"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    
    # 网络配置
    networks:
      - postgres_network

  # PostgreSQL管理工具 - pgAdmin (可选)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pgadmin4
    restart: unless-stopped
    
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=<EMAIL>
      - PGADMIN_LISTEN_PORT=80
    
    ports:
      - "8080:80"
    
    volumes:
      # pgAdmin数据：使用Docker数据卷
      - pgadmin_data_online:/var/lib/pgadmin
    
    networks:
      - postgres_network
    
    depends_on:
      postgresql:
        condition: service_healthy



# 数据卷定义
volumes:
  postgres_data_online:
    driver: local
    name: postgresql_11_19_data_online
  postgres_logs_online:
    driver: local
    name: postgresql_11_19_logs_online
  pgadmin_data_online:
    driver: local
    name: pgadmin_data_online

# 网络定义
networks:
  postgres_network:
    driver: bridge
    name: postgresql_network
