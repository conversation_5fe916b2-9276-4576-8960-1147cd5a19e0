{"app": {"name": "临时邮箱桌面应用", "version": "1.0.0", "author": "TempMail Desktop Team", "description": "基于 pywebview 的临时邮箱桌面应用程序"}, "window": {"title": "临时邮箱 - TempMail Desktop", "width": 1200, "height": 800, "min_width": 800, "min_height": 600, "resizable": true, "shadow": true, "on_top": false}, "server": {"host": "127.0.0.1", "port": 5000, "debug": false}, "database": {"path": "data/tempmail.db", "auto_cleanup": true, "cleanup_interval": 3600}, "email": {"default_domain": "mailto.plus", "default_duration": 60, "auto_refresh_interval": 30, "max_emails_display": 100}, "api": {"base_url": "https://tempmail.plus", "timeout": 30, "retry_count": 3, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "mode": "real", "comment_mode": "可选值: real(真实API), advanced(高级抓取), basic(模拟数据)", "use_selenium": false, "polling_interval": 10, "max_polling_attempts": 5, "discovered_endpoints": {"mails": "/api/mails/", "box_hidden": "/api/box/hidden", "attachments": "/attachments/0"}}, "ui": {"theme": "light", "language": "zh-CN", "sidebar_width": 300, "detail_width": 400}, "security": {"enable_pin": true, "auto_destroy_expired": true, "sanitize_html": true}, "logging": {"level": "INFO", "file": "logs/app.log", "max_size": "10MB", "backup_count": 5}}