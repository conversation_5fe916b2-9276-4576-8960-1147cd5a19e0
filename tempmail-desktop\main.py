#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时邮箱桌面应用程序主入口
使用 pywebview 创建桌面应用
"""

import webview
import threading
import time
import os
import sys
from backend.app import create_app, TempMailAPI

def get_resource_path(relative_path):
    """获取资源文件路径，支持打包后的应用"""
    try:
        # PyInstaller 创建的临时文件夹
        base_path = sys._MEIPASS
    except Exception:
        # 获取脚本所在目录作为基础路径
        base_path = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(base_path, relative_path)

def start_flask_app(api_instance):
    """启动 Flask 后端服务"""
    app = create_app(api_instance)  # 传递API实例
    # 尝试多个端口和主机配置，避免端口冲突和权限问题
    ports = [8001, 8002, 8003, 8004, 8005, 8006, 8007]
    hosts = ['127.0.0.1']

    for host in hosts:
        for port in ports:
            try:
                print(f"尝试启动 Flask 服务在 {host}:{port}")
                app.run(host=host, port=port, debug=False, use_reloader=False, threaded=True)
                return  # 成功启动，退出函数
            except OSError as e:
                print(f"主机 {host} 端口 {port} 不可用: {e}")
                continue

    # 如果所有配置都失败
    raise Exception("所有主机和端口配置都不可用，请检查系统设置和防火墙")

def main():
    """主函数"""
    # 创建 API 实例
    api = TempMailAPI()
    print(f"[TARGET] 主程序创建 API 实例: {id(api)}")

    # 在单独线程中启动 Flask 应用，传递API实例
    flask_thread = threading.Thread(target=start_flask_app, args=(api,), daemon=True)
    flask_thread.start()

    # 等待 Flask 服务启动
    time.sleep(2)

    # 获取前端文件路径
    frontend_path = get_resource_path('frontend')
    index_path = os.path.join(frontend_path, 'index.html')

    # 检查前端文件是否存在
    if not os.path.exists(index_path):
        print(f"错误: 找不到前端文件 {index_path}")
        return

    # 创建 webview 窗口
    window = webview.create_window(
        title='临时邮箱 - TempMail Desktop',
        url=f'file://{index_path}',
        width=1500,
        height=1150,
        min_size=(1350, 1000),
        resizable=True,
        js_api=api,  # 注入 Python API 到前端
        shadow=True,
        on_top=False
    )

    # 启动 webview
    webview.start(debug=False)

if __name__ == '__main__':
    main()
