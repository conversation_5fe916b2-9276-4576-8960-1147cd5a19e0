<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>银河麒麟v10 sp3 240服务器K8s二进制部署教程</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 13px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 机器标识样式 */
        .machine-tag {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            margin: 0 8px 12px 0;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .machine-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .machine-tag:hover::before {
            left: 100%;
        }

        .machine-master {
            background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
            color: white;
        }

        .machine-node {
            background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
            color: white;
        }

        .machine-all {
            background: linear-gradient(135deg, #f9ca24 0%, #f0932b 100%);
            color: white;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: var(--light-surface);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        th,
        td {
            padding: 18px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-decoration: none;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            z-index: 999;
            font-size: 20px;
        }

        .back-to-top:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .content-wrapper {
                padding: 30px;
            }

            .container {
                padding: 40px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
                border-radius: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 16px;
                margin-right: 15px;
            }
        }

        /* 打印样式 */
        @media print {

            .sidebar,
            .back-to-top,
            .mobile-menu-btn {
                display: none;
            }

            .main-content {
                margin-left: 0;
            }

            body {
                background: white;
                font-size: 12px;
            }

            .container {
                box-shadow: none;
                padding: 0;
            }

            pre {
                background: #f8f9fa;
                color: #333;
                border: 1px solid #ddd;
            }

            .info-box,
            .warning-box,
            .success-box,
            .danger-box {
                border: 1px solid #ddd;
                background: #f8f9fa;
            }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 12px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            transform: scale(1.1);
        }

        ::-webkit-scrollbar-thumb:active {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
        }

        /* 主内容区域滚动条 */
        .main-content::-webkit-scrollbar {
            width: 8px;
        }

        .main-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .main-content::-webkit-scrollbar-thumb {
            background: rgba(102, 126, 234, 0.3);
            border-radius: 4px;
        }

        .main-content::-webkit-scrollbar-thumb:hover {
            background: rgba(102, 126, 234, 0.6);
        }

        /* 滚动状态的视觉反馈 */


        /* 平滑滚动效果 */
        html {
            scroll-behavior: smooth;
        }






    </style>
</head>

<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-cubes"></i> K8s二进制部署</h2>
            <p>银河麒麟v10 sp3 240服务器</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#overview"><i class="fas fa-eye"></i>1. 概述</a></li>
                <li><a href="#environment-preparation"><i class="fas fa-cog"></i>2. 环境准备</a></li>
                <li><a href="#certificate-preparation"><i class="fas fa-key"></i>3. 证书准备</a></li>
                <li><a href="#etcd-deployment"><i class="fas fa-database"></i>4. ETCD集群部署</a></li>
                <li><a href="#master-deployment"><i class="fas fa-crown"></i>5. Master组件部署</a></li>
                <li><a href="#worker-deployment"><i class="fas fa-server"></i>6. Worker组件部署</a></li>
                <li><a href="#network-deployment"><i class="fas fa-network-wired"></i>7. 网络组件部署</a></li>
                <li><a href="#coredns-deployment"><i class="fas fa-globe"></i>8. CoreDNS部署</a></li>
                <li><a href="#dashboard-deployment"><i class="fas fa-chart-line"></i>9. Dashboard部署</a></li>
                <li><a href="#verify-deployment"><i class="fas fa-check-circle"></i>10. 验证部署</a></li>
                <li><a href="#troubleshooting"><i class="fas fa-bug"></i>11. 故障排查</a></li>
                <li><a href="#summary"><i class="fas fa-flag-checkered"></i>12. 总结</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-cubes"></i> 银河麒麟v10 sp3 240服务器K8s二进制部署教程</h1>

                <div class="info-box">
                    <strong><i class="fas fa-info-circle"></i>
                        教程说明：</strong>本教程详细介绍了如何在银河麒麟v10 sp3 240服务器上使用二进制方式部署Kubernetes(K8s)集群，包括环境准备、证书生成、各组件安装与配置等全过程。每个步骤都配有详细解释和预期效果，适合初学者按步骤完成部署。
                </div>

                <div class="warning-box">
                    <strong><i class="fas fa-exclamation-triangle"></i> 服务器配置说明：</strong>
                    <div style="margin-top: 15px;">
                        <span class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</span> -
                        IP：*************，4核8G内存<br>
                        <span class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</span> -
                        IP：*************，4核8G内存<br>
                        <span class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</span> -
                        需要执行的命令在所有节点上执行<br>
                    </div>
                    <div style="margin-top: 15px; padding: 10px; background: rgba(255, 193, 7, 0.1); border-left: 4px solid #ffc107;">
                        <strong><i class="fas fa-info-circle"></i> 银河麒麟v10sp3特别说明：</strong><br>
                        本教程已针对银河麒麟v10sp3系统进行优化，包含系统特殊配置和兼容性处理。
                    </div>
                </div>

                <!-- 概述部分 -->
                <section id="overview">
                    <h2><span class="step-number">1</span>概述</h2>

                    <h3><i class="fas fa-bookmark"></i> 1.1 什么是Kubernetes(K8s)</h3>
                    <p>Kubernetes(简称K8s)是Google开源的容器编排管理系统，它提供了应用部署、规划、更新、维护的一种机制。通过Kubernetes，我们可以：</p>
                    <ul style="list-style-type: none; padding-left: 20px;">
                        <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 自动化容器的部署和复制</li>
                        <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 随时扩展或收缩容器规模</li>
                        <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 将容器组织成组，并且提供容器间的负载均衡</li>
                        <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 很容易地升级应用程序容器的新版本</li>
                        <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 提供容器弹性，如果容器失效就替换它</li>
                    </ul>

                    <h3><i class="fas fa-sitemap"></i> 1.2 K8s集群架构</h3>
                    <p>Kubernetes集群主要由以下部分组成：</p>
                    <table>
                        <tr>
                            <th><i class="fas fa-layer-group"></i> 组件类型</th>
                            <th><i class="fas fa-box"></i> 组件名称</th>
                            <th><i class="fas fa-info-circle"></i> 功能描述</th>
                        </tr>
                        <tr>
                            <td rowspan="4">Master组件</td>
                            <td>kube-apiserver</td>
                            <td>提供集群管理的REST API接口，是集群控制的入口</td>
                        </tr>
                        <tr>
                            <td>kube-controller-manager</td>
                            <td>负责维护集群的状态，如故障检测、自动扩展、滚动更新等</td>
                        </tr>
                        <tr>
                            <td>kube-scheduler</td>
                            <td>负责资源的调度，按照预定的调度策略将Pod调度到相应的节点上</td>
                        </tr>
                        <tr>
                            <td>etcd</td>
                            <td>键值对数据库，保存了整个集群的状态</td>
                        </tr>
                        <tr>
                            <td rowspan="2">Node组件</td>
                            <td>kubelet</td>
                            <td>在每个节点上运行的代理，保证容器都运行在Pod中</td>
                        </tr>
                        <tr>
                            <td>kube-proxy</td>
                            <td>负责为Service提供集群内部的服务发现和负载均衡</td>
                        </tr>
                        <tr>
                            <td>网络插件</td>
                            <td>如Calico, Flannel等</td>
                            <td>实现容器之间的网络通信</td>
                        </tr>
                    </table>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 部署方式说明：</strong>本教程使用"二进制方式"部署K8s集群，相比kubeadm等工具化部署方式，二进制部署具有更高的灵活性和可控性，适合理解K8s组件工作原理，同时方便进行深度的定制化配置。
                    </div>

                    <h3><i class="fas fa-tasks"></i> 1.3 部署计划</h3>
                    <p>本次部署采用1个Master节点和1个Worker节点的最小化集群配置：</p>
                    <table>
                        <tr>
                            <th><i class="fas fa-network-wired"></i> 角色</th>
                            <th><i class="fas fa-server"></i> 主机名</th>
                            <th><i class="fas fa-project-diagram"></i> IP地址</th>
                            <th><i class="fas fa-boxes"></i> 部署组件</th>
                        </tr>
                        <tr>
                            <td>Master</td>
                            <td>k8s-master</td>
                            <td>*************</td>
                            <td>etcd、kube-apiserver、kube-controller-manager、kube-scheduler、kubectl</td>
                        </tr>
                        <tr>
                            <td>Worker</td>
                            <td>k8s-node1</td>
                            <td>*************</td>
                            <td>kubelet、kube-proxy、容器运行时(containerd)</td>
                        </tr>
                    </table>
                </section>

                <!-- 环境准备部分 -->
                <section id="environment-preparation">
                    <h2><span class="step-number">2</span>环境准备</h2>

                    <h3><i class="fas fa-server"></i> 2.1 硬件要求</h3>
                    <p>在银河麒麟v10 sp3 240服务器上部署Kubernetes，推荐的硬件配置如下：</p>
                    <table>
                        <tr>
                            <th><i class="fas fa-microchip"></i> 组件</th>
                            <th><i class="fas fa-info-circle"></i> 最低配置</th>
                            <th><i class="fas fa-thumbs-up"></i> 建议配置</th>
                            <th><i class="fas fa-comment"></i> 说明</th>
                        </tr>
                        <tr>
                            <td>CPU</td>
                            <td>2核</td>
                            <td>4核</td>
                            <td>Master节点CPU消耗较大</td>
                        </tr>
                        <tr>
                            <td>内存</td>
                            <td>4GB</td>
                            <td>8GB</td>
                            <td>建议Master节点至少8GB</td>
                        </tr>
                        <tr>
                            <td>硬盘</td>
                            <td>50GB</td>
                            <td>100GB+</td>
                            <td>系统盘建议SSD以提高性能</td>
                        </tr>
                        <tr>
                            <td>网卡</td>
                            <td>千兆网卡</td>
                            <td>万兆网卡</td>
                            <td>节点间通信较频繁</td>
                        </tr>
                    </table>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 本次部署硬件配置：</strong>
                        我们使用2台配置为4核8G内存的银河麒麟v10 sp3 240服务器，满足K8s集群的部署要求。
                    </div>

                    <h3 id="system-update"><i class="fas fa-sync-alt"></i> 2.1.1 系统更新和依赖环境准备</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <p>在开始Kubernetes部署之前，必须先更新系统并安装必要的依赖环境，这是确保部署成功的关键步骤。</p>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
                        系统更新和依赖安装是部署的第一步，跳过此步骤可能导致后续安装失败或运行异常。建议在网络状况良好时进行此操作。
                    </div>

                    <h4><i class="fas fa-download"></i> 更新系统软件包</h4>
                    <pre><code># 更新软件包索引
yum clean all
yum makecache

# 查看可更新的软件包
yum check-update

# 更新所有软件包（推荐）
yum update -y

# 或者只更新安全补丁（保守方式）
# yum update --security -y

# 验证系统版本
cat /etc/kylin-release
uname -r</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 更新说明：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- <strong>完整更新：</strong> 推荐使用 <code>yum update -y</code> 获得最新的系统补丁和驱动</li>
                            <li>- <strong>保守更新：</strong> 如果是生产环境，可以只更新安全补丁</li>
                            <li>- <strong>重启建议：</strong> 如果内核有更新，建议重启系统</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-tools"></i> 安装基础依赖工具</h4>
                    <pre><code># 安装基础开发工具和依赖
yum groupinstall -y "Development Tools"
yum install -y \
    wget \
    curl \
    vim \
    git \
    unzip \
    tar \
    gzip \
    net-tools \
    telnet \
    nmap \
    lsof \
    htop \
    iotop \
    sysstat \
    tcpdump \
    bind-utils \
    iputils \
    iproute \
    iptables \
    iptables-services \
    firewalld \
    chrony \
    rsync \
    tree \
    jq \
    yum-utils \
    device-mapper-persistent-data \
    lvm2

# 验证关键工具安装
which wget curl vim git
echo "基础工具安装完成"</code></pre>

                    <h4><i class="fas fa-puzzle-piece"></i> 安装容器相关依赖</h4>
                    <pre><code># 安装容器运行时依赖
yum install -y \
    container-selinux \
    libseccomp \
    libseccomp-devel

# 安装网络相关工具
yum install -y \
    bridge-utils \
    conntrack-tools \
    ipvsadm \
    ipset \
    socat \
    ebtables \
    ethtool

# 加载必要的内核模块
modprobe br_netfilter
modprobe ip_vs
modprobe ip_vs_rr
modprobe ip_vs_wrr
modprobe ip_vs_sh
modprobe nf_conntrack
modprobe overlay

# 确保开机自动加载
cat > /etc/modules-load.d/k8s.conf << EOF
br_netfilter
ip_vs
ip_vs_rr
ip_vs_wrr
ip_vs_sh
nf_conntrack
overlay
EOF

# 检查模块加载
lsmod | grep -E "br_netfilter|ip_vs|nf_conntrack|overlay"</code></pre>

                    <h4><i class="fas fa-clock"></i> 配置时间同步</h4>
                    <pre><code># 安装和配置chrony时间同步
yum install -y chrony

# 启动并设置开机启动
systemctl start chronyd
systemctl enable chronyd

# 配置时间服务器（使用国内NTP服务器）
cat > /etc/chrony.conf << EOF
# 使用国内NTP服务器
server ntp.aliyun.com iburst
server cn.pool.ntp.org iburst
server ntp.tuna.tsinghua.edu.cn iburst

# 其他配置保持默认
driftfile /var/lib/chrony/drift
makestep 1.0 3
rtcsync
logdir /var/log/chrony
EOF

# 重启chrony服务
systemctl restart chronyd

# 验证时间同步
chrony sources -v
timedatectl status</code></pre>

                    <h4><i class="fas fa-check-circle"></i> 系统准备验证</h4>
                    <pre><code># 创建系统检查脚本
cat > /tmp/system_check.sh << 'EOF'
#!/bin/bash

echo "=== 银河麒麟K8s部署前系统检查 ==="
echo

# 检查系统版本
echo "1. 系统版本检查："
cat /etc/kylin-release
uname -r
echo

# 检查内存和CPU
echo "2. 硬件资源检查："
echo "CPU核心数: $(nproc)"
echo "内存大小: $(free -h | grep Mem | awk '{print $2}')"
echo "磁盘空间: $(df -h / | tail -1 | awk '{print $4}') 可用"
echo

# 检查网络
echo "3. 网络检查："
echo "主网卡IP: $(ip route get ******* | awk '{print $7; exit}')"
ping -c 2 ******* > /dev/null && echo "外网连接: 正常" || echo "外网连接: 异常"
echo

# 检查关键服务
echo "4. 系统服务检查："
systemctl is-active chronyd && echo "时间同步: 正常" || echo "时间同步: 异常"
echo

# 检查内核模块
echo "5. 内核模块检查："
lsmod | grep -q br_netfilter && echo "br_netfilter: 已加载" || echo "br_netfilter: 未加载"
lsmod | grep -q ip_vs && echo "ip_vs: 已加载" || echo "ip_vs: 未加载"
lsmod | grep -q overlay && echo "overlay: 已加载" || echo "overlay: 未加载"
echo

# 检查必要工具
echo "6. 必要工具检查："
which wget > /dev/null && echo "wget: 已安装" || echo "wget: 未安装"
which curl > /dev/null && echo "curl: 已安装" || echo "curl: 未安装"
which git > /dev/null && echo "git: 已安装" || echo "git: 未安装"
which vim > /dev/null && echo "vim: 已安装" || echo "vim: 未安装"
echo

echo "=== 检查完成 ==="
echo "如果所有项目都显示正常，可以继续进行Kubernetes部署"
EOF

# 运行检查脚本
chmod +x /tmp/system_check.sh
/tmp/system_check.sh</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        检查脚本应该显示所有关键项目都正常，包括：系统版本正确、硬件资源充足、网络连接正常、关键服务状态正确、内核模块已加载、必要工具已安装。
                    </div>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- 如果内核有更新，请重启系统后再继续</li>
                            <li>- 确保所有节点都完成了相同的准备工作</li>
                            <li>- 如果检查脚本显示异常，请先解决问题再继续</li>
                            <li>- 建议在继续之前创建系统快照（如果是虚拟机）</li>
                        </ul>
                    </div>

                    <h3 id="system-config"><i class="fas fa-cog"></i> 2.1.2 银河麒麟系统检查与配置</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <p>在开始部署前，需要先检查和配置银河麒麟系统的特殊设置：</p>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 银河麒麟v10sp3 + K8s 1.26.3 关键兼容性要求：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>✓ 内核版本必须 >= 4.18（支持cgroup v2）</li>
                            <li>✓ 必须支持overlay和br_netfilter模块</li>
                            <li>✓ iptables版本必须兼容（建议 >= 1.6.0）</li>
                            <li>✓ 系统必须支持systemd cgroup驱动</li>
                            <li>✓ 确保runc版本 >= 1.1.0</li>
                        </ul>
                    </div>

                    <pre><code># 银河麒麟v10sp3 + K8s 1.26.3 兼容性检查脚本
cat > /tmp/kylin_k8s_check.sh << 'EOF'
#!/bin/bash
echo "=== 银河麒麟v10sp3 + Kubernetes 1.26.3 兼容性检查 ==="
echo

# 1. 检查系统版本
echo "1. 系统版本检查："
cat /etc/kylin-release 2>/dev/null || echo "警告：无法读取银河麒麟版本信息"
echo "内核版本: $(uname -r)"
KERNEL_VERSION=$(uname -r | cut -d. -f1,2)
if [ "$(echo "$KERNEL_VERSION >= 4.18" | bc 2>/dev/null)" = "1" ]; then
    echo "✓ 内核版本符合要求 (>= 4.18)"
else
    echo "✗ 内核版本过低，可能不支持cgroup v2"
fi
echo

# 2. 检查必要的内核模块
echo "2. 内核模块检查："
modprobe overlay 2>/dev/null && echo "✓ overlay模块加载成功" || echo "✗ overlay模块加载失败"
modprobe br_netfilter 2>/dev/null && echo "✓ br_netfilter模块加载成功" || echo "✗ br_netfilter模块加载失败"
modprobe ip_vs 2>/dev/null && echo "✓ ip_vs模块加载成功" || echo "✗ ip_vs模块加载失败"
echo

# 3. 检查cgroup支持
echo "3. cgroup支持检查："
if mount | grep -q cgroup2; then
    echo "✓ 系统支持cgroup v2"
elif mount | grep -q cgroup; then
    echo "⚠ 系统使用cgroup v1，建议升级到v2"
else
    echo "✗ 未检测到cgroup支持"
fi
echo

# 4. 检查iptables
echo "4. iptables检查："
IPTABLES_VERSION=$(iptables --version 2>/dev/null | grep -o 'v[0-9]\+\.[0-9]\+' | sed 's/v//')
if [ -n "$IPTABLES_VERSION" ]; then
    echo "✓ iptables版本: $IPTABLES_VERSION"
    if [ "$(echo "$IPTABLES_VERSION >= 1.6" | bc 2>/dev/null)" = "1" ]; then
        echo "✓ iptables版本符合要求"
    else
        echo "⚠ iptables版本较低，建议升级"
    fi
else
    echo "✗ 无法获取iptables版本"
fi
echo

# 5. 检查systemd
echo "5. systemd检查："
if systemctl --version >/dev/null 2>&1; then
    echo "✓ systemd可用"
    SYSTEMD_VERSION=$(systemctl --version | head -1 | grep -o '[0-9]\+')
    echo "systemd版本: $SYSTEMD_VERSION"
else
    echo "✗ systemd不可用"
fi
echo

# 6. 检查容器运行时依赖
echo "6. 容器运行时依赖检查："
which runc >/dev/null 2>&1 && echo "✓ runc已安装" || echo "⚠ runc未安装"
which containerd >/dev/null 2>&1 && echo "✓ containerd已安装" || echo "⚠ containerd未安装"
echo

echo "=== 检查完成 ==="
echo "如果有✗标记的项目，请先解决相关问题再继续部署"
EOF

chmod +x /tmp/kylin_k8s_check.sh
/tmp/kylin_k8s_check.sh

# 如果检查通过，继续配置
echo "继续银河麒麟系统配置..."

# 检查银河麒麟系统版本
cat /etc/kylin-release
cat /proc/version

# 检查系统架构
uname -m

# 检查内核版本（确保支持容器功能）
uname -r

# 配置银河麒麟的包管理器
yum clean all
yum makecache fast

# 检查并启用必要的内核模块
modprobe overlay
modprobe br_netfilter

# 确保开机自动加载
cat > /etc/modules-load.d/k8s.conf << EOF
overlay
br_netfilter
EOF

# 验证模块加载
lsmod | grep overlay
lsmod | grep br_netfilter

# 银河麒麟特殊检查：确保iptables工作正常
iptables --version
which iptables-legacy && echo "iptables-legacy可用" || echo "需要安装iptables-legacy"

# 检查cgroup v2支持（K8s 1.26重要）
mount | grep cgroup
ls -la /sys/fs/cgroup/</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        系统版本显示为银河麒麟v10 sp3，内核版本支持容器功能，必要的内核模块已加载。
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 银河麒麟特殊说明：</strong>
                        银河麒麟系统基于CentOS/RHEL，但可能有一些特殊的安全策略和配置。如果遇到权限问题，请检查系统的安全策略设置。<br>
                        <strong>重要提醒：</strong>确保内核版本支持cgroup v2，这对Kubernetes 1.26版本很重要。
                    </div>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 4核8G配置特别优化建议：</strong>
                        <pre><code># 针对4核8G配置的系统优化
cat > /tmp/4c8g_optimization.sh << 'EOF'
#!/bin/bash
echo "=== 4核8G配置优化脚本 ==="

# 1. 调整系统参数
cat >> /etc/sysctl.d/k8s-4c8g.conf << EOL
# 4核8G优化参数
vm.max_map_count = 262144
vm.swappiness = 1
net.core.somaxconn = 32768
net.ipv4.tcp_max_syn_backlog = 8192
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_keepalive_probes = 3
net.ipv4.tcp_keepalive_intvl = 15
EOL

# 2. 应用参数
sysctl -p /etc/sysctl.d/k8s-4c8g.conf

# 3. 调整文件描述符限制
cat >> /etc/security/limits.conf << EOL
# 4核8G配置优化
* soft nofile 65536
* hard nofile 65536
* soft nproc 32768
* hard nproc 32768
EOL

echo "4核8G优化配置完成"
EOF

chmod +x /tmp/4c8g_optimization.sh
/tmp/4c8g_optimization.sh</code></pre>
                    </div>

                    <h3><i class="fas fa-ban"></i> 2.2 关闭防火墙和SELinux</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <p>在所有节点执行以下命令，关闭防火墙和SELinux，以避免它们对Kubernetes网络通信的干扰：</p>

                    <pre><code># 配置SELinux（银河麒麟系统特殊处理）
# 检查SELinux状态
getenforce

# 临时禁用SELinux（重启后恢复）
setenforce 0

# 永久禁用SELinux
sed -i 's/^SELINUX=enforcing$/SELINUX=disabled/' /etc/selinux/config
sed -i 's/^SELINUX=permissive$/SELINUX=disabled/' /etc/selinux/config

# 关闭防火墙服务
systemctl stop firewalld
systemctl disable firewalld

# 查看防火墙和SELinux状态
systemctl status firewalld
getenforce

# 验证配置
echo "SELinux状态: $(getenforce)"
systemctl is-active firewalld && echo "防火墙: 运行中" || echo "防火墙: 已关闭"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        防火墙服务已关闭且已禁止开机启动，SELinux状态显示为"Disabled"或"Permissive"。
                    </div>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 注意事项：</strong>
                        在生产环境中，应当配置合理的防火墙规则而不是简单关闭。这里为了教程简单化，直接关闭了防火墙。
                    </div>

                    <h3><i class="fas fa-exchange-alt"></i> 2.3 关闭交换分区</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <p>Kubernetes建议关闭交换分区以确保服务性能和稳定性：</p>

                    <pre><code># 查看当前交换分区状态
free -h
swapon --show

# 临时关闭交换分区
swapoff -a

# 永久关闭交换分区（重启后依然生效）
sed -i '/swap/s/^/#/' /etc/fstab

# 验证交换分区是否已关闭
free -h | grep -i swap
echo "Swap应该显示为: 0B"

# 确认fstab中swap行已被注释
grep swap /etc/fstab</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        执行<code>free -m</code>命令后，应该看到swap行显示的值为0。
                    </div>

                    <h3><i class="fas fa-network-wired"></i> 2.4 配置主机名和IP映射</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <p>设置每个节点的主机名，并配置各节点的IP地址与主机名映射：</p>

                    <h4><i class="fas fa-search"></i> 2.4.0 获取节点IP地址</h4>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <pre><code># 查看网络接口和IP地址
ip addr show

# 获取主网卡IP地址
MASTER_IP=$(ip route get ******* | awk '{print $7; exit}')
echo "当前节点IP: $MASTER_IP"

# 测试网络连通性
ping -c 3 *******</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
                        本教程已配置好IP地址：Master节点(*************)，Worker节点(*************)。
                    </div>

                    <h4><i class="fas fa-tag"></i> 2.4.1 设置Master节点主机名</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 设置主机名
hostnamectl set-hostname k8s-master

# 验证主机名
hostname</code></pre>

                    <h4><i class="fas fa-tag"></i> 2.4.2 设置Worker节点主机名</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <pre><code># 设置主机名
hostnamectl set-hostname k8s-node1

# 验证主机名
hostname</code></pre>

                    <h4><i class="fas fa-edit"></i> 2.4.3 配置主机名映射</h4>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <pre><code># 编辑hosts文件
cat >> /etc/hosts << EOF
************* k8s-master
************* k8s-node1
EOF

# 测试主机名解析
ping -c 3 k8s-master
ping -c 3 k8s-node1</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        能够通过主机名互相ping通节点，表示主机名解析配置成功。
                    </div>

                    <h3><i class="fas fa-clock"></i> 2.5 同步系统时间</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <p>确保各节点时间同步，避免因时间不一致导致的各种问题：</p>

                    <pre><code># 安装chrony时间同步服务
yum install -y chrony

# 配置时间服务器（使用国内NTP服务器）
cat > /etc/chrony.conf << EOF
server ntp.aliyun.com iburst
server ntp1.aliyun.com iburst
server time1.cloud.tencent.com iburst
server cn.ntp.org.cn iburst
driftfile /var/lib/chrony/drift
makestep 1.0 3
rtcsync
logdir /var/log/chrony
EOF

# 启动时间同步服务
systemctl enable chronyd
systemctl restart chronyd

# 查看时间同步状态
chronyc sources -v
date</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        执行<code>chronyc sources -v</code>命令后，应看到已连接至NTP服务器，各节点时间一致。
                    </div>

                    <h3><i class="fas fa-cogs"></i> 2.6 内核参数优化</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <p>配置内核参数，优化网络性能与Kubernetes运行环境：</p>

                    <pre><code># 配置内核参数
cat > /etc/sysctl.d/k8s.conf << EOF
net.bridge.bridge-nf-call-ip6tables = 1
net.bridge.bridge-nf-call-iptables = 1
net.ipv4.ip_forward = 1
vm.swappiness = 0
vm.overcommit_memory = 1
vm.panic_on_oom = 0
fs.inotify.max_user_watches = 89100
fs.file-max = 52706963
fs.nr_open = 52706963
EOF

# 加载内核参数模块
modprobe br_netfilter

# 重新加载内核参数
sysctl -p /etc/sysctl.d/k8s.conf

# 验证内核模块加载情况
lsmod | grep br_netfilter</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        内核参数设置成功，br_netfilter模块已加载。
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 参数解释：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- net.bridge.bridge-nf-call-ip6tables = 1：允许ip6tables对bridge的数据进行处理</li>
                            <li>- net.bridge.bridge-nf-call-iptables = 1：允许iptables对bridge的数据进行处理</li>
                            <li>- net.ipv4.ip_forward = 1：开启IPv4转发，K8s集群中各Pod通信需要</li>
                            <li>- vm.swappiness = 0：尽量不使用交换分区</li>
                            <li>- vm.panic_on_oom = 0：发生OOM时不panic</li>
                            <li>- fs.inotify.max_user_watches = 89100：允许系统同时监控的文件数量</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-tools"></i> 2.7 安装基础工具包</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <p>安装部署K8s集群所需的基础工具：</p>

                    <pre><code># 配置银河麒麟系统的yum源（如果需要）
# 备份原有源配置
cp -r /etc/yum.repos.d /etc/yum.repos.d.backup

# 安装必备工具包
yum install -y wget vim net-tools telnet tree nmap sysstat lrzsz dos2unix bind-utils socat ipvsadm ipset conntrack ntpdate curl

# 安装容器相关工具
yum install -y yum-utils device-mapper-persistent-data lvm2

# 验证工具安装
which wget vim curl socat ipvsadm

# 更新系统(可选，建议在测试环境先验证)
# yum update -y</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        所有基础工具包安装成功，为后续操作做好准备。
                    </div>

                    <h3><i class="fas fa-folder-plus"></i> 2.8 创建工作目录</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <p>创建用于存放K8s二进制文件和配置的工作目录：</p>

                    <pre><code># 创建K8s工作目录
mkdir -p /opt/kubernetes/{bin,cfg,ssl,logs}

# 查看目录结构
ls -la /opt/kubernetes/</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        成功创建了Kubernetes工作目录及其子目录结构，为后续部署做好准备。
                    </div>

                    <h3><i class="fas fa-key"></i> 2.9 SSH互信配置（可选）</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>配置Master节点免密访问Worker节点，方便后续远程操作：</p>

                    <pre><code># 生成密钥对（一路回车即可）
ssh-keygen -t rsa

# 将公钥分发至各Worker节点
ssh-copy-id -i ~/.ssh/id_rsa.pub root@*************

# 测试SSH互信
ssh root@************* "hostname && exit"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        Master节点可以无需密码直接SSH登录Worker节点。
                    </div>

                    <h3><i class="fas fa-eye"></i> 2.10 环境检查清单</h3>
                    <p>在进行下一步之前，请检查以下项目是否已完成：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-tasks"></i> 检查项目</th>
                            <th><i class="fas fa-info-circle"></i> 检查命令</th>
                            <th><i class="fas fa-check-double"></i> 预期结果</th>
                        </tr>
                        <tr>
                            <td>防火墙状态</td>
                            <td>systemctl status firewalld</td>
                            <td>inactive (dead)</td>
                        </tr>
                        <tr>
                            <td>SELinux状态</td>
                            <td>getenforce</td>
                            <td>Disabled 或 Permissive</td>
                        </tr>
                        <tr>
                            <td>SWAP状态</td>
                            <td>free -m | grep Swap</td>
                            <td>Swap总值为0</td>
                        </tr>
                        <tr>
                            <td>主机名解析</td>
                            <td>ping -c 1 k8s-master<br>ping -c 1 k8s-node1</td>
                            <td>能够解析对应IP并ping通</td>
                        </tr>
                        <tr>
                            <td>系统时间同步</td>
                            <td>chronyc sources</td>
                            <td>显示已连接至NTP服务器</td>
                        </tr>
                        <tr>
                            <td>内核模块加载</td>
                            <td>lsmod | grep br_netfilter</td>
                            <td>显示br_netfilter已加载</td>
                        </tr>
                    </table>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
                        请确保上述所有检查项都符合预期结果，再继续下一步操作。环境准备工作是整个集群部署成功的基础！
                    </div>

                    <h3><i class="fas fa-clipboard-list"></i> 2.11 银河麒麟系统特殊检查清单</h3>
                    <p>针对银河麒麟v10sp3系统的额外检查项目：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-tasks"></i> 检查项目</th>
                            <th><i class="fas fa-terminal"></i> 检查命令</th>
                            <th><i class="fas fa-check-double"></i> 预期结果</th>
                        </tr>
                        <tr>
                            <td>系统版本确认</td>
                            <td>cat /etc/kylin-release</td>
                            <td>显示银河麒麟v10 sp3相关信息</td>
                        </tr>
                        <tr>
                            <td>内核模块支持</td>
                            <td>lsmod | grep -E "overlay|br_netfilter"</td>
                            <td>显示overlay和br_netfilter模块已加载</td>
                        </tr>
                        <tr>
                            <td>容器运行时支持</td>
                            <td>which runc && runc --version</td>
                            <td>显示runc版本信息</td>
                        </tr>
                        <tr>
                            <td>网络工具可用性</td>
                            <td>which iptables ipvsadm</td>
                            <td>显示工具路径</td>
                        </tr>
                        <tr>
                            <td>系统资源充足</td>
                            <td>free -h && df -h</td>
                            <td>内存8G，磁盘可用空间>50G</td>
                        </tr>
                    </table>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 银河麒麟系统提示：</strong>
                        如果在检查过程中发现任何问题，请参考银河麒麟官方文档或联系系统管理员。某些企业版本可能有额外的安全策略需要调整。
                    </div>

                </section>

                <!-- IP配置向导 -->
                <section id="ip-configuration-guide">
                    <h2><span class="step-number">2.5</span>IP配置向导</h2>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要：IP地址配置</strong><br>
                        本教程已配置好IP地址：Master节点(*************)，Worker节点(*************)。所有配置文件中的IP地址都已设置完成。
                    </div>

                    <h3><i class="fas fa-network-wired"></i> IP地址配置表</h3>
                    <p>请填写下表，并在后续配置中使用这些IP地址：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-server"></i> 节点角色</th>
                            <th><i class="fas fa-tag"></i> 主机名</th>
                            <th><i class="fas fa-network-wired"></i> IP地址</th>
                            <th><i class="fas fa-check"></i> 已配置IP</th>
                        </tr>
                        <tr>
                            <td>Master节点</td>
                            <td>k8s-master</td>
                            <td>示例：*************</td>
                            <td style="background: #d4edda; padding: 10px;"><strong>*************</strong></td>
                        </tr>
                        <tr>
                            <td>Worker节点</td>
                            <td>k8s-node1</td>
                            <td>示例：*************</td>
                            <td style="background: #d4edda; padding: 10px;"><strong>*************</strong></td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-terminal"></i> 获取IP地址的命令</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <pre><code># 方法1：查看所有网络接口
ip addr show

# 方法2：获取默认路由的IP
ip route get ******* | awk '{print $7; exit}'

# 方法3：查看主网卡IP
hostname -I | awk '{print $1}'

# 方法4：使用ifconfig（如果可用）
ifconfig | grep -E "inet.*broadcast" | awk '{print $2}'</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 配置完成确认：</strong>
                        确保两台服务器可以通过IP地址互相ping通，然后记录IP地址用于后续配置。
                    </div>

                </section>

                <!-- 证书准备部分 -->
                <section id="certificate-preparation">
                    <h2><span class="step-number">3</span>证书准备</h2>

                    <h3><i class="fas fa-unlock-alt"></i> 3.1 安装CFSSL工具</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>CFSSL是CloudFlare开源的证书管理工具，用于生成TLS证书。我们首先在Master节点上安装：</p>

                    <pre><code># 创建工作目录
mkdir -p /opt/ssl && cd /opt/ssl

# 下载CFSSL工具（使用多个镜像源确保下载成功）
# 方法1：从官方源下载
wget https://pkg.cfssl.org/R1.2/cfssl_linux-amd64 -O /usr/local/bin/cfssl || \
# 方法2：从GitHub下载
wget https://github.com/cloudflare/cfssl/releases/download/v1.6.4/cfssl_1.6.4_linux_amd64 -O /usr/local/bin/cfssl

wget https://pkg.cfssl.org/R1.2/cfssljson_linux-amd64 -O /usr/local/bin/cfssljson || \
wget https://github.com/cloudflare/cfssl/releases/download/v1.6.4/cfssljson_1.6.4_linux_amd64 -O /usr/local/bin/cfssljson

wget https://pkg.cfssl.org/R1.2/cfssl-certinfo_linux-amd64 -O /usr/local/bin/cfssl-certinfo || \
wget https://github.com/cloudflare/cfssl/releases/download/v1.6.4/cfssl-certinfo_1.6.4_linux_amd64 -O /usr/local/bin/cfssl-certinfo

# 添加可执行权限
chmod +x /usr/local/bin/cfssl*

# 验证安装
cfssl version
cfssljson --version
cfssl-certinfo --version</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        输出CFSSL工具的版本信息，表示安装成功。
                    </div>

                    <h3><i class="fas fa-file-signature"></i> 3.2 创建证书颁发机构(CA)</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>创建自签名CA证书，后续K8s集群各组件证书由此CA签发：</p>

                    <h4><i class="fas fa-scroll"></i> 3.2.1 创建CA配置文件</h4>
                    <pre><code># 切换到证书目录
cd /opt/ssl

# 创建CA配置文件
cat > ca-config.json << EOF
{
  "signing": {
    "default": {
      "expiry": "87600h"
    },
    "profiles": {
      "kubernetes": {
        "usages": [
          "signing",
          "key encipherment",
          "server auth",
          "client auth"
        ],
        "expiry": "87600h"
      }
    }
  }
}
EOF

# 创建CA证书请求文件
cat > ca-csr.json << EOF
{
  "CN": "kubernetes",
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "ST": "Beijing",
      "L": "Beijing",
      "O": "k8s",
      "OU": "System"
    }
  ]
}
EOF</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 文件参数说明：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- CN：Common Name，证书的主题名称</li>
                            <li>- key：指定算法和密钥长度</li>
                            <li>- C：Country，国家</li>
                            <li>- ST：State，州/省</li>
                            <li>- L：Locality，城市</li>
                            <li>- O：Organization，组织</li>
                            <li>- OU：Organization Unit，组织单位</li>
                            <li>- expiry：证书有效期，本例设置为10年(87600小时)</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-certificate"></i> 3.2.2 生成CA证书和私钥</h4>
                    <pre><code># 生成CA证书和私钥
cfssl gencert -initca ca-csr.json | cfssljson -bare ca

# 查看生成的证书和私钥
ls -l ca*.pem</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        成功生成CA证书(ca.pem)和CA私钥(ca-key.pem)。
                    </div>

                    <h3><i class="fas fa-key"></i> 3.3 生成etcd证书</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>为etcd集群生成服务端和客户端证书：</p>

                    <pre><code># 设置IP变量（已配置实际IP地址）
MASTER_IP="*************"
WORKER_IP="*************"

# 创建etcd证书请求文件
cat > etcd-csr.json << EOF
{
  "CN": "etcd",
  "hosts": [
    "127.0.0.1",
    "$MASTER_IP",
    "$WORKER_IP"
  ],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "ST": "Beijing",
      "L": "Beijing",
      "O": "k8s",
      "OU": "System"
    }
  ]
}
EOF

# 配置实际IP地址：
MASTER_IP="*************"
WORKER_IP="*************"

# 生成etcd证书和私钥
cfssl gencert -ca=ca.pem -ca-key=ca-key.pem -config=ca-config.json -profile=kubernetes etcd-csr.json | cfssljson -bare etcd

# 查看生成的etcd证书和私钥
ls -l etcd*.pem</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        成功生成etcd服务器证书(etcd.pem)和私钥(etcd-key.pem)。
                    </div>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> IP配置确认：</strong>
                        本教程使用的IP地址配置：Master节点(*************)，Worker节点(*************)。
                    </div>

                    <h3><i class="fas fa-key"></i> 3.4 生成Kubernetes证书</h3>

                    <h4><i class="fas fa-laptop-code"></i> 3.4.1 生成kube-apiserver证书</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>生成kube-apiserver使用的证书和私钥：</p>

                    <pre><code># 创建kube-apiserver证书请求文件
cat > kubernetes-csr.json << EOF
{
  "CN": "kubernetes",
  "hosts": [
    "127.0.0.1",
    "*************",
    "*************",
    "*********",
    "kubernetes",
    "kubernetes.default",
    "kubernetes.default.svc",
    "kubernetes.default.svc.cluster",
    "kubernetes.default.svc.cluster.local"
  ],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "ST": "Beijing",
      "L": "Beijing",
      "O": "k8s",
      "OU": "System"
    }
  ]
}
EOF

# 生成kubernetes证书和私钥
cfssl gencert -ca=ca.pem -ca-key=ca-key.pem -config=ca-config.json -profile=kubernetes kubernetes-csr.json | cfssljson -bare kubernetes

# 查看生成的证书和私钥
ls -l kubernetes*.pem</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 证书说明：</strong>
                        hosts字段中包括：<br>
                        - 集群所有节点IP(*************,*************)<br>
                        - localhost地址(127.0.0.1)<br>
                        - 集群内部Service网络地址(*********)<br>
                        - Kubernetes服务名及其所有域名变体
                    </div>

                    <h4><i class="fas fa-user-shield"></i> 3.4.2 生成kube-controller-manager证书</h4>
                    <pre><code># 创建kube-controller-manager证书请求文件
cat > kube-controller-manager-csr.json << EOF
{
  "CN": "system:kube-controller-manager",
  "hosts": [
    "127.0.0.1",
    "*************"
  ],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "ST": "Beijing",
      "L": "Beijing",
      "O": "system:kube-controller-manager",
      "OU": "System"
    }
  ]
}
EOF

# 生成kube-controller-manager证书和私钥
cfssl gencert -ca=ca.pem -ca-key=ca-key.pem -config=ca-config.json -profile=kubernetes kube-controller-manager-csr.json | cfssljson -bare kube-controller-manager

# 查看生成的证书和私钥
ls -l kube-controller-manager*.pem</code></pre>

                    <h4><i class="fas fa-calendar-check"></i> 3.4.3 生成kube-scheduler证书</h4>
                    <pre><code># 创建kube-scheduler证书请求文件
cat > kube-scheduler-csr.json << EOF
{
  "CN": "system:kube-scheduler",
  "hosts": [
    "127.0.0.1",
    "*************"
  ],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "ST": "Beijing",
      "L": "Beijing",
      "O": "system:kube-scheduler",
      "OU": "System"
    }
  ]
}
EOF

# 生成kube-scheduler证书和私钥
cfssl gencert -ca=ca.pem -ca-key=ca-key.pem -config=ca-config.json -profile=kubernetes kube-scheduler-csr.json | cfssljson -bare kube-scheduler

# 查看生成的证书和私钥
ls -l kube-scheduler*.pem</code></pre>

                    <h4><i class="fas fa-users-cog"></i> 3.4.4 生成admin用户证书</h4>
                    <pre><code># 创建admin证书请求文件
cat > admin-csr.json << EOF
{
  "CN": "admin",
  "hosts": [],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "ST": "Beijing",
      "L": "Beijing",
      "O": "system:masters",
      "OU": "System"
    }
  ]
}
EOF

# 生成admin证书和私钥
cfssl gencert -ca=ca.pem -ca-key=ca-key.pem -config=ca-config.json -profile=kubernetes admin-csr.json | cfssljson -bare admin

# 查看生成的证书和私钥
ls -l admin*.pem</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        成功生成admin证书(admin.pem)和私钥(admin-key.pem)，用于集群管理员认证。
                    </div>

                    <h4><i class="fas fa-cloud"></i> 3.4.5 生成kube-proxy证书</h4>
                    <pre><code># 创建kube-proxy证书请求文件
cat > kube-proxy-csr.json << EOF
{
  "CN": "system:kube-proxy",
  "hosts": [],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "ST": "Beijing",
      "L": "Beijing",
      "O": "k8s",
      "OU": "System"
    }
  ]
}
EOF

# 生成kube-proxy证书和私钥
cfssl gencert -ca=ca.pem -ca-key=ca-key.pem -config=ca-config.json -profile=kubernetes kube-proxy-csr.json | cfssljson -bare kube-proxy

# 查看生成的证书和私钥
ls -l kube-proxy*.pem</code></pre>

                    <h3><i class="fas fa-folder-open"></i> 3.5 分发证书</h3>

                    <h4><i class="fas fa-copy"></i> 3.5.1 分发证书到Master节点</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建证书存放目录
mkdir -p /opt/kubernetes/ssl

# 复制所有证书到指定目录
cp *.pem /opt/kubernetes/ssl/

# 验证证书复制结果
ls -l /opt/kubernetes/ssl/</code></pre>

                    <h4><i class="fas fa-share-alt"></i> 3.5.2 分发证书到Worker节点</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建Worker节点证书目录
ssh root@************* "mkdir -p /opt/kubernetes/ssl"

# 复制需要的证书到Worker节点
scp ca.pem kubernetes.pem kubernetes-key.pem kube-proxy.pem kube-proxy-key.pem root@*************:/opt/kubernetes/ssl/

# 验证证书传输结果
ssh root@************* "ls -l /opt/kubernetes/ssl/"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        证书文件成功分发到Worker节点的/opt/kubernetes/ssl/目录下。
                    </div>

                    <h3><i class="fas fa-clipboard-check"></i> 3.6 证书准备完成检查</h3>
                    <p>在进行下一步之前，请确保各个节点上都有正确的证书文件：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-server"></i> 节点</th>
                            <th><i class="fas fa-file-alt"></i> 应有证书</th>
                            <th><i class="fas fa-check-circle"></i> 检查命令</th>
                        </tr>
                        <tr>
                            <td rowspan="8">Master节点</td>
                            <td>CA证书(ca.pem, ca-key.pem)</td>
                            <td rowspan="8">ls -l /opt/kubernetes/ssl/</td>
                        </tr>
                        <tr>
                            <td>etcd证书(etcd.pem, etcd-key.pem)</td>
                        </tr>
                        <tr>
                            <td>kubernetes证书(kubernetes.pem, kubernetes-key.pem)</td>
                        </tr>
                        <tr>
                            <td>controller-manager证书(kube-controller-manager.pem, kube-controller-manager-key.pem)</td>
                        </tr>
                        <tr>
                            <td>scheduler证书(kube-scheduler.pem, kube-scheduler-key.pem)</td>
                        </tr>
                        <tr>
                            <td>admin证书(admin.pem, admin-key.pem)</td>
                        </tr>
                        <tr>
                            <td>proxy证书(kube-proxy.pem, kube-proxy-key.pem)</td>
                        </tr>
                        <tr>
                            <td>所有组件证书</td>
                        </tr>
                        <tr>
                            <td rowspan="2">Worker节点</td>
                            <td>CA证书(ca.pem)</td>
                            <td rowspan="2">ls -l /opt/kubernetes/ssl/</td>
                        </tr>
                        <tr>
                            <td>kube-proxy证书(kube-proxy.pem, kube-proxy-key.pem)</td>
                        </tr>
                    </table>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 证书安全提醒：</strong>
                        证书文件非常重要，请确保它们的安全性，并且不要将它们放在公开的地方。尤其是包含私钥的文件，一旦泄露可能导致集群安全隐患。
                    </div>

                </section>

                <!-- ETCD集群部署部分 -->
                <section id="etcd-deployment">
                    <h2><span class="step-number">4</span>ETCD集群部署</h2>

                    <h3><i class="fas fa-database"></i> 4.1 什么是ETCD</h3>
                    <p>ETCD是一个分布式、可靠的键值存储系统，用于存储Kubernetes集群的所有数据。在Kubernetes中，ETCD存储了集群的配置数据、状态信息和元数据等关键信息。在本教程中，我们将在Master节点上部署ETCD服务。</p>

                    <h3><i class="fas fa-cloud-download-alt"></i> 4.2 下载ETCD二进制文件</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建下载目录
mkdir -p /opt/etcd && cd /opt/etcd

# 下载ETCD（v3.5.10版本）
wget https://github.com/etcd-io/etcd/releases/download/v3.5.10/etcd-v3.5.10-linux-amd64.tar.gz

# 解压文件
tar -zxvf etcd-v3.5.10-linux-amd64.tar.gz

# 复制二进制文件到指定目录
cp etcd-v3.5.10-linux-amd64/etcd* /usr/local/bin/

# 验证安装
etcd --version
etcdctl version</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        显示etcd和etcdctl的版本信息，确认二进制文件安装成功。
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 版本兼容性说明：</strong>
                        本教程使用v3.5.10版本的ETCD，这是与Kubernetes 1.26.3完全兼容的稳定版本。<br>
                        <strong>重要：</strong>银河麒麟v10sp3系统已验证与此版本组合兼容。
                    </div>

                    <h3><i class="fas fa-folder-plus"></i> 4.3 创建ETCD工作目录</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建数据和配置目录
mkdir -p /opt/etcd/data
mkdir -p /opt/etcd/cfg
mkdir -p /var/lib/etcd/default.etcd

# 设置目录权限
chmod 755 -R /opt/etcd/
chmod 755 -R /var/lib/etcd/</code></pre>

                    <h3><i class="fas fa-cog"></i> 4.4 创建ETCD配置文件</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 切换到ETCD配置目录
cd /opt/etcd/cfg

# 创建ETCD配置文件
cat > etcd.conf << EOF
#[Member]
ETCD_NAME="etcd-1"
ETCD_DATA_DIR="/var/lib/etcd/default.etcd"
ETCD_LISTEN_PEER_URLS="https://*************:2380"
ETCD_LISTEN_CLIENT_URLS="https://*************:2379,http://127.0.0.1:2379"

#[Clustering]
ETCD_INITIAL_ADVERTISE_PEER_URLS="https://*************:2380"
ETCD_ADVERTISE_CLIENT_URLS="https://*************:2379"
ETCD_INITIAL_CLUSTER="etcd-1=https://*************:2380"
ETCD_INITIAL_CLUSTER_TOKEN="etcd-cluster"
ETCD_INITIAL_CLUSTER_STATE="new"

#[Security]
ETCD_CERT_FILE="/opt/kubernetes/ssl/etcd.pem"
ETCD_KEY_FILE="/opt/kubernetes/ssl/etcd-key.pem"
ETCD_TRUSTED_CA_FILE="/opt/kubernetes/ssl/ca.pem"
ETCD_PEER_CERT_FILE="/opt/kubernetes/ssl/etcd.pem"
ETCD_PEER_KEY_FILE="/opt/kubernetes/ssl/etcd-key.pem"
ETCD_PEER_TRUSTED_CA_FILE="/opt/kubernetes/ssl/ca.pem"
EOF</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 配置文件说明：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- ETCD_NAME: 节点名称</li>
                            <li>- ETCD_DATA_DIR: 数据目录</li>
                            <li>- ETCD_LISTEN_PEER_URLS: 集群通信监听地址</li>
                            <li>- ETCD_LISTEN_CLIENT_URLS: 客户端访问监听地址</li>
                            <li>- ETCD_INITIAL_CLUSTER: 集群节点列表</li>
                            <li>- ETCD_CERT_FILE/ETCD_KEY_FILE: TLS证书和私钥</li>
                            <li>- ETCD_TRUSTED_CA_FILE: CA证书</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-file-alt"></i> 4.5 创建ETCD系统服务文件</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建systemd服务文件
cat > /usr/lib/systemd/system/etcd.service << EOF
[Unit]
Description=Etcd Server
After=network.target
After=network-online.target
Wants=network-online.target

[Service]
Type=notify
EnvironmentFile=/opt/etcd/cfg/etcd.conf
ExecStart=/usr/local/bin/etcd \\
  --logger=zap
ExecReload=/bin/kill -s HUP \$MAINPID
Restart=on-failure
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 注意：</strong>
                        由于日志相关问题，我们这里使用的是zap日志记录器，避免不必要的报错。
                    </div>

                    <h3><i class="fas fa-play-circle"></i> 4.6 启动ETCD服务</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 重新加载systemd配置
systemctl daemon-reload

# 设置开机启动
systemctl enable etcd

# 启动etcd服务
systemctl start etcd

# 查看服务状态
systemctl status etcd</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        服务状态显示为"active (running)"，表示etcd服务成功启动。
                    </div>

                    <h3><i class="fas fa-check-double"></i> 4.7 验证ETCD集群</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>使用etcdctl命令行工具验证ETCD集群状态：</p>

                    <pre><code># 设置环境变量（注意使用v3版本API）
export ETCDCTL_API=3

# 查看集群状态
etcdctl --cacert=/opt/kubernetes/ssl/ca.pem \
  --cert=/opt/kubernetes/ssl/etcd.pem \
  --key=/opt/kubernetes/ssl/etcd-key.pem \
  --endpoints=https://*************:2379 \
  endpoint health

# 查看集群成员
etcdctl --cacert=/opt/kubernetes/ssl/ca.pem \
  --cert=/opt/kubernetes/ssl/etcd.pem \
  --key=/opt/kubernetes/ssl/etcd-key.pem \
  --endpoints=https://*************:2379 \
  member list</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        第一个命令显示"https://*************:2379 is healthy"，表示etcd节点正常。<br>
                        第二个命令显示集群成员信息，包括成员ID、名称和状态等。
                    </div>

                    <h3><i class="fas fa-tools"></i> 4.8 ETCD常用操作</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>以下是一些ETCD的常用操作命令，用于后续维护和故障排查：</p>

                    <pre><code># 写入测试数据
etcdctl --cacert=/opt/kubernetes/ssl/ca.pem \
  --cert=/opt/kubernetes/ssl/etcd.pem \
  --key=/opt/kubernetes/ssl/etcd-key.pem \
  --endpoints=https://*************:2379 \
  put /test/key1 "Hello Kubernetes"

# 读取测试数据
etcdctl --cacert=/opt/kubernetes/ssl/ca.pem \
  --cert=/opt/kubernetes/ssl/etcd.pem \
  --key=/opt/kubernetes/ssl/etcd-key.pem \
  --endpoints=https://*************:2379 \
  get /test/key1

# 查看ETCD版本
etcdctl --cacert=/opt/kubernetes/ssl/ca.pem \
  --cert=/opt/kubernetes/ssl/etcd.pem \
  --key=/opt/kubernetes/ssl/etcd-key.pem \
  --endpoints=https://*************:2379 \
  version

# 查看集群状态信息
etcdctl --cacert=/opt/kubernetes/ssl/ca.pem \
  --cert=/opt/kubernetes/ssl/etcd.pem \
  --key=/opt/kubernetes/ssl/etcd-key.pem \
  --endpoints=https://*************:2379 \
  endpoint status --write-out=table</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 小技巧：</strong>
                        为了简化etcdctl命令，可以把证书参数设置为环境变量：
                        <pre><code># 设置环境变量
export ETCDCTL_API=3
export ETCDCTL_CACERT=/opt/kubernetes/ssl/ca.pem
export ETCDCTL_CERT=/opt/kubernetes/ssl/etcd.pem
export ETCDCTL_KEY=/opt/kubernetes/ssl/etcd-key.pem
export ETCDCTL_ENDPOINTS=https://*************:2379

# 简化命令示例
etcdctl member list
etcdctl endpoint health</code></pre>
                    </div>

                    <h3><i class="fas fa-clipboard-check"></i> 4.9 ETCD部署完成检查</h3>
                    <p>在进行下一步之前，请检查以下项目确保ETCD部署成功：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-tasks"></i> 检查项目</th>
                            <th><i class="fas fa-terminal"></i> 检查命令</th>
                            <th><i class="fas fa-check"></i> 预期结果</th>
                        </tr>
                        <tr>
                            <td>服务状态</td>
                            <td>systemctl status etcd</td>
                            <td>active (running)</td>
                        </tr>
                        <tr>
                            <td>监听端口</td>
                            <td>netstat -lntp | grep etcd</td>
                            <td>显示2379和2380端口</td>
                        </tr>
                        <tr>
                            <td>健康状态</td>
                            <td>etcdctl endpoint health</td>
                            <td>显示节点健康状态</td>
                        </tr>
                        <tr>
                            <td>数据写入</td>
                            <td>etcdctl put /test/key "value"<br>etcdctl get /test/key</td>
                            <td>能够正常写入和读取数据</td>
                        </tr>
                    </table>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
                        ETCD是Kubernetes集群的核心组件，存储着所有的集群数据，确保它正确部署和配置是非常重要的。在继续下一步之前，请务必确认ETCD服务运行正常。
                    </div>

                </section>

                <!-- Master组件部署部分 -->
                <section id="master-deployment">
                    <h2><span class="step-number">5</span>Master组件部署</h2>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> Master组件介绍：</strong>
                        Kubernetes Master节点上需要部署的核心组件包括：kube-apiserver、kube-controller-manager和kube-scheduler。这三个组件共同构成了Kubernetes的控制平面，负责集群的管理和协调工作。
                    </div>

                    <h3><i class="fas fa-cloud-download-alt"></i> 5.1 下载Kubernetes二进制文件</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>首先，下载Kubernetes各组件的二进制文件：</p>

                    <pre><code># 创建下载目录
mkdir -p /opt/k8s && cd /opt/k8s

# 下载Kubernetes v1.26.3版本二进制文件（使用多个镜像源）
# 方法1：从官方源下载
wget https://dl.k8s.io/v1.26.3/kubernetes-server-linux-amd64.tar.gz || \
# 方法2：从阿里云镜像下载
wget https://kubernetes.oss-cn-hangzhou.aliyuncs.com/kubernetes-release/release/v1.26.3/kubernetes-server-linux-amd64.tar.gz || \
# 方法3：从GitHub下载
wget https://github.com/kubernetes/kubernetes/releases/download/v1.26.3/kubernetes-server-linux-amd64.tar.gz

# 验证下载文件
ls -lh kubernetes-server-linux-amd64.tar.gz

# 解压文件
tar -zxvf kubernetes-server-linux-amd64.tar.gz

# 将二进制文件复制到指定目录
cd kubernetes/server/bin/
cp kube-apiserver kube-controller-manager kube-scheduler kubectl /opt/kubernetes/bin/

# 验证二进制文件
cd /opt/kubernetes/bin/
chmod +x kube*
./kubectl version --client</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        显示kubectl的版本信息，表示二进制文件已成功安装到指定目录。
                    </div>

                    <h3><i class="fas fa-server"></i> 5.2 部署kube-apiserver</h3>

                    <h4><i class="fas fa-cog"></i> 5.2.1 创建kube-apiserver配置文件</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建配置和日志目录
mkdir -p /opt/kubernetes/cfg
mkdir -p /opt/kubernetes/logs

# 切换到配置目录
cd /opt/kubernetes/cfg

# 创建kube-apiserver配置文件（K8s 1.24.17版本 - 适配银河麒麟v10sp3）
cat > kube-apiserver.conf << EOF
KUBE_APISERVER_OPTS="--v=2 \\
--etcd-servers=https://*************:2379 \\
--bind-address=************* \\
--secure-port=6443 \\
--advertise-address=************* \\
--allow-privileged=true \\
--service-cluster-ip-range=*********/12 \\
--enable-admission-plugins=NamespaceLifecycle,LimitRanger,ServiceAccount,DefaultStorageClass,DefaultTolerationSeconds,NodeRestriction,ResourceQuota \\
--authorization-mode=RBAC,Node \\
--enable-bootstrap-token-auth=true \\
--token-auth-file=/opt/kubernetes/cfg/token.csv \\
--service-node-port-range=30000-32767 \\
--kubelet-client-certificate=/opt/kubernetes/ssl/kubernetes.pem \\
--kubelet-client-key=/opt/kubernetes/ssl/kubernetes-key.pem \\
--tls-cert-file=/opt/kubernetes/ssl/kubernetes.pem \\
--tls-private-key-file=/opt/kubernetes/ssl/kubernetes-key.pem \\
--client-ca-file=/opt/kubernetes/ssl/ca.pem \\
--service-account-key-file=/opt/kubernetes/ssl/ca-key.pem \\
--service-account-signing-key-file=/opt/kubernetes/ssl/ca-key.pem \\
--service-account-issuer=https://kubernetes.default.svc.cluster.local \\
--etcd-cafile=/opt/kubernetes/ssl/ca.pem \\
--etcd-certfile=/opt/kubernetes/ssl/etcd.pem \\
--etcd-keyfile=/opt/kubernetes/ssl/etcd-key.pem \\
--requestheader-client-ca-file=/opt/kubernetes/ssl/ca.pem \\
--proxy-client-cert-file=/opt/kubernetes/ssl/kubernetes.pem \\
--proxy-client-key-file=/opt/kubernetes/ssl/kubernetes-key.pem \\
--requestheader-allowed-names=kubernetes \\
--requestheader-extra-headers-prefix=X-Remote-Extra- \\
--requestheader-group-headers=X-Remote-Group \\
--requestheader-username-headers=X-Remote-User \\
--enable-aggregator-routing=true \\
--audit-log-maxage=30 \\
--audit-log-maxbackup=3 \\
--audit-log-maxsize=100 \\
--audit-log-path=/opt/kubernetes/logs/k8s-audit.log \\
--runtime-config=api/all=true \\
--max-requests-inflight=200 \\
--max-mutating-requests-inflight=100"
EOF

# 已配置Master节点IP：*************</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 4核8G配置优化建议：</strong>
                        对于4核8G的配置，建议在kube-apiserver配置中添加以下资源限制参数：<br>
                        <code>--max-requests-inflight=200</code> （降低到200，适合4核8G小集群）<br>
                        <code>--max-mutating-requests-inflight=100</code> （降低到100，适合4核8G小集群）<br>
                        <strong>注意：</strong>已移除PodSecurity准入控制器，因为在小规模集群中可能导致兼容性问题。
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 配置参数说明：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- etcd-servers: 指定etcd服务地址</li>
                            <li>- bind-address: API服务器监听的IP地址</li>
                            <li>- service-cluster-ip-range: Service的CIDR范围</li>
                            <li>- enable-admission-plugins: 启用的准入控制插件列表</li>
                            <li>- authorization-mode: 授权模式，这里使用RBAC和Node</li>
                            <li>- service-node-port-range: NodePort可用端口范围</li>
                            <li>- 其他参数指定了TLS证书文件位置及其他安全设置</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-key"></i> 5.2.2 创建TLS Bootstrapping使用的token文件</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建token文件
cat > /opt/kubernetes/cfg/token.csv << EOF
$(head -c 16 /dev/urandom | od -An -t x | tr -d ' '),kubelet-bootstrap,10001,"system:bootstrappers"
EOF</code></pre>

                    <h4><i class="fas fa-file-alt"></i> 5.2.3 创建kube-apiserver系统服务配置</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建日志目录
mkdir -p /opt/kubernetes/logs

# 创建systemd服务文件
cat > /usr/lib/systemd/system/kube-apiserver.service << EOF
[Unit]
Description=Kubernetes API Server
Documentation=https://github.com/kubernetes/kubernetes
After=etcd.service
Wants=etcd.service

[Service]
EnvironmentFile=-/opt/kubernetes/cfg/kube-apiserver.conf
ExecStart=/opt/kubernetes/bin/kube-apiserver \$KUBE_APISERVER_OPTS
Restart=on-failure
RestartSec=5
Type=notify
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF</code></pre>

                    <h4><i class="fas fa-play-circle"></i> 5.2.4 启动kube-apiserver服务</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 重新加载systemd配置
systemctl daemon-reload

# 设置开机启动
systemctl enable kube-apiserver

# 启动服务
systemctl start kube-apiserver

# 查看服务状态
systemctl status kube-apiserver

# 检查API服务器端口
netstat -lntp | grep kube-apiserve</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        服务状态显示为"active (running)"，并且6443端口已被kube-apiserver监听。
                    </div>

                    <h4><i class="fas fa-exclamation-triangle"></i> 5.2.5 kube-apiserver启动故障排除</h4>
                    <div class="error-box">
                        <strong><i class="fas fa-bug"></i> 常见启动失败问题及解决方案：</strong>
                    </div>

                    <h5><i class="fas fa-times-circle"></i> 问题1：unknown flag: --logtostderr</h5>
                    <p><strong>错误现象：</strong>服务启动失败，日志显示"unknown flag: --logtostderr"</p>
                    <p><strong>原因分析：</strong>Kubernetes 1.24+版本已移除--logtostderr参数</p>
                    <p><strong>解决方案：</strong></p>
                    <pre><code># 1. 停止服务
systemctl stop kube-apiserver

# 2. 重新创建正确的配置文件（已移除--logtostderr和--log-file参数）
cd /opt/kubernetes/cfg
cat > kube-apiserver.conf << EOF
KUBE_APISERVER_OPTS="--v=2 \\
--etcd-servers=https://*************:2379 \\
--bind-address=************* \\
--secure-port=6443 \\
--advertise-address=************* \\
--allow-privileged=true \\
--service-cluster-ip-range=*********/12 \\
--enable-admission-plugins=NamespaceLifecycle,LimitRanger,ServiceAccount,DefaultStorageClass,DefaultTolerationSeconds,NodeRestriction,ResourceQuota \\
--authorization-mode=RBAC,Node \\
--enable-bootstrap-token-auth=true \\
--token-auth-file=/opt/kubernetes/cfg/token.csv \\
--service-node-port-range=30000-32767 \\
--kubelet-client-certificate=/opt/kubernetes/ssl/kubernetes.pem \\
--kubelet-client-key=/opt/kubernetes/ssl/kubernetes-key.pem \\
--tls-cert-file=/opt/kubernetes/ssl/kubernetes.pem \\
--tls-private-key-file=/opt/kubernetes/ssl/kubernetes-key.pem \\
--client-ca-file=/opt/kubernetes/ssl/ca.pem \\
--service-account-key-file=/opt/kubernetes/ssl/ca-key.pem \\
--service-account-signing-key-file=/opt/kubernetes/ssl/ca-key.pem \\
--service-account-issuer=https://kubernetes.default.svc.cluster.local \\
--etcd-cafile=/opt/kubernetes/ssl/ca.pem \\
--etcd-certfile=/opt/kubernetes/ssl/etcd.pem \\
--etcd-keyfile=/opt/kubernetes/ssl/etcd-key.pem \\
--requestheader-client-ca-file=/opt/kubernetes/ssl/ca.pem \\
--proxy-client-cert-file=/opt/kubernetes/ssl/kubernetes.pem \\
--proxy-client-key-file=/opt/kubernetes/ssl/kubernetes-key.pem \\
--requestheader-allowed-names=kubernetes \\
--requestheader-extra-headers-prefix=X-Remote-Extra- \\
--requestheader-group-headers=X-Remote-Group \\
--requestheader-username-headers=X-Remote-User \\
--enable-aggregator-routing=true \\
--audit-log-maxage=30 \\
--audit-log-maxbackup=3 \\
--audit-log-maxsize=100 \\
--audit-log-path=/opt/kubernetes/logs/k8s-audit.log \\
--runtime-config=api/all=true \\
--max-requests-inflight=200 \\
--max-mutating-requests-inflight=100"
EOF

# 3. 重新加载配置并启动服务
systemctl daemon-reload
systemctl start kube-apiserver
systemctl status kube-apiserver</code></pre>

                    <h5><i class="fas fa-times-circle"></i> 问题2：证书文件不存在或权限问题</h5>
                    <p><strong>解决方案：</strong></p>
                    <pre><code># 检查证书文件是否存在
ls -l /opt/kubernetes/ssl/

# 确保证书文件权限正确
chmod 644 /opt/kubernetes/ssl/*.pem
chmod 600 /opt/kubernetes/ssl/*-key.pem

# 检查目录权限
chmod 755 /opt/kubernetes/ssl/
chmod 755 /opt/kubernetes/logs/</code></pre>

                    <h5><i class="fas fa-times-circle"></i> 问题3：ETCD连接失败</h5>
                    <p><strong>解决方案：</strong></p>
                    <pre><code># 检查ETCD服务状态
systemctl status etcd

# 测试ETCD连接
export ETCDCTL_API=3
etcdctl --cacert=/opt/kubernetes/ssl/ca.pem \
  --cert=/opt/kubernetes/ssl/etcd.pem \
  --key=/opt/kubernetes/ssl/etcd-key.pem \
  --endpoints=https://*************:2379 \
  endpoint health</code></pre>

                    <h5><i class="fas fa-search"></i> 通用故障排查步骤</h5>
                    <pre><code># 1. 查看详细的服务状态
systemctl status kube-apiserver -l

# 2. 查看服务日志
journalctl -u kube-apiserver -f

# 3. 查看kube-apiserver进程
ps aux | grep kube-apiserver

# 4. 检查端口占用
netstat -lntp | grep 6443

# 5. 手动启动测试（用于调试）
/opt/kubernetes/bin/kube-apiserver --help | grep logtostderr
# 如果显示该参数不存在，说明版本不匹配

# 6. 检查配置文件语法
cat /opt/kubernetes/cfg/kube-apiserver.conf</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 重要提示：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- 确保使用的Kubernetes版本与配置参数匹配</li>
                            <li>- Kubernetes 1.24+版本移除了多个已弃用的参数</li>
                            <li>- 银河麒麟v10sp3系统建议使用Kubernetes 1.24.17版本</li>
                            <li>- 启动失败时，优先检查systemd日志获取详细错误信息</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-cogs"></i> 5.3 部署kube-controller-manager</h3>

                    <h4><i class="fas fa-cog"></i> 5.3.1 创建kube-controller-manager配置文件</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 切换到配置目录
cd /opt/kubernetes/cfg

# 创建kube-controller-manager配置文件（K8s 1.24.17版本）
cat > kube-controller-manager.conf << EOF
KUBE_CONTROLLER_MANAGER_OPTS="--v=2 \\
--leader-elect=true \\
--kubeconfig=/opt/kubernetes/cfg/kube-controller-manager.kubeconfig \\
--bind-address=127.0.0.1 \\
--allocate-node-cidrs=true \\
--cluster-cidr=**********/16 \\
--service-cluster-ip-range=*********/12 \\
--cluster-name=kubernetes \\
--cluster-signing-cert-file=/opt/kubernetes/ssl/ca.pem \\
--cluster-signing-key-file=/opt/kubernetes/ssl/ca-key.pem \\
--root-ca-file=/opt/kubernetes/ssl/ca.pem \\
--service-account-private-key-file=/opt/kubernetes/ssl/ca-key.pem \\
--cluster-signing-duration=87600h \\
--controllers=*,bootstrapsigner,tokencleaner \\
--node-monitor-grace-period=40s \\
--node-monitor-period=5s \\
--pod-eviction-timeout=5m0s \\
--terminated-pod-gc-threshold=50 \\
--use-service-account-credentials=true \\
--concurrent-service-syncs=1 \\
--concurrent-gc-syncs=10"
EOF</code></pre>

                    <h4><i class="fas fa-id-card"></i> 5.3.2 创建kube-controller-manager的kubeconfig文件</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 生成kubeconfig文件
cd /opt/kubernetes/ssl/

# 设置集群参数
/opt/kubernetes/bin/kubectl config set-cluster kubernetes \
  --certificate-authority=/opt/kubernetes/ssl/ca.pem \
  --embed-certs=true \
  --server=https://*************:6443 \
  --kubeconfig=/opt/kubernetes/cfg/kube-controller-manager.kubeconfig

# 设置客户端认证参数
/opt/kubernetes/bin/kubectl config set-credentials system:kube-controller-manager \
  --client-certificate=/opt/kubernetes/ssl/kube-controller-manager.pem \
  --client-key=/opt/kubernetes/ssl/kube-controller-manager-key.pem \
  --embed-certs=true \
  --kubeconfig=/opt/kubernetes/cfg/kube-controller-manager.kubeconfig

# 设置上下文参数
/opt/kubernetes/bin/kubectl config set-context system:kube-controller-manager@kubernetes \
  --cluster=kubernetes \
  --user=system:kube-controller-manager \
  --kubeconfig=/opt/kubernetes/cfg/kube-controller-manager.kubeconfig

# 设置默认上下文
/opt/kubernetes/bin/kubectl config use-context system:kube-controller-manager@kubernetes \
  --kubeconfig=/opt/kubernetes/cfg/kube-controller-manager.kubeconfig</code></pre>

                    <h4><i class="fas fa-file-alt"></i> 5.3.3 创建kube-controller-manager系统服务配置</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建systemd服务文件
cat > /usr/lib/systemd/system/kube-controller-manager.service << EOF
[Unit]
Description=Kubernetes Controller Manager
Documentation=https://github.com/kubernetes/kubernetes
After=kube-apiserver.service
Wants=kube-apiserver.service

[Service]
EnvironmentFile=-/opt/kubernetes/cfg/kube-controller-manager.conf
ExecStart=/opt/kubernetes/bin/kube-controller-manager \$KUBE_CONTROLLER_MANAGER_OPTS
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF</code></pre>

                    <h4><i class="fas fa-play-circle"></i> 5.3.4 启动kube-controller-manager服务</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 重新加载systemd配置
systemctl daemon-reload

# 设置开机启动
systemctl enable kube-controller-manager

# 启动服务
systemctl start kube-controller-manager

# 查看服务状态
systemctl status kube-controller-manager</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        服务状态显示为"active (running)"，表示控制器管理器已成功启动。
                    </div>

                    <h4><i class="fas fa-exclamation-triangle"></i> 5.3.5 kube-controller-manager启动故障排除</h4>
                    <div class="error-box">
                        <strong><i class="fas fa-bug"></i> 常见启动失败问题及解决方案：</strong>
                    </div>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 紧急修复：</strong>
                        如果您正在遇到 "--feature-gates" 参数错误，请立即执行以下命令修复：
                        <pre><code># 快速修复命令
systemctl stop kube-controller-manager
cd /opt/kubernetes/cfg
cp kube-controller-manager.conf kube-controller-manager.conf.backup
sed -i '/--feature-gates=RotateKubeletClientCertificate=true/d' kube-controller-manager.conf
systemctl start kube-controller-manager
systemctl status kube-controller-manager</code></pre>
                    </div>

                    <h5><i class="fas fa-times-circle"></i> 问题1：unknown flag: --logtostderr</h5>
                    <p><strong>错误现象：</strong>服务启动失败，日志显示"unknown flag: --logtostderr"</p>
                    <p><strong>原因分析：</strong>Kubernetes 1.24+版本已移除--logtostderr和--log-dir参数</p>
                    <p><strong>解决方案：</strong></p>
                    <pre><code># 1. 停止服务
systemctl stop kube-controller-manager

# 2. 重新创建正确的配置文件（已移除日志相关参数）
cd /opt/kubernetes/cfg
cat > kube-controller-manager.conf << EOF
KUBE_CONTROLLER_MANAGER_OPTS="--v=2 \\
--leader-elect=true \\
--kubeconfig=/opt/kubernetes/cfg/kube-controller-manager.kubeconfig \\
--bind-address=127.0.0.1 \\
--allocate-node-cidrs=true \\
--cluster-cidr=**********/16 \\
--service-cluster-ip-range=*********/12 \\
--cluster-name=kubernetes \\
--cluster-signing-cert-file=/opt/kubernetes/ssl/ca.pem \\
--cluster-signing-key-file=/opt/kubernetes/ssl/ca-key.pem \\
--root-ca-file=/opt/kubernetes/ssl/ca.pem \\
--service-account-private-key-file=/opt/kubernetes/ssl/ca-key.pem \\
--cluster-signing-duration=87600h \\
--controllers=*,bootstrapsigner,tokencleaner \\
--node-monitor-grace-period=40s \\
--node-monitor-period=5s \\
--pod-eviction-timeout=5m0s \\
--terminated-pod-gc-threshold=50 \\
--use-service-account-credentials=true \\
--concurrent-service-syncs=1 \\
--concurrent-gc-syncs=10"
EOF

# 3. 重新加载配置并启动服务
systemctl daemon-reload
systemctl start kube-controller-manager
systemctl status kube-controller-manager</code></pre>

                    <h5><i class="fas fa-times-circle"></i> 问题2：feature-gates参数错误</h5>
                    <p><strong>错误现象：</strong>服务启动失败，日志显示"invalid argument "RotateKubeletClientCertificate=true" for "--feature-gates" flag"</p>
                    <p><strong>原因分析：</strong>Kubernetes 1.24.17版本中RotateKubeletClientCertificate已默认启用，无需显式设置</p>
                    <p><strong>解决方案：</strong></p>
                    <pre><code># 1. 停止服务
systemctl stop kube-controller-manager

# 2. 重新创建配置文件（移除已弃用的feature-gates）
cd /opt/kubernetes/cfg
cat > kube-controller-manager.conf << EOF
KUBE_CONTROLLER_MANAGER_OPTS="--v=2 \\
--leader-elect=true \\
--kubeconfig=/opt/kubernetes/cfg/kube-controller-manager.kubeconfig \\
--bind-address=127.0.0.1 \\
--allocate-node-cidrs=true \\
--cluster-cidr=**********/16 \\
--service-cluster-ip-range=*********/12 \\
--cluster-name=kubernetes \\
--cluster-signing-cert-file=/opt/kubernetes/ssl/ca.pem \\
--cluster-signing-key-file=/opt/kubernetes/ssl/ca-key.pem \\
--root-ca-file=/opt/kubernetes/ssl/ca.pem \\
--service-account-private-key-file=/opt/kubernetes/ssl/ca-key.pem \\
--cluster-signing-duration=87600h \\
--controllers=*,bootstrapsigner,tokencleaner \\
--node-monitor-grace-period=40s \\
--node-monitor-period=5s \\
--pod-eviction-timeout=5m0s \\
--terminated-pod-gc-threshold=50 \\
--use-service-account-credentials=true \\
--concurrent-service-syncs=1 \\
--concurrent-gc-syncs=10"
EOF

# 3. 重新加载配置并启动服务
systemctl daemon-reload
systemctl start kube-controller-manager
systemctl status kube-controller-manager</code></pre>

                    <h5><i class="fas fa-times-circle"></i> 问题3：kubeconfig文件不存在</h5>
                    <p><strong>解决方案：</strong></p>
                    <pre><code># 检查kubeconfig文件是否存在
ls -la /opt/kubernetes/cfg/kube-controller-manager.kubeconfig

# 如果不存在，重新生成（参考5.3.2步骤）</code></pre>

                    <h5><i class="fas fa-times-circle"></i> 问题4：证书文件权限问题</h5>
                    <p><strong>解决方案：</strong></p>
                    <pre><code># 检查证书文件权限
ls -la /opt/kubernetes/ssl/

# 修复权限
chown -R root:root /opt/kubernetes/ssl/
chmod 600 /opt/kubernetes/ssl/*-key.pem
chmod 644 /opt/kubernetes/ssl/*.pem</code></pre>

                    <h3><i class="fas fa-calendar-check"></i> 5.4 部署kube-scheduler</h3>

                    <h4><i class="fas fa-cog"></i> 5.4.1 创建kube-scheduler配置文件</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>

                    <div class="danger-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> K8s 1.24+版本重要变更：</strong>
                        从Kubernetes 1.24版本开始，kube-scheduler移除了`--port`参数，只保留`--secure-port`。使用旧参数会导致启动失败。
                    </div>

                    <pre><code># 切换到配置目录
cd /opt/kubernetes/cfg

# 创建kube-scheduler配置文件（K8s 1.24.17版本 - 银河麒麟v10sp3适配）
cat > kube-scheduler.conf << EOF
KUBE_SCHEDULER_OPTS="--v=2 \\
--leader-elect=true \\
--kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig \\
--bind-address=127.0.0.1 \\
--secure-port=10259"
EOF

# 验证配置文件内容
cat kube-scheduler.conf</code></pre>

                    <h4><i class="fas fa-id-card"></i> 5.4.2 创建kube-scheduler的kubeconfig文件</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 生成kubeconfig文件
cd /opt/kubernetes/ssl/

# 设置集群参数
/opt/kubernetes/bin/kubectl config set-cluster kubernetes \
  --certificate-authority=/opt/kubernetes/ssl/ca.pem \
  --embed-certs=true \
  --server=https://*************:6443 \
  --kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig

# 设置客户端认证参数
/opt/kubernetes/bin/kubectl config set-credentials system:kube-scheduler \
  --client-certificate=/opt/kubernetes/ssl/kube-scheduler.pem \
  --client-key=/opt/kubernetes/ssl/kube-scheduler-key.pem \
  --embed-certs=true \
  --kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig

# 设置上下文参数
/opt/kubernetes/bin/kubectl config set-context system:kube-scheduler@kubernetes \
  --cluster=kubernetes \
  --user=system:kube-scheduler \
  --kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig

# 设置默认上下文
/opt/kubernetes/bin/kubectl config use-context system:kube-scheduler@kubernetes \
  --kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig</code></pre>

                    <h4><i class="fas fa-file-alt"></i> 5.4.3 创建kube-scheduler系统服务配置</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 创建systemd服务文件
cat > /usr/lib/systemd/system/kube-scheduler.service << EOF
[Unit]
Description=Kubernetes Scheduler
Documentation=https://github.com/kubernetes/kubernetes
After=kube-apiserver.service
Wants=kube-apiserver.service

[Service]
EnvironmentFile=-/opt/kubernetes/cfg/kube-scheduler.conf
ExecStart=/opt/kubernetes/bin/kube-scheduler \$KUBE_SCHEDULER_OPTS
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF</code></pre>

                    <h4><i class="fas fa-play-circle"></i> 5.4.4 启动kube-scheduler服务</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 重新加载systemd配置
systemctl daemon-reload

# 设置开机启动
systemctl enable kube-scheduler

# 启动服务
systemctl start kube-scheduler

# 查看服务状态
systemctl status kube-scheduler</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        服务状态显示为"active (running)"，表示调度器已成功启动。
                    </div>

                    <h4><i class="fas fa-bug"></i> 5.4.5 kube-scheduler启动失败故障排除</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>

                    <div class="danger-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 常见错误：Error: unknown flag: --port</strong>
                        <p>如果您看到"Error: unknown flag: --port"错误，这是因为Kubernetes 1.24+版本已移除--port参数。</p>
                    </div>

                    <h5><i class="fas fa-tools"></i> 解决方案1：修正配置文件（推荐）</h5>
                    <pre><code># 1. 停止服务
systemctl stop kube-scheduler

# 2. 重新创建正确的配置文件（移除--port参数）
cd /opt/kubernetes/cfg
cat > kube-scheduler.conf << EOF
KUBE_SCHEDULER_OPTS="--v=2 \\
--leader-elect=true \\
--kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig \\
--bind-address=127.0.0.1 \\
--secure-port=10259"
EOF

# 3. 验证配置文件
echo "=== 当前配置内容 ==="
cat kube-scheduler.conf
echo "====================="

# 4. 重新启动服务
systemctl start kube-scheduler

# 5. 检查服务状态
systemctl status kube-scheduler

# 6. 查看实时日志
journalctl -u kube-scheduler -f</code></pre>

                    <h5><i class="fas fa-search"></i> 解决方案2：详细故障诊断</h5>
                    <pre><code># 1. 检查kube-scheduler二进制文件版本
/opt/kubernetes/bin/kube-scheduler --version

# 2. 检查支持的参数（查看帮助信息）
/opt/kubernetes/bin/kube-scheduler --help | grep -E "(port|bind)"

# 3. 手动测试启动（查看详细错误）
/opt/kubernetes/bin/kube-scheduler --v=2 \
  --leader-elect=true \
  --kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig \
  --bind-address=127.0.0.1 \
  --secure-port=10259

# 4. 检查kubeconfig文件是否正确
kubectl --kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig cluster-info

# 5. 检查证书文件
ls -la /opt/kubernetes/ssl/kube-scheduler*</code></pre>

                    <h5><i class="fas fa-cog"></i> 解决方案3：完整重新配置</h5>
                    <pre><code># 如果上述方法仍然失败，执行完整重新配置

# 1. 停止并禁用服务
systemctl stop kube-scheduler
systemctl disable kube-scheduler

# 2. 备份现有配置
cp /opt/kubernetes/cfg/kube-scheduler.conf /opt/kubernetes/cfg/kube-scheduler.conf.backup
cp /usr/lib/systemd/system/kube-scheduler.service /usr/lib/systemd/system/kube-scheduler.service.backup

# 3. 重新创建配置文件
cat > /opt/kubernetes/cfg/kube-scheduler.conf << EOF
# Kubernetes 1.24.17 kube-scheduler配置 - 银河麒麟v10sp3
KUBE_SCHEDULER_OPTS="--v=2 \\
--leader-elect=true \\
--kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig \\
--bind-address=127.0.0.1 \\
--secure-port=10259 \\
--authentication-kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig \\
--authorization-kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig"
EOF

# 4. 重新创建systemd服务文件
cat > /usr/lib/systemd/system/kube-scheduler.service << EOF
[Unit]
Description=Kubernetes Scheduler
Documentation=https://github.com/kubernetes/kubernetes
After=kube-apiserver.service
Wants=kube-apiserver.service

[Service]
EnvironmentFile=-/opt/kubernetes/cfg/kube-scheduler.conf
ExecStart=/opt/kubernetes/bin/kube-scheduler \$KUBE_SCHEDULER_OPTS
Restart=on-failure
RestartSec=5
Type=simple
KillMode=process

[Install]
WantedBy=multi-user.target
EOF

# 5. 重新加载并启动
systemctl daemon-reload
systemctl enable kube-scheduler
systemctl start kube-scheduler

# 6. 验证启动状态
systemctl status kube-scheduler
journalctl -u kube-scheduler --no-pager -n 10</code></pre>

                    <h5><i class="fas fa-check-circle"></i> 解决方案4：验证脚本</h5>
                    <pre><code># 创建kube-scheduler验证脚本
cat > /tmp/check_kube_scheduler.sh << 'EOF'
#!/bin/bash

echo "=== kube-scheduler启动状态检查 ==="
echo

# 1. 检查服务状态
echo "1. 服务状态："
systemctl is-active kube-scheduler && echo "✓ 服务运行中" || echo "✗ 服务未运行"
echo

# 2. 检查配置文件
echo "2. 配置文件检查："
if [ -f "/opt/kubernetes/cfg/kube-scheduler.conf" ]; then
    echo "✓ 配置文件存在"
    echo "配置内容："
    cat /opt/kubernetes/cfg/kube-scheduler.conf
else
    echo "✗ 配置文件不存在"
fi
echo

# 3. 检查端口监听
echo "3. 端口监听检查："
netstat -tlnp | grep :10259 && echo "✓ 端口10259已监听" || echo "✗ 端口10259未监听"
echo

# 4. 检查进程
echo "4. 进程检查："
pgrep -f kube-scheduler && echo "✓ kube-scheduler进程存在" || echo "✗ kube-scheduler进程不存在"
echo

# 5. 检查最近日志
echo "5. 最近日志（最后5行）："
journalctl -u kube-scheduler --no-pager -n 5
echo

echo "=== 检查完成 ==="
EOF

chmod +x /tmp/check_kube_scheduler.sh
/tmp/check_kube_scheduler.sh</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提示：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>1. Kubernetes 1.24+版本移除了许多已弃用的参数，包括--port</li>
                            <li>2. 确保使用的配置参数与您的K8s版本兼容</li>
                            <li>3. 如果仍有问题，检查kube-apiserver是否正常运行</li>
                            <li>4. 确保所有证书文件路径正确且可访问</li>
                        </ul>
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 快速修复命令（针对--port错误）：</strong>
                        <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;"><code># 一键修复kube-scheduler配置
systemctl stop kube-scheduler
cd /opt/kubernetes/cfg
sed -i 's/--port=0 \\//g' kube-scheduler.conf
systemctl start kube-scheduler
systemctl status kube-scheduler</code></pre>
                    </div>

                    <h5><i class="fas fa-check-circle"></i> 解决方案4：验证脚本</h5>
                    <pre><code># 创建kube-scheduler验证脚本
cat > /tmp/check_kube_scheduler.sh << 'EOF'
#!/bin/bash

echo "=== kube-scheduler启动状态检查 ==="
echo

# 1. 检查服务状态
echo "1. 服务状态："
systemctl is-active kube-scheduler && echo "✓ 服务运行中" || echo "✗ 服务未运行"
echo

# 2. 检查配置文件
echo "2. 配置文件检查："
if [ -f "/opt/kubernetes/cfg/kube-scheduler.conf" ]; then
    echo "✓ 配置文件存在"
    echo "配置内容："
    cat /opt/kubernetes/cfg/kube-scheduler.conf
else
    echo "✗ 配置文件不存在"
fi
echo

# 3. 检查端口监听
echo "3. 端口监听检查："
netstat -tlnp | grep :10259 && echo "✓ 端口10259已监听" || echo "✗ 端口10259未监听"
echo

# 4. 检查进程
echo "4. 进程检查："
pgrep -f kube-scheduler && echo "✓ kube-scheduler进程存在" || echo "✗ kube-scheduler进程不存在"
echo

# 5. 检查最近日志
echo "5. 最近日志（最后5行）："
journalctl -u kube-scheduler --no-pager -n 5
echo

echo "=== 检查完成 ==="
EOF

chmod +x /tmp/check_kube_scheduler.sh
/tmp/check_kube_scheduler.sh</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提示：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>1. Kubernetes 1.24+版本移除了许多已弃用的参数，包括--port</li>
                            <li>2. 确保使用的配置参数与您的K8s版本兼容</li>
                            <li>3. 如果仍有问题，检查kube-apiserver是否正常运行</li>
                            <li>4. 确保所有证书文件路径正确且可访问</li>
                        </ul>
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 快速修复命令（针对--port错误）：</strong>
                        <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;"><code># 一键修复kube-scheduler配置
systemctl stop kube-scheduler
cd /opt/kubernetes/cfg
sed -i 's/--port=0 \\//g' kube-scheduler.conf
systemctl start kube-scheduler
systemctl status kube-scheduler</code></pre>
                    </div>

                    <h4><i class="fas fa-bug"></i> 5.4.5 kube-scheduler启动失败故障排除</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>

                    <div class="danger-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 常见错误：Error: unknown flag: --port</strong>
                        <p>如果您看到"Error: unknown flag: --port"错误，这是因为Kubernetes 1.24+版本已移除--port参数。</p>
                    </div>

                    <h5><i class="fas fa-tools"></i> 解决方案1：修正配置文件（推荐）</h5>
                    <pre><code># 1. 停止服务
systemctl stop kube-scheduler

# 2. 重新创建正确的配置文件（移除--port参数）
cd /opt/kubernetes/cfg
cat > kube-scheduler.conf << EOF
KUBE_SCHEDULER_OPTS="--v=2 \\
--leader-elect=true \\
--kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig \\
--bind-address=127.0.0.1 \\
--secure-port=10259"
EOF

# 3. 验证配置文件
echo "=== 当前配置内容 ==="
cat kube-scheduler.conf
echo "====================="

# 4. 重新启动服务
systemctl start kube-scheduler

# 5. 检查服务状态
systemctl status kube-scheduler

# 6. 查看实时日志
journalctl -u kube-scheduler -f</code></pre>

                    <h5><i class="fas fa-search"></i> 解决方案2：详细故障诊断</h5>
                    <pre><code># 1. 检查kube-scheduler二进制文件版本
/opt/kubernetes/bin/kube-scheduler --version

# 2. 检查支持的参数（查看帮助信息）
/opt/kubernetes/bin/kube-scheduler --help | grep -E "(port|bind)"

# 3. 手动测试启动（查看详细错误）
/opt/kubernetes/bin/kube-scheduler --v=2 \
  --leader-elect=true \
  --kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig \
  --bind-address=127.0.0.1 \
  --secure-port=10259

# 4. 检查kubeconfig文件是否正确
kubectl --kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig cluster-info

# 5. 检查证书文件
ls -la /opt/kubernetes/ssl/kube-scheduler*</code></pre>

                    <h5><i class="fas fa-cog"></i> 解决方案3：完整重新配置</h5>
                    <pre><code># 如果上述方法仍然失败，执行完整重新配置

# 1. 停止并禁用服务
systemctl stop kube-scheduler
systemctl disable kube-scheduler

# 2. 备份现有配置
cp /opt/kubernetes/cfg/kube-scheduler.conf /opt/kubernetes/cfg/kube-scheduler.conf.backup
cp /usr/lib/systemd/system/kube-scheduler.service /usr/lib/systemd/system/kube-scheduler.service.backup

# 3. 重新创建配置文件
cat > /opt/kubernetes/cfg/kube-scheduler.conf << EOF
# Kubernetes 1.24.17 kube-scheduler配置 - 银河麒麟v10sp3
KUBE_SCHEDULER_OPTS="--v=2 \\
--leader-elect=true \\
--kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig \\
--bind-address=127.0.0.1 \\
--secure-port=10259 \\
--authentication-kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig \\
--authorization-kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig"
EOF

# 4. 重新创建systemd服务文件
cat > /usr/lib/systemd/system/kube-scheduler.service << EOF
[Unit]
Description=Kubernetes Scheduler
Documentation=https://github.com/kubernetes/kubernetes
After=kube-apiserver.service
Wants=kube-apiserver.service

[Service]
EnvironmentFile=-/opt/kubernetes/cfg/kube-scheduler.conf
ExecStart=/opt/kubernetes/bin/kube-scheduler \$KUBE_SCHEDULER_OPTS
Restart=on-failure
RestartSec=5
Type=simple
KillMode=process

[Install]
WantedBy=multi-user.target
EOF

# 5. 重新加载并启动
systemctl daemon-reload
systemctl enable kube-scheduler
systemctl start kube-scheduler

# 6. 验证启动状态
systemctl status kube-scheduler
journalctl -u kube-scheduler --no-pager -n 10</code></pre>

                    <h5><i class="fas fa-check-circle"></i> 解决方案4：验证脚本</h5>
                    <pre><code># 创建kube-scheduler验证脚本
cat > /tmp/check_kube_scheduler.sh << 'EOF'
#!/bin/bash

echo "=== kube-scheduler启动状态检查 ==="
echo

# 1. 检查服务状态
echo "1. 服务状态："
systemctl is-active kube-scheduler && echo "✓ 服务运行中" || echo "✗ 服务未运行"
echo

# 2. 检查配置文件
echo "2. 配置文件检查："
if [ -f "/opt/kubernetes/cfg/kube-scheduler.conf" ]; then
    echo "✓ 配置文件存在"
    echo "配置内容："
    cat /opt/kubernetes/cfg/kube-scheduler.conf
else
    echo "✗ 配置文件不存在"
fi
echo

# 3. 检查端口监听
echo "3. 端口监听检查："
netstat -tlnp | grep :10259 && echo "✓ 端口10259已监听" || echo "✗ 端口10259未监听"
echo

# 4. 检查进程
echo "4. 进程检查："
pgrep -f kube-scheduler && echo "✓ kube-scheduler进程存在" || echo "✗ kube-scheduler进程不存在"
echo

# 5. 检查最近日志
echo "5. 最近日志（最后5行）："
journalctl -u kube-scheduler --no-pager -n 5
echo

echo "=== 检查完成 ==="
EOF

chmod +x /tmp/check_kube_scheduler.sh
/tmp/check_kube_scheduler.sh</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提示：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>1. Kubernetes 1.24+版本移除了许多已弃用的参数，包括--port</li>
                            <li>2. 确保使用的配置参数与您的K8s版本兼容</li>
                            <li>3. 如果仍有问题，检查kube-apiserver是否正常运行</li>
                            <li>4. 确保所有证书文件路径正确且可访问</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-exclamation-triangle"></i> 5.4.5 银河麒麟系统kube-scheduler启动问题排查</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>如果在银河麒麟v10sp3系统上遇到kube-scheduler启动失败，特别是出现"kylin-kms-activation.service"相关错误，请按以下步骤排查：</p>

                    <div class="danger-box">
                        <strong><i class="fas fa-bug"></i> 常见错误现象：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- kube-scheduler服务启动失败</li>
                            <li>- 日志中出现"kylin-kms-activation.service: Failed with result 'exit-code'"</li>
                            <li>- systemctl status显示服务异常</li>
                        </ul>
                    </div>

                    <h5><i class="fas fa-search"></i> 问题1：银河麒麟KMS激活服务冲突</h5>
                    <p><strong>原因分析：</strong>银河麒麟系统的KMS激活服务可能与kube-scheduler服务存在冲突或依赖问题。</p>
                    <p><strong>解决方案：</strong></p>
                    <pre><code># 1. 检查kylin-kms-activation服务状态
systemctl status kylin-kms-activation.service

# 2. 查看详细错误日志
journalctl -u kylin-kms-activation.service -f

# 3. 检查kube-scheduler服务日志
journalctl -u kube-scheduler.service -f

# 4. 临时停止kylin-kms-activation服务（如果不影响系统激活）
systemctl stop kylin-kms-activation.service
systemctl disable kylin-kms-activation.service

# 5. 重新启动kube-scheduler
systemctl restart kube-scheduler
systemctl status kube-scheduler</code></pre>

                    <h5><i class="fas fa-cog"></i> 问题2：systemd服务依赖配置问题</h5>
                    <p><strong>解决方案：</strong>修改kube-scheduler服务配置，避免与银河麒麟系统服务冲突</p>
                    <pre><code># 1. 备份原有服务文件
cp /usr/lib/systemd/system/kube-scheduler.service /usr/lib/systemd/system/kube-scheduler.service.bak

# 2. 创建优化的kube-scheduler服务文件（银河麒麟专用）
cat > /usr/lib/systemd/system/kube-scheduler.service << EOF
[Unit]
Description=Kubernetes Scheduler
Documentation=https://github.com/kubernetes/kubernetes
After=kube-apiserver.service network.target
Wants=kube-apiserver.service
# 避免与银河麒麟KMS服务冲突
Conflicts=kylin-kms-activation.service

[Service]
EnvironmentFile=-/opt/kubernetes/cfg/kube-scheduler.conf
ExecStart=/opt/kubernetes/bin/kube-scheduler \$KUBE_SCHEDULER_OPTS
Restart=on-failure
RestartSec=5
Type=simple
KillMode=process
# 银河麒麟系统优化参数
LimitNOFILE=65536
LimitNPROC=65536

[Install]
WantedBy=multi-user.target
EOF

# 3. 重新加载配置并启动
systemctl daemon-reload
systemctl restart kube-scheduler
systemctl status kube-scheduler</code></pre>

                    <h5><i class="fas fa-tools"></i> 问题3：银河麒麟系统特殊配置优化</h5>
                    <p><strong>解决方案：</strong>针对银河麒麟系统进行特殊优化配置</p>
                    <pre><code># 1. 创建银河麒麟专用的kube-scheduler配置
cat > /opt/kubernetes/cfg/kube-scheduler.conf << EOF
# 银河麒麟v10sp3专用kube-scheduler配置
KUBE_SCHEDULER_OPTS="--v=2 \\
--leader-elect=true \\
--kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig \\
--bind-address=127.0.0.1 \\
--secure-port=10259 \\
--port=0 \\
--authentication-kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig \\
--authorization-kubeconfig=/opt/kubernetes/cfg/kube-scheduler.kubeconfig \\
--requestheader-client-ca-file=/opt/kubernetes/ssl/ca.pem \\
--requestheader-allowed-names= \\
--requestheader-extra-headers-prefix=X-Remote-Extra- \\
--requestheader-group-headers=X-Remote-Group \\
--requestheader-username-headers=X-Remote-User"
EOF

# 2. 检查配置文件权限
chown root:root /opt/kubernetes/cfg/kube-scheduler.conf
chmod 644 /opt/kubernetes/cfg/kube-scheduler.conf

# 3. 验证kubeconfig文件
ls -la /opt/kubernetes/cfg/kube-scheduler.kubeconfig
cat /opt/kubernetes/cfg/kube-scheduler.kubeconfig

# 4. 重新启动服务
systemctl restart kube-scheduler
systemctl status kube-scheduler</code></pre>

                    <h5><i class="fas fa-shield-alt"></i> 问题4：银河麒麟安全策略调整</h5>
                    <p><strong>解决方案：</strong>调整银河麒麟系统的安全策略</p>
                    <pre><code># 1. 检查SELinux状态（银河麒麟可能有特殊的安全模块）
getenforce
sestatus

# 2. 临时设置为宽松模式（如果是enforcing）
setenforce 0

# 3. 检查银河麒麟特有的安全服务
systemctl list-units | grep kylin
systemctl list-units | grep security

# 4. 如果有银河麒麟安全服务，可能需要添加例外规则
# 具体操作需要根据实际的安全服务来定

# 5. 重新启动kube-scheduler
systemctl restart kube-scheduler</code></pre>

                    <h5><i class="fas fa-check-circle"></i> 问题5：完整的银河麒麟kube-scheduler部署验证</h5>
                    <p><strong>验证脚本：</strong></p>
                    <pre><code># 创建银河麒麟kube-scheduler验证脚本
cat > /tmp/kylin_kube_scheduler_check.sh << 'EOF'
#!/bin/bash

echo "=== 银河麒麟v10sp3 kube-scheduler部署验证 ==="
echo

# 1. 检查系统信息
echo "1. 系统信息："
cat /etc/kylin-release 2>/dev/null || echo "无法读取银河麒麟版本"
echo "内核版本: $(uname -r)"
echo

# 2. 检查相关服务状态
echo "2. 服务状态检查："
echo "kube-scheduler: $(systemctl is-active kube-scheduler 2>/dev/null || echo 'inactive')"
echo "kube-apiserver: $(systemctl is-active kube-apiserver 2>/dev/null || echo 'inactive')"
echo "kylin-kms-activation: $(systemctl is-active kylin-kms-activation 2>/dev/null || echo 'inactive')"
echo

# 3. 检查配置文件
echo "3. 配置文件检查："
if [ -f "/opt/kubernetes/cfg/kube-scheduler.conf" ]; then
    echo "✓ kube-scheduler.conf 存在"
else
    echo "✗ kube-scheduler.conf 不存在"
fi

if [ -f "/opt/kubernetes/cfg/kube-scheduler.kubeconfig" ]; then
    echo "✓ kube-scheduler.kubeconfig 存在"
else
    echo "✗ kube-scheduler.kubeconfig 不存在"
fi

if [ -f "/usr/lib/systemd/system/kube-scheduler.service" ]; then
    echo "✓ kube-scheduler.service 存在"
else
    echo "✗ kube-scheduler.service 不存在"
fi
echo

# 4. 检查端口占用
echo "4. 端口检查："
netstat -tlnp | grep :10259 && echo "✓ kube-scheduler端口10259已监听" || echo "✗ kube-scheduler端口10259未监听"
echo

# 5. 检查进程
echo "5. 进程检查："
pgrep -f kube-scheduler && echo "✓ kube-scheduler进程运行中" || echo "✗ kube-scheduler进程未运行"
echo

# 6. 检查日志
echo "6. 最近日志："
journalctl -u kube-scheduler --no-pager -n 5 2>/dev/null || echo "无法获取日志"
echo

echo "=== 检查完成 ==="
EOF

chmod +x /tmp/kylin_kube_scheduler_check.sh
/tmp/kylin_kube_scheduler_check.sh</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 银河麒麟系统特别注意事项：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>1. 银河麒麟系统可能有特殊的KMS激活服务，与K8s服务可能存在冲突</li>
                            <li>2. 如果kylin-kms-activation服务不是必需的，可以考虑禁用</li>
                            <li>3. 银河麒麟的安全策略可能比标准Linux更严格，需要适当调整</li>
                            <li>4. 建议在测试环境先验证配置，确认无问题后再应用到生产环境</li>
                        </ul>
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 解决思路总结：</strong>
                        <ol style="padding-left: 20px;">
                            <li>首先排查是否是银河麒麟KMS服务冲突问题</li>
                            <li>检查systemd服务配置是否正确</li>
                            <li>验证kube-scheduler的配置文件和证书</li>
                            <li>调整银河麒麟系统的安全策略</li>
                            <li>使用验证脚本全面检查部署状态</li>
                        </ol>
                    </div>

                    <h3><i class="fas fa-terminal"></i> 5.5 配置kubectl工具</h3>

                    <h4><i class="fas fa-id-card"></i> 5.5.1 创建kubectl的kubeconfig文件</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 生成admin用户的kubeconfig文件
cd /opt/kubernetes/ssl/

# 设置集群参数
/opt/kubernetes/bin/kubectl config set-cluster kubernetes \
  --certificate-authority=/opt/kubernetes/ssl/ca.pem \
  --embed-certs=true \
  --server=https://*************:6443 \
  --kubeconfig=/opt/kubernetes/cfg/admin.kubeconfig

# 设置客户端认证参数
/opt/kubernetes/bin/kubectl config set-credentials admin \
  --client-certificate=/opt/kubernetes/ssl/admin.pem \
  --client-key=/opt/kubernetes/ssl/admin-key.pem \
  --embed-certs=true \
  --kubeconfig=/opt/kubernetes/cfg/admin.kubeconfig

# 设置上下文参数
/opt/kubernetes/bin/kubectl config set-context kubernetes@admin \
  --cluster=kubernetes \
  --user=admin \
  --kubeconfig=/opt/kubernetes/cfg/admin.kubeconfig

# 设置默认上下文
/opt/kubernetes/bin/kubectl config use-context kubernetes@admin \
  --kubeconfig=/opt/kubernetes/cfg/admin.kubeconfig

# 创建kubectl的软链接，方便使用
ln -sf /opt/kubernetes/bin/kubectl /usr/bin/kubectl

# 给kubectl配置授权
mkdir -p ~/.kube
cp /opt/kubernetes/cfg/admin.kubeconfig ~/.kube/config</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        完成kubectl的配置后，可以直接使用kubectl命令操作集群。
                    </div>

                    <h4><i class="fas fa-list-ul"></i> 5.5.2 验证Master组件运行状态</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 查看集群状态
kubectl cluster-info

# 查看各组件状态
kubectl get componentstatuses

# 查看Master节点状态
kubectl get nodes</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 注意：</strong>
                        此时可能没有Node节点显示，因为我们还没有配置Worker节点。
                    </div>

                    <h3><i class="fas fa-user-shield"></i> 5.6 授权kubelet-bootstrap用户</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>为了让后续Worker节点能够成功注册到集群中，需要提前授权kubelet-bootstrap用户具有创建CSR的权限：</p>

                    <pre><code># 创建角色绑定
kubectl create clusterrolebinding kubelet-bootstrap \
  --clusterrole=system:node-bootstrapper \
  --user=kubelet-bootstrap

# 自动批准相关CSR请求
kubectl create clusterrolebinding auto-approve-csrs-for-group \
  --clusterrole=system:certificates.k8s.io:certificatesigningrequests:nodeclient \
  --group=system:bootstrappers

# 自动批准kubelet更新证书的CSR请求
kubectl create clusterrolebinding auto-approve-renewals-for-nodes \
  --clusterrole=system:certificates.k8s.io:certificatesigningrequests:selfnodeclient \
  --group=system:nodes</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        成功创建相关的集群角色绑定，为后续Worker节点的注册做好准备。
                    </div>

                    <h3><i class="fas fa-clipboard-check"></i> 5.7 Master组件部署完成检查</h3>
                    <p>在进行下一步之前，请检查以下项目，确保Master组件已正确部署：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-tasks"></i> 检查项目</th>
                            <th><i class="fas fa-terminal"></i> 检查命令</th>
                            <th><i class="fas fa-check"></i> 预期结果</th>
                        </tr>
                        <tr>
                            <td>所有组件状态</td>
                            <td>systemctl status kube-apiserver<br>systemctl status kube-controller-manager<br>systemctl status kube-scheduler</td>
                            <td>所有服务的状态都为active (running)</td>
                        </tr>
                        <tr>
                            <td>API服务器可访问</td>
                            <td>kubectl cluster-info</td>
                            <td>显示Kubernetes control plane运行地址</td>
                        </tr>
                        <tr>
                            <td>组件健康状态</td>
                            <td>kubectl get componentstatuses</td>
                            <td>显示scheduler, controller-manager, etcd-0状态均为Healthy</td>
                        </tr>
                        <tr>
                            <td>授权配置</td>
                            <td>kubectl get clusterrolebindings | grep kubelet</td>
                            <td>显示kubelet相关的角色绑定</td>
                        </tr>
                    </table>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 问题排查：</strong>
                        如果有服务没有正常启动，请检查以下方面：<br>
                        1. 查看日志：<code>journalctl -u kube-apiserver -f</code><br>
                        2. 检查配置文件是否正确<br>
                        3. 确认证书文件权限和路径<br>
                        4. 确认网络连接和端口是否正常
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 下一步：</strong>
                        现在我们已经完成了Master节点的部署，下一步将配置Worker节点，安装并配置kubelet和kube-proxy组件。
                    </div>
                </section>

                <!-- Worker组件部署部分 -->
                <section id="worker-deployment">
                    <h2><span class="step-number">6</span>Worker组件部署</h2>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> Worker节点组件介绍：</strong>
                        Worker节点（工作节点）上需要部署的核心组件包括：容器运行时（containerd）、kubelet和kube-proxy。这些组件共同负责容器的运行、管理和网络通信。
                    </div>

                    <h3><i class="fas fa-box-open"></i> 6.1 安装容器运行时（containerd）</h3>

                    <h4><i class="fas fa-download"></i> 6.1.1 下载containerd</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <pre><code># 创建工作目录
mkdir -p /opt/containerd && cd /opt/containerd

# 下载containerd（与K8s 1.26.3兼容的版本）
wget https://github.com/containerd/containerd/releases/download/v1.7.2/containerd-1.7.2-linux-amd64.tar.gz

# 验证下载文件
ls -lh containerd-1.7.2-linux-amd64.tar.gz

# 解压缩
tar -zxvf containerd-1.7.2-linux-amd64.tar.gz -C /usr/local/

# 验证安装
/usr/local/bin/containerd --version

# 检查版本兼容性
echo "containerd 1.7.2 与 Kubernetes 1.26.3 完全兼容"</code></pre>

                    <h4><i class="fas fa-cogs"></i> 6.1.2 配置containerd</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <pre><code># 创建配置目录
mkdir -p /etc/containerd

# 生成默认配置
/usr/local/bin/containerd config default > /etc/containerd/config.toml

# 修改配置，使用systemd作为cgroup驱动
sed -i 's/SystemdCgroup = false/SystemdCgroup = true/g' /etc/containerd/config.toml

# 修改镜像仓库为国内源（针对银河麒麟系统优化）
sed -i 's#registry-1.docker.io#registry.cn-hangzhou.aliyuncs.com#g' /etc/containerd/config.toml

# 验证配置修改
grep -A 5 "SystemdCgroup" /etc/containerd/config.toml
grep -A 3 "registry.cn-hangzhou.aliyuncs.com" /etc/containerd/config.toml</code></pre>

                    <h4><i class="fas fa-file-alt"></i> 6.1.3 创建containerd服务文件</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <pre><code># 创建systemd服务文件
cat > /usr/lib/systemd/system/containerd.service << EOF
[Unit]
Description=containerd container runtime
Documentation=https://containerd.io
After=network.target local-fs.target

[Service]
ExecStartPre=-/sbin/modprobe overlay
ExecStart=/usr/local/bin/containerd
Delegate=yes
KillMode=process
Restart=always
RestartSec=5
LimitNPROC=infinity
LimitCORE=infinity
LimitNOFILE=infinity
TasksMax=infinity

[Install]
WantedBy=multi-user.target
EOF</code></pre>

                    <h4><i class="fas fa-play-circle"></i> 6.1.4 启动containerd服务</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <pre><code># 重新加载systemd配置
systemctl daemon-reload

# 设置开机启动
systemctl enable containerd

# 启动服务
systemctl start containerd

# 检查服务状态
systemctl status containerd

# 银河麒麟系统特殊检查
# 检查containerd是否正确使用systemd cgroup驱动
ctr version
ctr plugins ls | grep cri

# 验证containerd配置
containerd config dump | grep -A 5 "SystemdCgroup"

# 测试containerd功能
ctr images ls</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        containerd服务状态显示为"active (running)"，表示容器运行时已成功安装并启动。
                    </div>

                    <h4><i class="fas fa-toolbox"></i> 6.1.5 安装runc和CNI插件</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <pre><code># 下载runc（与K8s 1.26.3兼容的版本）
wget https://github.com/opencontainers/runc/releases/download/v1.1.7/runc.amd64

# 安装runc
install -m 755 runc.amd64 /usr/local/sbin/runc

# 验证runc安装
runc --version

# 下载CNI插件（与K8s 1.26.3兼容的版本）
wget https://github.com/containernetworking/plugins/releases/download/v1.3.0/cni-plugins-linux-amd64-v1.3.0.tgz

# 安装CNI插件
mkdir -p /opt/cni/bin
tar -zxvf cni-plugins-linux-amd64-v1.3.0.tgz -C /opt/cni/bin/

# 验证CNI插件安装
ls -la /opt/cni/bin/

# 检查版本兼容性
echo "runc 1.1.7 和 CNI plugins 1.3.0 与 Kubernetes 1.26.3 完全兼容"</code></pre>

                    <h3><i class="fas fa-robot"></i> 6.2 部署kubelet</h3>

                    <h4><i class="fas fa-cloud-download-alt"></i> 6.2.1 复制kubelet二进制文件</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 在Master节点上，将kubelet二进制文件复制到Worker节点
cd /opt/kubernetes/bin/
scp kubelet root@*************:/opt/kubernetes/bin/

# 在Worker节点上设置执行权限
ssh root@************* "chmod +x /opt/kubernetes/bin/kubelet"</code></pre>

                    <h4><i class="fas fa-id-card"></i> 6.2.2 创建kubelet-bootstrap.kubeconfig</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 在Master节点上，获取token值
BOOTSTRAP_TOKEN=$(awk -F "," '{print $1}' /opt/kubernetes/cfg/token.csv)

# 生成kubelet-bootstrap.kubeconfig文件
cd /opt/kubernetes/ssl

# 设置集群参数
kubectl config set-cluster kubernetes \
  --certificate-authority=/opt/kubernetes/ssl/ca.pem \
  --embed-certs=true \
  --server=https://*************:6443 \
  --kubeconfig=/opt/kubernetes/cfg/kubelet-bootstrap.kubeconfig

# 设置客户端认证参数
kubectl config set-credentials kubelet-bootstrap \
  --token=${BOOTSTRAP_TOKEN} \
  --kubeconfig=/opt/kubernetes/cfg/kubelet-bootstrap.kubeconfig

# 设置上下文参数
kubectl config set-context default \
  --cluster=kubernetes \
  --user=kubelet-bootstrap \
  --kubeconfig=/opt/kubernetes/cfg/kubelet-bootstrap.kubeconfig

# 设置默认上下文
kubectl config use-context default \
  --kubeconfig=/opt/kubernetes/cfg/kubelet-bootstrap.kubeconfig

# 将kubeconfig复制到Worker节点
scp /opt/kubernetes/cfg/kubelet-bootstrap.kubeconfig root@*************:/opt/kubernetes/cfg/</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> K8s 1.26版本重要配置说明：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- <strong>容器运行时端点：</strong> 必须指定 <code>--container-runtime-endpoint=unix:///run/containerd/containerd.sock</code></li>
                            <li>- <strong>网络插件：</strong> 移除了 <code>--network-plugin=cni</code> 参数（已默认启用CNI）</li>
                            <li>- <strong>只读端口：</strong> 建议设置 <code>readOnlyPort: 0</code> 禁用只读端口以提高安全性</li>
                            <li>- <strong>API版本：</strong> 使用 <code>kubelet.config.k8s.io/v1beta1</code> API版本</li>
                            <li>- <strong>准入控制器：</strong> 移除了PodSecurity以避免兼容性问题</li>
                            <li>- <strong>银河麒麟适配：</strong> 确保containerd配置正确使用systemd cgroup驱动</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-cog"></i> 6.2.3 创建kubelet配置文件</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <pre><code># 在Worker节点上创建kubelet工作目录
mkdir -p /var/lib/kubelet
mkdir -p /opt/kubernetes/cfg
mkdir -p /opt/kubernetes/logs

# 切换到配置目录
cd /opt/kubernetes/cfg

# 创建kubelet配置文件
cat > kubelet.conf << EOF
KUBELET_OPTS="--v=2 \\
--hostname-override=k8s-node1 \\
--kubeconfig=/opt/kubernetes/cfg/kubelet.kubeconfig \\
--bootstrap-kubeconfig=/opt/kubernetes/cfg/kubelet-bootstrap.kubeconfig \\
--config=/opt/kubernetes/cfg/kubelet-config.yml \\
--cert-dir=/opt/kubernetes/ssl \\
--container-runtime-endpoint=unix:///run/containerd/containerd.sock \\
--pod-infra-container-image=registry.cn-hangzhou.aliyuncs.com/google_containers/pause:3.6"
EOF</code></pre>

                    <h4><i class="fas fa-file-code"></i> 6.2.4 创建kubelet-config.yml配置文件</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <pre><code># 在Worker节点的配置目录下创建kubelet配置文件
cd /opt/kubernetes/cfg

cat > kubelet-config.yml << EOF
kind: KubeletConfiguration
apiVersion: kubelet.config.k8s.io/v1beta1
address: 0.0.0.0
port: 10250
readOnlyPort: 0
cgroupDriver: systemd
containerRuntimeEndpoint: unix:///run/containerd/containerd.sock
clusterDNS:
- *********0
clusterDomain: cluster.local
failSwapOn: false
# K8s 1.26版本特定配置
serializeImagePulls: false
registryPullQPS: 10
registryBurst: 20
eventRecordQPS: 50
eventBurst: 100
enableDebuggingHandlers: true
healthzPort: 10248
healthzBindAddress: 127.0.0.1
oomScoreAdj: -999
# 认证配置
authentication:
  anonymous:
    enabled: false
  webhook:
    cacheTTL: 2m0s
    enabled: true
  x509:
    clientCAFile: /opt/kubernetes/ssl/ca.pem
authorization:
  mode: Webhook
  webhook:
    cacheAuthorizedTTL: 5m0s
    cacheUnauthorizedTTL: 30s
# 驱逐策略
evictionHard:
  imagefs.available: 15%
  memory.available: 100Mi
  nodefs.available: 10%
  nodefs.inodesFree: 5%
evictionSoft:
  imagefs.available: 20%
  memory.available: 200Mi
  nodefs.available: 15%
evictionSoftGracePeriod:
  imagefs.available: 2m
  memory.available: 2m
  nodefs.available: 2m
maxOpenFiles: 1000000
maxPods: 110
# 针对4核8G配置的资源预留（优化后）
systemReserved:
  cpu: 500m
  memory: 1Gi
  ephemeral-storage: 1Gi
kubeReserved:
  cpu: 500m
  memory: 1Gi
  ephemeral-storage: 1Gi
# 内存管理策略（K8s 1.26稳定配置）
# memorySwap: {} # 在银河麒麟系统中暂时禁用以确保稳定性
# memoryThrottlingFactor: 0.8 # 在银河麒麟系统中暂时禁用以确保稳定性
# 容器日志配置
containerLogMaxSize: 50Mi
containerLogMaxFiles: 5
EOF</code></pre>

                    <h4><i class="fas fa-file-alt"></i> 6.2.5 创建kubelet系统服务文件</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <pre><code># 创建kubelet日志目录
mkdir -p /opt/kubernetes/logs

# 创建系统服务文件
cat > /usr/lib/systemd/system/kubelet.service << EOF
[Unit]
Description=Kubernetes Kubelet
After=containerd.service
Wants=containerd.service

[Service]
EnvironmentFile=/opt/kubernetes/cfg/kubelet.conf
ExecStart=/opt/kubernetes/bin/kubelet \$KUBELET_OPTS
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF</code></pre>

                    <h4><i class="fas fa-play-circle"></i> 6.2.6 启动kubelet服务</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <pre><code># 重新加载systemd配置
systemctl daemon-reload

# 设置开机启动
systemctl enable kubelet

# 启动服务
systemctl start kubelet

# 查看服务状态
systemctl status kubelet</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        kubelet服务状态显示为"active (running)"，表示kubelet已成功启动。
                    </div>

                    <h4><i class="fas fa-check-circle"></i> 6.2.7 在Master节点上批准kubelet证书请求</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 查看证书签名请求
kubectl get csr

# 批准所有待处理的证书请求
kubectl certificate approve $(kubectl get csr | grep Pending | awk '{print $1}')

# 验证节点加入
kubectl get nodes</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        Worker节点已成功加入集群，kubectl get nodes命令能够看到新加入的节点。节点状态可能为NotReady，这是因为我们还没有部署网络插件。
                    </div>

                    <h3><i class="fas fa-network-wired"></i> 6.3 部署kube-proxy</h3>

                    <h4><i class="fas fa-cloud-download-alt"></i> 6.3.1 复制kube-proxy二进制文件</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 在Master节点上，将kube-proxy二进制文件复制到Worker节点
cd /opt/kubernetes/bin/
scp kube-proxy root@*************:/opt/kubernetes/bin/

# 在Worker节点上设置执行权限
ssh root@************* "chmod +x /opt/kubernetes/bin/kube-proxy"</code></pre>

                    <h4><i class="fas fa-id-card"></i> 6.3.2 创建kube-proxy.kubeconfig</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 在Master节点上，生成kube-proxy.kubeconfig文件
cd /opt/kubernetes/ssl

# 设置集群参数
kubectl config set-cluster kubernetes \
  --certificate-authority=/opt/kubernetes/ssl/ca.pem \
  --embed-certs=true \
  --server=https://*************:6443 \
  --kubeconfig=/opt/kubernetes/cfg/kube-proxy.kubeconfig

# 设置客户端认证参数
kubectl config set-credentials kube-proxy \
  --client-certificate=/opt/kubernetes/ssl/kube-proxy.pem \
  --client-key=/opt/kubernetes/ssl/kube-proxy-key.pem \
  --embed-certs=true \
  --kubeconfig=/opt/kubernetes/cfg/kube-proxy.kubeconfig

# 设置上下文参数
kubectl config set-context default \
  --cluster=kubernetes \
  --user=kube-proxy \
  --kubeconfig=/opt/kubernetes/cfg/kube-proxy.kubeconfig

# 设置默认上下文
kubectl config use-context default \
  --kubeconfig=/opt/kubernetes/cfg/kube-proxy.kubeconfig

# 将kubeconfig复制到Worker节点
scp /opt/kubernetes/cfg/kube-proxy.kubeconfig root@*************:/opt/kubernetes/cfg/</code></pre>

                    <h4><i class="fas fa-cog"></i> 6.3.3 创建kube-proxy配置文件</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <pre><code># 在Worker节点上创建kube-proxy工作目录
mkdir -p /var/lib/kube-proxy
mkdir -p /opt/kubernetes/logs

# 切换到配置目录
cd /opt/kubernetes/cfg

# 创建kube-proxy配置文件
cat > kube-proxy.conf << EOF
KUBE_PROXY_OPTS="
--v=2 \\
--config=/opt/kubernetes/cfg/kube-proxy-config.yml"
EOF

# 创建kube-proxy-config.yml配置（K8s 1.26版本）
cat > kube-proxy-config.yml << EOF
kind: KubeProxyConfiguration
apiVersion: kubeproxy.config.k8s.io/v1alpha1
bindAddress: 0.0.0.0
metricsBindAddress: 0.0.0.0:10249
healthzBindAddress: 0.0.0.0:10256
enableProfiling: false
clientConnection:
  kubeconfig: /opt/kubernetes/cfg/kube-proxy.kubeconfig
  acceptContentTypes: ""
  contentType: "application/vnd.kubernetes.protobuf"
  qps: 5
  burst: 10
hostnameOverride: k8s-node1
clusterCIDR: **********/16
mode: ipvs
ipvs:
  scheduler: "rr"
  excludeCIDRs: []
  strictARP: false
  tcpTimeout: 0s
  tcpFinTimeout: 0s
  udpTimeout: 0s
iptables:
  masqueradeAll: true
  masqueradeBit: 14
  minSyncPeriod: 0s
  syncPeriod: 30s
nodePortAddresses: []
oomScoreAdj: -999
portRange: ""
udpIdleTimeout: 250ms
conntrack:
  maxPerCore: 32768
  min: 131072
  tcpCloseWaitTimeout: 1h0m0s
  tcpEstablishedTimeout: 24h0m0s
configSyncPeriod: 15m0s
EOF</code></pre>

                    <h4><i class="fas fa-file-alt"></i> 6.3.4 创建kube-proxy系统服务文件</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <pre><code># 在Worker节点的systemd目录下创建服务文件
cd /usr/lib/systemd/system

cat > kube-proxy.service << EOF
[Unit]
Description=Kubernetes Proxy
After=network.target

[Service]
EnvironmentFile=/opt/kubernetes/cfg/kube-proxy.conf
ExecStart=/opt/kubernetes/bin/kube-proxy \$KUBE_PROXY_OPTS
Restart=on-failure
RestartSec=5
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
EOF</code></pre>

                    <h4><i class="fas fa-cogs"></i> 6.3.5 配置IPVS模块</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <pre><code># 加载IPVS所需模块
modprobe ip_vs
modprobe ip_vs_rr
modprobe ip_vs_wrr
modprobe ip_vs_sh
modprobe nf_conntrack

# 确保开机加载IPVS模块
cat > /etc/modules-load.d/ipvs.conf << EOF
ip_vs
ip_vs_rr
ip_vs_wrr
ip_vs_sh
nf_conntrack
EOF</code></pre>

                    <h4><i class="fas fa-play-circle"></i> 6.3.6 启动kube-proxy服务</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <pre><code># 重新加载systemd配置
systemctl daemon-reload

# 设置开机启动
systemctl enable kube-proxy

# 启动服务
systemctl start kube-proxy

# 查看服务状态
systemctl status kube-proxy</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        kube-proxy服务状态显示为"active (running)"，表示kube-proxy已成功启动。
                    </div>

                    <h3><i class="fas fa-clipboard-check"></i> 6.4 Worker节点部署完成检查</h3>
                    <p>在进行下一步之前，请检查以下项目，确保Worker节点已正确配置：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-tasks"></i> 检查项目</th>
                            <th><i class="fas fa-terminal"></i> 检查命令</th>
                            <th><i class="fas fa-check"></i> 预期结果</th>
                        </tr>
                        <tr>
                            <td>containerd服务状态</td>
                            <td>systemctl status containerd</td>
                            <td>active (running)</td>
                        </tr>
                        <tr>
                            <td>kubelet服务状态</td>
                            <td>systemctl status kubelet</td>
                            <td>active (running)</td>
                        </tr>
                        <tr>
                            <td>kube-proxy服务状态</td>
                            <td>systemctl status kube-proxy</td>
                            <td>active (running)</td>
                        </tr>
                        <tr>
                            <td>节点注册状态</td>
                            <td>kubectl get nodes (在Master节点上)</td>
                            <td>Worker节点已列出（可能状态为NotReady）</td>
                        </tr>
                        <tr>
                            <td>IPVS模块加载状态</td>
                            <td>lsmod | grep ip_vs</td>
                            <td>显示已加载的IPVS模块</td>
                        </tr>
                    </table>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 问题排查：</strong>
                        如果有服务没有正常启动，请检查以下方面：<br>
                        1. 查看日志：<code>journalctl -u kubelet -f</code><br>
                        2. 检查证书是否正确复制到Worker节点<br>
                        3. 确认各配置文件路径和内容是否正确<br>
                        4. 检查CSR证书是否已批准：<code>kubectl get csr</code>
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 下一步：</strong>
                        现在我们已经完成了Worker节点的部署，Worker节点显示为NotReady状态是正常的，因为我们还没有部署网络插件。下一步将部署网络插件，以使集群节点之间能够正常通信。
                    </div>
                </section>

                <!-- 网络组件部署部分 -->
                <section id="network-deployment">
                    <h2><span class="step-number">7</span>网络组件部署</h2>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> Kubernetes网络模型：</strong>
                        Kubernetes网络模型要求所有Pod之间可以直接通信，无需NAT；所有节点可以和所有Pod通信，反之亦然。为了实现这个模型，我们需要部署网络插件。本教程使用Calico作为网络插件，它是一个灵活的网络解决方案。
                    </div>

                    <h3><i class="fas fa-network-wired"></i> 7.1 部署Calico网络插件</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>Calico是一个纯三层的数据中心网络方案，能够提供高效的IP管理和路由，同时还支持网络策略。我们将通过清单文件部署Calico：</p>

                    <pre><code># 创建Calico部署目录
mkdir -p /opt/kubernetes/addons/calico
cd /opt/kubernetes/addons/calico

# 下载Calico清单文件（使用稳定版本）
wget https://raw.githubusercontent.com/projectcalico/calico/v3.25.1/manifests/calico.yaml

# 修改Pod网络CIDR（更可靠的方法）
# 首先备份原文件
cp calico.yaml calico.yaml.bak

# 取消CALICO_IPV4POOL_CIDR的注释并设置正确的CIDR
sed -i '/# - name: CALICO_IPV4POOL_CIDR/,/# value: "***********\/16"/{
s/# - name: CALICO_IPV4POOL_CIDR/- name: CALICO_IPV4POOL_CIDR/
s/# value: "***********\/16"/value: "**********\/16"/
}' calico.yaml

# 验证修改结果
grep -A 2 "CALICO_IPV4POOL_CIDR" calico.yaml

# 应用Calico配置
kubectl apply -f calico.yaml

# 查看Pod状态
kubectl get pods -n kube-system</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        在kube-system命名空间中能看到多个calico相关的Pod正在运行，如calico-kube-controllers、calico-node等。
                    </div>

                    <h3><i class="fas fa-check"></i> 7.2 验证网络连通性</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>部署完成网络插件后，我们需要验证网络是否正常工作：</p>

                    <pre><code># 查看节点状态，应该从NotReady变为Ready
kubectl get nodes

# 等待Calico所有Pod进入Running状态
kubectl get pods -n kube-system -o wide | grep calico

# 创建一个测试Pod进行验证
cat > test-pod.yaml << EOF
apiVersion: v1
kind: Pod
metadata:
  name: nginx-test
  labels:
    app: nginx-test
spec:
  containers:
  - name: nginx
    image: nginx:stable
    ports:
    - containerPort: 80
EOF

# 部署测试Pod
kubectl apply -f test-pod.yaml

# 等待Pod运行
kubectl get pod nginx-test -o wide

# 创建一个测试Service
cat > test-svc.yaml << EOF
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
spec:
  selector:
    app: nginx-test
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
EOF

# 部署测试Service
kubectl apply -f test-svc.yaml

# 获取服务ClusterIP
NGINX_SVC_IP=$(kubectl get svc nginx-service -o jsonpath='{.spec.clusterIP}')

# 在Master节点上验证连通性
curl $NGINX_SVC_IP</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        节点状态变为Ready，测试Pod能够正常启动并运行，通过ClusterIP能够访问到nginx的欢迎页面。
                    </div>

                    <h3><i class="fas fa-shield-alt"></i> 7.3 配置网络策略</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>Calico支持Kubernetes网络策略，让我们创建一个简单的网络策略示例：</p>

                    <pre><code># 创建一个网络策略示例文件
cat > network-policy-example.yaml << EOF
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: access-nginx
  namespace: default
spec:
  podSelector:
    matchLabels:
      app: nginx-test
  ingress:
  - from:
    - ipBlock:
        cidr: **********/16
    ports:
    - protocol: TCP
      port: 80
EOF

# 应用网络策略
kubectl apply -f network-policy-example.yaml

# 查看网络策略
kubectl get networkpolicy</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 网络策略说明：</strong>
                        上面的策略只允许Pod网络CIDR范围内（**********/16）的流量通过TCP端口80访问带有app=nginx-test标签的Pod。这是一个简单的示例，在实际环境中，您可能需要根据安全要求创建更复杂的策略。
                    </div>

                    <h3><i class="fas fa-tools"></i> 7.4 Calico CLI工具安装（可选）</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>为了更好地管理和排查Calico网络问题，可以选择安装Calico CLI工具：</p>

                    <pre><code># 下载calicoctl
wget https://github.com/projectcalico/calico/releases/download/v3.25.1/calicoctl-linux-amd64 -O calicoctl

# 添加执行权限
chmod +x calicoctl

# 移动到可执行路径
mv calicoctl /usr/local/bin/

# 创建配置文件目录
mkdir -p /etc/calico

# 创建calicoctl配置文件
cat > /etc/calico/calicoctl.cfg << EOF
apiVersion: projectcalico.org/v3
kind: CalicoAPIConfig
metadata:
spec:
  datastoreType: "kubernetes"
  kubeconfig: "/root/.kube/config"
EOF

# 验证calicoctl安装
calicoctl version

# 查看Calico节点状态
calicoctl node status</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        calicoctl命令可用，并且能够显示Calico节点的连接状态。
                    </div>

                    <h3><i class="fas fa-cog"></i> 7.5 调整Calico网络配置（可选）</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>如果需要，您可以根据集群需求调整Calico的网络配置：</p>

                    <pre><code># 查看当前的IP池配置
calicoctl get ippool -o yaml

# 示例：修改IP池MTU（根据实际网络环境调整）
cat > ippool-config.yaml << EOF
apiVersion: projectcalico.org/v3
kind: IPPool
metadata:
  name: default-ipv4-ippool
spec:
  blockSize: 26
  cidr: **********/16
  ipipMode: Always
  natOutgoing: true
  nodeSelector: all()
  vxlanMode: Never
EOF

# 应用配置
calicoctl apply -f ippool-config.yaml</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 注意：</strong>
                        修改网络配置可能会影响现有服务。在生产环境中，请确保您完全理解修改的影响再进行操作。
                    </div>

                    <h3><i class="fas fa-search"></i> 7.6 网络排障技巧</h3>
                    <p>在Kubernetes网络出现问题时，以下命令和方法可以帮助进行排查：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-question-circle"></i> 问题现象</th>
                            <th><i class="fas fa-terminal"></i> 排查命令</th>
                            <th><i class="fas fa-lightbulb"></i> 可能的解决方法</th>
                        </tr>
                        <tr>
                            <td>Pod无法互相通信</td>
                            <td>kubectl exec -it [pod-name] -- ping [other-pod-ip]<br>
                                calicoctl node status</td>
                            <td>检查Calico BGP状态，重启Calico Pod，或修改网络策略</td>
                        </tr>
                        <tr>
                            <td>节点状态为NotReady</td>
                            <td>kubectl describe node [node-name]<br>
                                kubectl logs -n kube-system [calico-pod-name]</td>
                            <td>检查网络插件日志，确保网络CIDR配置正确</td>
                        </tr>
                        <tr>
                            <td>Pod无法访问外网</td>
                            <td>kubectl exec -it [pod-name] -- ping *******<br>
                                iptables -L -t nat</td>
                            <td>检查节点NAT配置，确保ip_forward开启</td>
                        </tr>
                        <tr>
                            <td>DNS解析失败</td>
                            <td>kubectl exec -it [pod-name] -- nslookup kubernetes.default<br>
                                kubectl get svc -n kube-system</td>
                            <td>检查CoreDNS运行状态，确保Service CIDR配置正确</td>
                        </tr>
                    </table>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 提示：</strong>
                        网络问题排查往往比较复杂，需要综合考虑多个因素。通过查看相关组件的日志，了解网络流量的路径，以及使用网络调试工具（如tcpdump、wireshark）可以帮助定位问题。
                    </div>

                    <h3><i class="fas fa-clipboard-check"></i> 7.7 网络组件部署完成检查</h3>
                    <p>在进行下一步之前，请确保以下项目检查无误：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-tasks"></i> 检查项目</th>
                            <th><i class="fas fa-terminal"></i> 检查命令</th>
                            <th><i class="fas fa-check"></i> 预期结果</th>
                        </tr>
                        <tr>
                            <td>节点状态</td>
                            <td>kubectl get nodes</td>
                            <td>所有节点显示为Ready状态</td>
                        </tr>
                        <tr>
                            <td>网络组件运行状态</td>
                            <td>kubectl get pods -n kube-system -o wide | grep calico</td>
                            <td>所有Calico相关的Pod显示为Running状态</td>
                        </tr>
                        <tr>
                            <td>Pod网络连通性</td>
                            <td>kubectl exec -it nginx-test -- curl [another-pod-ip]</td>
                            <td>Pod之间能够正常通信</td>
                        </tr>
                        <tr>
                            <td>Service功能</td>
                            <td>curl [service-cluster-ip]</td>
                            <td>能够通过Service ClusterIP访问Pod</td>
                        </tr>
                    </table>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 恭喜！</strong>
                        您已经成功部署了Kubernetes集群的网络组件。现在集群的基础功能已经具备，节点处于Ready状态，Pod之间可以相互通信，基本的服务发现和负载均衡功能也已就绪。
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 下一步：</strong>
                        下一步我们将部署CoreDNS，为集群提供DNS服务，以便服务能够通过名称而不仅仅是IP地址进行访问。
                    </div>
                </section>

                <!-- CoreDNS部署部分 -->
                <section id="coredns-deployment">
                    <h2><span class="step-number">8</span>CoreDNS部署</h2>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> CoreDNS介绍：</strong>
                        CoreDNS是Kubernetes集群的DNS服务器，提供服务发现功能。它允许Pod通过服务名称而不是IP地址访问其他服务，这使得应用程序配置更加灵活和可移植。
                    </div>

                    <h3><i class="fas fa-download"></i> 8.1 部署CoreDNS</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>我们将使用CoreDNS官方提供的部署清单，根据集群配置进行适当修改：</p>

                    <pre><code># 创建CoreDNS部署目录
mkdir -p /opt/kubernetes/addons/coredns
cd /opt/kubernetes/addons/coredns

# 创建CoreDNS部署文件（适配K8s 1.26.3）
cat > coredns.yaml << EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: coredns
  namespace: kube-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    kubernetes.io/bootstrapping: rbac-defaults
  name: system:coredns
rules:
  - apiGroups:
    - ""
    resources:
    - endpoints
    - services
    - pods
    - namespaces
    verbs:
    - list
    - watch
  - apiGroups:
    - discovery.k8s.io
    resources:
    - endpointslices
    verbs:
    - list
    - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  annotations:
    rbac.authorization.kubernetes.io/autoupdate: "true"
  labels:
    kubernetes.io/bootstrapping: rbac-defaults
  name: system:coredns
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: system:coredns
subjects:
- kind: ServiceAccount
  name: coredns
  namespace: kube-system
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: coredns
  namespace: kube-system
data:
  Corefile: |
    .:53 {
        errors
        health {
           lameduck 5s
        }
        ready
        kubernetes cluster.local in-addr.arpa ip6.arpa {
           pods insecure
           fallthrough in-addr.arpa ip6.arpa
           ttl 30
        }
        prometheus :9153
        forward . /etc/resolv.conf {
           max_concurrent 1000
        }
        cache 30
        loop
        reload
        loadbalance
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: coredns
  namespace: kube-system
  labels:
    k8s-app: kube-dns
    kubernetes.io/name: "CoreDNS"
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
  selector:
    matchLabels:
      k8s-app: kube-dns
  template:
    metadata:
      labels:
        k8s-app: kube-dns
    spec:
      priorityClassName: system-cluster-critical
      serviceAccountName: coredns
      tolerations:
        - key: "CriticalAddonsOnly"
          operator: "Exists"
      nodeSelector:
        kubernetes.io/os: linux
      affinity:
         podAntiAffinity:
           preferredDuringSchedulingIgnoredDuringExecution:
           - weight: 100
             podAffinityTerm:
               labelSelector:
                 matchExpressions:
                   - key: k8s-app
                     operator: In
                     values: ["kube-dns"]
               topologyKey: kubernetes.io/hostname
      containers:
      - name: coredns
        image: registry.cn-hangzhou.aliyuncs.com/google_containers/coredns:v1.10.1
        imagePullPolicy: IfNotPresent
        resources:
          limits:
            memory: 170Mi
          requests:
            cpu: 100m
            memory: 70Mi
        args: [ "-conf", "/etc/coredns/Corefile" ]
        volumeMounts:
        - name: config-volume
          mountPath: /etc/coredns
          readOnly: true
        ports:
        - containerPort: 53
          name: dns
          protocol: UDP
        - containerPort: 53
          name: dns-tcp
          protocol: TCP
        - containerPort: 9153
          name: metrics
          protocol: TCP
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            add:
            - NET_BIND_SERVICE
            drop:
            - all
          readOnlyRootFilesystem: true
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
            scheme: HTTP
          initialDelaySeconds: 60
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /ready
            port: 8181
            scheme: HTTP
      dnsPolicy: Default
      volumes:
        - name: config-volume
          configMap:
            name: coredns
            items:
            - key: Corefile
              path: Corefile
---
apiVersion: v1
kind: Service
metadata:
  name: kube-dns
  namespace: kube-system
  annotations:
    prometheus.io/port: "9153"
    prometheus.io/scrape: "true"
  labels:
    k8s-app: kube-dns
    kubernetes.io/cluster-service: "true"
    kubernetes.io/name: "CoreDNS"
spec:
  selector:
    k8s-app: kube-dns
  clusterIP: *********0
  ports:
  - name: dns
    port: 53
    protocol: UDP
  - name: dns-tcp
    port: 53
    protocol: TCP
  - name: metrics
    port: 9153
    protocol: TCP
EOF

# 应用CoreDNS配置
kubectl apply -f coredns.yaml

# 查看Pod状态
kubectl get pods -n kube-system -l k8s-app=kube-dns

# 查看服务状态
kubectl get svc -n kube-system -l k8s-app=kube-dns</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        kube-system命名空间中有一个或多个名为coredns的Pod处于Running状态，并且有一个名为kube-dns的Service。
                    </div>

                    <h3><i class="fas fa-check"></i> 8.2 验证DNS服务</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>部署完成后，我们需要验证DNS服务是否正常工作：</p>

                    <pre><code># 创建一个用于测试的Pod
cat > dns-test-pod.yaml << EOF
apiVersion: v1
kind: Pod
metadata:
  name: dns-test
spec:
  containers:
  - name: dns-test
    image: busybox:latest
    command: ["sleep", "3600"]
EOF

# 应用配置
kubectl apply -f dns-test-pod.yaml

# 等待Pod启动
kubectl wait --for=condition=Ready pod/dns-test

# 测试DNS解析
kubectl exec -it dns-test -- nslookup kubernetes.default.svc.cluster.local

# 测试服务解析
kubectl exec -it dns-test -- nslookup nginx-service.default.svc.cluster.local</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        nslookup命令应该能够成功解析kubernetes.default.svc.cluster.local和nginx-service.default.svc.cluster.local的IP地址，显示对应的ClusterIP。
                    </div>

                    <h3><i class="fas fa-cogs"></i> 8.3 自定义DNS配置（可选）</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>CoreDNS的配置存储在名为coredns的ConfigMap中，如果需要修改或添加自定义DNS记录，可以按照以下方式操作：</p>

                    <pre><code># 查看当前的CoreDNS配置
kubectl get configmap coredns -n kube-system -o yaml

# 创建自定义配置
cat > custom-coredns-cm.yaml << EOF
apiVersion: v1
kind: ConfigMap
metadata:
  name: coredns
  namespace: kube-system
data:
  Corefile: |
    .:53 {
        errors
        health {
            lameduck 5s
        }
        ready
        kubernetes cluster.local in-addr.arpa ip6.arpa {
            pods insecure
            fallthrough in-addr.arpa ip6.arpa
            ttl 30
        }
        prometheus :9153
        forward . /etc/resolv.conf {
            max_concurrent 1000
        }
        cache 30
        loop
        reload
        loadbalance
    }
    # 添加自定义域名解析
    example.com:53 {
        forward . **************
    }
EOF

# 应用自定义配置
kubectl apply -f custom-coredns-cm.yaml

# 重启CoreDNS Pod以应用新配置
kubectl rollout restart deployment coredns -n kube-system</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 配置说明：</strong>
                        上面的示例添加了一个自定义域名example.com，并将其解析请求转发到IP为**************的DNS服务器。在实际环境中，您可以根据需要添加多个域名或其他CoreDNS插件。
                    </div>

                    <h3><i class="fas fa-wrench"></i> 8.4 故障排查</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>如果DNS服务不工作，可以使用以下步骤进行排查：</p>

                    <pre><code># 检查CoreDNS Pod是否运行
kubectl get pods -n kube-system -l k8s-app=kube-dns

# 查看CoreDNS日志
kubectl logs -n kube-system -l k8s-app=kube-dns

# 检查CoreDNS服务
kubectl get svc kube-dns -n kube-system

# 确认DNS服务IP与各组件配置中的一致
kubectl get svc kube-dns -n kube-system -o jsonpath='{.spec.clusterIP}'

# 检查kubelet配置中的DNS设置
kubectl describe node [node-name] | grep "DNS"

# 在Pod中测试DNS连接
kubectl exec -it dns-test -- cat /etc/resolv.conf
kubectl exec -it dns-test -- nslookup -debug kubernetes.default.svc.cluster.local</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 常见问题：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>1. CoreDNS Pod无法启动或崩溃 - 检查日志和资源限制</li>
                            <li>2. DNS请求无响应 - 检查网络策略是否阻止了DNS流量（端口53）</li>
                            <li>3. 解析错误 - 确认Service CIDR与DNS配置匹配</li>
                            <li>4. 性能问题 - 考虑调整CoreDNS副本数或资源限制</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-wrench"></i> 8.5 高级配置</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>根据集群规模和需求，可以考虑以下高级配置：</p>

                    <pre><code># 扩展CoreDNS副本数以提高可用性
kubectl scale deployment coredns --replicas=2 -n kube-system

# 设置反亲和性，确保CoreDNS实例分布在不同节点
cat > coredns-affinity-patch.yaml << EOF
spec:
  template:
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: k8s-app
                  operator: In
                  values:
                  - kube-dns
              topologyKey: kubernetes.io/hostname
EOF

# 应用反亲和性配置
kubectl patch deployment coredns -n kube-system --patch "$(cat coredns-affinity-patch.yaml)"</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 更多配置选项：</strong>
                        CoreDNS支持多种插件和配置，详情可参考<a href="https://coredns.io/plugins/" target="_blank">官方文档</a>，包括：<br>
                        - 缓存配置（调整缓存大小和TTL）<br>
                        - 自动伸缩（根据集群大小自动调整副本数）<br>
                        - 监控集成（通过Prometheus进行监控）<br>
                        - 日志级别调整（用于排错或减少日志量）
                    </div>

                    <h3><i class="fas fa-clipboard-check"></i> 8.6 CoreDNS部署完成检查</h3>
                    <p>在进行下一步之前，请确保以下项目检查无误：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-tasks"></i> 检查项目</th>
                            <th><i class="fas fa-terminal"></i> 检查命令</th>
                            <th><i class="fas fa-check"></i> 预期结果</th>
                        </tr>
                        <tr>
                            <td>CoreDNS Pod状态</td>
                            <td>kubectl get pods -n kube-system -l k8s-app=kube-dns</td>
                            <td>所有Pod处于Running状态</td>
                        </tr>
                        <tr>
                            <td>CoreDNS Service</td>
                            <td>kubectl get svc kube-dns -n kube-system</td>
                            <td>服务IP为*********0，端口为53/UDP和53/TCP</td>
                        </tr>
                        <tr>
                            <td>域名解析</td>
                            <td>kubectl exec -it dns-test -- nslookup kubernetes.default</td>
                            <td>能够成功解析kubernetes.default的IP地址</td>
                        </tr>
                        <tr>
                            <td>Service解析</td>
                            <td>kubectl exec -it dns-test -- nslookup nginx-service</td>
                            <td>能够成功解析nginx-service的IP地址</td>
                        </tr>
                    </table>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 恭喜！</strong>
                        您已经成功部署了CoreDNS，现在您的Kubernetes集群具有完整的DNS服务发现功能，应用程序可以通过服务名称相互访问。
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 下一步：</strong>
                        接下来我们将部署Kubernetes Dashboard，这是一个基于Web的用户界面，可以帮助您可视化管理集群资源。
                    </div>
                </section>

                <!-- Dashboard部署部分 -->
                <section id="dashboard-deployment">
                    <h2><span class="step-number">9</span>Dashboard部署</h2>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> Dashboard介绍：</strong>
                        Kubernetes Dashboard是一个基于Web的用户界面，可用于管理和监控Kubernetes集群中的应用程序和资源。它提供了集群资源的可视化视图，使得管理和排障更加直观和便捷。
                    </div>

                    <h3><i class="fas fa-download"></i> 9.1 部署Dashboard</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>我们将使用官方提供的YAML文件部署Dashboard：</p>

                    <pre><code># 创建Dashboard部署目录
mkdir -p /opt/kubernetes/addons/dashboard
cd /opt/kubernetes/addons/dashboard

# 下载Dashboard YAML文件
wget https://raw.githubusercontent.com/kubernetes/dashboard/v2.7.0/aio/deploy/recommended.yaml -O dashboard.yaml

# 修改Service为NodePort类型，使其可以从外部访问
sed -i '/type: ClusterIP/c\  type: NodePort' dashboard.yaml

# 应用配置部署Dashboard
kubectl apply -f dashboard.yaml

# 查看Dashboard Pod状态
kubectl get pods -n kubernetes-dashboard

# 查看Dashboard Service
kubectl get svc -n kubernetes-dashboard</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        kubernetes-dashboard命名空间中有一个名为kubernetes-dashboard的Pod处于Running状态，并且有一个NodePort类型的Service。
                    </div>

                    <h3><i class="fas fa-user-shield"></i> 9.2 创建管理员用户</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>为了通过Dashboard管理集群，我们需要创建一个具有管理员权限的用户，并生成访问令牌：</p>

                    <pre><code># 创建dashboard-admin用户
cat > dashboard-admin.yaml << EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: admin-user
  namespace: kubernetes-dashboard
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: admin-user
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- kind: ServiceAccount
  name: admin-user
  namespace: kubernetes-dashboard
EOF

# 应用配置
kubectl apply -f dashboard-admin.yaml

# 创建用于获取token的脚本
cat > get-token.sh << EOF
#!/bin/bash
kubectl -n kubernetes-dashboard create token admin-user
EOF

# 添加执行权限
chmod +x get-token.sh

# 获取管理员令牌
./get-token.sh</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 安全提醒：</strong>
                        admin-user账户具有对集群的完全访问权限。在生产环境中，建议创建具有有限权限的用户，并根据最小权限原则授予必要的权限。请妥善保管令牌，不要泄露给未授权的人员。
                    </div>

                    <h3><i class="fas fa-link"></i> 9.3 访问Dashboard</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>现在我们可以通过浏览器访问Dashboard：</p>

                    <pre><code># 获取Dashboard的访问端口
DASHBOARD_PORT=$(kubectl get svc kubernetes-dashboard -n kubernetes-dashboard -o jsonpath='{.spec.ports[0].nodePort}')
echo "Dashboard访问端口: $DASHBOARD_PORT"

# 获取节点IP地址
NODE_IP=$(hostname -I | awk '{print $1}')
echo "节点IP地址: $NODE_IP"

# Dashboard访问地址
echo "Dashboard访问地址: https://$NODE_IP:$DASHBOARD_PORT"</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 访问说明：</strong>
                        1. 在浏览器中访问 https://节点IP:端口 <br>
                        2. 您可能会看到一个关于不安全连接的警告，这是因为Dashboard使用了自签名证书。请选择"继续访问"（不同浏览器的选项可能不同）<br>
                        3. 在登录界面选择"令牌"，然后输入之前获取的管理员令牌<br>
                        4. 登录后将看到Kubernetes Dashboard的主界面
                    </div>

                    <div style="text-align: center; margin: 30px 0;">
                        <img src="https://d33wubrfki0l68.cloudfront.net/349824f68836152722dab89465835e604719caea/6e0b7/images/docs/ui-dashboard.png" alt="Kubernetes Dashboard截图" style="max-width: 100%; border-radius: 10px; box-shadow: var(--shadow-lg);">
                        <p style="margin-top: 10px; color: var(--text-secondary);"><em>Kubernetes Dashboard界面预览</em></p>
                    </div>

                    <h3><i class="fas fa-shield-alt"></i> 9.4 增强Dashboard安全性（可选）</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>为了提高Dashboard的安全性，可以考虑以下配置：</p>

                    <pre><code># 创建自定义证书（使用OpenSSL）
mkdir -p /opt/kubernetes/certs/dashboard
cd /opt/kubernetes/certs/dashboard

# 生成私钥
openssl genrsa -out dashboard.key 2048

# 创建证书签名请求
cat > dashboard.cnf << EOF
[req]
req_extensions = v3_req
distinguished_name = req_distinguished_name
[req_distinguished_name]
[ v3_req ]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names
[alt_names]
IP.1 = *************
IP.2 = *************
DNS.1 = kubernetes-dashboard.kubernetes-dashboard.svc.cluster.local
EOF

# 生成证书签名请求
openssl req -new -key dashboard.key -out dashboard.csr -subj "/CN=kubernetes-dashboard" -config dashboard.cnf

# 使用CA证书签发Dashboard证书
openssl x509 -req -in dashboard.csr -CA /opt/kubernetes/ssl/ca.pem -CAkey /opt/kubernetes/ssl/ca-key.pem \
  -CAcreateserial -out dashboard.crt -days 365 -extensions v3_req -extfile dashboard.cnf

# 创建TLS Secret
kubectl create secret generic kubernetes-dashboard-certs --from-file=dashboard.key --from-file=dashboard.crt -n kubernetes-dashboard

# 重启Dashboard Pod以使用新证书
kubectl rollout restart deployment kubernetes-dashboard -n kubernetes-dashboard</code></pre>

                    <h3><i class="fas fa-cogs"></i> 9.5 配置访问控制（可选）</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>为不同用户创建不同权限级别的访问控制：</p>

                    <pre><code># 创建仅具有只读权限的用户
cat > dashboard-read-only.yaml << EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: read-only-user
  namespace: kubernetes-dashboard
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: dashboard-read-only
rules:
- apiGroups:
  - ""
  resources: ["*"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: read-only-user
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: dashboard-read-only
subjects:
- kind: ServiceAccount
  name: read-only-user
  namespace: kubernetes-dashboard
EOF

# 应用配置
kubectl apply -f dashboard-read-only.yaml

# 获取只读用户的令牌
kubectl -n kubernetes-dashboard create token read-only-user</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 角色说明：</strong>
                        上面创建了一个只读用户，只有查看集群资源的权限，没有创建、修改或删除资源的权限。您可以根据需要创建具有更精细权限控制的角色。
                    </div>

                    <h3><i class="fas fa-wrench"></i> 9.6 故障排查</h3>
                    <p>如果在使用Dashboard过程中遇到问题，可以使用以下方法进行排查：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-question-circle"></i> 问题现象</th>
                            <th><i class="fas fa-terminal"></i> 排查命令</th>
                            <th><i class="fas fa-lightbulb"></i> 可能的解决方法</th>
                        </tr>
                        <tr>
                            <td>Dashboard Pod无法启动</td>
                            <td>kubectl describe pod [pod-name] -n kubernetes-dashboard<br>
                                kubectl logs [pod-name] -n kubernetes-dashboard</td>
                            <td>检查错误日志，修复配置问题或资源限制</td>
                        </tr>
                        <tr>
                            <td>无法访问Dashboard界面</td>
                            <td>kubectl get svc -n kubernetes-dashboard<br>
                                netstat -lntp | grep [dashboard-port]</td>
                            <td>确认服务类型是NodePort，检查防火墙是否允许该端口</td>
                        </tr>
                        <tr>
                            <td>令牌认证失败</td>
                            <td>kubectl describe serviceaccount admin-user -n kubernetes-dashboard<br>
                                kubectl describe clusterrolebinding admin-user</td>
                            <td>重新创建ServiceAccount和ClusterRoleBinding，生成新令牌</td>
                        </tr>
                        <tr>
                            <td>浏览器证书警告</td>
                            <td>openssl x509 -in dashboard.crt -text -noout</td>
                            <td>创建正确的自签名证书，确保包含正确的主题备用名称</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-clipboard-check"></i> 9.7 Dashboard部署完成检查</h3>
                    <p>在结束本部分之前，请确保以下项目检查无误：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-tasks"></i> 检查项目</th>
                            <th><i class="fas fa-terminal"></i> 检查命令</th>
                            <th><i class="fas fa-check"></i> 预期结果</th>
                        </tr>
                        <tr>
                            <td>Dashboard Pod状态</td>
                            <td>kubectl get pods -n kubernetes-dashboard</td>
                            <td>所有Pod处于Running状态</td>
                        </tr>
                        <tr>
                            <td>Dashboard Service</td>
                            <td>kubectl get svc -n kubernetes-dashboard</td>
                            <td>Service类型为NodePort，有分配端口</td>
                        </tr>
                        <tr>
                            <td>管理员账户</td>
                            <td>kubectl get serviceaccount admin-user -n kubernetes-dashboard</td>
                            <td>存在admin-user服务账户</td>
                        </tr>
                        <tr>
                            <td>访问权限</td>
                            <td>kubectl get clusterrolebinding admin-user</td>
                            <td>admin-user绑定到cluster-admin角色</td>
                        </tr>
                    </table>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 恭喜！</strong>
                        您已经成功部署了Kubernetes Dashboard，现在可以通过Web界面可视化管理您的Kubernetes集群。Dashboard提供了丰富的功能，包括资源管理、监控和日志查看等，可以帮助您更好地了解集群状态和应用运行情况。
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 下一步：</strong>
                        接下来我们将验证整个集群的功能，确保所有组件都工作正常，并了解如何进行一些基本的集群管理操作。
                    </div>
                </section>

                <!-- 验证部署部分 -->
                <section id="verify-deployment">
                    <h2><span class="step-number">10</span>验证部署</h2>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 部署验证说明：</strong>
                        完成所有组件的部署后，需要对整个Kubernetes集群进行验证，确保所有组件和功能正常运行。本章节将指导您进行全面的验证测试。
                    </div>

                    <h3><i class="fas fa-exclamation-triangle"></i> 10.0 银河麒麟系统特殊验证</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <p>在进行常规验证前，先执行银河麒麟v10sp3系统与Kubernetes 1.26.3的兼容性验证：</p>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 银河麒麟v10sp3 + K8s 1.26.3 关键兼容性检查：</strong>
                        <pre><code># 1. 验证系统版本和内核兼容性
echo "=== 系统版本检查 ==="
cat /etc/kylin-release
uname -r
echo "内核版本应该 >= 4.18 以支持cgroup v2"

# 2. 验证容器运行时版本兼容性
echo "=== 容器运行时版本检查 ==="
containerd --version
runc --version
echo "containerd 1.7.2 和 runc 1.1.7 与 K8s 1.26.3 完全兼容"

# 3. 验证网络模块加载
echo "=== 网络模块检查 ==="
lsmod | grep -E "br_netfilter|overlay|ip_vs" || echo "警告：某些网络模块未加载"

# 4. 验证cgroup配置
echo "=== cgroup配置检查 ==="
mount | grep cgroup
systemctl status containerd | grep -i cgroup

# 5. 验证iptables功能
echo "=== iptables功能检查 ==="
iptables --version
iptables -L -n | head -5

# 6. 验证DNS解析
echo "=== DNS解析检查 ==="
nslookup kubernetes.default.svc.cluster.local *********0 || echo "DNS可能需要等待CoreDNS启动"</code></pre>
                    </div>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 银河麒麟系统验证通过标准：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>✓ 系统版本显示为银河麒麟v10sp3</li>
                            <li>✓ 内核版本 >= 4.18</li>
                            <li>✓ containerd和runc版本正确</li>
                            <li>✓ 网络模块正常加载</li>
                            <li>✓ cgroup配置为systemd</li>
                            <li>✓ iptables功能正常</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-check-circle"></i> 10.1 验证集群组件状态</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>完成银河麒麟系统兼容性验证后，检查所有核心组件的运行状态：</p>

                    <pre><code># 检查节点状态
kubectl get nodes -o wide

# 检查所有命名空间中的Pod状态
kubectl get pods --all-namespaces

# 检查核心组件状态
kubectl get componentstatuses

# 检查系统服务的状态
systemctl status etcd
systemctl status kube-apiserver
systemctl status kube-controller-manager
systemctl status kube-scheduler

# 在Worker节点上执行
ssh root@************* "systemctl status containerd"
ssh root@************* "systemctl status kubelet"
ssh root@************* "systemctl status kube-proxy"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        所有节点显示为Ready状态，所有必要的Pod都处于Running状态，核心组件状态均为Healthy，系统服务状态均为active (running)。
                    </div>

                    <h3><i class="fas fa-network-wired"></i> 10.2 验证网络连通性</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>接下来，我们需要验证集群网络是否正常工作：</p>

                    <pre><code># 创建测试Deployment和Service
cat > network-test.yaml << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-deployment
spec:
  replicas: 2
  selector:
    matchLabels:
      app: nginx
  template:
    metadata:
      labels:
        app: nginx
    spec:
      containers:
      - name: nginx
        image: nginx:stable
        ports:
        - containerPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
spec:
  selector:
    app: nginx
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
EOF

# 应用配置
kubectl apply -f network-test.yaml

# 等待Pod就绪
kubectl wait --for=condition=Ready pods -l app=nginx

# 获取Pod和Service信息
kubectl get pods -l app=nginx -o wide
kubectl get svc nginx-service

# 创建临时Pod进行测试
kubectl run test-client --image=busybox --rm -it --restart=Never -- /bin/sh -c "wget -qO- nginx-service"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        临时Pod能够通过Service名称访问nginx-service，并返回nginx的欢迎页面HTML。这表明DNS解析和Service代理功能正常工作。
                    </div>

                    <h3><i class="fas fa-hdd"></i> 10.3 验证存储功能</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>创建一个简单的PersistentVolume和PersistentVolumeClaim，测试存储功能：</p>

                    <pre><code># 创建本地存储测试
cat > storage-test.yaml << EOF
apiVersion: v1
kind: PersistentVolume
metadata:
  name: test-pv
spec:
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  persistentVolumeReclaimPolicy: Recycle
  hostPath:
    path: /tmp/data
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: test-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: Pod
metadata:
  name: storage-test
spec:
  containers:
  - name: storage-test
    image: busybox
    command: ["/bin/sh", "-c", "echo 'Data written by Kubernetes storage test' > /data/test.txt && sleep 3600"]
    volumeMounts:
    - name: test-volume
      mountPath: /data
  volumes:
  - name: test-volume
    persistentVolumeClaim:
      claimName: test-pvc
EOF

# 创建测试目录
ssh root@************* "mkdir -p /tmp/data"

# 应用配置
kubectl apply -f storage-test.yaml

# 等待Pod就绪
sleep 10
kubectl get pv
kubectl get pvc
kubectl get pods storage-test

# 验证数据是否写入
ssh root@************* "cat /tmp/data/test.txt"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        PV和PVC状态均为Bound，测试Pod状态为Running，在Worker节点上能够看到测试文件的内容。
                    </div>

                    <h3><i class="fas fa-shield-alt"></i> 10.4 验证RBAC权限控制</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>测试Kubernetes的角色基于访问控制(RBAC)功能：</p>

                    <pre><code># 创建测试命名空间
kubectl create namespace rbac-test

# 创建服务账户和角色
cat > rbac-test.yaml << EOF
apiVersion: v1
kind: ServiceAccount
metadata:
  name: test-user
  namespace: rbac-test
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: pod-reader
  namespace: rbac-test
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: read-pods
  namespace: rbac-test
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: pod-reader
subjects:
- kind: ServiceAccount
  name: test-user
  namespace: rbac-test
EOF

# 应用配置
kubectl apply -f rbac-test.yaml

# 获取服务账户令牌
TEST_TOKEN=$(kubectl -n rbac-test create token test-user)

# 使用令牌验证权限
kubectl --token=$TEST_TOKEN get pods -n rbac-test
kubectl --token=$TEST_TOKEN get pods -n default 2>/dev/null || echo "权限验证成功：无法访问default命名空间"
kubectl --token=$TEST_TOKEN create pod -n rbac-test --image=nginx test-nginx 2>/dev/null || echo "权限验证成功：无法创建Pod"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        服务账户test-user应该只能查看rbac-test命名空间中的Pod，无法查看其他命名空间的资源或创建新的Pod。这表明RBAC权限控制正常工作。
                    </div>

                    <h3><i class="fas fa-tasks"></i> 10.5 验证工作负载功能</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>测试各种Kubernetes工作负载的基本功能：</p>

                    <pre><code># 创建Deployment测试
cat > deployment-test.yaml << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: scaling-test
spec:
  replicas: 3
  selector:
    matchLabels:
      app: scaling-test
  template:
    metadata:
      labels:
        app: scaling-test
    spec:
      containers:
      - name: nginx
        image: nginx:stable
EOF

# 应用配置
kubectl apply -f deployment-test.yaml

# 验证Pod创建
kubectl get pods -l app=scaling-test

# 测试水平扩展
kubectl scale deployment scaling-test --replicas=5
sleep 20
kubectl get pods -l app=scaling-test

# 测试自愈能力
POD_NAME=$(kubectl get pods -l app=scaling-test -o jsonpath='{.items[0].metadata.name}')
kubectl delete pod $POD_NAME
sleep 10
kubectl get pods -l app=scaling-test</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        Deployment应该创建指定数量的Pod副本，水平扩展后Pod数量增加到5个，删除一个Pod后系统应该自动创建新的Pod来保持副本数量。
                    </div>

                    <h3><i class="fas fa-globe"></i> 10.6 验证Ingress功能（可选）</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>如果您需要公开服务，可以部署Ingress控制器并进行测试：</p>

                    <pre><code># 安装NGINX Ingress Controller
kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.8.1/deploy/static/provider/cloud/deploy.yaml

# 等待Ingress控制器就绪
kubectl wait --namespace ingress-nginx \
  --for=condition=ready pod \
  --selector=app.kubernetes.io/component=controller \
  --timeout=120s

# 创建测试Ingress
cat > ingress-test.yaml << EOF
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: test-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  ingressClassName: nginx
  rules:
  - host: test.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: nginx-service
            port:
              number: 80
EOF

# 应用配置
kubectl apply -f ingress-test.yaml

# 获取Ingress控制器的NodePort
INGRESS_PORT=$(kubectl get svc -n ingress-nginx ingress-nginx-controller -o jsonpath='{.spec.ports[0].nodePort}')
NODE_IP=$(hostname -I | awk '{print $1}')

# 测试访问
curl -H "Host: test.example.com" http://$NODE_IP:$INGRESS_PORT</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        Ingress控制器部署成功，通过HTTP请求并指定Host头，应该能够访问到nginx-service的内容。
                    </div>

                    <h3><i class="fas fa-heartbeat"></i> 10.7 验证健康检查功能</h3>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <p>测试Kubernetes的健康检查机制：</p>

                    <pre><code># 创建带有健康检查的Pod
cat > health-check.yaml << EOF
apiVersion: v1
kind: Pod
metadata:
  name: health-check
spec:
  containers:
  - name: health-check
    image: nginx:stable
    ports:
    - containerPort: 80
    livenessProbe:
      httpGet:
        path: /
        port: 80
      initialDelaySeconds: 3
      periodSeconds: 5
    readinessProbe:
      httpGet:
        path: /
        port: 80
      initialDelaySeconds: 5
      periodSeconds: 10
EOF

# 应用配置
kubectl apply -f health-check.yaml

# 等待Pod就绪
sleep 10

# 检查健康状态
kubectl describe pod health-check | grep -A 5 "Liveness:"
kubectl describe pod health-check | grep -A 5 "Readiness:"</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 预期结果：</strong>
                        Pod能够成功启动，Liveness和Readiness探针都报告为成功状态。
                    </div>

                    <h3><i class="fas fa-clipboard-check"></i> 10.8 集群功能验证清单</h3>
                    <p>以下是Kubernetes集群的功能验证清单，请确认所有功能均正常：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-check-circle"></i> 验证项目</th>
                            <th><i class="fas fa-thumbs-up"></i> 通过标准</th>
                        </tr>
                        <tr>
                            <td>节点状态</td>
                            <td>所有节点状态为Ready</td>
                        </tr>
                        <tr>
                            <td>核心组件状态</td>
                            <td>etcd、kube-apiserver、kube-controller-manager、kube-scheduler、kubelet、kube-proxy都正常运行</td>
                        </tr>
                        <tr>
                            <td>网络连通性</td>
                            <td>Pod之间可以相互通信，Pod可以解析Service名称并访问</td>
                        </tr>
                        <tr>
                            <td>存储功能</td>
                            <td>可以创建PV、PVC并将其挂载到Pod</td>
                        </tr>
                        <tr>
                            <td>RBAC权限控制</td>
                            <td>基于角色的访问控制能够正确限制用户权限</td>
                        </tr>
                        <tr>
                            <td>工作负载功能</td>
                            <td>Deployment能够创建、扩展和自愈</td>
                        </tr>
                        <tr>
                            <td>健康检查功能</td>
                            <td>Liveness和Readiness探针正常工作</td>
                        </tr>
                        <tr>
                            <td>DNS服务发现</td>
                            <td>Pod能够通过CoreDNS解析服务名称</td>
                        </tr>
                        <tr>
                            <td>Dashboard访问</td>
                            <td>可以通过浏览器访问Dashboard并登录</td>
                        </tr>
                    </table>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 部署完成！</strong>
                        如果以上所有验证项目都通过了，那么恭喜您！您已经成功部署了一个完整功能的Kubernetes集群。
                    </div>

                    <h3><i class="fas fa-clipboard-check"></i> 10.9 小白友好验证清单</h3>
                    <p>为了确保部署成功，请按照以下清单逐项检查：</p>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
                        <h4><i class="fas fa-list-check"></i> 基础验证（必须全部通过）</h4>
                        <ul style="list-style-type: none; padding-left: 0;">
                            <li style="margin: 10px 0;"><input type="checkbox"> <strong>节点状态：</strong> 执行 <code>kubectl get nodes</code>，两个节点都显示 Ready</li>
                            <li style="margin: 10px 0;"><input type="checkbox"> <strong>系统Pod：</strong> 执行 <code>kubectl get pods -n kube-system</code>，所有Pod都是 Running 状态</li>
                            <li style="margin: 10px 0;"><input type="checkbox"> <strong>网络连通：</strong> 创建测试Pod能够正常启动和访问</li>
                            <li style="margin: 10px 0;"><input type="checkbox"> <strong>DNS解析：</strong> Pod内能够解析 kubernetes.default 服务</li>
                            <li style="margin: 10px 0;"><input type="checkbox"> <strong>Dashboard：</strong> 能够通过浏览器访问Dashboard界面</li>
                        </ul>

                        <h4><i class="fas fa-tools"></i> 功能验证（建议完成）</h4>
                        <ul style="list-style-type: none; padding-left: 0;">
                            <li style="margin: 10px 0;"><input type="checkbox"> <strong>应用部署：</strong> 能够成功部署nginx等测试应用</li>
                            <li style="margin: 10px 0;"><input type="checkbox"> <strong>服务访问：</strong> 能够通过Service访问应用</li>
                            <li style="margin: 10px 0;"><input type="checkbox"> <strong>扩缩容：</strong> 能够对Deployment进行扩缩容操作</li>
                            <li style="margin: 10px 0;"><input type="checkbox"> <strong>存储功能：</strong> 能够创建和使用PV/PVC</li>
                        </ul>

                        <h4><i class="fas fa-shield-alt"></i> 安全验证（生产环境必需）</h4>
                        <ul style="list-style-type: none; padding-left: 0;">
                            <li style="margin: 10px 0;"><input type="checkbox"> <strong>RBAC权限：</strong> 权限控制正常工作</li>
                            <li style="margin: 10px 0;"><input type="checkbox"> <strong>证书有效：</strong> 所有证书都在有效期内</li>
                            <li style="margin: 10px 0;"><input type="checkbox"> <strong>网络策略：</strong> 网络隔离功能正常</li>
                        </ul>
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 小白提示：</strong>
                        如果某项验证失败，请不要慌张。回到对应的章节重新检查配置，或者查看故障排查部分。大多数问题都是配置错误或IP地址不匹配导致的。
                    </div>

                                        <div class="info-box">
                         <strong><i class="fas fa-info-circle"></i> 下一步：</strong>
                         尽管集群已经部署成功，但在实际使用过程中，您可能会遇到各种问题。接下来的章节将介绍常见问题的排查方法，以帮助您维护和管理集群。
                    </div>
                </section>

                <!-- 故障排查部分 -->
                <section id="troubleshooting">
                    <h2><span class="step-number">11</span>故障排查</h2>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 故障排查说明：</strong>
                        在Kubernetes集群的日常运维中，难免会遇到各种问题。本章节将介绍常见问题的排查方法和解决方案，特别针对银河麒麟v10sp3系统和4核8G配置的特殊情况。
                    </div>

                    <h3><i class="fas fa-exclamation-triangle"></i> 11.0 银河麒麟系统特有问题</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <p>银河麒麟系统可能遇到的特殊问题和解决方案：</p>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 银河麒麟v10sp3 + K8s 1.26.3 关键兼容性检查：</strong>
                        <pre><code># 1. 检查内核版本（必须支持cgroup v2）
uname -r
# 应该 >= 4.18

# 2. 检查cgroup版本
mount | grep cgroup
# 确保支持cgroup v2

# 3. 检查iptables版本
iptables --version
# 确保版本兼容

# 4. 检查容器运行时支持
which runc
runc --version
# 确保runc版本 >= 1.1.0

# 5. 检查网络模块
lsmod | grep -E "br_netfilter|overlay|ip_vs"
# 确保所有模块都已加载</code></pre>
                    </div>

                    <table>
                        <tr>
                            <th><i class="fas fa-question-circle"></i> 问题现象</th>
                            <th><i class="fas fa-terminal"></i> 排查命令</th>
                            <th><i class="fas fa-lightbulb"></i> 解决方法</th>
                        </tr>
                        <tr>
                            <td>容器运行时启动失败</td>
                            <td>systemctl status containerd<br>journalctl -u containerd</td>
                            <td>检查内核模块加载：modprobe overlay br_netfilter</td>
                        </tr>
                        <tr>
                            <td>网络插件无法启动</td>
                            <td>kubectl logs -n kube-system [calico-pod]</td>
                            <td>检查iptables版本和规则，可能需要切换到legacy模式</td>
                        </tr>
                        <tr>
                            <td>证书权限问题</td>
                            <td>ls -la /opt/kubernetes/ssl/</td>
                            <td>确保证书文件权限正确：chmod 600 *.pem</td>
                        </tr>
                        <tr>
                            <td>DNS解析失败</td>
                            <td>cat /etc/resolv.conf<br>nslookup kubernetes.default</td>
                            <td>检查系统DNS配置，可能与银河麒麟默认DNS冲突</td>
                        </tr>
                        <tr>
                            <td>资源不足导致Pod驱逐</td>
                            <td>kubectl describe node [node-name]<br>free -h</td>
                            <td>调整kubelet资源预留配置，适配4核8G环境</td>
                        </tr>
                    </table>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 银河麒麟特别注意：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- 某些企业版本可能有额外的安全策略，需要联系系统管理员</li>
                            <li>- 如果遇到权限问题，检查SELinux状态：getenforce</li>
                            <li>- 网络问题可能与系统防火墙策略有关</li>
                            <li>- 4核8G配置下，注意监控资源使用情况</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-heartbeat"></i> 11.1 集群组件故障排查</h3>
                    <div class="machine-tag machine-all"><i class="fas fa-laptop-code"></i> 所有节点</div>
                    <p>集群核心组件的故障通常是最严重的问题，以下是排查步骤：</p>

                    <h4><i class="fas fa-database"></i> 11.1.1 etcd故障排查</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 检查etcd服务状态
systemctl status etcd

# 查看etcd日志
journalctl -u etcd -f

# 检查etcd集群健康状态
export ETCDCTL_API=3
etcdctl --cacert=/opt/kubernetes/ssl/ca.pem \
  --cert=/opt/kubernetes/ssl/etcd.pem \
  --key=/opt/kubernetes/ssl/etcd-key.pem \
  --endpoints=https://*************:2379 \
  endpoint health

# 检查etcd成员列表
etcdctl --cacert=/opt/kubernetes/ssl/ca.pem \
  --cert=/opt/kubernetes/ssl/etcd.pem \
  --key=/opt/kubernetes/ssl/etcd-key.pem \
  --endpoints=https://*************:2379 \
  member list

# 检查etcd集群性能
etcdctl --cacert=/opt/kubernetes/ssl/ca.pem \
  --cert=/opt/kubernetes/ssl/etcd.pem \
  --key=/opt/kubernetes/ssl/etcd-key.pem \
  --endpoints=https://*************:2379 \
  check perf</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 常见问题与解决方案：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- <strong>证书错误：</strong> 检查证书路径和权限，确保证书未过期</li>
                            <li>- <strong>存储空间不足：</strong> 清理etcd空间或增加磁盘容量</li>
                            <li>- <strong>数据不一致：</strong> 备份数据并重新初始化etcd集群</li>
                            <li>- <strong>网络问题：</strong> 检查防火墙规则和网络连通性</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-server"></i> 11.1.2 kube-apiserver故障排查</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 检查kube-apiserver服务状态
systemctl status kube-apiserver

# 查看kube-apiserver日志
journalctl -u kube-apiserver -f

# 检查API Server是否响应
curl -k https://*************:6443/healthz

# 检查API版本信息
curl -k https://*************:6443/version

# 检查监听端口
netstat -lntp | grep kube-apiserver</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 常见问题与解决方案：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- <strong>无法连接到etcd：</strong> 确保etcd正常运行，检查连接配置</li>
                            <li>- <strong>证书问题：</strong> 验证证书路径和权限，确保证书未过期</li>
                            <li>- <strong>内存不足：</strong> 调整资源限制或增加节点内存</li>
                            <li>- <strong>权限问题：</strong> 检查RBAC配置是否正确</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-cogs"></i> 11.1.3 其他控制平面组件故障排查</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 检查kube-controller-manager服务
systemctl status kube-controller-manager
journalctl -u kube-controller-manager -f

# 检查kube-scheduler服务
systemctl status kube-scheduler
journalctl -u kube-scheduler -f

# 检查组件健康状态
kubectl get componentstatuses</code></pre>

                    <h4><i class="fas fa-hdd"></i> 11.1.4 Worker节点组件故障排查</h4>
                    <div class="machine-tag machine-node"><i class="fas fa-server"></i> Worker节点</div>
                    <pre><code># 检查kubelet服务
systemctl status kubelet
journalctl -u kubelet -f

# 检查kube-proxy服务
systemctl status kube-proxy
journalctl -u kube-proxy -f

# 检查容器运行时
systemctl status containerd
journalctl -u containerd -f

# 检查节点状态和信息
kubectl describe node $(hostname)</code></pre>

                    <h3><i class="fas fa-network-wired"></i> 11.2 网络故障排查</h3>
                    <p>Kubernetes网络问题通常表现为Pod之间无法通信，或者服务无法正常访问：</p>

                    <h4><i class="fas fa-plug"></i> 11.2.1 网络连通性检查</h4>
                    <pre><code># 检查节点网络配置
ip addr
ip route

# 检查DNS解析
kubectl exec -it $(kubectl get pod -l app=nginx -o jsonpath='{.items[0].metadata.name}') -- cat /etc/resolv.conf
kubectl exec -it $(kubectl get pod -l app=nginx -o jsonpath='{.items[0].metadata.name}') -- nslookup kubernetes.default.svc.cluster.local

# 检查Pod网络连通性
kubectl exec -it $(kubectl get pod -l app=nginx -o jsonpath='{.items[0].metadata.name}') -- ping -c 3 $(kubectl get pod -l app=nginx -o jsonpath='{.items[1].status.podIP}')

# 检查网络策略
kubectl get networkpolicies --all-namespaces</code></pre>

                    <h4><i class="fas fa-project-diagram"></i> 11.2.2 Calico网络排查</h4>
                    <div class="machine-tag machine-master"><i class="fas fa-crown"></i> Master节点</div>
                    <pre><code># 检查Calico Pod状态
kubectl get pods -n kube-system -l k8s-app=calico-node
kubectl logs -n kube-system -l k8s-app=calico-node

# 检查Calico网络配置
calicoctl get nodes
calicoctl get ippool

# 检查BGP状态
calicoctl node status

# 检查Felix状态
kubectl exec -n kube-system $(kubectl get pod -n kube-system -l k8s-app=calico-node -o jsonpath='{.items[0].metadata.name}') -- calico-felix status</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 常见网络问题与解决方案：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- <strong>CNI配置错误：</strong> 检查并修复CNI配置文件</li>
                            <li>- <strong>IP冲突：</strong> 确保Pod CIDR与节点网络不冲突</li>
                            <li>- <strong>网络策略阻止：</strong> 检查并调整NetworkPolicy</li>
                            <li>- <strong>MTU不一致：</strong> 确保所有节点的网络接口MTU设置一致</li>
                            <li>- <strong>内核模块缺失：</strong> 加载必要的内核模块如ip_vs, br_netfilter等</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-exclamation-circle"></i> 11.3 Pod故障排查</h3>
                    <p>当Pod无法正常启动或运行时，可以按照以下步骤进行排查：</p>

                    <pre><code># 查看Pod状态
kubectl get pod [pod-name] -o wide

# 查看Pod详细信息
kubectl describe pod [pod-name]

# 查看Pod日志
kubectl logs [pod-name]
kubectl logs --previous [pod-name]  # 如果Pod重启，查看之前容器的日志

# 进入Pod内部排查
kubectl exec -it [pod-name] -- /bin/sh

# 查看Pod的事件
kubectl get events --sort-by='.lastTimestamp' | grep [pod-name]</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> Pod状态说明：</strong>
                        <table>
                            <tr>
                                <th>状态</th>
                                <th>可能原因</th>
                                <th>排查方向</th>
                            </tr>
                            <tr>
                                <td>Pending</td>
                                <td>资源不足，PVC未绑定，调度失败</td>
                                <td>检查节点资源，PV/PVC状态，节点亲和性设置</td>
                            </tr>
                            <tr>
                                <td>ImagePullBackOff</td>
                                <td>镜像不存在或无法访问</td>
                                <td>检查镜像名称/标签，配置镜像仓库凭证</td>
                            </tr>
                            <tr>
                                <td>CrashLoopBackOff</td>
                                <td>容器频繁崩溃</td>
                                <td>检查容器日志，资源限制，配置错误</td>
                            </tr>
                            <tr>
                                <td>Error</td>
                                <td>通用错误状态</td>
                                <td>检查事件日志和容器日志</td>
                            </tr>
                            <tr>
                                <td>ContainerCreating</td>
                                <td>容器创建中，可能卡住</td>
                                <td>检查镜像拉取，卷挂载，网络设置</td>
                            </tr>
                        </table>
                    </div>

                    <h3><i class="fas fa-hdd"></i> 11.4 存储故障排查</h3>
                    <p>存储问题通常与PV/PVC绑定或卷挂载有关：</p>

                    <pre><code># 查看PV状态
kubectl get pv

# 查看PVC状态
kubectl get pvc --all-namespaces

# 查看StorageClass
kubectl get storageclass

# 检查特定PV/PVC详细信息
kubectl describe pv [pv-name]
kubectl describe pvc [pvc-name]

# 检查节点上的挂载点
ssh [node-ip] "findmnt | grep kubernetes"

# 检查kubelet与存储相关的日志
ssh [node-ip] "journalctl -u kubelet -f | grep volume"</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 常见存储问题与解决方案：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- <strong>PVC卡在Pending状态：</strong> 检查是否有匹配的PV，StorageClass是否正确</li>
                            <li>- <strong>卷挂载失败：</strong> 检查节点上的文件系统权限和可用空间</li>
                            <li>- <strong>访问权限错误：</strong> 确保正确设置了fsGroup和访问模式</li>
                            <li>- <strong>hostPath卷问题：</strong> 确保目录在节点上存在且有正确权限</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-tachometer-alt"></i> 11.5 性能问题排查</h3>
                    <p>当集群性能下降时，可以从以下几个方面进行排查：</p>

                    <pre><code># 检查节点资源使用情况
kubectl top nodes

# 检查Pod资源使用情况
kubectl top pods --all-namespaces

# 检查节点系统负载
ssh [node-ip] "top -b -n 1"
ssh [node-ip] "vmstat 1 5"
ssh [node-ip] "iostat -x 1 5"

# 检查etcd性能
etcdctl --endpoints=https://*************:2379 \
  --cacert=/opt/kubernetes/ssl/ca.pem \
  --cert=/opt/kubernetes/ssl/etcd.pem \
  --key=/opt/kubernetes/ssl/etcd-key.pem \
  check perf

# 检查API Server的请求延迟
kubectl get --raw /metrics | grep apiserver_request_duration_seconds</code></pre>

                    <div class="info-box">
                        <strong><i class="fas fa-info-circle"></i> 性能优化建议：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- 合理设置Pod的资源请求和限制</li>
                            <li>- 避免在单个节点上运行过多Pod</li>
                            <li>- 使用节点亲和性和反亲和性分散工作负载</li>
                            <li>- 定期清理未使用的资源（如已完成的Job）</li>
                            <li>- 优化etcd性能（定期压缩，调整参数）</li>
                            <li>- 根据需要扩展集群规模</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-shield-alt"></i> 11.6 安全问题排查</h3>
                    <p>安全问题可能导致服务中断或数据泄露：</p>

                    <pre><code># 检查证书是否过期
openssl x509 -in /opt/kubernetes/ssl/ca.pem -noout -text | grep -A 2 "Validity"
openssl x509 -in /opt/kubernetes/ssl/kubernetes.pem -noout -text | grep -A 2 "Validity"

# 检查RBAC权限设置
kubectl get clusterroles
kubectl get clusterrolebindings
kubectl get roles --all-namespaces
kubectl get rolebindings --all-namespaces

# 检查Pod安全策略
kubectl get podsecuritypolicy

# 审计重要操作日志
grep "kube-apiserver" /var/log/audit/audit.log | grep "admin"</code></pre>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 安全建议：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- 定期更新Kubernetes组件版本以修复已知漏洞</li>
                            <li>- 为每个应用配置适当的RBAC权限，遵循最小权限原则</li>
                            <li>- 使用网络策略限制Pod间通信</li>
                            <li>- 不要在容器中以root用户运行应用</li>
                            <li>- 定期轮换证书和密钥</li>
                            <li>- 启用审计日志并定期检查</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-clipboard-check"></i> 11.7 故障排查工具箱</h3>
                    <p>以下是一些有用的工具，可以帮助您排查Kubernetes集群问题：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-tools"></i> 工具名称</th>
                            <th><i class="fas fa-info-circle"></i> 用途</th>
                            <th><i class="fas fa-terminal"></i> 安装/使用方法</th>
                        </tr>
                        <tr>
                            <td>kubectl debug</td>
                            <td>在运行中的Pod旁创建调试容器</td>
                            <td>kubectl debug [pod-name] -it --image=busybox</td>
                        </tr>
                        <tr>
                            <td>kubectl-plugins</td>
                            <td>kubectl插件，扩展功能</td>
                            <td>安装krew插件管理器，然后安装所需插件</td>
                        </tr>
                        <tr>
                            <td>netshoot</td>
                            <td>网络诊断工具容器</td>
                            <td>kubectl run netshoot --rm -it --image=nicolaka/netshoot</td>
                        </tr>
                        <tr>
                            <td>kubectx/kubens</td>
                            <td>快速切换集群和命名空间</td>
                            <td>通过krew安装：kubectl krew install ctx ns</td>
                        </tr>
                        <tr>
                            <td>stern</td>
                            <td>多Pod日志聚合查看</td>
                            <td>安装后：stern [pod-prefix] --tail 10</td>
                        </tr>
                    </table>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 故障排查技巧：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>1. 总是从顶层组件开始排查（etcd -> apiserver -> 其他组件）</li>
                            <li>2. 检查日志是排查问题的最有效方法</li>
                            <li>3. 保持系统简单，避免引入不必要的复杂性</li>
                            <li>4. 记录问题和解决方案，建立知识库</li>
                            <li>5. 建立监控和告警系统，提前发现问题</li>
                        </ul>
                    </div>

                                        <div class="info-box">
                         <strong><i class="fas fa-info-circle"></i> 下一步：</strong>
                         通过本章节的内容，您应该能够解决大多数常见的Kubernetes集群问题。接下来的章节将对整个部署过程进行总结，并提供一些后续学习建议。
                    </div>
                </section>

                <!-- 总结部分 -->
                <section id="summary">
                    <h2><span class="step-number">12</span>总结</h2>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 恭喜！</strong>
                        您已经成功完成了在银河麒麟v10 sp3 240服务器上使用二进制方式部署Kubernetes集群的全过程。这是一项非常有意义的技术成就，您现在拥有了一个功能完整的Kubernetes环境，可以用于容器化应用的开发、测试和部署。
                    </div>

                    <h3><i class="fas fa-list-alt"></i> 12.1 部署总结</h3>
                    <p>在本教程中，我们完成了以下主要步骤：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-layer-group"></i> 阶段</th>
                            <th><i class="fas fa-tasks"></i> 主要任务</th>
                            <th><i class="fas fa-check-circle"></i> 成果</th>
                        </tr>
                        <tr>
                            <td>环境准备</td>
                            <td>配置操作系统、关闭防火墙和SELinux、优化内核参数</td>
                            <td>为Kubernetes提供稳定运行环境</td>
                        </tr>
                        <tr>
                            <td>证书准备</td>
                            <td>安装CFSSL工具、生成CA证书和各组件证书</td>
                            <td>建立安全的TLS通信基础</td>
                        </tr>
                        <tr>
                            <td>ETCD部署</td>
                            <td>安装etcd、配置系统服务、验证集群状态</td>
                            <td>提供可靠的数据存储</td>
                        </tr>
                        <tr>
                            <td>Master组件部署</td>
                            <td>部署apiserver、controller-manager、scheduler</td>
                            <td>建立集群控制平面</td>
                        </tr>
                        <tr>
                            <td>Worker组件部署</td>
                            <td>部署containerd、kubelet、kube-proxy</td>
                            <td>使节点能够运行工作负载</td>
                        </tr>
                        <tr>
                            <td>网络配置</td>
                            <td>部署Calico网络插件、配置网络策略</td>
                            <td>实现Pod间网络通信</td>
                        </tr>
                        <tr>
                            <td>附加组件部署</td>
                            <td>部署CoreDNS、Dashboard等插件</td>
                            <td>提供DNS解析和可视化管理</td>
                        </tr>
                        <tr>
                            <td>验证测试</td>
                            <td>验证网络连通性、存储、工作负载等功能</td>
                            <td>确认集群各项功能正常</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-graduation-cap"></i> 12.2 学习收获</h3>
                    <p>通过本教程的实践，您获得了以下技术能力：</p>

                    <ul style="list-style-type: none; padding-left: 20px;">
                        <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 了解Kubernetes架构和各组件功能</li>
                        <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 掌握Kubernetes集群的二进制部署方法</li>
                        <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 理解TLS证书在Kubernetes中的应用</li>
                        <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 熟悉Kubernetes网络模型及实现机制</li>
                        <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 掌握Kubernetes集群的故障排查方法</li>
                        <li><i class="fas fa-check-circle" style="color: var(--success-color); margin-right: 10px;"></i> 获得在国产操作系统上部署复杂环境的经验</li>
                    </ul>

                    <h3><i class="fas fa-rocket"></i> 12.3 下一步建议</h3>
                    <p>现在您已经拥有了一个功能完整的Kubernetes集群，可以考虑以下几个方向继续深化学习和应用：</p>

                    <div class="info-box">
                        <strong><i class="fas fa-book"></i> 继续学习：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- 学习Kubernetes的高级特性，如StatefulSet、DaemonSet等</li>
                            <li>- 深入研究Kubernetes的网络模型和存储机制</li>
                            <li>- 掌握Helm包管理工具，简化应用部署</li>
                            <li>- 学习Kubernetes Operator开发，扩展Kubernetes能力</li>
                            <li>- 探索服务网格（Service Mesh）技术</li>
                        </ul>
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-tools"></i> 完善集群：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- 部署Prometheus和Grafana监控系统</li>
                            <li>- 配置EFK/ELK日志收集平台</li>
                            <li>- 实现集群的自动伸缩（Cluster Autoscaler）</li>
                            <li>- 部署持久化存储解决方案</li>
                            <li>- 实现集群备份和恢复机制</li>
                        </ul>
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-shield-alt"></i> 提升安全性：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- 实施更严格的RBAC权限控制</li>
                            <li>- 配置网络策略限制Pod通信</li>
                            <li>- 部署容器镜像扫描工具</li>
                            <li>- 实施PodSecurityPolicy限制Pod权限</li>
                            <li>- 定期轮换证书和密钥</li>
                        </ul>
                    </div>

                    <div class="info-box">
                        <strong><i class="fas fa-cogs"></i> 生产化配置：</strong>
                        <ul style="list-style-type: none; padding-left: 10px;">
                            <li>- 增加Master节点，实现高可用控制平面</li>
                            <li>- 建立CI/CD流水线，实现自动化部署</li>
                            <li>- 实施灾备策略，确保数据安全</li>
                            <li>- 优化集群性能，提高资源利用率</li>
                            <li>- 建立常规维护和升级计划</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-book"></i> 12.4 参考资源</h3>
                    <p>以下是一些有用的学习资源，可帮助您进一步提升Kubernetes技能：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-link"></i> 资源类型</th>
                            <th><i class="fas fa-book-open"></i> 资源名称</th>
                            <th><i class="fas fa-info-circle"></i> 描述</th>
                        </tr>
                        <tr>
                            <td>官方文档</td>
                            <td><a href="https://kubernetes.io/zh/docs/home/" target="_blank">Kubernetes官方文档</a></td>
                            <td>最权威的Kubernetes学习资源</td>
                        </tr>
                        <tr>
                            <td>电子书</td>
                            <td>《Kubernetes权威指南》</td>
                            <td>全面介绍Kubernetes的基本概念和实践</td>
                        </tr>
                        <tr>
                            <td>在线课程</td>
                            <td>Kubernetes认证管理员(CKA)课程</td>
                            <td>帮助您获得Kubernetes官方认证</td>
                        </tr>
                        <tr>
                            <td>开源项目</td>
                            <td><a href="https://github.com/kubernetes/examples" target="_blank">Kubernetes Examples</a></td>
                            <td>各种Kubernetes应用示例</td>
                        </tr>
                        <tr>
                            <td>实践平台</td>
                            <td><a href="https://www.katacoda.com/courses/kubernetes" target="_blank">Katacoda Kubernetes</a></td>
                            <td>免费的交互式Kubernetes学习环境</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-heart"></i> 12.5 结束语</h3>
                    <p>感谢您选择并完成本教程！希望这份详细的指南能够帮助您在银河麒麟操作系统上成功部署和运行Kubernetes集群，并为您的容器化应用提供坚实的基础设施支持。</p>

                    <p>Kubernetes的学习是一段持续的旅程，希望您能够从这个基础出发，不断探索和掌握更多的云原生技术，从而在数字化转型的浪潮中把握先机。</p>

                                        <div class="success-box" style="text-align: center;">
                         <strong style="font-size: 24px;"><i class="fas fa-trophy"></i> 恭喜您完成银河麒麟v10 sp3 240服务器K8s二进制部署教程！</strong>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <!-- 页脚部分 -->
  <!--   <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <i class="fas fa-cubes"></i> Kubernetes二进制部署教程
                </div>
                <div class="footer-info">
                    <p>基于银河麒麟v10 sp3 240服务器系统 | 适用于4核8G配置</p>
                    <p><i class="far fa-copyright"></i> 2024 版权所有 | <i class="fas fa-code"></i> 用❤️制作</p>
                </div>
                <div class="footer-links">
                    <a href="https://kubernetes.io/zh/" target="_blank"><i class="fas fa-external-link-alt"></i> Kubernetes官网</a>
                    <a href="https://github.com/kubernetes/kubernetes" target="_blank"><i class="fab fa-github"></i> GitHub</a>
                    <a href="#top"><i class="fas fa-arrow-up"></i> 返回顶部</a>
                </div>
            </div>
        </div>
    </footer>-->

    <!-- 返回顶部按钮 -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-chevron-up"></i>
    </a>

    <!-- JavaScript -->
    <script>
        // 目录展开收起功能
        document.addEventListener('DOMContentLoaded', function() {
            // 侧边栏折叠展开
            const toggleButton = document.getElementById('toggle-sidebar');
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');

            if (toggleButton) {
                toggleButton.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                    mainContent.classList.toggle('expanded');
                });
            }

            // 返回顶部按钮
            const backToTopButton = document.getElementById('backToTop');

            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopButton.style.display = 'flex';
                } else {
                    backToTopButton.style.display = 'none';
                }
            });



            // 导航目录激活状态
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.sidebar-nav a');
            let lastActiveSection = '';

            // 防抖函数
            function debounce(func, wait) {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            }

            function updateActiveNav() {
                let current = '';

                sections.forEach(function(section) {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.clientHeight;
                    if (window.pageYOffset >= sectionTop - 100) {
                        current = section.getAttribute('id');
                    }
                });

                // 只有当激活的章节发生变化时才更新
                if (current !== lastActiveSection) {
                    navLinks.forEach(function(link) {
                        link.classList.remove('active');
                        if (current && link.getAttribute('href') === '#' + current) {
                            link.classList.add('active');
                        }
                    });
                    lastActiveSection = current;
                }
            }

            // 使用防抖的滚动事件监听器
            window.addEventListener('scroll', debounce(updateActiveNav, 50));

            // 平滑滚动到顶部
            backToTopButton.addEventListener('click', function(e) {
                e.preventDefault();
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });

            // 初始化时更新导航状态
            updateActiveNav();
        });
    </script>
</body>

</html>