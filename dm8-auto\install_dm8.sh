#!/bin/bash
set -e

echo "=== 开始达梦数据库静默安装 ==="

# 设置安装目录
INSTALL_DIR="/home/<USER>/dmdbms"
SETUP_DIR="/tmp/dm8_setup"
AUTO_XML="/tmp/auto.xml"

# 检查安装文件
if [ ! -f "$SETUP_DIR/DMInstall.bin" ]; then
    echo "错误：找不到安装文件 $SETUP_DIR/DMInstall.bin"
    exit 1
fi

if [ ! -f "$AUTO_XML" ]; then
    echo "错误：找不到配置文件 $AUTO_XML"
    exit 1
fi

# 设置权限
chmod 755 "$SETUP_DIR/DMInstall.bin"

# 切换到安装目录
cd "$SETUP_DIR"

echo "开始执行静默安装..."
echo "安装目录: $INSTALL_DIR"
echo "配置文件: $AUTO_XML"

# 使用expect来处理交互式输入
expect << 'EOF'
set timeout 300
spawn ./DMInstall.bin -q /tmp/auto.xml

# 处理可能的覆盖提示
expect {
    "overwrite" {
        send "Y\r"
        exp_continue
    }
    "already existed" {
        send "Y\r"
        exp_continue
    }
    "已经存在" {
        send "Y\r"
        exp_continue
    }
    "Input err" {
        send "Y\r"
        exp_continue
    }
    "输入错误" {
        send "Y\r"
        exp_continue
    }
    eof {
        puts "安装完成"
    }
    timeout {
        puts "安装超时"
        exit 1
    }
}
EOF

# 检查安装结果
if [ $? -eq 0 ] && [ -d "$INSTALL_DIR" ]; then
    echo "✓ 达梦数据库安装成功"
    
    # 设置权限
    chown -R dmdba:dmdba "$INSTALL_DIR"
    
    # 清理不必要的文件
    echo "清理不必要的文件..."
    rm -rf "$INSTALL_DIR/doc" \
           "$INSTALL_DIR/samples" \
           "$INSTALL_DIR/uninstall" \
           "$INSTALL_DIR/web" \
           "$INSTALL_DIR/tool/dmhs" \
           "$INSTALL_DIR/tool/dts" 2>/dev/null || true
    
    echo "✓ 文件清理完成"
else
    echo "✗ 达梦数据库安装失败"
    exit 1
fi

echo "=== 达梦数据库安装完成 ==="
