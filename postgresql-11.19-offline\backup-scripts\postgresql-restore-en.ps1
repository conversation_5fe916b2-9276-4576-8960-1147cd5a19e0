# PostgreSQL 11.19 Offline Complete Restore Script
# Version: 2.1
# Author: AI Assistant
# Date: 2025-07-12
#
# Usage:
#   .\postgresql-restore-en.ps1 -BackupPath "path\to\backup"
#   .\postgresql-restore-en.ps1 -BackupPath "path\to\backup" -VerboseLog

param(
    [Parameter(Mandatory=$true)]
    [string]$BackupPath,
    [switch]$ForceRestore,
    [switch]$VerboseLog
)

# Global variables
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$TempDir = Join-Path (Get-Location) "temp_restore_$Timestamp"
$LogFile = Join-Path $BackupPath "restore_log_$Timestamp.txt"

# Logging function
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogContent = "[$Time] [$Level] $Message"

    switch ($Level) {
        "ERROR" { Write-Host $LogContent -ForegroundColor Red }
        "WARN"  { Write-Host $LogContent -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $LogContent -ForegroundColor Green }
        default { Write-Host $LogContent -ForegroundColor White }
    }

    if (Test-Path $BackupPath) {
        try {
            $LogDir = Split-Path $LogFile -Parent
            if (!(Test-Path $LogDir)) {
                New-Item -ItemType Directory -Path $LogDir -Force | Out-Null
            }
            $LogContent | Out-File -FilePath $LogFile -Append -Encoding UTF8 -Force
        }
        catch {
            Write-Warning "Unable to write to log file: $($_.Exception.Message)"
        }
    }
}

# Progress display function
function Show-Progress {
    param([string]$Activity, [string]$Status, [int]$PercentComplete)
    Write-Progress -Activity $Activity -Status $Status -PercentComplete $PercentComplete
}

# Get container user ID function
function Get-ContainerUserId {
    param([string]$ImageName, [string]$UserName)
    try {
        $UserId = docker run --rm --entrypoint="" $ImageName id -u $UserName 2>$null
        if ($LASTEXITCODE -eq 0) {
            return $UserId.Trim()
        }
    } catch {}
    return $null
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "PostgreSQL 11.19 Offline Complete Restore Script v2.1" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Log "Starting PostgreSQL complete restore"
Write-Log "Backup path: $BackupPath"
Write-Log "Temporary directory: $TempDir"

# Validate backup path
if (-not (Test-Path $BackupPath)) {
    Write-Log "Error: Backup path does not exist: $BackupPath" "ERROR"
    exit 1
}

# Create temporary directory
try {
    New-Item -ItemType Directory -Path $TempDir -Force | Out-Null
    Write-Log "Temporary directory created successfully" "SUCCESS"
} catch {
    Write-Log "Failed to create temporary directory: $($_.Exception.Message)" "ERROR"
    exit 1
}

try {
    # Step 1: Check Docker environment
    Show-Progress "Environment Check" "Checking Docker environment..." 5
    Write-Log "Checking Docker environment..."
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker is not running or not installed"
    }
    Write-Log "Docker version: $dockerVersion" "SUCCESS"

    # Step 2: Validate backup files
    Show-Progress "Environment Check" "Validating backup files..." 10
    Write-Log "Validating backup files..."
    $RequiredFiles = @(
        "postgres_data_volume.zip",
        "postgres_logs_volume.zip"
    )

    $MissingFiles = @()
    foreach ($File in $RequiredFiles) {
        $FilePath = Join-Path $BackupPath $File
        if (-not (Test-Path $FilePath)) {
            $MissingFiles += $File
            Write-Log "Warning: Missing backup file $File" "WARN"
        } else {
            $Size = [math]::Round((Get-Item $FilePath).Length / 1MB, 2)
            Write-Log "Found backup file: $File (${Size}MB)" "SUCCESS"
        }
    }

    if ($MissingFiles.Count -gt 0 -and -not $ForceRestore) {
        throw "Missing critical backup files, use -ForceRestore parameter to continue"
    }

    # Step 3: Get container user ID
    Show-Progress "Environment Check" "Detecting container user ID..." 15
    Write-Log "Detecting container user ID..."

    # For offline version, try to get user ID from existing container first
    $PostgresUserId = $null
    $ExistingContainer = docker ps -a --filter "name=postgresql-11.19" --format "{{.Names}}" 2>$null
    if ($ExistingContainer -eq "postgresql-11.19") {
        try {
            $PostgresUserId = docker exec postgresql-11.19 id -u postgres 2>$null
            if ($LASTEXITCODE -eq 0 -and $PostgresUserId) {
                Write-Log "Got PostgreSQL user ID from existing container: $PostgresUserId" "SUCCESS"
            }
        } catch {
            # Container might not be running, continue with other methods
        }
    }

    # If unable to get from existing container, try from image
    if (-not $PostgresUserId) {
        $PostgresUserId = Get-ContainerUserId "postgresql:11.19" "postgres"
        if ($PostgresUserId) {
            Write-Log "Got PostgreSQL user ID from image: $PostgresUserId" "SUCCESS"
        }
    }

    # If still unable to get, use offline version default user ID
    if (-not $PostgresUserId) {
        # Offline version usually uses system default postgres user ID, typically 26 or 999
        Write-Log "Unable to detect PostgreSQL user ID, trying common values..." "WARN"
        # Try to get system postgres user ID first
        try {
            $SystemPostgresId = docker run --rm postgresql:11.19 id -u postgres 2>$null
            if ($LASTEXITCODE -eq 0 -and $SystemPostgresId) {
                $PostgresUserId = $SystemPostgresId.Trim()
                Write-Log "Using system postgres user ID: $PostgresUserId" "SUCCESS"
            } else {
                $PostgresUserId = "26"  # CentOS default postgres user ID
                Write-Log "Using CentOS default postgres user ID: $PostgresUserId" "WARN"
            }
        } catch {
            $PostgresUserId = "26"
            Write-Log "Using default postgres user ID: $PostgresUserId" "WARN"
        }
    }

    # Step 4: Stop existing services
    Show-Progress "Prepare Restore" "Stopping existing services..." 20
    Write-Log "Stopping existing services..."
    docker-compose down 2>&1 | Out-Null
    Start-Sleep -Seconds 5
    Write-Log "Existing services stopped" "SUCCESS"

    # Step 5: Remove existing data volumes
    Show-Progress "Prepare Restore" "Removing existing data volumes..." 25
    Write-Log "Removing existing data volumes..."
    $VolumeList = @("postgresql_11_19_data_offline", "postgresql_11_19_logs_offline", "pgadmin_data_offline")
    foreach ($VolumeName in $VolumeList) {
        docker volume rm $VolumeName -f 2>$null | Out-Null
        Write-Log "Removed data volume: $VolumeName" "SUCCESS"
    }

    # Step 6: Restore configuration files
    Show-Progress "Restore Configuration" "Restoring configuration files..." 30
    Write-Log "Restoring configuration files..."
    $ConfigFileList = @("docker-compose.yml", "Dockerfile", "postgresql.conf", "pg_hba.conf", "docker-entrypoint.sh")
    $RestoredFiles = 0
    foreach ($File in $ConfigFileList) {
        $BackupFile = Join-Path $BackupPath $File
        if (Test-Path $BackupFile) {
            Copy-Item $BackupFile -Destination . -Force
            Write-Log "Restored: $File" "SUCCESS"
            $RestoredFiles++
        }
    }
    
    # Restore initialization scripts directory
    $InitScriptsBackup = Join-Path $BackupPath "init-scripts"
    if (Test-Path $InitScriptsBackup) {
        if (Test-Path "init-scripts") {
            Remove-Item "init-scripts" -Recurse -Force
        }
        Copy-Item $InitScriptsBackup -Destination . -Recurse -Force
        Write-Log "Restored: init-scripts directory" "SUCCESS"
        $RestoredFiles++
    }
    
    Write-Log "Configuration file restore completed, restored $RestoredFiles files/directories" "SUCCESS"

    # Step 7: Create new data volumes
    Show-Progress "Restore Data Volumes" "Creating new data volumes..." 35
    Write-Log "Creating new data volumes..."
    foreach ($VolumeName in $VolumeList) {
        docker volume create $VolumeName 2>&1 | Out-Null
        Write-Log "Created data volume: $VolumeName" "SUCCESS"
    }

    # Step 8: Restore PostgreSQL main data volume
    Show-Progress "Restore Data Volumes" "Restoring PostgreSQL main data volume..." 45
    Write-Log "Restoring PostgreSQL main data volume..."
    $DataVolumeFile = Join-Path $BackupPath "postgres_data_volume.zip"
    if (Test-Path $DataVolumeFile) {
        try {
            # Create temporary extraction directory
            $TempExtractDir = Join-Path $env:TEMP "postgres_restore_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            New-Item -ItemType Directory -Path $TempExtractDir -Force | Out-Null

            # Extract backup file
            Expand-Archive -Path $DataVolumeFile -DestinationPath $TempExtractDir -Force

            # Use docker cp method to restore data volume
            $RestoreContainer = docker create -v postgresql_11_19_data_offline:/data alpine 2>&1
            if ($LASTEXITCODE -eq 0) {
                # Check extracted directory structure
                $DataSourcePath = if (Test-Path "${TempExtractDir}/postgres_data") { "${TempExtractDir}/postgres_data/." } else { "${TempExtractDir}/data/." }
                docker cp $DataSourcePath "${RestoreContainer}:/data/" 2>&1 | Out-Null
                docker rm $RestoreContainer 2>&1 | Out-Null

                # Fix data directory permissions - optimized for offline version
                Write-Log "Fixing data directory permissions..."
                docker run --rm -v postgresql_11_19_data_offline:/data alpine sh -c "
                    # Set basic permissions
                    chown -R ${PostgresUserId}:${PostgresUserId} /data 2>/dev/null || chown -R ${PostgresUserId}:0 /data

                    # Set data directory permissions (required by PostgreSQL)
                    chmod 750 /data 2>/dev/null || chmod 755 /data

                    # Set subdirectory permissions
                    find /data -type d -exec chmod 750 {} \; 2>/dev/null || find /data -type d -exec chmod 755 {} \;

                    # Set file permissions
                    find /data -type f -exec chmod 640 {} \; 2>/dev/null || find /data -type f -exec chmod 644 {} \;

                    # Special file permissions
                    find /data -name 'postgresql.conf' -exec chmod 600 {} \; 2>/dev/null || true
                    find /data -name 'pg_hba.conf' -exec chmod 600 {} \; 2>/dev/null || true
                    find /data -name 'pg_ident.conf' -exec chmod 600 {} \; 2>/dev/null || true
                    find /data -name 'postmaster.pid' -exec chmod 644 {} \; 2>/dev/null || true

                    echo 'PostgreSQL data directory permissions fixed'
                " 2>&1 | Out-Null

                Write-Log "PostgreSQL main data volume restore completed" "SUCCESS"
            } else {
                Write-Log "Failed to create restore container" "ERROR"
            }

            # Clean up temporary directory
            Remove-Item $TempExtractDir -Recurse -Force -ErrorAction SilentlyContinue
        } catch {
            Write-Log "PostgreSQL main data volume restore failed: $($_.Exception.Message)" "ERROR"
        }
    }

    # Step 9: Restore PostgreSQL log volume
    Show-Progress "Restore Data Volumes" "Restoring PostgreSQL log volume..." 55
    Write-Log "Restoring PostgreSQL log volume..."
    $LogVolumeFile = Join-Path $BackupPath "postgres_logs_volume.zip"
    if (Test-Path $LogVolumeFile) {
        try {
            # Create temporary extraction directory
            $TempExtractDir = Join-Path $env:TEMP "postgres_logs_restore_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            New-Item -ItemType Directory -Path $TempExtractDir -Force | Out-Null

            # Extract backup file
            Expand-Archive -Path $LogVolumeFile -DestinationPath $TempExtractDir -Force

            # Use docker cp method to restore log volume
            $RestoreContainer = docker create -v postgresql_11_19_logs_offline:/data alpine 2>&1
            if ($LASTEXITCODE -eq 0) {
                # Check extracted directory structure
                $LogSourcePath = if (Test-Path "${TempExtractDir}/postgres_logs") { "${TempExtractDir}/postgres_logs/." } else { "${TempExtractDir}/data/." }
                docker cp $LogSourcePath "${RestoreContainer}:/data/" 2>&1 | Out-Null
                docker rm $RestoreContainer 2>&1 | Out-Null

                # Fix log directory permissions
                Write-Log "Fixing log directory permissions..."
                docker run --rm -v postgresql_11_19_logs_offline:/data alpine sh -c "
                    # Set log directory permissions
                    chown -R ${PostgresUserId}:${PostgresUserId} /data 2>/dev/null || chown -R ${PostgresUserId}:0 /data
                    chmod 755 /data

                    # Set log file permissions
                    find /data -type f -name '*.log' -exec chmod 644 {} \; 2>/dev/null || true
                    find /data -type d -exec chmod 755 {} \; 2>/dev/null || true

                    echo 'PostgreSQL log directory permissions fixed'
                " 2>&1 | Out-Null

                Write-Log "PostgreSQL log volume restore completed" "SUCCESS"
            } else {
                Write-Log "Failed to create log restore container" "WARN"
            }

            # Clean up temporary directory
            Remove-Item $TempExtractDir -Recurse -Force -ErrorAction SilentlyContinue
        } catch {
            Write-Log "PostgreSQL log volume restore failed: $($_.Exception.Message)" "WARN"
        }
    }

    # Step 10: Restore pgAdmin data volume
    Show-Progress "Restore Data Volumes" "Restoring pgAdmin data volume..." 60
    Write-Log "Restoring pgAdmin data volume..."
    $PgAdminVolumeFile = Join-Path $BackupPath "pgadmin_data_volume.zip"
    if (Test-Path $PgAdminVolumeFile) {
        try {
            # Create temporary extraction directory
            $TempExtractDir = Join-Path $env:TEMP "pgadmin_restore_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            New-Item -ItemType Directory -Path $TempExtractDir -Force | Out-Null

            # Extract backup file
            Expand-Archive -Path $PgAdminVolumeFile -DestinationPath $TempExtractDir -Force

            # Use docker cp method to restore pgAdmin volume
            $RestoreContainer = docker create -v pgadmin_data_offline:/data alpine 2>&1
            if ($LASTEXITCODE -eq 0) {
                # Check extracted directory structure
                $PgAdminSourcePath = if (Test-Path "${TempExtractDir}/pgadmin_data") { "${TempExtractDir}/pgadmin_data/." } else { "${TempExtractDir}/data/." }
                docker cp $PgAdminSourcePath "${RestoreContainer}:/data/" 2>&1 | Out-Null
                docker rm $RestoreContainer 2>&1 | Out-Null

                # Fix pgAdmin permissions
                Write-Log "Fixing pgAdmin permissions..."
                docker run --rm -v pgadmin_data_offline:/data alpine sh -c "
                    # Set pgAdmin user permissions (5050:5050)
                    chown -R 5050:5050 /data 2>/dev/null || chown -R 5050:0 /data

                    # Set directory permissions
                    chmod 755 /data
                    find /data -type d -exec chmod 755 {} \; 2>/dev/null || true

                    # Set file permissions
                    find /data -type f -exec chmod 644 {} \; 2>/dev/null || true

                    # Special permission settings
                    find /data -name '*.db' -exec chmod 600 {} \; 2>/dev/null || true
                    find /data -name '*.log' -exec chmod 644 {} \; 2>/dev/null || true

                    echo 'pgAdmin permissions fixed'
                " 2>&1 | Out-Null

                Write-Log "pgAdmin data volume restore completed" "SUCCESS"
            } else {
                Write-Log "Failed to create pgAdmin restore container" "WARN"
            }

            # Clean up temporary directory
            Remove-Item $TempExtractDir -Recurse -Force -ErrorAction SilentlyContinue
        } catch {
            Write-Log "pgAdmin data volume restore failed: $($_.Exception.Message)" "WARN"
        }
    }

    # Step 11: Start services
    Show-Progress "Start Services" "Starting PostgreSQL services..." 70
    Write-Log "Starting PostgreSQL services..."
    docker-compose up -d 2>&1 | Out-Null

    # Step 12: Wait for PostgreSQL to start
    Show-Progress "Start Services" "Waiting for PostgreSQL to start..." 80
    Write-Log "Waiting for PostgreSQL to start..."
    $Count = 0
    $MaxRetries = 15
    $PostgresStarted = $false

    do {
        Start-Sleep -Seconds 8
        $Count++
        Write-Log "Checking PostgreSQL status (attempt $Count/$MaxRetries)..."

        # First check container status
        $ContainerStatus = docker inspect postgresql-11.19 --format='{{.State.Status}}' 2>$null
        if ($ContainerStatus -eq "running") {
            Write-Log "PostgreSQL container running normally" "SUCCESS"

            # Check PostgreSQL service status
            $Status = docker exec postgresql-11.19 pg_isready -U postgres -p 3433 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Log "PostgreSQL service started successfully" "SUCCESS"
                $PostgresStarted = $true
                break
            } else {
                Write-Log "PostgreSQL service not ready yet, continuing to wait..." "INFO"
            }
        } elseif ($ContainerStatus -eq "restarting") {
            Write-Log "PostgreSQL container is restarting, this may indicate configuration or permission issues" "WARN"
            # If container keeps restarting, exit early
            if ($Count -gt 5) {
                Write-Log "Container keeps restarting, possible configuration issue, check container logs: docker logs postgresql-11.19" "ERROR"
                break
            }
        } else {
            Write-Log "PostgreSQL container status abnormal: $ContainerStatus" "WARN"
        }
    } while ($Count -lt $MaxRetries)

    if (-not $PostgresStarted) {
        if ($ContainerStatus -eq "restarting") {
            Write-Log "PostgreSQL container restart issue, recommend checking:" "ERROR"
            Write-Log "1. Container logs: docker logs postgresql-11.19" "INFO"
            Write-Log "2. Data directory permissions are correct" "INFO"
            Write-Log "3. Configuration files are valid" "INFO"
        } else {
            Write-Log "PostgreSQL service startup timeout, please check container status manually" "WARN"
        }
    }

    # Step 13: Restore SQL data (if available)
    Show-Progress "Restore SQL Data" "Restoring SQL data..." 85
    Write-Log "Restoring SQL data..."
    $SqlBackupFile = Join-Path $BackupPath "postgresql_all_databases.sql.zip"
    if (Test-Path $SqlBackupFile) {
        try {
            # Extract SQL backup
            $SqlTempDir = Join-Path $env:TEMP "postgres_sql_restore_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
            if (Test-Path $SqlTempDir) {
                Remove-Item $SqlTempDir -Recurse -Force -ErrorAction SilentlyContinue
            }
            New-Item -ItemType Directory -Path $SqlTempDir -Force | Out-Null
            Expand-Archive -Path $SqlBackupFile -DestinationPath $SqlTempDir -Force

            $SqlFile = Get-ChildItem -Path $SqlTempDir -Filter "*.sql" | Select-Object -First 1
            if ($SqlFile -and (Test-Path $SqlFile.FullName)) {
                Write-Log "Importing SQL data from file: $($SqlFile.Name)"

                # First validate SQL file content
                $FirstLine = Get-Content $SqlFile.FullName -TotalCount 1 -ErrorAction SilentlyContinue
                if ($FirstLine -and ($FirstLine.StartsWith("--") -or $FirstLine.StartsWith("/*") -or $FirstLine.Contains("PostgreSQL"))) {
                    # Import SQL with improved error handling
                    try {
                        $ImportCommand = "PGPASSWORD=postgres psql -U postgres -h localhost -p 3433 -d postgres"
                        $SqlContent = Get-Content $SqlFile.FullName -Raw -ErrorAction Stop
                        if ($SqlContent) {
                            $ImportResult = $SqlContent | docker exec -i postgresql-11.19 sh -c "$ImportCommand" 2>&1

                            # Check if import was successful by testing database connectivity
                            $TestResult = docker exec postgresql-11.19 sh -c "PGPASSWORD=postgres psql -U postgres -h localhost -p 3433 -d postgres -c '\l'" 2>$null
                            if ($TestResult -and $TestResult.Contains("postgres")) {
                                Write-Log "SQL data import completed successfully" "SUCCESS"
                            } else {
                                Write-Log "SQL data import completed, but verification failed" "WARN"
                            }
                        } else {
                            Write-Log "SQL file is empty, skipping import" "WARN"
                        }
                    } catch {
                        Write-Log "SQL import process error: $($_.Exception.Message)" "WARN"
                    }
                } else {
                    Write-Log "SQL file validation failed or file is empty, skipping import" "WARN"
                }
            } else {
                Write-Log "No valid SQL file found in backup, skipping SQL data restore" "WARN"
            }

            # Clean up temporary directory
            if (Test-Path $SqlTempDir) {
                Remove-Item $SqlTempDir -Recurse -Force -ErrorAction SilentlyContinue
            }
        } catch {
            Write-Log "SQL data restore failed: $($_.Exception.Message)" "WARN"
        }
    } else {
        Write-Log "No SQL backup file found, skipping SQL data restore" "INFO"
    }

    # Step 14: Verify restore
    Show-Progress "Verify Restore" "Verifying restore results..." 95
    Write-Log "Verifying restore results..."

    # Check container status
    $ContainerStatus = docker inspect postgresql-11.19 --format='{{.State.Status}}' 2>$null
    if ($ContainerStatus -eq "running") {
        Write-Log "Container status: Running" "SUCCESS"

        # Check database connectivity
        $DbTest = docker exec postgresql-11.19 sh -c "PGPASSWORD=postgres psql -U postgres -h localhost -p 3433 -d postgres -c '\l'" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Database connectivity: Normal" "SUCCESS"
            Write-Log "PostgreSQL service restored successfully" "SUCCESS"
        } else {
            Write-Log "Database connectivity: Failed" "WARN"
        }
    } else {
        Write-Log "Container status: $ContainerStatus" "WARN"
    }

} catch {
    Write-Log "Restore failed: $($_.Exception.Message)" "ERROR"
    Write-Log "Please check Docker status and backup file integrity" "ERROR"
    exit 1
} finally {
    # Clean up temporary directory
    if (Test-Path $TempDir) {
        Remove-Item $TempDir -Recurse -Force
        Write-Log "Cleaned up temporary directory" "INFO"
    }
    Write-Progress -Activity "Restore Complete" -Completed
}

Show-Progress "Restore Complete" "Restore completed!" 100
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "Restore Complete!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Log "Restore completed successfully" "SUCCESS"
Write-Log "PostgreSQL service accessible at: localhost:3433" "SUCCESS"
Write-Log "Username: postgres, Password: postgres" "SUCCESS"

# If PostgreSQL has issues, try automatic fix
$FinalPostgresStatus = docker inspect postgresql-11.19 --format='{{.State.Status}}' 2>$null
if ($FinalPostgresStatus -eq "restarting" -or $FinalPostgresStatus -ne "running") {
    Write-Log "Detected PostgreSQL may have issues, attempting automatic permission fix..." "WARN"
    try {
        $FixScript = Join-Path (Split-Path $MyInvocation.MyCommand.Path -Parent) "fix-postgresql-permissions.ps1"
        if (Test-Path $FixScript) {
            & $FixScript -Restart
            Write-Log "PostgreSQL permission fix completed" "SUCCESS"
        } else {
            Write-Log "Permission fix script not found, please run manually: .\backup-scripts\fix-postgresql-permissions.ps1" "WARN"
        }
    } catch {
        Write-Log "Automatic fix failed: $($_.Exception.Message)" "WARN"
        Write-Log "Please run permission fix script manually: .\backup-scripts\fix-postgresql-permissions.ps1" "WARN"
    }
}

# Check pgAdmin status and auto-fix
$FinalPgAdminStatus = docker inspect pgadmin4 --format='{{.State.Status}}' 2>$null
if ($FinalPgAdminStatus -eq "restarting") {
    Write-Log "Detected pgAdmin is restarting, attempting automatic permission fix..." "WARN"
    try {
        $PgAdminFixScript = Join-Path (Split-Path $MyInvocation.MyCommand.Path -Parent) "fix-pgadmin-permissions.ps1"
        if (Test-Path $PgAdminFixScript) {
            & $PgAdminFixScript -Restart
            Write-Log "pgAdmin permission fix completed" "SUCCESS"
        } else {
            Write-Log "pgAdmin permission fix script not found, please run manually: .\backup-scripts\fix-pgadmin-permissions.ps1" "WARN"
        }
    } catch {
        Write-Log "pgAdmin automatic fix failed: $($_.Exception.Message)" "WARN"
        Write-Log "Please run pgAdmin permission fix script manually: .\backup-scripts\fix-pgadmin-permissions.ps1" "WARN"
    }
}

Write-Host "`nRestore script execution completed" -ForegroundColor Green
