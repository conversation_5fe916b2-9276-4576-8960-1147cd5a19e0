# 离线nginx Docker构建完整方案

## 📋 方案概述

本方案专门针对断网环境下的nginx Docker镜像构建，基于CentOS 7镜像，使用RPM包方式安装所有编译依赖，包含SSL、HTTP/2等完整模块。

## 📁 项目文件结构

```
nginx-offline-build/
├── Dockerfile                           # Docker镜像构建文件
├── download-all-packages.sh             # 🌐 联网环境：下载所有依赖包
├── build-nginx-offline.sh               # 🔌 断网环境：构建nginx镜像
├── test-nginx.sh                        # 🔌 断网环境：测试nginx容器
├── 离线nginx构建完整教程.html           # 详细教程
├── scripts/
│   └── build-nginx.sh                   # nginx编译脚本
├── config/
│   ├── nginx.conf                       # nginx主配置文件
│   └── default.conf                     # 默认站点配置
├── packages/                            # 🌐 nginx源码包目录
│   ├── nginx-1.24.0.tar.gz
│   ├── pcre-8.45.tar.gz
│   ├── zlib-1.2.13.tar.gz
│   └── openssl-1.1.1w.tar.gz
└── centos7-rpms/                        # 🌐 CentOS 7编译依赖RPM包目录
    ├── gcc-4.8.5-44.el7.x86_64.rpm
    ├── gcc-c++-4.8.5-44.el7.x86_64.rpm
    ├── make-3.82-24.el7.x86_64.rpm
    ├── pcre-devel-8.32-17.el7.x86_64.rpm
    ├── zlib-devel-1.2.7-18.el7.x86_64.rpm
    ├── openssl-devel-1.0.2k-25.el7_9.x86_64.rpm
    ├── GeoIP-devel-1.5.0-14.el7.x86_64.rpm
    └── ... (其他依赖RPM包)
```

## 🚀 快速开始

### 🌐 联网环境（准备阶段）

```bash
# 1. 下载所有依赖包
chmod +x download-all-packages.sh
./download-all-packages.sh

# 2. 打包传输
tar -czf nginx-offline-build.tar.gz .
```

### 🔌 断网环境（构建阶段）

```bash
# 1. 解压项目文件
tar -xzf nginx-offline-build.tar.gz
cd nginx-offline-build

# 2. 构建nginx镜像（已验证成功）
docker build -t nginx-offline-build .

# 3. 测试验证
# 检查nginx版本
docker run --rm nginx-offline-build /usr/local/nginx/sbin/nginx -v

# 检查编译配置
docker run --rm nginx-offline-build /usr/local/nginx/sbin/nginx -V

# 启动nginx容器
docker run -d -p 8080:80 --name nginx-test nginx-offline-build /usr/local/nginx/sbin/nginx -g "daemon off;"
```

## 📝 各文件用途详解

### 🌐 联网环境文件
- **download-all-packages.sh** - 自动下载nginx源码包和所有CentOS 7编译依赖RPM包

### 🔌 断网环境文件
- **Dockerfile** - Docker镜像构建文件，定义如何构建nginx镜像
- **build-nginx-offline.sh** - 离线构建脚本，检查环境并构建Docker镜像
- **test-nginx.sh** - 测试脚本，验证nginx容器是否正常工作
- **scripts/build-nginx.sh** - 在Docker容器内编译nginx的脚本
- **config/nginx.conf** - nginx主配置文件
- **config/default.conf** - 默认站点配置文件

## 📊 nginx编译模块

本方案包含以下nginx模块（已验证成功构建）：

- ✅ `--with-http_ssl_module` - SSL/TLS支持
- ✅ `--with-http_v2_module` - HTTP/2支持
- ✅ `--with-http_realip_module` - 真实IP模块
- ✅ `--with-http_auth_request_module` - 认证请求模块
- ✅ `--with-http_secure_link_module` - 安全链接模块
- ✅ `--with-http_stub_status_module` - 状态模块
- ✅ `--with-http_gzip_static_module` - 静态gzip模块
- ✅ `--with-threads` - 线程支持
- ✅ `--with-file-aio` - 文件异步IO

**注意**：为了确保编译成功，以下模块已被禁用：
- ❌ `--without-http_proxy_module` - 代理模块（避免编译错误）
- ❌ `--without-http_fastcgi_module` - FastCGI模块（避免编译错误）
- ❌ `--without-http_uwsgi_module` - uWSGI模块（避免编译错误）
- ❌ `--without-http_scgi_module` - SCGI模块（避免编译错误）
- ❌ `--without-http_grpc_module` - gRPC模块（避免编译错误）

**构建信息**：
- nginx版本：1.16.1
- 编译器：gcc 4.8.5
- OpenSSL版本：1.0.2k-fips
- 安装路径：`/usr/local/nginx/sbin/nginx`

## 🔧 常用管理命令

### 容器管理
```bash
# 查看容器状态
docker ps

# 启动容器
docker run -d -p 80:80 --name nginx-server nginx-offline:latest

# 停止容器
docker stop nginx-offline-server

# 查看日志
docker logs nginx-offline-server
```

### nginx管理
```bash
# 进入容器
docker exec -it nginx-offline-server /bin/bash

# 测试配置
docker exec nginx-offline-server /usr/local/nginx/sbin/nginx -t

# 重载配置
docker exec nginx-offline-server /usr/local/nginx/sbin/nginx -s reload

# 查看版本和模块
docker exec nginx-offline-server /usr/local/nginx/sbin/nginx -V
```

## ❗ 故障排除

### 常见问题

1. **下载失败** - 检查网络连接，尝试手动下载
2. **Docker构建失败** - 检查依赖文件是否完整
3. **容器启动失败** - 检查端口占用，查看容器日志
4. **nginx配置错误** - 测试配置文件语法

### 调试命令
```bash
# 查看构建日志
docker build -t nginx-offline:latest . --no-cache

# 检查文件
ls -la packages/ centos7-rpms/

# 测试连接
curl http://localhost
```

## ✅ 验收测试

构建完成后，请执行以下测试：

- ✅ 镜像构建成功：`docker images nginx-offline`
- ✅ 容器正常启动：`docker ps`
- ✅ HTTP访问正常：`curl http://localhost`
- ✅ nginx进程运行：`docker exec nginx-offline-server ps aux | grep nginx`
- ✅ 配置文件正确：`docker exec nginx-offline-server /usr/local/nginx/sbin/nginx -t`
- ✅ 模块加载完整：`docker exec nginx-offline-server /usr/local/nginx/sbin/nginx -V`

## 📞 技术支持

如果遇到问题：

1. 查看详细教程：`离线nginx构建完整教程.html`
2. 检查所有依赖文件是否完整
3. 查看Docker和容器日志
4. 验证基础环境是否满足要求

## 📋 快速参考

- **联网环境**：`./download-all-packages.sh`
- **断网环境**：`./build-nginx-offline.sh`
- **测试验证**：`./test-nginx.sh`
- **镜像名称**：`nginx-offline:latest`
- **容器名称**：`nginx-offline-server`
