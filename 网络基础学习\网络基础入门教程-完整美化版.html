<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络基础入门教程 - 从零开始学网络</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #48bb78;
            --warning-color: #ed8936;
            --error-color: #f56565;
            --info-color: #4299e1;
            --dark-bg: #1a202c;
            --dark-surface: #2d3748;
            --light-bg: #f7fafc;
            --light-surface: #ffffff;
            --text-primary: #2d3748;
            --text-secondary: #718096;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 320px;
            height: 100vh;
            background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
            color: white;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .sidebar-header {
            padding: 30px 25px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px) rotate(0deg);
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

        .sidebar-header h2 {
            color: #ffffff;
            text-align: center;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
            position: relative;
            z-index: 1;
        }

        .sidebar-header p {
            color: rgba(255, 255, 255, 0.8);
            text-align: center;
            font-size: 13px;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .sidebar ul {
            list-style: none;
        }

        .sidebar li {
            margin: 3px 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 15px 25px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
            font-weight: 500;
            border-left: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 0;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            transition: width 0.3s ease;
        }

        .sidebar a:hover::before {
            width: 100%;
        }

        .sidebar a:hover {
            color: #ffffff;
            border-left-color: var(--accent-color);
            padding-left: 35px;
            background: rgba(255, 255, 255, 0.05);
        }

        .sidebar a.active {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-left-color: var(--accent-color);
            box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .sidebar a i {
            margin-right: 12px;
            width: 16px;
            text-align: center;
            font-size: 14px;
        }

        /* 主内容区域 */
        .main-content {
            margin-left: 320px;
            min-height: 100vh;
            background: var(--light-bg);
        }

        .content-wrapper {
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .container {
            background: var(--light-surface);
            padding: 50px;
            border-radius: 20px;
            box-shadow: var(--shadow-xl);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
        }

        /* 标题样式 */
        h1 {
            color: var(--text-primary);
            text-align: center;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 40px;
            position: relative;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 2px;
        }

        h2 {
            color: var(--text-primary);
            font-size: 28px;
            font-weight: 600;
            margin: 50px 0 25px 0;
            padding-left: 20px;
            border-left: 5px solid var(--primary-color);
            position: relative;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 0 3px 3px 0;
        }

        h3 {
            color: var(--primary-dark);
            font-size: 22px;
            font-weight: 600;
            margin: 35px 0 20px 0;
            position: relative;
            padding-left: 15px;
        }

        h3::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        h4 {
            color: var(--success-color);
            font-size: 18px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            display: flex;
            align-items: center;
        }

        h4::before {
            content: '▶';
            margin-right: 8px;
            color: var(--success-color);
            font-size: 12px;
        }

        /* 提示框样式 */
        .info-box,
        .warning-box,
        .success-box,
        .danger-box {
            padding: 25px;
            margin: 25px 0;
            border-radius: 15px;
            border-left: 5px solid;
            position: relative;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .info-box:hover,
        .warning-box:hover,
        .success-box:hover,
        .danger-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .info-box {
            background: linear-gradient(135deg, rgba(66, 153, 225, 0.1) 0%, rgba(66, 153, 225, 0.05) 100%);
            border-left-color: var(--info-color);
            color: #2c5282;
        }

        .warning-box {
            background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%);
            border-left-color: var(--warning-color);
            color: #c05621;
        }

        .success-box {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%);
            border-left-color: var(--success-color);
            color: #276749;
        }

        .danger-box {
            background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%);
            border-left-color: var(--error-color);
            color: #c53030;
        }

        /* 代码样式 */
        pre {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            padding: 25px;
            border-radius: 15px;
            overflow-x: auto;
            margin: 25px 0;
            border-left: 5px solid var(--primary-color);
            position: relative;
            box-shadow: var(--shadow-lg);
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.6;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border-radius: 15px 15px 0 0;
        }

        code {
            background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
            padding: 4px 8px;
            border-radius: 6px;
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
            color: var(--error-color);
            font-size: 13px;
            font-weight: 500;
            box-shadow: var(--shadow-sm);
        }

        pre code {
            background: transparent;
            padding: 0;
            color: #e2e8f0;
            box-shadow: none;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 25px 0;
            background: var(--light-surface);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        th,
        td {
            padding: 18px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        tr:nth-child(even) {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }

        tr:hover {
            background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        /* 步骤编号 */
        .step-number {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-weight: 700;
            font-size: 18px;
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .step-number::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* 网络图标样式 */
        .network-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 15px;
            margin: 10px;
            color: white;
            font-size: 24px;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .network-icon:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-xl);
        }

        .icon-router {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .icon-switch {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .icon-computer {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .icon-server {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            text-decoration: none;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-xl);
            z-index: 999;
            font-size: 20px;
        }

        .back-to-top:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        /* 移动端菜单按钮 */
        .mobile-menu-btn {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: var(--primary-color);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            font-size: 20px;
            cursor: pointer;
            box-shadow: var(--shadow-lg);
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .sidebar {
                width: 280px;
            }

            .main-content {
                margin-left: 280px;
            }

            .content-wrapper {
                padding: 30px;
            }

            .container {
                padding: 40px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-wrapper {
                padding: 20px;
            }

            .container {
                padding: 30px;
                border-radius: 15px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 24px;
            }

            .step-number {
                width: 35px;
                height: 35px;
                font-size: 16px;
                margin-right: 15px;
            }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
        }
    </style>
</head>

<body>
    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" id="mobileMenuBtn">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏导航 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h2><i class="fas fa-network-wired"></i> 网络基础教程</h2>
            <p>从零开始学网络 - 小白入门指南</p>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li><a href="#introduction"><i class="fas fa-play"></i>1. 网络入门介绍</a></li>
                <li><a href="#basic-concepts"><i class="fas fa-lightbulb"></i>2. 基础概念</a></li>
                <li><a href="#network-models"><i class="fas fa-layer-group"></i>3. 网络模型</a></li>
                <li><a href="#ip-addressing"><i class="fas fa-map-marker-alt"></i>4. IP地址详解</a></li>
                <li><a href="#subnetting"><i class="fas fa-sitemap"></i>5. 子网划分</a></li>
                <li><a href="#routing"><i class="fas fa-route"></i>6. 路由基础</a></li>
                <li><a href="#switching"><i class="fas fa-exchange-alt"></i>7. 交换基础</a></li>
                <li><a href="#protocols"><i class="fas fa-handshake"></i>8. 常用协议</a></li>
                <li><a href="#troubleshooting"><i class="fas fa-tools"></i>9. 故障排查</a></li>
                <li><a href="#practical-examples"><i class="fas fa-laptop-code"></i>10. 实践案例</a></li>
                <li><a href="#advanced-topics"><i class="fas fa-graduation-cap"></i>11. 进阶话题</a></li>
                <li><a href="#summary"><i class="fas fa-flag-checkered"></i>12. 总结</a></li>
            </ul>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-wrapper">
            <div class="container">
                <h1><i class="fas fa-rocket"></i> 网络基础入门教程 - 从零开始学网络</h1>

                <div class="info-box">
                    <strong><i class="fas fa-info-circle"></i>
                        教程说明：</strong>本教程专为网络小白设计，从最基础的概念开始，循序渐进地介绍网络知识。每个概念都会配有详细的解释、图解和实际例子，确保您能够真正理解并掌握网络基础知识。
                </div>

                <div class="warning-box">
                    <strong><i class="fas fa-exclamation-triangle"></i> 学习建议：</strong>
                    <div style="margin-top: 15px;">
                        <ul>
                            <li>📚 <strong>按顺序学习：</strong>建议按照章节顺序学习，每个概念都是后续内容的基础</li>
                            <li>🔍 <strong>动手实践：</strong>理论结合实践，多做练习和实验</li>
                            <li>📝 <strong>做好笔记：</strong>记录重要概念和自己的理解</li>
                            <li>🤔 <strong>多思考：</strong>遇到不懂的地方要多思考，可以查阅更多资料</li>
                        </ul>
                    </div>
                </div>

                <section id="introduction">
                    <h2><span class="step-number">1</span>网络入门介绍</h2>

                    <h3><i class="fas fa-question-circle"></i> 1.1 什么是计算机网络？</h3>
                    <p>计算机网络就像是连接不同设备的"桥梁"，让它们能够互相通信和共享资源。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 简单理解：</strong>
                        <p>想象一下，网络就像是城市里的道路系统：</p>
                        <ul>
                            <li>🏠 <strong>房子</strong> = 计算机、手机等设备</li>
                            <li>🛣️ <strong>道路</strong> = 网络连接（网线、WiFi等）</li>
                            <li>🚗 <strong>汽车</strong> = 数据包（传输的信息）</li>
                            <li>🚦 <strong>红绿灯</strong> = 路由器、交换机（控制数据流向）</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-history"></i> 1.2 网络的发展历程</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-calendar"></i> 时期</th>
                            <th><i class="fas fa-rocket"></i> 重要事件</th>
                            <th><i class="fas fa-info-circle"></i> 意义</th>
                        </tr>
                        <tr>
                            <td>1960年代</td>
                            <td>ARPANET诞生</td>
                            <td>互联网的前身，最初只连接4台计算机</td>
                        </tr>
                        <tr>
                            <td>1980年代</td>
                            <td>TCP/IP协议确立</td>
                            <td>奠定了现代互联网的基础</td>
                        </tr>
                        <tr>
                            <td>1990年代</td>
                            <td>万维网(WWW)出现</td>
                            <td>让普通人也能轻松使用互联网</td>
                        </tr>
                        <tr>
                            <td>2000年代</td>
                            <td>宽带普及</td>
                            <td>网速大幅提升，多媒体应用兴起</td>
                        </tr>
                        <tr>
                            <td>2010年代至今</td>
                            <td>移动互联网、5G</td>
                            <td>随时随地上网，万物互联</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-users"></i> 1.3 网络在生活中的应用</h3>
                    <div class="success-box">
                        <strong><i class="fas fa-star"></i> 网络无处不在：</strong>
                        <div style="margin-top: 15px;">
                            <div style="display: flex; flex-wrap: wrap; gap: 15px;">
                                <div class="network-icon icon-computer">
                                    <i class="fas fa-laptop"></i>
                                </div>
                                <div class="network-icon icon-router">
                                    <i class="fas fa-wifi"></i>
                                </div>
                                <div class="network-icon icon-server">
                                    <i class="fas fa-mobile-alt"></i>
                                </div>
                                <div class="network-icon icon-switch">
                                    <i class="fas fa-gamepad"></i>
                                </div>
                            </div>
                            <ul style="margin-top: 20px;">
                                <li>💻 <strong>办公学习：</strong>远程办公、在线教育、视频会议</li>
                                <li>🛒 <strong>购物娱乐：</strong>网购、在线视频、游戏、社交媒体</li>
                                <li>🏥 <strong>生活服务：</strong>网上银行、医疗挂号、外卖订餐</li>
                                <li>🏭 <strong>工业应用：</strong>智能制造、物联网、自动化控制</li>
                            </ul>
                        </div>
                    </div>

                    <h3><i class="fas fa-sitemap"></i> 1.4 网络的基本组成</h3>
                    <p>一个完整的网络系统包含以下几个关键部分：</p>

                    <h4><i class="fas fa-desktop"></i> 1.4.1 终端设备（端点）</h4>
                    <div class="info-box">
                        <p><strong>作用：</strong>发送和接收数据的设备</p>
                        <p><strong>举例：</strong></p>
                        <ul>
                            <li>🖥️ 台式电脑、笔记本电脑</li>
                            <li>📱 智能手机、平板电脑</li>
                            <li>🖨️ 打印机、扫描仪</li>
                            <li>📺 智能电视、游戏机</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-exchange-alt"></i> 1.4.2 网络设备（中介）</h4>
                    <div class="info-box">
                        <p><strong>作用：</strong>转发和控制数据流向</p>
                        <p><strong>举例：</strong></p>
                        <ul>
                            <li>🔀 <strong>路由器：</strong>连接不同网络，决定数据传输路径</li>
                            <li>🔄 <strong>交换机：</strong>在同一网络内转发数据</li>
                            <li>📡 <strong>无线接入点：</strong>提供WiFi连接</li>
                            <li>🔥 <strong>防火墙：</strong>保护网络安全</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-plug"></i> 1.4.3 传输介质（道路）</h4>
                    <div class="info-box">
                        <p><strong>作用：</strong>承载数据传输的物理通道</p>
                        <p><strong>举例：</strong></p>
                        <ul>
                            <li>🔌 <strong>有线：</strong>网线（双绞线）、光纤、同轴电缆</li>
                            <li>📶 <strong>无线：</strong>WiFi、蓝牙、4G/5G、卫星</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-cogs"></i> 1.4.4 网络协议（规则）</h4>
                    <div class="info-box">
                        <p><strong>作用：</strong>规定数据传输的格式和规则</p>
                        <p><strong>举例：</strong></p>
                        <ul>
                            <li>🌐 <strong>HTTP/HTTPS：</strong>网页浏览协议</li>
                            <li>📧 <strong>SMTP/POP3：</strong>邮件传输协议</li>
                            <li>📁 <strong>FTP：</strong>文件传输协议</li>
                            <li>🔗 <strong>TCP/IP：</strong>互联网基础协议</li>
                        </ul>
                    </div>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
                        <p>这四个组成部分缺一不可，就像建房子需要地基、墙体、屋顶和门窗一样。理解了这些基本组成，你就掌握了网络的整体框架！</p>
                    </div>
                </section>

                <section id="basic-concepts">
                    <h2><span class="step-number">2</span>基础概念</h2>

                    <h3><i class="fas fa-lightbulb"></i> 2.1 网络中的重要概念</h3>
                    <p>在学习网络之前，我们需要先了解一些基本概念，这些就像学习语言前要先学字母一样重要。</p>

                    <h4><i class="fas fa-map-marker-alt"></i> 2.1.1 IP地址 - 网络中的"门牌号"</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-home"></i> 生活类比：</strong>
                        <p>IP地址就像你家的门牌号码，每个连接到网络的设备都有一个唯一的IP地址。</p>
                        <ul>
                            <li>🏠 <strong>IPv4地址：</strong>*************（像"北京市朝阳区XX街道XX号"）</li>
                            <li>🏢 <strong>IPv6地址：</strong>2001:db8::1（像"更详细的地址编码"）</li>
                        </ul>
                        <p><strong>实际例子：</strong>当你在浏览器输入网址时，系统会自动把网址转换成IP地址来找到对应的服务器。</p>
                    </div>

                    <h4><i class="fas fa-door-open"></i> 2.1.2 端口 - 设备上的"房间号"</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-building"></i> 生活类比：</strong>
                        <p>如果IP地址是大楼地址，那么端口就是大楼里的房间号。</p>
                        <ul>
                            <li>🌐 <strong>80端口：</strong>网页服务的房间（HTTP）</li>
                            <li>🔒 <strong>443端口：</strong>安全网页服务的房间（HTTPS）</li>
                            <li>📧 <strong>25端口：</strong>邮件发送服务的房间（SMTP）</li>
                            <li>📁 <strong>21端口：</strong>文件传输服务的房间（FTP）</li>
                        </ul>
                        <p><strong>实际例子：</strong>*************:80 表示访问IP为*************设备上的80端口服务。</p>
                    </div>

                    <h4><i class="fas fa-route"></i> 2.1.3 路由 - 数据传输的"导航"</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-map"></i> 生活类比：</strong>
                        <p>路由就像GPS导航，帮助数据包找到从源地址到目标地址的最佳路径。</p>
                        <ul>
                            <li>🗺️ <strong>路由表：</strong>记录到各个网络的路径信息</li>
                            <li>🚦 <strong>路由器：</strong>根据路由表决定数据包的下一跳</li>
                            <li>🛣️ <strong>跳数：</strong>数据包经过的路由器数量</li>
                        </ul>
                        <p><strong>实际例子：</strong>你发送的微信消息会经过多个路由器才能到达朋友的手机。</p>
                    </div>

                    <h4><i class="fas fa-exchange-alt"></i> 2.1.4 交换 - 同一网络内的"邮递员"</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-mail-bulk"></i> 生活类比：</strong>
                        <p>交换机就像小区内的邮递员，负责在同一个网络内准确投递数据包。</p>
                        <ul>
                            <li>📮 <strong>MAC地址：</strong>设备的物理地址（像身份证号）</li>
                            <li>📋 <strong>MAC地址表：</strong>记录每个设备连接在哪个端口</li>
                            <li>📦 <strong>帧转发：</strong>根据MAC地址表转发数据帧</li>
                        </ul>
                        <p><strong>实际例子：</strong>办公室内的电脑通过交换机互相通信，不需要经过路由器。</p>
                    </div>

                    <h3><i class="fas fa-layer-group"></i> 2.2 网络的分类</h3>
                    <p>根据覆盖范围和用途，网络可以分为不同类型：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 网络类型</th>
                            <th><i class="fas fa-ruler"></i> 覆盖范围</th>
                            <th><i class="fas fa-users"></i> 典型应用</th>
                            <th><i class="fas fa-example"></i> 举例</th>
                        </tr>
                        <tr>
                            <td><strong>PAN</strong><br>个人区域网</td>
                            <td>几米范围</td>
                            <td>个人设备互连</td>
                            <td>蓝牙耳机连手机</td>
                        </tr>
                        <tr>
                            <td><strong>LAN</strong><br>局域网</td>
                            <td>建筑物内</td>
                            <td>办公室、家庭网络</td>
                            <td>公司内部网络</td>
                        </tr>
                        <tr>
                            <td><strong>MAN</strong><br>城域网</td>
                            <td>城市范围</td>
                            <td>城市内机构互连</td>
                            <td>市政府各部门网络</td>
                        </tr>
                        <tr>
                            <td><strong>WAN</strong><br>广域网</td>
                            <td>跨地区、国家</td>
                            <td>远程通信</td>
                            <td>互联网、专线网络</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-sitemap"></i> 2.3 网络拓扑结构</h3>
                    <p>网络拓扑描述了网络中设备的连接方式，就像房子的结构图一样。</p>

                    <h4><i class="fas fa-minus"></i> 2.3.1 总线型拓扑</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-bus"></i> 特点：</strong>
                        <p>所有设备连接到一条主干线上，就像公交车站点都在一条线路上。</p>
                        <ul>
                            <li>✅ <strong>优点：</strong>布线简单，成本低</li>
                            <li>❌ <strong>缺点：</strong>主干线故障会影响整个网络</li>
                            <li>📍 <strong>应用：</strong>早期的以太网</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-star"></i> 2.3.2 星型拓扑</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-star"></i> 特点：</strong>
                        <p>所有设备都连接到中央设备（如交换机），就像星星围绕太阳。</p>
                        <ul>
                            <li>✅ <strong>优点：</strong>故障隔离好，易于管理</li>
                            <li>❌ <strong>缺点：</strong>中央设备故障影响全网</li>
                            <li>📍 <strong>应用：</strong>现代局域网的主流结构</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-circle"></i> 2.3.3 环型拓扑</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-circle"></i> 特点：</strong>
                        <p>设备连接成环形，数据沿着环路传输。</p>
                        <ul>
                            <li>✅ <strong>优点：</strong>数据传输有序，无冲突</li>
                            <li>❌ <strong>缺点：</strong>任一节点故障可能影响全网</li>
                            <li>📍 <strong>应用：</strong>令牌环网络</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-project-diagram"></i> 2.3.4 网状拓扑</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-network-wired"></i> 特点：</strong>
                        <p>设备之间有多条连接路径，就像蜘蛛网一样。</p>
                        <ul>
                            <li>✅ <strong>优点：</strong>可靠性高，有多条备用路径</li>
                            <li>❌ <strong>缺点：</strong>布线复杂，成本高</li>
                            <li>📍 <strong>应用：</strong>互联网骨干网</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <strong><i class="fas fa-lightbulb"></i> 小贴士：</strong>
                        <p>现实中的网络往往是多种拓扑的组合。比如：办公室内用星型拓扑，不同办公室之间用网状拓扑连接。</p>
                    </div>
                </section>

                <section id="network-models">
                    <h2><span class="step-number">3</span>网络模型</h2>

                    <h3><i class="fas fa-layer-group"></i> 3.1 为什么需要网络模型？</h3>
                    <p>网络模型就像建筑图纸，它帮助我们理解复杂的网络通信过程。想象一下，如果没有标准的建筑图纸，不同的工人就无法协作建房子。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 简单理解：</strong>
                        <p>网络模型的作用：</p>
                        <ul>
                            <li>📋 <strong>标准化：</strong>让不同厂商的设备能够互相通信</li>
                            <li>🔧 <strong>模块化：</strong>把复杂问题分解成简单的小问题</li>
                            <li>🛠️ <strong>故障排查：</strong>出问题时知道从哪一层开始检查</li>
                            <li>📚 <strong>学习指导：</strong>为学习网络提供清晰的框架</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-building"></i> 3.2 OSI七层模型</h3>
                    <p>OSI（开放系统互连）模型是国际标准，把网络通信分为7层，每层都有特定的功能。</p>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 记忆技巧：</strong>
                        <p>从下往上记忆口诀：<strong>"物数网传会表应"</strong></p>
                        <p>或者英文：<strong>"Please Do Not Throw Sausage Pizza Away"</strong></p>
                    </div>

                    <table>
                        <tr>
                            <th><i class="fas fa-layer-group"></i> 层次</th>
                            <th><i class="fas fa-tag"></i> 名称</th>
                            <th><i class="fas fa-cogs"></i> 主要功能</th>
                            <th><i class="fas fa-tools"></i> 设备/协议</th>
                            <th><i class="fas fa-lightbulb"></i> 生活类比</th>
                        </tr>
                        <tr>
                            <td><strong>第7层</strong></td>
                            <td>应用层<br>(Application)</td>
                            <td>为用户提供网络服务接口</td>
                            <td>HTTP、FTP、SMTP</td>
                            <td>🏪 商店柜台（直接面对客户）</td>
                        </tr>
                        <tr>
                            <td><strong>第6层</strong></td>
                            <td>表示层<br>(Presentation)</td>
                            <td>数据格式转换、加密解密</td>
                            <td>SSL/TLS、JPEG、MP3</td>
                            <td>🔄 翻译员（不同语言转换）</td>
                        </tr>
                        <tr>
                            <td><strong>第5层</strong></td>
                            <td>会话层<br>(Session)</td>
                            <td>建立、管理、终止会话</td>
                            <td>SQL、RPC、NetBIOS</td>
                            <td>📞 电话接线员（管理通话）</td>
                        </tr>
                        <tr>
                            <td><strong>第4层</strong></td>
                            <td>传输层<br>(Transport)</td>
                            <td>端到端可靠传输</td>
                            <td>TCP、UDP</td>
                            <td>🚚 快递公司（保证包裹送达）</td>
                        </tr>
                        <tr>
                            <td><strong>第3层</strong></td>
                            <td>网络层<br>(Network)</td>
                            <td>路径选择和逻辑寻址</td>
                            <td>IP、路由器</td>
                            <td>🗺️ GPS导航（选择路线）</td>
                        </tr>
                        <tr>
                            <td><strong>第2层</strong></td>
                            <td>数据链路层<br>(Data Link)</td>
                            <td>帧传输和错误检测</td>
                            <td>以太网、交换机</td>
                            <td>🚦 交通信号灯（本地交通管理）</td>
                        </tr>
                        <tr>
                            <td><strong>第1层</strong></td>
                            <td>物理层<br>(Physical)</td>
                            <td>比特传输和物理连接</td>
                            <td>网线、光纤、集线器</td>
                            <td>🛣️ 道路（物理通道）</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-envelope"></i> 3.3 数据传输过程详解</h3>
                    <p>让我们通过一个具体例子来理解数据是如何在网络中传输的：</p>

                    <div class="info-box">
                        <strong><i class="fas fa-scenario"></i> 场景：</strong>小明用浏览器访问百度网站
                    </div>

                    <h4><i class="fas fa-arrow-down"></i> 3.3.1 发送端（小明的电脑）- 数据封装过程</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-box"></i> 数据打包过程（从上到下）：</strong>
                        <ol>
                            <li><strong>应用层：</strong>浏览器生成HTTP请求 "GET / HTTP/1.1"</li>
                            <li><strong>表示层：</strong>如果是HTTPS，进行SSL加密</li>
                            <li><strong>会话层：</strong>建立与百度服务器的会话</li>
                            <li><strong>传输层：</strong>TCP添加端口信息（源端口:随机，目标端口:80）</li>
                            <li><strong>网络层：</strong>IP添加地址信息（源IP:*************，目标IP:百度服务器IP）</li>
                            <li><strong>数据链路层：</strong>以太网添加MAC地址</li>
                            <li><strong>物理层：</strong>转换为电信号通过网线传输</li>
                        </ol>
                    </div>

                    <h4><i class="fas fa-arrow-up"></i> 3.3.2 接收端（百度服务器）- 数据解封过程</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-box-open"></i> 数据拆包过程（从下到上）：</strong>
                        <ol>
                            <li><strong>物理层：</strong>接收电信号并转换为数字信号</li>
                            <li><strong>数据链路层：</strong>检查MAC地址，去掉以太网头部</li>
                            <li><strong>网络层：</strong>检查IP地址，去掉IP头部</li>
                            <li><strong>传输层：</strong>检查端口号，去掉TCP头部</li>
                            <li><strong>会话层：</strong>管理会话状态</li>
                            <li><strong>表示层：</strong>如果有加密，进行解密</li>
                            <li><strong>应用层：</strong>Web服务器处理HTTP请求，返回网页内容</li>
                        </ol>
                    </div>

                    <h3><i class="fas fa-layer-group"></i> 3.4 TCP/IP模型</h3>
                    <p>TCP/IP模型是实际互联网使用的模型，比OSI模型更简化实用。</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-layer-group"></i> TCP/IP层</th>
                            <th><i class="fas fa-arrows-alt-h"></i> 对应OSI层</th>
                            <th><i class="fas fa-cogs"></i> 主要功能</th>
                            <th><i class="fas fa-tools"></i> 主要协议</th>
                        </tr>
                        <tr>
                            <td><strong>应用层</strong></td>
                            <td>应用层+表示层+会话层</td>
                            <td>提供各种网络应用服务</td>
                            <td>HTTP、FTP、SMTP、DNS</td>
                        </tr>
                        <tr>
                            <td><strong>传输层</strong></td>
                            <td>传输层</td>
                            <td>端到端的数据传输</td>
                            <td>TCP、UDP</td>
                        </tr>
                        <tr>
                            <td><strong>网络层</strong></td>
                            <td>网络层</td>
                            <td>路由和逻辑寻址</td>
                            <td>IP、ICMP、ARP</td>
                        </tr>
                        <tr>
                            <td><strong>网络接口层</strong></td>
                            <td>数据链路层+物理层</td>
                            <td>物理网络访问</td>
                            <td>以太网、WiFi</td>
                        </tr>
                    </table>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
                        <p>虽然OSI模型有7层，但在实际工作中，我们更多使用TCP/IP的4层模型。理解这两个模型的对应关系很重要！</p>
                    </div>

                    <h3><i class="fas fa-tools"></i> 3.5 实际应用中的理解</h3>
                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 故障排查思路：</strong>
                        <p>当网络出现问题时，我们可以按层次排查：</p>
                        <ol>
                            <li><strong>物理层：</strong>检查网线是否插好，指示灯是否亮</li>
                            <li><strong>数据链路层：</strong>检查交换机端口状态</li>
                            <li><strong>网络层：</strong>用ping命令测试IP连通性</li>
                            <li><strong>传输层：</strong>检查端口是否开放</li>
                            <li><strong>应用层：</strong>检查应用程序配置</li>
                        </ol>
                    </div>
                </section>

                <section id="ip-addressing">
                    <h2><span class="step-number">4</span>IP地址详解</h2>

                    <h3><i class="fas fa-map-marker-alt"></i> 4.1 什么是IP地址？</h3>
                    <p>IP地址是网络中设备的唯一标识，就像现实生活中的门牌号码一样。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-home"></i> 生活类比：</strong>
                        <p>想象一下邮递员送信的过程：</p>
                        <ul>
                            <li>🏠 <strong>门牌号码</strong> = IP地址（*************）</li>
                            <li>🏘️ <strong>小区名称</strong> = 网络地址（***********）</li>
                            <li>📮 <strong>具体房间</strong> = 主机地址（.100）</li>
                            <li>🗺️ <strong>邮政编码</strong> = 子网掩码（告诉邮递员这是哪个区域）</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-code"></i> 4.2 IPv4地址结构</h3>
                    <p>IPv4地址由32位二进制数组成，通常用点分十进制表示。</p>

                    <h4><i class="fas fa-calculator"></i> 4.2.1 二进制与十进制转换</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 转换示例：</strong>
                        <p>IP地址：*************</p>
                        <table style="margin-top: 15px;">
                            <tr>
                                <th>十进制</th>
                                <th>二进制</th>
                                <th>说明</th>
                            </tr>
                            <tr>
                                <td>192</td>
                                <td>11000000</td>
                                <td>128+64=192</td>
                            </tr>
                            <tr>
                                <td>168</td>
                                <td>10101000</td>
                                <td>128+32+8=168</td>
                            </tr>
                            <tr>
                                <td>1</td>
                                <td>00000001</td>
                                <td>1</td>
                            </tr>
                            <tr>
                                <td>100</td>
                                <td>01100100</td>
                                <td>64+32+4=100</td>
                            </tr>
                        </table>
                        <p><strong>完整二进制：</strong>11000000.10101000.00000001.01100100</p>
                    </div>

                    <h4><i class="fas fa-layer-group"></i> 4.2.2 IP地址分类</h4>
                    <p>IPv4地址分为A、B、C、D、E五类，其中A、B、C类用于普通网络：</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 类别</th>
                            <th><i class="fas fa-range"></i> 地址范围</th>
                            <th><i class="fas fa-network-wired"></i> 网络数</th>
                            <th><i class="fas fa-desktop"></i> 主机数</th>
                            <th><i class="fas fa-users"></i> 适用场景</th>
                        </tr>
                        <tr>
                            <td><strong>A类</strong></td>
                            <td>******* - ***************</td>
                            <td>126个</td>
                            <td>1677万个</td>
                            <td>大型网络（如大型企业）</td>
                        </tr>
                        <tr>
                            <td><strong>B类</strong></td>
                            <td>128.0.0.0 - 191.255.255.255</td>
                            <td>16384个</td>
                            <td>6.5万个</td>
                            <td>中型网络（如大学）</td>
                        </tr>
                        <tr>
                            <td><strong>C类</strong></td>
                            <td>192.0.0.0 - 223.255.255.255</td>
                            <td>209万个</td>
                            <td>254个</td>
                            <td>小型网络（如小公司）</td>
                        </tr>
                        <tr>
                            <td><strong>D类</strong></td>
                            <td>224.0.0.0 - 239.255.255.255</td>
                            <td colspan="2">组播地址</td>
                            <td>多播通信</td>
                        </tr>
                        <tr>
                            <td><strong>E类</strong></td>
                            <td>240.0.0.0 - ***************</td>
                            <td colspan="2">保留地址</td>
                            <td>实验和研究</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-mask"></i> 4.3 子网掩码</h3>
                    <p>子网掩码用来区分IP地址中的网络部分和主机部分。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 简单理解：</strong>
                        <p>子网掩码就像一个"筛子"，帮助设备判断目标地址是在本地网络还是远程网络。</p>
                        <ul>
                            <li>🔍 <strong>网络部分：</strong>子网掩码为1的位置（确定是哪个网络）</li>
                            <li>🏠 <strong>主机部分：</strong>子网掩码为0的位置（确定是网络中的哪台设备）</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-calculator"></i> 4.3.1 默认子网掩码</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 地址类别</th>
                            <th><i class="fas fa-mask"></i> 默认子网掩码</th>
                            <th><i class="fas fa-code"></i> CIDR表示法</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td>A类</td>
                            <td>255.0.0.0</td>
                            <td>/8</td>
                            <td>前8位是网络地址</td>
                        </tr>
                        <tr>
                            <td>B类</td>
                            <td>255.255.0.0</td>
                            <td>/16</td>
                            <td>前16位是网络地址</td>
                        </tr>
                        <tr>
                            <td>C类</td>
                            <td>*************</td>
                            <td>/24</td>
                            <td>前24位是网络地址</td>
                        </tr>
                    </table>

                    <h4><i class="fas fa-example"></i> 4.3.2 实际计算示例</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-calculator"></i> 例子：IP地址 *************，子网掩码 *************</strong>
                        <pre><code>IP地址：    *************  = 11000000.10101000.00000001.01100100
子网掩码：  *************  = 11111111.11111111.11111111.00000000
                              ↑————————网络部分————————↑ ↑主机部分↑

网络地址：  ***********    （网络部分保持不变，主机部分全为0）
广播地址：  *************  （网络部分保持不变，主机部分全为1）
可用主机：  *********** - *************  （共254个地址）</code></pre>
                    </div>

                    <h3><i class="fas fa-home"></i> 4.4 私有IP地址</h3>
                    <p>私有IP地址是专门为内部网络保留的地址，不能在互联网上直接使用。</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 类别</th>
                            <th><i class="fas fa-range"></i> 私有地址范围</th>
                            <th><i class="fas fa-users"></i> 常见用途</th>
                            <th><i class="fas fa-example"></i> 举例</th>
                        </tr>
                        <tr>
                            <td>A类私有</td>
                            <td>10.0.0.0 - **************</td>
                            <td>大型企业内网</td>
                            <td>公司总部网络</td>
                        </tr>
                        <tr>
                            <td>B类私有</td>
                            <td>********** - **************</td>
                            <td>中型企业内网</td>
                            <td>分公司网络</td>
                        </tr>
                        <tr>
                            <td>C类私有</td>
                            <td>*********** - ***************</td>
                            <td>家庭、小型办公网络</td>
                            <td>家用路由器网络</td>
                        </tr>
                    </table>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 重要概念：NAT（网络地址转换）</strong>
                        <p>由于私有IP不能直接访问互联网，需要通过NAT技术将私有IP转换为公网IP。这就像酒店的房间号（私有IP）需要通过酒店总台（路由器）才能与外界联系。</p>
                    </div>

                    <h3><i class="fas fa-tools"></i> 4.5 特殊IP地址</h3>
                    <div class="info-box">
                        <strong><i class="fas fa-star"></i> 需要了解的特殊地址：</strong>
                        <ul>
                            <li>🏠 <strong>127.0.0.1：</strong>本地回环地址（localhost），用于测试本机网络</li>
                            <li>📡 <strong>0.0.0.0：</strong>表示所有网络或未知网络</li>
                            <li>🌐 <strong>***************：</strong>有限广播地址</li>
                            <li>🔗 <strong>169.254.x.x：</strong>APIPA地址（自动分配的私有地址）</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-terminal"></i> 4.6 实用命令</h3>
                    <p>学会这些命令，可以帮助你查看和测试IP地址配置：</p>

                    <h4><i class="fas fa-windows"></i> 4.6.1 Windows系统</h4>
                    <pre><code># 查看IP配置
ipconfig

# 查看详细IP配置
ipconfig /all

# 释放IP地址
ipconfig /release

# 重新获取IP地址
ipconfig /renew

# 测试网络连通性
ping ***********</code></pre>

                    <h4><i class="fas fa-linux"></i> 4.6.2 Linux系统</h4>
                    <pre><code># 查看IP配置（新版本）
ip addr show

# 查看IP配置（传统命令）
ifconfig

# 测试网络连通性
ping ***********

# 查看路由表
ip route show</code></pre>

                    <div class="success-box">
                        <strong><i class="fas fa-lightbulb"></i> 实践建议：</strong>
                        <p>打开命令行，试试这些命令，看看你的电脑现在使用的是什么IP地址！</p>
                    </div>
                </section>

                <section id="subnetting">
                    <h2><span class="step-number">5</span>子网划分</h2>

                    <h3><i class="fas fa-sitemap"></i> 5.1 为什么要进行子网划分？</h3>
                    <p>子网划分就像把一栋大楼分成不同的楼层和房间，让网络管理更加高效和安全。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-building"></i> 生活类比：</strong>
                        <p>想象一个大型购物中心：</p>
                        <ul>
                            <li>🏢 <strong>整栋楼</strong> = 原始网络（如***********/24）</li>
                            <li>🏪 <strong>不同楼层</strong> = 不同子网（销售部、技术部、财务部）</li>
                            <li>🚪 <strong>房间号</strong> = 具体的IP地址</li>
                            <li>🔐 <strong>门禁系统</strong> = 子网间的访问控制</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-star"></i> 5.2 子网划分的好处</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-lightbulb"></i> 好处</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                            <th><i class="fas fa-example"></i> 实际例子</th>
                        </tr>
                        <tr>
                            <td><strong>提高安全性</strong></td>
                            <td>不同部门网络隔离</td>
                            <td>财务部网络与访客网络分离</td>
                        </tr>
                        <tr>
                            <td><strong>减少广播域</strong></td>
                            <td>降低网络拥堵</td>
                            <td>避免一个部门的广播影响全公司</td>
                        </tr>
                        <tr>
                            <td><strong>便于管理</strong></td>
                            <td>按部门或功能分组</td>
                            <td>技术部、销售部分别管理</td>
                        </tr>
                        <tr>
                            <td><strong>节约IP地址</strong></td>
                            <td>按需分配地址空间</td>
                            <td>小部门分配小子网</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-calculator"></i> 5.3 子网划分基础计算</h3>
                    <p>子网划分的核心是借用主机位来创建更多的网络。</p>

                    <h4><i class="fas fa-lightbulb"></i> 5.3.1 基本概念</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-key"></i> 关键公式：</strong>
                        <ul>
                            <li>🔢 <strong>子网数量 = 2^借用位数</strong></li>
                            <li>🏠 <strong>每个子网主机数 = 2^剩余主机位数 - 2</strong></li>
                            <li>📏 <strong>子网间隔 = 256 - 子网掩码最后一个非255的值</strong></li>
                        </ul>
                        <p><strong>注意：</strong>减2是因为网络地址和广播地址不能分配给主机</p>
                    </div>

                    <h4><i class="fas fa-example"></i> 5.3.2 实际案例：公司网络规划</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-building"></i> 场景：</strong>某公司有***********/24网络，需要划分为4个部门
                        <br><br>
                        <strong><i class="fas fa-calculator"></i> 计算过程：</strong>
                        <ol>
                            <li><strong>确定需求：</strong>需要4个子网，2^2 = 4，所以需要借用2位</li>
                            <li><strong>新子网掩码：</strong>/24 + 2 = /26，即255.255.255.192</li>
                            <li><strong>每个子网主机数：</strong>2^(32-26) - 2 = 62台主机</li>
                            <li><strong>子网间隔：</strong>256 - 192 = 64</li>
                        </ol>
                    </div>

                    <table>
                        <tr>
                            <th><i class="fas fa-network-wired"></i> 子网</th>
                            <th><i class="fas fa-map-marker-alt"></i> 网络地址</th>
                            <th><i class="fas fa-range"></i> 主机地址范围</th>
                            <th><i class="fas fa-broadcast-tower"></i> 广播地址</th>
                            <th><i class="fas fa-users"></i> 用途</th>
                        </tr>
                        <tr>
                            <td>子网1</td>
                            <td>***********/26</td>
                            <td>*********** - 192.168.1.62</td>
                            <td>192.168.1.63</td>
                            <td>技术部</td>
                        </tr>
                        <tr>
                            <td>子网2</td>
                            <td>192.168.1.64/26</td>
                            <td>192.168.1.65 - ***********26</td>
                            <td>***********27</td>
                            <td>销售部</td>
                        </tr>
                        <tr>
                            <td>子网3</td>
                            <td>***********28/26</td>
                            <td>***********29 - ***********90</td>
                            <td>***********91</td>
                            <td>财务部</td>
                        </tr>
                        <tr>
                            <td>子网4</td>
                            <td>*************/26</td>
                            <td>************* - *************</td>
                            <td>*************</td>
                            <td>行政部</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-tools"></i> 5.4 VLSM（可变长子网掩码）</h3>
                    <p>VLSM允许在同一个网络中使用不同长度的子网掩码，更灵活地分配IP地址。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> VLSM的优势：</strong>
                        <ul>
                            <li>💡 <strong>按需分配：</strong>大部门分配大子网，小部门分配小子网</li>
                            <li>💰 <strong>节约地址：</strong>避免IP地址浪费</li>
                            <li>🔧 <strong>灵活管理：</strong>可以根据实际需求调整</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-example"></i> 5.4.1 VLSM实际案例</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-building"></i> 场景：</strong>公司各部门人数不同
                        <ul>
                            <li>技术部：50人</li>
                            <li>销售部：25人</li>
                            <li>财务部：10人</li>
                            <li>行政部：5人</li>
                        </ul>

                        <strong><i class="fas fa-calculator"></i> VLSM分配：</strong>
                        <table style="margin-top: 15px;">
                            <tr>
                                <th>部门</th>
                                <th>需要主机数</th>
                                <th>分配子网</th>
                                <th>可用主机数</th>
                                <th>子网掩码</th>
                            </tr>
                            <tr>
                                <td>技术部</td>
                                <td>50</td>
                                <td>***********/26</td>
                                <td>62</td>
                                <td>/26</td>
                            </tr>
                            <tr>
                                <td>销售部</td>
                                <td>25</td>
                                <td>192.168.1.64/27</td>
                                <td>30</td>
                                <td>/27</td>
                            </tr>
                            <tr>
                                <td>财务部</td>
                                <td>10</td>
                                <td>192.168.1.96/28</td>
                                <td>14</td>
                                <td>/28</td>
                            </tr>
                            <tr>
                                <td>行政部</td>
                                <td>5</td>
                                <td>***********12/29</td>
                                <td>6</td>
                                <td>/29</td>
                            </tr>
                        </table>
                    </div>

                    <h3><i class="fas fa-route"></i> 5.5 路由汇总（超网）</h3>
                    <p>路由汇总是子网划分的逆过程，将多个小网络合并成一个大网络，减少路由表条目。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 汇总的好处：</strong>
                        <ul>
                            <li>📊 <strong>减少路由表：</strong>路由器需要记住的路由条目更少</li>
                            <li>⚡ <strong>提高效率：</strong>路由查找速度更快</li>
                            <li>🔧 <strong>简化管理：</strong>网络配置更简单</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-example"></i> 5.5.1 汇总示例</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-calculator"></i> 汇总计算：</strong>
                        <p>要汇总以下网络：</p>
                        <ul>
                            <li>***********/24</li>
                            <li>***********/24</li>
                            <li>***********/24</li>
                            <li>192.168.3.0/24</li>
                        </ul>

                        <p><strong>汇总结果：</strong>***********/22</p>
                        <p><strong>说明：</strong>这4个/24网络可以汇总为一个/22网络</p>
                    </div>

                    <h3><i class="fas fa-tools"></i> 5.6 实用工具和技巧</h3>

                    <h4><i class="fas fa-calculator"></i> 5.6.1 快速计算技巧</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> 记忆技巧：</strong>
                        <table>
                            <tr>
                                <th>子网掩码</th>
                                <th>CIDR</th>
                                <th>主机位数</th>
                                <th>可用主机数</th>
                                <th>子网间隔</th>
                            </tr>
                            <tr>
                                <td>*************</td>
                                <td>/24</td>
                                <td>8位</td>
                                <td>254</td>
                                <td>256</td>
                            </tr>
                            <tr>
                                <td>255.255.255.128</td>
                                <td>/25</td>
                                <td>7位</td>
                                <td>126</td>
                                <td>128</td>
                            </tr>
                            <tr>
                                <td>255.255.255.192</td>
                                <td>/26</td>
                                <td>6位</td>
                                <td>62</td>
                                <td>64</td>
                            </tr>
                            <tr>
                                <td>255.255.255.224</td>
                                <td>/27</td>
                                <td>5位</td>
                                <td>30</td>
                                <td>32</td>
                            </tr>
                            <tr>
                                <td>255.255.255.240</td>
                                <td>/28</td>
                                <td>4位</td>
                                <td>14</td>
                                <td>16</td>
                            </tr>
                        </table>
                    </div>

                    <h4><i class="fas fa-globe"></i> 5.6.2 在线计算工具</h4>
                    <div class="warning-box">
                        <strong><i class="fas fa-tools"></i> 推荐工具：</strong>
                        <ul>
                            <li>🌐 <strong>在线子网计算器：</strong>搜索"subnet calculator"</li>
                            <li>📱 <strong>手机APP：</strong>Network Calculator、IP Calculator</li>
                            <li>💻 <strong>命令行工具：</strong>ipcalc（Linux）</li>
                        </ul>
                    </div>

                    <div class="success-box">
                        <strong><i class="fas fa-graduation-cap"></i> 学习建议：</strong>
                        <p>子网划分需要大量练习才能熟练掌握。建议：</p>
                        <ol>
                            <li>先理解基本概念和公式</li>
                            <li>多做计算练习</li>
                            <li>使用在线工具验证计算结果</li>
                            <li>在实际环境中应用所学知识</li>
                        </ol>
                    </div>
                </section>

                <section id="routing">
                    <h2><span class="step-number">6</span>路由基础</h2>

                    <h3><i class="fas fa-route"></i> 6.1 什么是路由？</h3>
                    <p>路由是指数据包从源地址到目标地址的路径选择过程，就像GPS为你规划最佳行车路线一样。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-car"></i> 生活类比：</strong>
                        <p>路由就像城市交通系统：</p>
                        <ul>
                            <li>🗺️ <strong>地图</strong> = 路由表（记录到各个目的地的路径）</li>
                            <li>🚦 <strong>交通指示牌</strong> = 路由器（指引方向）</li>
                            <li>🛣️ <strong>道路</strong> = 网络链路</li>
                            <li>🚗 <strong>汽车</strong> = 数据包</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-table"></i> 6.2 路由表</h3>
                    <p>路由表是路由器的"地图册"，记录了到达各个网络的路径信息。</p>

                    <h4><i class="fas fa-list"></i> 6.2.1 路由表的组成</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 字段</th>
                            <th><i class="fas fa-info-circle"></i> 含义</th>
                            <th><i class="fas fa-example"></i> 示例</th>
                        </tr>
                        <tr>
                            <td><strong>目标网络</strong></td>
                            <td>要到达的网络地址</td>
                            <td>***********/24</td>
                        </tr>
                        <tr>
                            <td><strong>下一跳</strong></td>
                            <td>数据包的下一个转发地址</td>
                            <td>***********</td>
                        </tr>
                        <tr>
                            <td><strong>接口</strong></td>
                            <td>数据包从哪个接口发出</td>
                            <td>eth0</td>
                        </tr>
                        <tr>
                            <td><strong>度量值</strong></td>
                            <td>路径的"成本"（距离、速度等）</td>
                            <td>1（跳数）</td>
                        </tr>
                    </table>

                    <h4><i class="fas fa-terminal"></i> 6.2.2 查看路由表命令</h4>
                    <pre><code># Windows系统
route print

# Linux系统
ip route show
# 或者
route -n

# 查看默认网关
ip route | grep default</code></pre>

                    <h3><i class="fas fa-cogs"></i> 6.3 路由类型</h3>

                    <h4><i class="fas fa-hand-paper"></i> 6.3.1 静态路由</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-map"></i> 特点：</strong>
                        <ul>
                            <li>✅ <strong>优点：</strong>配置简单，安全性高，不占用带宽</li>
                            <li>❌ <strong>缺点：</strong>不能自动适应网络变化，维护工作量大</li>
                            <li>📍 <strong>适用：</strong>小型网络，网络拓扑稳定的环境</li>
                        </ul>

                        <strong>配置示例：</strong>
                        <pre><code># 添加静态路由（Linux）
ip route add ***********/24 via ***********

# 添加静态路由（Windows）
route add *********** mask ************* ***********</code></pre>
                    </div>

                    <h4><i class="fas fa-sync-alt"></i> 6.3.2 动态路由</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-robot"></i> 特点：</strong>
                        <ul>
                            <li>✅ <strong>优点：</strong>自动适应网络变化，维护简单</li>
                            <li>❌ <strong>缺点：</strong>配置复杂，占用带宽和CPU资源</li>
                            <li>📍 <strong>适用：</strong>大型网络，网络拓扑经常变化的环境</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-exchange-alt"></i> 6.4 常见路由协议</h3>

                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 协议</th>
                            <th><i class="fas fa-layer-group"></i> 类型</th>
                            <th><i class="fas fa-calculator"></i> 度量值</th>
                            <th><i class="fas fa-users"></i> 适用场景</th>
                        </tr>
                        <tr>
                            <td><strong>RIP</strong></td>
                            <td>距离矢量</td>
                            <td>跳数（最大15跳）</td>
                            <td>小型网络</td>
                        </tr>
                        <tr>
                            <td><strong>OSPF</strong></td>
                            <td>链路状态</td>
                            <td>带宽</td>
                            <td>中大型企业网络</td>
                        </tr>
                        <tr>
                            <td><strong>BGP</strong></td>
                            <td>路径矢量</td>
                            <td>AS路径</td>
                            <td>互联网骨干网</td>
                        </tr>
                        <tr>
                            <td><strong>EIGRP</strong></td>
                            <td>混合型</td>
                            <td>复合度量值</td>
                            <td>思科设备网络</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-home"></i> 6.5 默认路由</h3>
                    <p>默认路由是网络的"万能钥匙"，当路由表中没有匹配的路由时，数据包会被发送到默认网关。</p>

                    <div class="success-box">
                        <strong><i class="fas fa-lightbulb"></i> 实际应用：</strong>
                        <p>家用路由器通常配置默认路由指向ISP（互联网服务提供商），这样家里的设备就能访问互联网了。</p>
                        <pre><code># 查看默认网关
ip route | grep default
# 输出示例：default via *********** dev eth0</code></pre>
                    </div>
                </section>

                <section id="switching">
                    <h2><span class="step-number">7</span>交换基础</h2>

                    <h3><i class="fas fa-exchange-alt"></i> 7.1 什么是交换？</h3>
                    <p>交换是在同一个网络内转发数据帧的过程，交换机就像一个智能的邮递员，知道每个地址对应哪个门牌号。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-building"></i> 生活类比：</strong>
                        <p>交换机就像小区的邮递员：</p>
                        <ul>
                            <li>📮 <strong>邮件</strong> = 数据帧</li>
                            <li>🏠 <strong>门牌号</strong> = MAC地址</li>
                            <li>📋 <strong>住户名册</strong> = MAC地址表</li>
                            <li>🚪 <strong>楼栋入口</strong> = 交换机端口</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-id-card"></i> 7.2 MAC地址</h3>
                    <p>MAC地址是网卡的物理地址，全球唯一，就像身份证号码一样。</p>

                    <h4><i class="fas fa-info-circle"></i> 7.2.1 MAC地址格式</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-code"></i> 格式说明：</strong>
                        <ul>
                            <li>📏 <strong>长度：</strong>48位（6字节）</li>
                            <li>🔢 <strong>表示：</strong>12个十六进制数字</li>
                            <li>📝 <strong>格式：</strong>AA:BB:CC:DD:EE:FF 或 AA-BB-CC-DD-EE-FF</li>
                            <li>🏭 <strong>前3字节：</strong>厂商标识（OUI）</li>
                            <li>🔢 <strong>后3字节：</strong>设备序列号</li>
                        </ul>

                        <strong>示例：</strong>
                        <pre><code>MAC地址：00:1B:44:11:3A:B7
前3字节：00:1B:44（厂商：Dell Inc.）
后3字节：11:3A:B7（设备序列号）</code></pre>
                    </div>

                    <h4><i class="fas fa-terminal"></i> 7.2.2 查看MAC地址</h4>
                    <pre><code># Windows系统
ipconfig /all
getmac

# Linux系统
ip link show
ifconfig
cat /sys/class/net/eth0/address</code></pre>

                    <h3><i class="fas fa-table"></i> 7.3 MAC地址表</h3>
                    <p>交换机通过学习来建立MAC地址表，记录每个MAC地址对应的端口。</p>

                    <h4><i class="fas fa-graduation-cap"></i> 7.3.1 学习过程</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-step-forward"></i> 学习步骤：</strong>
                        <ol>
                            <li><strong>接收帧：</strong>交换机从某个端口接收到数据帧</li>
                            <li><strong>学习源MAC：</strong>记录源MAC地址和对应的端口</li>
                            <li><strong>查找目标MAC：</strong>在MAC地址表中查找目标MAC地址</li>
                            <li><strong>转发决策：</strong>
                                <ul>
                                    <li>找到：单播转发到对应端口</li>
                                    <li>未找到：泛洪到所有端口（除源端口）</li>
                                </ul>
                            </li>
                        </ol>
                    </div>

                    <h4><i class="fas fa-clock"></i> 7.3.2 老化机制</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-hourglass-half"></i> 老化时间：</strong>
                        <p>MAC地址表条目有生存时间（通常300秒），超时未使用的条目会被删除，避免表项过多影响性能。</p>
                    </div>

                    <h3><i class="fas fa-broadcast-tower"></i> 7.4 交换机的转发方式</h3>

                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 转发方式</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                            <th><i class="fas fa-clock"></i> 延迟</th>
                            <th><i class="fas fa-shield-alt"></i> 错误检测</th>
                        </tr>
                        <tr>
                            <td><strong>直通转发</strong></td>
                            <td>读取目标MAC后立即转发</td>
                            <td>最低</td>
                            <td>无</td>
                        </tr>
                        <tr>
                            <td><strong>存储转发</strong></td>
                            <td>接收完整帧后再转发</td>
                            <td>较高</td>
                            <td>完整检测</td>
                        </tr>
                        <tr>
                            <td><strong>无碎片转发</strong></td>
                            <td>读取前64字节后转发</td>
                            <td>中等</td>
                            <td>碰撞检测</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-network-wired"></i> 7.5 VLAN（虚拟局域网）</h3>
                    <p>VLAN可以将一个物理交换机划分为多个逻辑网络，提高安全性和管理效率。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-building"></i> 生活类比：</strong>
                        <p>VLAN就像公寓楼的门禁系统：</p>
                        <ul>
                            <li>🏢 <strong>整栋楼</strong> = 物理交换机</li>
                            <li>🏠 <strong>不同楼层</strong> = 不同VLAN</li>
                            <li>🔐 <strong>门禁卡</strong> = VLAN标签</li>
                            <li>🚪 <strong>电梯</strong> = Trunk链路（连接不同楼层）</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-star"></i> 7.5.1 VLAN的优势</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-lightbulb"></i> 优势</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td><strong>安全隔离</strong></td>
                            <td>不同VLAN之间默认无法通信</td>
                        </tr>
                        <tr>
                            <td><strong>广播控制</strong></td>
                            <td>广播只在同一VLAN内传播</td>
                        </tr>
                        <tr>
                            <td><strong>灵活管理</strong></td>
                            <td>可以跨物理位置组建逻辑网络</td>
                        </tr>
                        <tr>
                            <td><strong>节约成本</strong></td>
                            <td>一台交换机实现多个网络功能</td>
                        </tr>
                    </table>

                    <div class="warning-box">
                        <strong><i class="fas fa-exclamation-triangle"></i> 注意：</strong>
                        <p>不同VLAN之间要通信，需要通过路由器或三层交换机进行路由。</p>
                    </div>
                </section>

                <section id="protocols">
                    <h2><span class="step-number">8</span>常用协议</h2>

                    <h3><i class="fas fa-handshake"></i> 8.1 什么是网络协议？</h3>
                    <p>网络协议就像人们交流时使用的语言规则，确保不同设备能够正确理解和交换信息。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-comments"></i> 生活类比：</strong>
                        <p>协议就像社交礼仪：</p>
                        <ul>
                            <li>🤝 <strong>握手</strong> = 建立连接（TCP三次握手）</li>
                            <li>💬 <strong>对话</strong> = 数据传输</li>
                            <li>👋 <strong>告别</strong> = 断开连接</li>
                            <li>📝 <strong>语言规则</strong> = 协议标准</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-layer-group"></i> 8.2 TCP/IP协议族</h3>
                    <p>TCP/IP是互联网的基础协议族，包含了多个不同层次的协议。</p>

                    <h4><i class="fas fa-truck"></i> 8.2.1 TCP协议（传输控制协议）</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-shield-alt"></i> TCP特点：</strong>
                        <ul>
                            <li>✅ <strong>可靠传输：</strong>保证数据完整到达</li>
                            <li>🔄 <strong>面向连接：</strong>传输前先建立连接</li>
                            <li>📦 <strong>有序传输：</strong>数据按顺序到达</li>
                            <li>🚦 <strong>流量控制：</strong>防止发送方发送过快</li>
                            <li>🔧 <strong>拥塞控制：</strong>避免网络拥堵</li>
                        </ul>

                        <strong>适用场景：</strong>网页浏览、文件传输、邮件发送等需要可靠传输的应用
                    </div>

                    <h4><i class="fas fa-paper-plane"></i> 8.2.2 UDP协议（用户数据报协议）</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-rocket"></i> UDP特点：</strong>
                        <ul>
                            <li>⚡ <strong>速度快：</strong>无需建立连接</li>
                            <li>📦 <strong>简单：</strong>协议开销小</li>
                            <li>❌ <strong>不可靠：</strong>不保证数据到达</li>
                            <li>🔀 <strong>无序：</strong>数据可能乱序到达</li>
                        </ul>

                        <strong>适用场景：</strong>在线游戏、视频直播、DNS查询等对速度要求高的应用
                    </div>

                    <h4><i class="fas fa-handshake"></i> 8.2.3 TCP三次握手</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-phone"></i> 三次握手过程（像打电话）：</strong>
                        <ol>
                            <li><strong>第一次握手：</strong>客户端说"喂，你好吗？"（SYN）</li>
                            <li><strong>第二次握手：</strong>服务器说"我很好，你好吗？"（SYN+ACK）</li>
                            <li><strong>第三次握手：</strong>客户端说"我也很好，我们开始聊吧"（ACK）</li>
                        </ol>
                        <p><strong>目的：</strong>确保双方都能正常收发数据</p>
                    </div>

                    <h3><i class="fas fa-globe"></i> 8.3 应用层协议</h3>

                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 协议</th>
                            <th><i class="fas fa-door-open"></i> 端口</th>
                            <th><i class="fas fa-cogs"></i> 功能</th>
                            <th><i class="fas fa-example"></i> 应用场景</th>
                        </tr>
                        <tr>
                            <td><strong>HTTP</strong></td>
                            <td>80</td>
                            <td>网页传输</td>
                            <td>浏览网站</td>
                        </tr>
                        <tr>
                            <td><strong>HTTPS</strong></td>
                            <td>443</td>
                            <td>安全网页传输</td>
                            <td>网上银行、购物</td>
                        </tr>
                        <tr>
                            <td><strong>FTP</strong></td>
                            <td>21</td>
                            <td>文件传输</td>
                            <td>上传下载文件</td>
                        </tr>
                        <tr>
                            <td><strong>SMTP</strong></td>
                            <td>25</td>
                            <td>邮件发送</td>
                            <td>发送电子邮件</td>
                        </tr>
                        <tr>
                            <td><strong>POP3</strong></td>
                            <td>110</td>
                            <td>邮件接收</td>
                            <td>接收电子邮件</td>
                        </tr>
                        <tr>
                            <td><strong>DNS</strong></td>
                            <td>53</td>
                            <td>域名解析</td>
                            <td>将网址转换为IP地址</td>
                        </tr>
                        <tr>
                            <td><strong>DHCP</strong></td>
                            <td>67/68</td>
                            <td>自动分配IP</td>
                            <td>设备自动获取网络配置</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-search"></i> 8.4 DNS协议详解</h3>
                    <p>DNS（域名系统）是互联网的"电话簿"，将人类容易记忆的域名转换为计算机使用的IP地址。</p>

                    <h4><i class="fas fa-step-forward"></i> 8.4.1 DNS解析过程</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-search"></i> 解析步骤（以访问www.baidu.com为例）：</strong>
                        <ol>
                            <li><strong>本地缓存：</strong>检查浏览器和系统DNS缓存</li>
                            <li><strong>本地DNS服务器：</strong>查询ISP提供的DNS服务器</li>
                            <li><strong>根DNS服务器：</strong>查询.com顶级域的DNS服务器</li>
                            <li><strong>顶级域DNS：</strong>查询baidu.com的权威DNS服务器</li>
                            <li><strong>权威DNS：</strong>返回www.baidu.com的IP地址</li>
                            <li><strong>返回结果：</strong>IP地址逐级返回到客户端</li>
                        </ol>
                    </div>

                    <h4><i class="fas fa-terminal"></i> 8.4.2 DNS相关命令</h4>
                    <pre><code># 查询域名对应的IP地址
nslookup www.baidu.com

# 查询详细DNS信息
dig www.baidu.com

# 清除DNS缓存（Windows）
ipconfig /flushdns

# 查看DNS配置（Linux）
cat /etc/resolv.conf</code></pre>

                    <h3><i class="fas fa-network-wired"></i> 8.5 DHCP协议</h3>
                    <p>DHCP（动态主机配置协议）自动为设备分配网络配置，让设备能够"即插即用"。</p>

                    <h4><i class="fas fa-handshake"></i> 8.5.1 DHCP工作过程</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-exchange-alt"></i> 四步过程（DORA）：</strong>
                        <ol>
                            <li><strong>Discover（发现）：</strong>客户端广播寻找DHCP服务器</li>
                            <li><strong>Offer（提供）：</strong>DHCP服务器提供IP地址等配置</li>
                            <li><strong>Request（请求）：</strong>客户端请求使用提供的配置</li>
                            <li><strong>Acknowledge（确认）：</strong>服务器确认分配</li>
                        </ol>
                    </div>

                    <h4><i class="fas fa-list"></i> 8.5.2 DHCP分配的信息</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-cogs"></i> 自动配置内容：</strong>
                        <ul>
                            <li>🌐 <strong>IP地址：</strong>设备在网络中的地址</li>
                            <li>🎭 <strong>子网掩码：</strong>确定网络范围</li>
                            <li>🚪 <strong>默认网关：</strong>访问其他网络的出口</li>
                            <li>🔍 <strong>DNS服务器：</strong>域名解析服务器地址</li>
                            <li>⏰ <strong>租期：</strong>IP地址的使用时间</li>
                        </ul>
                    </div>
                </section>

                <section id="troubleshooting">
                    <h2><span class="step-number">9</span>故障排查</h2>

                    <h3><i class="fas fa-tools"></i> 9.1 网络故障排查思路</h3>
                    <p>网络故障排查要遵循系统性的方法，从底层到高层逐步检查。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-layer-group"></i> 分层排查法（按OSI模型）：</strong>
                        <ol>
                            <li><strong>物理层：</strong>检查硬件连接</li>
                            <li><strong>数据链路层：</strong>检查交换机状态</li>
                            <li><strong>网络层：</strong>检查IP连通性</li>
                            <li><strong>传输层：</strong>检查端口状态</li>
                            <li><strong>应用层：</strong>检查应用程序</li>
                        </ol>
                    </div>

                    <h3><i class="fas fa-terminal"></i> 9.2 常用排查命令</h3>

                    <h4><i class="fas fa-ping-pong-paddle-ball"></i> 9.2.1 ping命令</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-bullseye"></i> ping命令用途：</strong>
                        <ul>
                            <li>🎯 <strong>测试连通性：</strong>检查网络是否可达</li>
                            <li>⏱️ <strong>测试延迟：</strong>查看网络响应时间</li>
                            <li>📊 <strong>测试稳定性：</strong>检查网络是否稳定</li>
                        </ul>

                        <pre><code># 基本ping测试
ping ***********

# 持续ping（Windows用-t，Linux默认持续）
ping -t ***********

# 指定ping次数
ping -c 4 ***********

# ping域名
ping www.baidu.com</code></pre>
                    </div>

                    <h4><i class="fas fa-route"></i> 9.2.2 traceroute/tracert命令</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-map-marked-alt"></i> 路径跟踪：</strong>
                        <p>显示数据包到达目标的完整路径，帮助定位网络问题位置。</p>

                        <pre><code># Windows系统
tracert www.baidu.com

# Linux系统
traceroute www.baidu.com

# 显示IP地址而不解析域名
traceroute -n www.baidu.com</code></pre>
                    </div>

                    <h4><i class="fas fa-network-wired"></i> 9.2.3 netstat命令</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-list"></i> 网络连接状态：</strong>
                        <pre><code># 显示所有网络连接
netstat -a

# 显示TCP连接
netstat -t

# 显示监听端口
netstat -l

# 显示进程信息
netstat -p

# 组合使用
netstat -tulpn</code></pre>
                    </div>

                    <h3><i class="fas fa-exclamation-triangle"></i> 9.3 常见网络问题</h3>

                    <h4><i class="fas fa-wifi"></i> 9.3.1 无法连接网络</h4>
                    <div class="warning-box">
                        <strong><i class="fas fa-search"></i> 排查步骤：</strong>
                        <ol>
                            <li><strong>检查物理连接：</strong>网线是否插好，WiFi是否连接</li>
                            <li><strong>检查网卡状态：</strong>设备管理器中网卡是否正常</li>
                            <li><strong>检查IP配置：</strong>是否获取到正确的IP地址</li>
                            <li><strong>测试本地连通性：</strong>ping 127.0.0.1</li>
                            <li><strong>测试网关连通性：</strong>ping 默认网关</li>
                            <li><strong>测试外网连通性：</strong>ping *******</li>
                        </ol>
                    </div>

                    <h4><i class="fas fa-clock"></i> 9.3.2 网络速度慢</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-tachometer-alt"></i> 可能原因：</strong>
                        <ul>
                            <li>🚦 <strong>网络拥塞：</strong>带宽不足或使用过度</li>
                            <li>📡 <strong>信号问题：</strong>WiFi信号弱或干扰</li>
                            <li>🔧 <strong>设备问题：</strong>网卡驱动或硬件故障</li>
                            <li>🦠 <strong>恶意软件：</strong>病毒或恶意程序占用带宽</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-globe"></i> 9.3.3 无法访问特定网站</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-search"></i> 排查方法：</strong>
                        <ol>
                            <li><strong>DNS测试：</strong>nslookup 网站域名</li>
                            <li><strong>直接IP访问：</strong>用IP地址访问网站</li>
                            <li><strong>更换DNS：</strong>尝试使用*******或***************</li>
                            <li><strong>检查防火墙：</strong>是否被防火墙阻止</li>
                            <li><strong>清除缓存：</strong>清除浏览器和DNS缓存</li>
                        </ol>
                    </div>

                    <h3><i class="fas fa-tools"></i> 9.4 网络监控工具</h3>
                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 工具</th>
                            <th><i class="fas fa-desktop"></i> 平台</th>
                            <th><i class="fas fa-cogs"></i> 功能</th>
                            <th><i class="fas fa-money-bill"></i> 费用</th>
                        </tr>
                        <tr>
                            <td><strong>Wireshark</strong></td>
                            <td>跨平台</td>
                            <td>数据包分析</td>
                            <td>免费</td>
                        </tr>
                        <tr>
                            <td><strong>PRTG</strong></td>
                            <td>Windows</td>
                            <td>网络监控</td>
                            <td>商业</td>
                        </tr>
                        <tr>
                            <td><strong>Nagios</strong></td>
                            <td>Linux</td>
                            <td>网络监控</td>
                            <td>开源</td>
                        </tr>
                        <tr>
                            <td><strong>SolarWinds</strong></td>
                            <td>Windows</td>
                            <td>网络管理</td>
                            <td>商业</td>
                        </tr>
                    </table>
                </section>

                <section id="practical-examples">
                    <h2><span class="step-number">10</span>实践案例</h2>

                    <h3><i class="fas fa-home"></i> 10.1 家庭网络搭建</h3>
                    <p>让我们通过一个实际案例来理解如何搭建家庭网络。</p>

                    <div class="success-box">
                        <strong><i class="fas fa-home"></i> 场景：</strong>小明家要搭建无线网络
                        <br><br>
                        <strong><i class="fas fa-list"></i> 需求分析：</strong>
                        <ul>
                            <li>📱 支持手机、电脑、智能电视上网</li>
                            <li>🎮 保证游戏和视频流畅</li>
                            <li>🔒 确保网络安全</li>
                            <li>💰 成本控制在合理范围</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-shopping-cart"></i> 10.1.1 设备选择</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 设备</th>
                            <th><i class="fas fa-cogs"></i> 规格要求</th>
                            <th><i class="fas fa-info-circle"></i> 说明</th>
                        </tr>
                        <tr>
                            <td><strong>光猫</strong></td>
                            <td>千兆端口</td>
                            <td>ISP提供，负责光信号转换</td>
                        </tr>
                        <tr>
                            <td><strong>无线路由器</strong></td>
                            <td>WiFi 6，千兆端口</td>
                            <td>核心设备，提供WiFi和有线连接</td>
                        </tr>
                        <tr>
                            <td><strong>网线</strong></td>
                            <td>Cat6类线</td>
                            <td>连接光猫和路由器</td>
                        </tr>
                    </table>

                    <h4><i class="fas fa-cogs"></i> 10.1.2 网络配置</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-step-forward"></i> 配置步骤：</strong>
                        <ol>
                            <li><strong>物理连接：</strong>光猫LAN口连接路由器WAN口</li>
                            <li><strong>登录路由器：</strong>浏览器访问***********</li>
                            <li><strong>设置上网方式：</strong>选择PPPoE拨号，输入宽带账号密码</li>
                            <li><strong>配置WiFi：</strong>设置WiFi名称和密码</li>
                            <li><strong>安全设置：</strong>修改管理员密码，启用防火墙</li>
                        </ol>
                    </div>

                    <h4><i class="fas fa-network-wired"></i> 10.1.3 IP地址规划</h4>
                    <pre><code>网络规划：
- 网络地址：***********/24
- 路由器地址：***********
- DHCP范围：*************-*************
- 静态IP预留：***********0-************（服务器、打印机等）</code></pre>

                    <h3><i class="fas fa-building"></i> 10.2 小型办公网络</h3>
                    <p>一个20人的小公司网络规划案例。</p>

                    <div class="info-box">
                        <strong><i class="fas fa-building"></i> 需求分析：</strong>
                        <ul>
                            <li>👥 支持20台电脑同时上网</li>
                            <li>🖨️ 共享打印机和文件服务器</li>
                            <li>📱 访客WiFi网络</li>
                            <li>🔒 内网安全隔离</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-sitemap"></i> 10.2.1 网络拓扑设计</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-project-diagram"></i> 拓扑结构：</strong>
                        <pre><code>互联网
    |
防火墙/路由器
    |
核心交换机
    |
+---+---+---+
|   |   |   |
办公区1 办公区2 服务器区 访客区
(VLAN10) (VLAN20) (VLAN30) (VLAN40)</code></pre>
                    </div>

                    <h4><i class="fas fa-network-wired"></i> 10.2.2 VLAN划分</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> VLAN</th>
                            <th><i class="fas fa-network-wired"></i> 网络</th>
                            <th><i class="fas fa-users"></i> 用途</th>
                            <th><i class="fas fa-shield-alt"></i> 安全策略</th>
                        </tr>
                        <tr>
                            <td>VLAN 10</td>
                            <td>192.168.10.0/24</td>
                            <td>办公区1（管理部门）</td>
                            <td>高安全级别</td>
                        </tr>
                        <tr>
                            <td>VLAN 20</td>
                            <td>192.168.20.0/24</td>
                            <td>办公区2（业务部门）</td>
                            <td>中等安全级别</td>
                        </tr>
                        <tr>
                            <td>VLAN 30</td>
                            <td>192.168.30.0/24</td>
                            <td>服务器区</td>
                            <td>最高安全级别</td>
                        </tr>
                        <tr>
                            <td>VLAN 40</td>
                            <td>192.168.40.0/24</td>
                            <td>访客网络</td>
                            <td>隔离访问</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-laptop-code"></i> 10.3 实验练习</h3>
                    <p>通过实际操作来巩固所学知识。</p>

                    <h4><i class="fas fa-terminal"></i> 10.3.1 基础网络命令练习</h4>
                    <div class="warning-box">
                        <strong><i class="fas fa-tasks"></i> 练习任务：</strong>
                        <ol>
                            <li>查看本机IP配置信息</li>
                            <li>测试到网关的连通性</li>
                            <li>查看到百度的路由路径</li>
                            <li>查看本机开放的端口</li>
                            <li>查询百度的IP地址</li>
                        </ol>

                        <strong>参考命令：</strong>
                        <pre><code># Windows系统
ipconfig /all
ping ***********
tracert www.baidu.com
netstat -an
nslookup www.baidu.com

# Linux系统
ip addr show
ping ***********
traceroute www.baidu.com
netstat -tulpn
dig www.baidu.com</code></pre>
                    </div>

                    <h4><i class="fas fa-calculator"></i> 10.3.2 子网划分练习</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-question-circle"></i> 练习题：</strong>
                        <p>给定网络***********/24，要求划分为8个子网，每个子网至少支持25台主机。</p>

                        <details style="margin-top: 15px;">
                            <summary><strong>点击查看答案</strong></summary>
                            <div style="margin-top: 10px;">
                                <p><strong>解答：</strong></p>
                                <ul>
                                    <li>需要8个子网：2^3 = 8，借用3位</li>
                                    <li>新子网掩码：/24 + 3 = /27</li>
                                    <li>每个子网主机数：2^5 - 2 = 30台（满足25台需求）</li>
                                    <li>子网间隔：256 - 224 = 32</li>
                                </ul>
                                <p><strong>子网列表：</strong></p>
                                <ul>
                                    <li>***********/27 (192.168.0.1-192.168.0.30)</li>
                                    <li>192.168.0.32/27 (192.168.0.33-192.168.0.62)</li>
                                    <li>192.168.0.64/27 (192.168.0.65-192.168.0.94)</li>
                                    <li>... 以此类推</li>
                                </ul>
                            </div>
                        </details>
                    </div>
                </section>

                <section id="advanced-topics">
                    <h2><span class="step-number">11</span>进阶话题</h2>

                    <h3><i class="fas fa-shield-alt"></i> 11.1 网络安全基础</h3>
                    <p>网络安全是网络管理中不可忽视的重要方面。</p>

                    <h4><i class="fas fa-exclamation-triangle"></i> 11.1.1 常见安全威胁</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-bug"></i> 威胁类型</th>
                            <th><i class="fas fa-info-circle"></i> 描述</th>
                            <th><i class="fas fa-shield-alt"></i> 防护措施</th>
                        </tr>
                        <tr>
                            <td><strong>恶意软件</strong></td>
                            <td>病毒、木马、勒索软件</td>
                            <td>安装杀毒软件，定期更新</td>
                        </tr>
                        <tr>
                            <td><strong>网络攻击</strong></td>
                            <td>DDoS、端口扫描</td>
                            <td>配置防火墙，关闭不必要端口</td>
                        </tr>
                        <tr>
                            <td><strong>数据泄露</strong></td>
                            <td>敏感信息被窃取</td>
                            <td>数据加密，访问控制</td>
                        </tr>
                        <tr>
                            <td><strong>社会工程</strong></td>
                            <td>钓鱼邮件、欺诈电话</td>
                            <td>安全意识培训</td>
                        </tr>
                    </table>

                    <h4><i class="fas fa-fire"></i> 11.1.2 防火墙基础</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-shield-alt"></i> 防火墙类型：</strong>
                        <ul>
                            <li>🔥 <strong>包过滤防火墙：</strong>基于IP地址和端口过滤</li>
                            <li>🔍 <strong>状态检测防火墙：</strong>跟踪连接状态</li>
                            <li>🛡️ <strong>应用层防火墙：</strong>深度包检测</li>
                            <li>🌐 <strong>下一代防火墙：</strong>集成多种安全功能</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-cloud"></i> 11.2 云网络基础</h3>
                    <p>云计算改变了传统网络架构，带来了新的概念和技术。</p>

                    <h4><i class="fas fa-server"></i> 11.2.1 云网络特点</h4>
                    <div class="success-box">
                        <strong><i class="fas fa-cloud"></i> 主要特点：</strong>
                        <ul>
                            <li>🔧 <strong>软件定义：</strong>网络功能通过软件实现</li>
                            <li>📈 <strong>弹性扩展：</strong>根据需求动态调整</li>
                            <li>🌐 <strong>全球分布：</strong>多地域部署</li>
                            <li>💰 <strong>按需付费：</strong>根据使用量计费</li>
                        </ul>
                    </div>

                    <h4><i class="fas fa-network-wired"></i> 11.2.2 SDN（软件定义网络）</h4>
                    <div class="info-box">
                        <strong><i class="fas fa-lightbulb"></i> SDN核心概念：</strong>
                        <ul>
                            <li>🧠 <strong>控制平面：</strong>网络大脑，制定转发策略</li>
                            <li>📦 <strong>数据平面：</strong>执行数据转发</li>
                            <li>🔌 <strong>南向接口：</strong>控制器与交换机通信</li>
                            <li>📡 <strong>北向接口：</strong>应用与控制器通信</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-mobile-alt"></i> 11.3 移动网络基础</h3>
                    <p>移动通信网络的发展历程和关键技术。</p>

                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 技术代</th>
                            <th><i class="fas fa-calendar"></i> 时期</th>
                            <th><i class="fas fa-tachometer-alt"></i> 速度</th>
                            <th><i class="fas fa-star"></i> 主要特点</th>
                        </tr>
                        <tr>
                            <td><strong>2G</strong></td>
                            <td>1990年代</td>
                            <td>64 Kbps</td>
                            <td>数字语音，短信</td>
                        </tr>
                        <tr>
                            <td><strong>3G</strong></td>
                            <td>2000年代</td>
                            <td>2 Mbps</td>
                            <td>移动数据，视频通话</td>
                        </tr>
                        <tr>
                            <td><strong>4G</strong></td>
                            <td>2010年代</td>
                            <td>100 Mbps</td>
                            <td>高速数据，移动互联网</td>
                        </tr>
                        <tr>
                            <td><strong>5G</strong></td>
                            <td>2020年代</td>
                            <td>10 Gbps</td>
                            <td>超低延迟，万物互联</td>
                        </tr>
                    </table>

                    <h3><i class="fas fa-globe"></i> 11.4 IPv6简介</h3>
                    <p>IPv6是下一代互联网协议，解决IPv4地址不足的问题。</p>

                    <h4><i class="fas fa-compare"></i> 11.4.1 IPv4 vs IPv6</h4>
                    <table>
                        <tr>
                            <th><i class="fas fa-tag"></i> 特性</th>
                            <th><i class="fas fa-4"></i> IPv4</th>
                            <th><i class="fas fa-6"></i> IPv6</th>
                        </tr>
                        <tr>
                            <td><strong>地址长度</strong></td>
                            <td>32位</td>
                            <td>128位</td>
                        </tr>
                        <tr>
                            <td><strong>地址数量</strong></td>
                            <td>43亿个</td>
                            <td>340万亿亿亿个</td>
                        </tr>
                        <tr>
                            <td><strong>地址表示</strong></td>
                            <td>***********</td>
                            <td>2001:db8::1</td>
                        </tr>
                        <tr>
                            <td><strong>配置方式</strong></td>
                            <td>手动或DHCP</td>
                            <td>自动配置</td>
                        </tr>
                    </table>

                    <div class="warning-box">
                        <strong><i class="fas fa-info-circle"></i> 过渡期：</strong>
                        <p>目前IPv4和IPv6并存，通过双栈、隧道、转换等技术实现互通。</p>
                    </div>
                </section>

                <section id="summary">
                    <h2><span class="step-number">12</span>总结</h2>

                    <h3><i class="fas fa-graduation-cap"></i> 12.1 学习回顾</h3>
                    <p>恭喜你完成了网络基础知识的学习！让我们回顾一下学到的重要内容：</p>

                    <div class="success-box">
                        <strong><i class="fas fa-check-circle"></i> 你已经掌握了：</strong>
                        <ul>
                            <li>🌐 <strong>网络基本概念：</strong>理解了网络的组成和工作原理</li>
                            <li>📊 <strong>网络模型：</strong>掌握了OSI七层模型和TCP/IP模型</li>
                            <li>🏠 <strong>IP地址：</strong>学会了IP地址的分类和使用</li>
                            <li>✂️ <strong>子网划分：</strong>能够进行基本的子网规划</li>
                            <li>🗺️ <strong>路由交换：</strong>理解了数据转发的基本原理</li>
                            <li>📡 <strong>网络协议：</strong>了解了常用协议的作用</li>
                            <li>🔧 <strong>故障排查：</strong>掌握了基本的网络诊断方法</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-road"></i> 12.2 继续学习的方向</h3>
                    <p>网络技术博大精深，这只是一个开始。根据你的兴趣和职业规划，可以选择以下方向深入学习：</p>

                    <div class="info-box">
                        <strong><i class="fas fa-arrow-right"></i> 进阶学习路径：</strong>

                        <h4><i class="fas fa-network-wired"></i> 网络工程师方向</h4>
                        <ul>
                            <li>🔧 深入学习路由交换技术</li>
                            <li>🏢 企业网络设计与实施</li>
                            <li>📜 考取网络认证（CCNA、HCNA等）</li>
                        </ul>

                        <h4><i class="fas fa-shield-alt"></i> 网络安全方向</h4>
                        <ul>
                            <li>🔒 网络安全防护技术</li>
                            <li>🕵️ 渗透测试和漏洞分析</li>
                            <li>📜 考取安全认证（CISSP、CEH等）</li>
                        </ul>

                        <h4><i class="fas fa-cloud"></i> 云网络方向</h4>
                        <ul>
                            <li>☁️ 云平台网络服务</li>
                            <li>🔧 软件定义网络（SDN）</li>
                            <li>📜 考取云认证（AWS、Azure等）</li>
                        </ul>
                    </div>

                    <h3><i class="fas fa-lightbulb"></i> 12.3 实践建议</h3>
                    <div class="warning-box">
                        <strong><i class="fas fa-hands-helping"></i> 持续提升的建议：</strong>
                        <ol>
                            <li><strong>动手实践：</strong>搭建实验环境，多做实验</li>
                            <li><strong>关注新技术：</strong>网络技术发展很快，要持续学习</li>
                            <li><strong>参与社区：</strong>加入技术论坛，与同行交流</li>
                            <li><strong>解决实际问题：</strong>将所学知识应用到工作中</li>
                            <li><strong>系统学习：</strong>选择一个方向深入研究</li>
                        </ol>
                    </div>

                    <h3><i class="fas fa-heart"></i> 12.4 结语</h3>
                    <div class="success-box">
                        <strong><i class="fas fa-star"></i> 最后的话：</strong>
                        <p>网络技术是现代信息社会的基础，掌握网络知识不仅能帮助你更好地理解数字世界，还能为你的职业发展打开新的大门。</p>
                        <p>记住：<strong>学习是一个持续的过程，实践是最好的老师。</strong>希望这个教程能够成为你网络学习之路的良好开端！</p>
                        <p style="text-align: center; margin-top: 20px;">
                            <strong>🎉 祝你在网络技术的道路上越走越远！ 🎉</strong>
                        </p>
                    </div>
                </section>
            </div>
        </div>
    </div>

    <!-- 返回顶部按钮 -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </a>

    <script>
        // 移动端菜单切换
        document.getElementById('mobileMenuBtn').addEventListener('click', function () {
            document.getElementById('sidebar').classList.toggle('active');
        });

        // 返回顶部功能
        window.addEventListener('scroll', function () {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'flex';
            } else {
                backToTop.style.display = 'none';
            }
        });

        document.getElementById('backToTop').addEventListener('click', function (e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // 侧边栏导航高亮
        window.addEventListener('scroll', function () {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.sidebar a[href^="#"]');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>

</html>