services:
  dm8-database:
    build:
      context: .
      dockerfile: Dockerfile
    image: dm8-auto:latest
    container_name: dm8-database
    hostname: dm8-server
    restart: unless-stopped

    # 端口映射 - 根据auto.xml配置
    ports:
      - "5236:5236"

    # 环境变量
    environment:
      - LANG=zh_CN.UTF-8
      - LC_ALL=zh_CN.UTF-8
      - TZ=Asia/Shanghai
      - DM_HOME=/home/<USER>/dmdbms
      - DM_DATA=/home/<USER>/dmdbms/data
      - DM_DB_NAME=DAMENG
      - DM_INSTANCE_NAME=DMSERVER
      - DM_PORT=5236

    # 数据卷挂载
    volumes:
      - dm8_data:/home/<USER>/dmdbms/data
      - dm8_logs:/home/<USER>/dmdbms/log

    # 健康检查
    healthcheck:
      test: ["CMD", "/usr/local/bin/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

    # 资源限制
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'

# 数据卷定义
volumes:
  dm8_data:
    driver: local
    name: dm8_database_data
  dm8_logs:
    driver: local
    name: dm8_database_logs

# 网络定义
networks:
  default:
    name: dm8_network
    driver: bridge
