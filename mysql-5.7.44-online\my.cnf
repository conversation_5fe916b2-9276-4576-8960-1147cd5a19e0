[client]
port = 3306
socket = /usr/local/mysql/mysql.sock
default-character-set = utf8mb4

[mysql]
default-character-set = utf8mb4

[mysqld]
# 基本设置
user = mysql
port = 3306
basedir = /usr/local/mysql
datadir = /usr/local/mysql/data
socket = /usr/local/mysql/mysql.sock
pid-file = /usr/local/mysql/mysql.pid

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# 大小写不敏感设置
lower_case_table_names = 1

# 网络设置
bind-address = 0.0.0.0
skip-name-resolve
back_log = 600
max_connections = 1000
max_connect_errors = 6000
open_files_limit = 65535
table_open_cache = 128
max_allowed_packet = 500M
binlog_cache_size = 1M
max_heap_table_size = 8M
tmp_table_size = 16M

# 查询缓存设置
query_cache_size = 8M
query_cache_type = 1
query_cache_limit = 2M

# 排序和分组设置
sort_buffer_size = 8M
join_buffer_size = 8M
thread_cache_size = 8
# thread_concurrency = 8  # 在MySQL 5.7中已废弃
query_cache_size = 8M
thread_stack = 192K

# MyISAM设置
key_buffer_size = 4M
read_buffer_size = 2M
read_rnd_buffer_size = 8M
bulk_insert_buffer_size = 64M

# InnoDB设置
default-storage-engine = INNODB
sql-mode="STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION"
innodb_flush_log_at_trx_commit = 1
innodb_log_buffer_size = 2M
innodb_buffer_pool_size = 128M
innodb_log_file_size = 10M
# innodb_thread_concurrency = 8  # 在MySQL 5.7中已废弃
innodb_autoextend_increment = 64
innodb_buffer_pool_instances = 8
innodb_concurrency_tickets = 5000
innodb_old_blocks_time = 1000
innodb_open_files = 300
innodb_stats_on_metadata = 0
innodb_file_per_table = 1

# 日志设置
log-error = /usr/local/mysql/logs/error.log
slow_query_log = 1
slow_query_log_file = /usr/local/mysql/logs/slow.log
long_query_time = 3
log_queries_not_using_indexes = 1

# 二进制日志设置
server-id = 1
log-bin = /usr/local/mysql/logs/mysql-bin
binlog_format = mixed
sync_binlog = 0
expire_logs_days = 10

# 安全设置
skip-external-locking
skip-ssl

# 其他优化设置
transaction_isolation = REPEATABLE-READ
concurrent_insert = 2
connect_timeout = 20
wait_timeout = 28800
max_user_connections = 0
skip-name-resolve

# 时区设置
default-time-zone = '+8:00'

# 性能模式
performance_schema_max_table_instances = 600
table_definition_cache = 700
table_open_cache = 600

[mysqldump]
quick
max_allowed_packet = 500M

[mysql]
no-auto-rehash

[myisamchk]
key_buffer_size = 8M
sort_buffer_size = 8M
read_buffer = 4M
write_buffer = 4M

[mysqlhotcopy]
interactive-timeout

[mysqld_safe]
log-error = /usr/local/mysql/logs/error.log
pid-file = /usr/local/mysql/mysql.pid
socket = /usr/local/mysql/mysql.sock
