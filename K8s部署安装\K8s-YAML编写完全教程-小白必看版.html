<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>K8s YAML编写完全教程 - 小白必看版</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <style>
    :root {
      --primary-color: #667eea;
      --primary-dark: #5a67d8;
      --secondary-color: #764ba2;
      --accent-color: #f093fb;
      --success-color: #48bb78;
      --warning-color: #ed8936;
      --error-color: #f56565;
      --info-color: #4299e1;
      --dark-bg: #1a202c;
      --dark-surface: #2d3748;
      --light-bg: #f7fafc;
      --light-surface: #ffffff;
      --text-primary: #2d3748;
      --text-secondary: #718096;
      --border-color: #e2e8f0;
      --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
      line-height: 1.7;
      color: var(--text-primary);
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }

    /* 侧边栏样式 */
    .sidebar {
      position: fixed;
      left: 0;
      top: 0;
      width: 320px;
      height: 100vh;
      background: linear-gradient(180deg, var(--dark-bg) 0%, var(--dark-surface) 100%);
      color: white;
      overflow-y: auto;
      z-index: 1000;
      box-shadow: var(--shadow-xl);
      backdrop-filter: blur(10px);
    }

    .sidebar::-webkit-scrollbar {
      width: 6px;
    }

    .sidebar::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
    }

    .sidebar::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;
    }

    .sidebar::-webkit-scrollbar-thumb:hover {
      background: rgba(255, 255, 255, 0.5);
    }

    .sidebar-header {
      padding: 30px 25px;
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      position: relative;
      overflow: hidden;
    }

    .sidebar-header::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
      animation: float 6s ease-in-out infinite;
    }

    @keyframes float {

      0%,
      100% {
        transform: translateY(0px) rotate(0deg);
      }

      50% {
        transform: translateY(-20px) rotate(180deg);
      }
    }

    .sidebar-header h2 {
      color: #ffffff;
      text-align: center;
      font-size: 20px;
      font-weight: 700;
      margin-bottom: 8px;
      position: relative;
      z-index: 1;
    }

    .sidebar-header p {
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
      font-size: 13px;
      font-weight: 400;
      position: relative;
      z-index: 1;
    }

    .sidebar-nav {
      padding: 20px 0;
    }

    .sidebar ul {
      list-style: none;
    }

    .sidebar li {
      margin: 3px 0;
    }

    .sidebar a {
      display: flex;
      align-items: center;
      color: rgba(255, 255, 255, 0.7);
      text-decoration: none;
      padding: 15px 25px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      font-size: 14px;
      font-weight: 500;
      border-left: 3px solid transparent;
      position: relative;
      overflow: hidden;
    }

    .sidebar a::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 0;
      height: 100%;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
      transition: width 0.3s ease;
    }

    .sidebar a:hover::before {
      width: 100%;
    }

    .sidebar a:hover {
      color: #ffffff;
      border-left-color: var(--accent-color);
      padding-left: 35px;
      background: rgba(255, 255, 255, 0.05);
    }

    .sidebar a.active {
      background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
      color: white;
      border-left-color: var(--accent-color);
      box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .sidebar a i {
      margin-right: 12px;
      width: 16px;
      text-align: center;
      font-size: 14px;
    }

    /* 主内容区域 */
    .main-content {
      margin-left: 320px;
      min-height: 100vh;
      background: var(--light-bg);
    }

    .content-wrapper {
      padding: 40px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .container {
      background: var(--light-surface);
      padding: 50px;
      border-radius: 20px;
      box-shadow: var(--shadow-xl);
      position: relative;
      overflow: hidden;
    }

    .container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
    }

    /* 标题样式 */
    h1 {
      color: var(--text-primary);
      text-align: center;
      font-size: 36px;
      font-weight: 700;
      margin-bottom: 40px;
      position: relative;
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    h1::after {
      content: '';
      position: absolute;
      bottom: -15px;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 4px;
      background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
      border-radius: 2px;
    }

    h2 {
      color: var(--text-primary);
      font-size: 28px;
      font-weight: 600;
      margin: 50px 0 25px 0;
      padding-left: 20px;
      border-left: 5px solid var(--primary-color);
      position: relative;
    }

    h2::before {
      content: '';
      position: absolute;
      left: -5px;
      top: 0;
      bottom: 0;
      width: 5px;
      background: linear-gradient(180deg, var(--primary-color) 0%, var(--accent-color) 100%);
      border-radius: 0 3px 3px 0;
    }

    h3 {
      color: var(--primary-dark);
      font-size: 22px;
      font-weight: 600;
      margin: 35px 0 20px 0;
      position: relative;
      padding-left: 15px;
    }

    h3::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      background: var(--primary-color);
      border-radius: 50%;
    }

    h4 {
      color: var(--success-color);
      font-size: 18px;
      font-weight: 600;
      margin: 25px 0 15px 0;
      display: flex;
      align-items: center;
    }

    h4::before {
      content: '▶';
      margin-right: 8px;
      color: var(--success-color);
      font-size: 12px;
    }

    /* 提示框样式 */
    .info-box,
    .warning-box,
    .success-box,
    .danger-box,
    .tip-box {
      padding: 25px;
      margin: 25px 0;
      border-radius: 15px;
      border-left: 5px solid;
      position: relative;
      backdrop-filter: blur(10px);
      box-shadow: var(--shadow-md);
      transition: all 0.3s ease;
    }

    .info-box:hover,
    .warning-box:hover,
    .success-box:hover,
    .danger-box:hover,
    .tip-box:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }

    .info-box {
      background: linear-gradient(135deg, rgba(93, 173, 226, 0.12) 0%, rgba(93, 173, 226, 0.06) 100%);
      border-left-color: #5dade2;
      color: #2c3e50;
    }

    .warning-box {
      background: linear-gradient(135deg, rgba(230, 126, 34, 0.12) 0%, rgba(230, 126, 34, 0.06) 100%);
      border-left-color: #e67e22;
      color: #8b4513;
    }

    .success-box {
      background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%);
      border: 1px solid #c8e6c9;
      border-left: 4px solid #4caf50;
      border-radius: 6px;
      box-shadow: 0 2px 4px rgba(76, 175, 80, 0.1);
      color: #2e7d32 !important;
    }

    .success-box * {
      color: #2e7d32 !important;
    }

    .success-box strong {
      color: #1b5e20 !important;
      font-weight: 600;
    }

    .success-box code {
      background: rgba(255, 255, 255, 0.8) !important;
      color: #1b5e20 !important;
      padding: 2px 6px !important;
      border-radius: 4px !important;
      border: 1px solid #c8e6c9 !important;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
    }

    .success-box pre {
      background: rgba(255, 255, 255, 0.6) !important;
      border: 1px solid #c8e6c9 !important;
      border-radius: 4px !important;
      padding: 12px !important;
      margin: 10px 0 !important;
    }

    .danger-box {
      background: linear-gradient(135deg, rgba(231, 76, 60, 0.12) 0%, rgba(231, 76, 60, 0.06) 100%);
      border-left-color: #e74c3c;
      color: #922b21;
    }

    .tip-box {
      background: linear-gradient(135deg, rgba(155, 89, 182, 0.12) 0%, rgba(155, 89, 182, 0.06) 100%);
      border-left-color: #9b59b6;
      color: #4a235a;
    }

    /* 代码样式 */
    pre {
      background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
      color: #e2e8f0;
      padding: 25px;
      border-radius: 15px;
      overflow-x: auto;
      margin: 25px 0;
      border-left: 5px solid var(--primary-color);
      position: relative;
      box-shadow: var(--shadow-lg);
      font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
      font-size: 14px;
      line-height: 1.6;
    }

    pre::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, var(--primary-color) 0%, var(--accent-color) 100%);
      border-radius: 15px 15px 0 0;
    }

    /* YAML特殊样式 - 柔和蓝灰色 */
    .yaml-example {
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      border-left-color: #5dade2;
      color: #ecf0f1;
    }

    .yaml-example::before {
      background: linear-gradient(90deg, #5dade2 0%, #3498db 100%);
    }

    /* 错误示例样式 - 柔和橙红色 */
    .yaml-wrong {
      background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
      border-left-color: #e67e22;
      color: #fdf2e9;
    }

    .yaml-wrong::before {
      background: linear-gradient(90deg, #e67e22 0%, #d35400 100%);
    }

    /* 正确示例样式 - 柔和绿色 */
    .yaml-correct {
      background: linear-gradient(135deg, #1e3a2e 0%, #2d5a3d 100%);
      border-left-color: #58d68d;
      color: #eafaf1;
    }

    .yaml-correct::before {
      background: linear-gradient(90deg, #58d68d 0%, #27ae60 100%);
    }

    code {
      background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
      padding: 4px 8px;
      border-radius: 6px;
      font-family: 'Fira Code', 'Consolas', 'Monaco', monospace;
      color: var(--error-color);
      font-size: 13px;
      font-weight: 500;
      box-shadow: var(--shadow-sm);
    }

    pre code {
      background: transparent;
      padding: 0;
      color: #e2e8f0;
      box-shadow: none;
    }

    /* 表格样式 */
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 25px 0;
      background: var(--light-surface);
      border-radius: 15px;
      overflow: hidden;
      box-shadow: var(--shadow-lg);
    }

    th,
    td {
      padding: 18px;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
    }

    th {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
      color: white;
      font-weight: 600;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    tr:nth-child(even) {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    }

    tr:hover {
      background: linear-gradient(135deg, #e6fffa 0%, #b2f5ea 100%);
      transform: scale(1.01);
      transition: all 0.3s ease;
    }

    /* 步骤编号 */
    .step-number {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
      color: white;
      border-radius: 50%;
      width: 45px;
      height: 45px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      margin-right: 20px;
      font-weight: 700;
      font-size: 18px;
      box-shadow: var(--shadow-lg);
      position: relative;
      overflow: hidden;
    }

    .step-number::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
      animation: rotate 3s linear infinite;
    }

    @keyframes rotate {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    /* 返回顶部按钮 */
    .back-to-top {
      position: fixed;
      bottom: 30px;
      right: 30px;
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
      color: white;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      text-decoration: none;
      display: none;
      align-items: center;
      justify-content: center;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: var(--shadow-xl);
      z-index: 999;
      font-size: 20px;
    }

    .back-to-top:hover {
      transform: translateY(-5px) scale(1.1);
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    /* 移动端菜单按钮 */
    .mobile-menu-btn {
      display: none;
      position: fixed;
      top: 20px;
      left: 20px;
      z-index: 1001;
      background: var(--primary-color);
      color: white;
      border: none;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      font-size: 20px;
      cursor: pointer;
      box-shadow: var(--shadow-lg);
    }

    /* 响应式设计 */
    @media (max-width: 1024px) {
      .sidebar {
        width: 280px;
      }

      .main-content {
        margin-left: 280px;
      }

      .content-wrapper {
        padding: 30px;
      }

      .container {
        padding: 40px;
      }
    }

    @media (max-width: 768px) {
      .mobile-menu-btn {
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        width: 100%;
      }

      .sidebar.active {
        transform: translateX(0);
      }

      .main-content {
        margin-left: 0;
      }

      .content-wrapper {
        padding: 20px;
      }

      .container {
        padding: 30px;
        border-radius: 15px;
      }

      h1 {
        font-size: 28px;
      }

      h2 {
        font-size: 24px;
      }

      .step-number {
        width: 35px;
        height: 35px;
        font-size: 16px;
        margin-right: 15px;
      }
    }

    /* 滚动条美化 */
    ::-webkit-scrollbar {
      width: 8px;
    }

    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    }
  </style>
</head>

<body>
  <!-- 移动端菜单按钮 -->
  <button class="mobile-menu-btn" id="mobileMenuBtn">
    <i class="fas fa-bars"></i>
  </button>

  <!-- 侧边栏导航 -->
  <div class="sidebar" id="sidebar">
    <div class="sidebar-header">
      <h2><i class="fas fa-file-code"></i> K8s YAML教程</h2>
      <p>从零开始学会写YAML</p>
    </div>
    <nav class="sidebar-nav">
      <ul>
        <li><a href="#introduction"><i class="fas fa-play"></i>1. 入门介绍</a></li>
        <li><a href="#yaml-basics"><i class="fas fa-book"></i>2. YAML基础语法</a></li>
        <li><a href="#k8s-structure"><i class="fas fa-sitemap"></i>3. K8s资源结构</a></li>
        <li><a href="#pod-yaml"><i class="fas fa-cube"></i>4. Pod配置详解</a></li>
        <li><a href="#deployment-yaml"><i class="fas fa-rocket"></i>5. Deployment配置</a></li>
        <li><a href="#service-yaml"><i class="fas fa-network-wired"></i>6. Service配置</a></li>
        <li><a href="#configmap-secret"><i class="fas fa-key"></i>7. ConfigMap和Secret</a></li>
        <li><a href="#volume-storage"><i class="fas fa-hdd"></i>8. 存储配置</a></li>
        <li><a href="#ingress-yaml"><i class="fas fa-globe"></i>9. Ingress配置</a></li>
        <li><a href="#advanced-features"><i class="fas fa-cogs"></i>10. 高级特性</a></li>
        <li><a href="#best-practices"><i class="fas fa-star"></i>11. 最佳实践</a></li>
        <li><a href="#troubleshooting"><i class="fas fa-bug"></i>12. 常见错误</a></li>
      </ul>
    </nav>
  </div>

  <!-- 主内容区域 -->
  <div class="main-content">
    <div class="content-wrapper">
      <div class="container">
        <h1><i class="fas fa-file-code"></i> K8s YAML编写完全教程</h1>

        <div class="info-box">
          <strong><i class="fas fa-info-circle"></i>
            教程说明：</strong>本教程专为K8s小白设计，从YAML基础语法开始，逐步深入到K8s各种资源的配置编写。每个示例都有详细的注释和解释，确保您能完全理解每一行代码的含义和作用。
        </div>

        <div class="tip-box">
          <strong><i class="fas fa-lightbulb"></i> 学习建议：</strong>
          <ul>
            <li>建议按章节顺序学习，每个章节都有实际的代码示例</li>
            <li>可以复制示例代码到您的环境中实际测试</li>
            <li>遇到不理解的地方，可以参考注释和说明</li>
            <li>建议准备一个K8s测试环境来验证配置</li>
          </ul>
        </div>

        <section id="introduction">
          <h2><span class="step-number">1</span>入门介绍</h2>

          <h3><i class="fas fa-question-circle"></i> 1.1 什么是YAML</h3>
          <p>YAML（YAML Ain't Markup Language）是一种人类可读的数据序列化标准，常用于配置文件。在Kubernetes中，我们使用YAML文件来定义和配置各种资源。</p>

          <div class="info-box">
            <strong><i class="fas fa-info-circle"></i> YAML的特点：</strong>
            <ul>
              <li><strong>人类可读：</strong>语法简洁，易于理解</li>
              <li><strong>层次结构：</strong>通过缩进表示数据的层次关系</li>
              <li><strong>数据类型丰富：</strong>支持字符串、数字、布尔值、列表、字典等</li>
              <li><strong>注释支持：</strong>可以添加注释来解释配置</li>
            </ul>
          </div>

          <h3><i class="fas fa-dharmachakra"></i> 1.2 为什么K8s使用YAML</h3>
          <table>
            <tr>
              <th><i class="fas fa-check"></i> 优势</th>
              <th><i class="fas fa-info-circle"></i> 说明</th>
            </tr>
            <tr>
              <td><i class="fas fa-eye"></i> 可读性强</td>
              <td>比JSON更容易阅读和编写</td>
            </tr>
            <tr>
              <td><i class="fas fa-comment"></i> 支持注释</td>
              <td>可以在配置中添加说明和文档</td>
            </tr>
            <tr>
              <td><i class="fas fa-code-branch"></i> 版本控制友好</td>
              <td>文本格式便于Git等版本控制系统管理</td>
            </tr>
            <tr>
              <td><i class="fas fa-layer-group"></i> 层次清晰</td>
              <td>通过缩进清晰表达配置的层次结构</td>
            </tr>
          </table>

          <h3><i class="fas fa-tools"></i> 1.3 编写YAML的工具推荐</h3>
          <div class="tip-box">
            <strong><i class="fas fa-lightbulb"></i> 推荐工具：</strong>
            <ul>
              <li><strong>VS Code：</strong>安装Kubernetes插件，提供语法高亮和自动补全</li>
              <li><strong>vim/nano：</strong>服务器上直接编辑，注意设置正确的缩进</li>
              <li><strong>在线验证器：</strong>http://www.yamllint.com/ 验证YAML语法</li>
              <li><strong>kubectl：</strong>使用 <code>kubectl apply --dry-run=client</code> 验证配置</li>
            </ul>
          </div>
        </section>

        <section id="yaml-basics">
          <h2><span class="step-number">2</span>YAML基础语法</h2>

          <h3><i class="fas fa-indent"></i> 2.1 缩进规则（最重要！）</h3>
          <div class="warning-box">
            <strong><i class="fas fa-exclamation-triangle"></i> 缩进是YAML的灵魂！</strong>
            <ul>
              <li><strong>只能使用空格，不能使用Tab键</strong></li>
              <li><strong>同一层级的元素必须左对齐</strong></li>
              <li><strong>子元素必须比父元素多缩进</strong></li>
              <li><strong>建议使用2个空格作为一个缩进级别</strong></li>
            </ul>
          </div>

          <h4><i class="fas fa-times-circle"></i> 错误示例（不要这样写）</h4>
          <pre class="yaml-wrong"><code># 错误：混用空格和Tab
apiVersion: v1
kind: Pod
	metadata:  # 这里用了Tab，错误！
  name: my-pod

# 错误：缩进不一致
spec:
  containers:
  - name: app
      image: nginx  # 缩进过多
    ports:
   - containerPort: 80  # 缩进不够</code></pre>

          <h4><i class="fas fa-check-circle"></i> 正确示例（要这样写）</h4>
          <pre class="yaml-correct"><code># 正确：统一使用2个空格缩进
apiVersion: v1
kind: Pod
metadata:  # 2个空格缩进
  name: my-pod  # 4个空格缩进
spec:  # 2个空格缩进
  containers:  # 4个空格缩进
  - name: app  # 4个空格缩进，-后面有一个空格
    image: nginx  # 6个空格缩进
    ports:  # 6个空格缩进
    - containerPort: 80  # 6个空格缩进</code></pre>

          <h3><i class="fas fa-list"></i> 2.2 数据类型详解</h3>

          <h4><i class="fas fa-font"></i> 2.2.1 字符串（String）</h4>
          <pre class="yaml-example"><code># 字符串可以不加引号（推荐）
name: my-application
namespace: default

# 包含特殊字符时需要加引号
message: "Hello, World!"
path: "/var/log/app.log"

# 多行字符串
description: |
  这是一个多行字符串
  每一行都会保留
  包括换行符

# 折叠字符串（会把换行变成空格）
summary: >
  这是一个很长的描述
  会被折叠成一行
  中间用空格连接</code></pre>

          <h4><i class="fas fa-hashtag"></i> 2.2.2 数字和布尔值</h4>
          <pre class="yaml-example"><code># 整数
replicas: 3
port: 8080

# 浮点数
cpu: 0.5
memory: 1.5

# 布尔值（注意：必须小写）
enabled: true
debug: false

# 字符串形式的数字（加引号）
version: "1.0"
tag: "v2.1.0"</code></pre>

          <h4><i class="fas fa-list-ul"></i> 2.2.3 列表（数组）</h4>
          <pre class="yaml-example"><code># 方式1：使用短横线（推荐）
ports:
- containerPort: 80    # 注意：- 后面有一个空格
- containerPort: 443
- containerPort: 8080

# 方式2：行内格式（简单列表可用）
tags: [web, frontend, nginx]

# 复杂对象的列表
containers:
- name: web-server          # 第一个容器
  image: nginx:1.20
  ports:
  - containerPort: 80
- name: sidecar            # 第二个容器
  image: busybox:latest
  command: ["sleep", "3600"]</code></pre>

          <h4><i class="fas fa-sitemap"></i> 2.2.4 字典（对象）</h4>
          <pre class="yaml-example"><code># 字典：键值对的集合
metadata:
  name: my-pod              # 键：name，值：my-pod
  namespace: default        # 键：namespace，值：default
  labels:                   # 键：labels，值：下面的字典
    app: web-server
    version: v1.0
    environment: production

# 嵌套字典
spec:
  containers:
  - name: app
    resources:              # 嵌套字典
      requests:             # 再次嵌套
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"</code></pre>

          <h3><i class="fas fa-comment"></i> 2.3 注释的使用</h3>
          <pre class="yaml-example"><code># 这是一个完整的Pod配置示例
# 作者：K8s学习者
# 创建时间：2023-12-01

apiVersion: v1              # API版本
kind: Pod                   # 资源类型
metadata:
  name: nginx-pod           # Pod名称
  labels:
    app: nginx              # 应用标签
spec:
  containers:               # 容器列表
  - name: nginx             # 容器名称
    image: nginx:1.20       # 镜像版本
    ports:
    - containerPort: 80     # 容器端口
      protocol: TCP         # 协议类型（默认TCP）
    # 资源限制（可选）
    resources:
      requests:
        memory: "64Mi"      # 最小内存需求
        cpu: "250m"         # 最小CPU需求</code></pre>

          <div class="tip-box">
            <strong><i class="fas fa-lightbulb"></i> 注释最佳实践：</strong>
            <ul>
              <li>在复杂配置前添加说明注释</li>
              <li>解释非标准或特殊的配置选项</li>
              <li>标注配置的作用和影响</li>
              <li>记录配置的修改历史和原因</li>
            </ul>
          </div>
        </section>

        <section id="k8s-structure">
          <h2><span class="step-number">3</span>K8s资源结构</h2>

          <h3><i class="fas fa-puzzle-piece"></i> 3.1 K8s YAML的基本结构</h3>
          <p>每个K8s资源的YAML文件都有四个必需的顶级字段：</p>

          <pre class="yaml-example"><code># K8s资源的标准结构
apiVersion: v1              # 1. API版本（必需）
kind: Pod                   # 2. 资源类型（必需）
metadata:                   # 3. 元数据（必需）
  name: example-pod
  namespace: default
spec:                       # 4. 规格说明（必需）
  containers:
  - name: app
    image: nginx</code></pre>

          <h3><i class="fas fa-info-circle"></i> 3.2 四大核心字段详解</h3>

          <h4><i class="fas fa-code-branch"></i> 3.2.1 apiVersion（API版本）</h4>
          <div class="info-box">
            <strong><i class="fas fa-info-circle"></i> 常用API版本：</strong>
            <table style="margin-top: 15px;">
              <tr>
                <th>资源类型</th>
                <th>API版本</th>
                <th>说明</th>
              </tr>
              <tr>
                <td>Pod, Service, ConfigMap</td>
                <td>v1</td>
                <td>稳定版本</td>
              </tr>
              <tr>
                <td>Deployment, ReplicaSet</td>
                <td>apps/v1</td>
                <td>应用相关资源</td>
              </tr>
              <tr>
                <td>Ingress</td>
                <td>networking.k8s.io/v1</td>
                <td>网络相关资源</td>
              </tr>
              <tr>
                <td>Job, CronJob</td>
                <td>batch/v1</td>
                <td>批处理相关资源</td>
              </tr>
            </table>
          </div>

          <pre class="yaml-example"><code># 查看可用的API版本
# kubectl api-versions

# 不同资源的API版本示例
---
apiVersion: v1                    # Pod使用v1
kind: Pod
metadata:
  name: my-pod
---
apiVersion: apps/v1               # Deployment使用apps/v1
kind: Deployment
metadata:
  name: my-deployment
---
apiVersion: networking.k8s.io/v1  # Ingress使用networking.k8s.io/v1
kind: Ingress
metadata:
  name: my-ingress</code></pre>

          <h4><i class="fas fa-tag"></i> 3.2.2 kind（资源类型）</h4>
          <div class="info-box">
            <strong><i class="fas fa-info-circle"></i> 常用资源类型：</strong>
            <ul>
              <li><strong>Pod：</strong>最小部署单元，包含一个或多个容器</li>
              <li><strong>Deployment：</strong>管理Pod的部署和更新</li>
              <li><strong>Service：</strong>为Pod提供网络访问</li>
              <li><strong>ConfigMap：</strong>存储配置数据</li>
              <li><strong>Secret：</strong>存储敏感数据</li>
              <li><strong>Ingress：</strong>管理外部访问</li>
            </ul>
          </div>

          <h4><i class="fas fa-info"></i> 3.2.3 metadata（元数据）</h4>
          <pre class="yaml-example"><code>metadata:
  name: my-application          # 资源名称（必需）
  namespace: production         # 命名空间（可选，默认default）
  labels:                       # 标签（可选但重要）
    app: web-server             # 应用名称
    version: v1.0               # 版本信息
    environment: production     # 环境标识
    tier: frontend              # 层级标识
  annotations:                  # 注解（可选）
    description: "Web服务器应用"
    maintainer: "<EMAIL>"
    last-updated: "2023-12-01"</code></pre>

          <div class="tip-box">
            <strong><i class="fas fa-lightbulb"></i> labels vs annotations：</strong>
            <ul>
              <li><strong>labels：</strong>用于选择和过滤资源，可以被selector使用</li>
              <li><strong>annotations：</strong>用于存储描述性信息，不能被selector使用</li>
            </ul>
          </div>
        </section>

        <section id="pod-yaml">
          <h2><span class="step-number">4</span>Pod配置详解</h2>

          <h3><i class="fas fa-cube"></i> 4.1 简单Pod示例</h3>
          <p>Pod是K8s中最小的部署单元，让我们从一个简单的例子开始：</p>

          <pre class="yaml-example"><code># 文件名：simple-pod.yaml
apiVersion: v1                    # Pod使用v1版本
kind: Pod                         # 资源类型是Pod
metadata:
  name: nginx-pod                 # Pod的名称
  labels:
    app: nginx                    # 给Pod打标签
spec:                            # Pod的规格说明
  containers:                    # 容器列表（Pod可以包含多个容器）
  - name: nginx-container         # 容器名称
    image: nginx:1.20            # 容器镜像
    ports:                       # 端口配置
    - containerPort: 80          # 容器内部端口</code></pre>

          <div class="success-box">
            <strong><i class="fas fa-check-circle"></i> 创建和测试：</strong>
            <pre style="background: transparent; border: none; padding: 0; margin: 10px 0;"><code># 创建Pod
kubectl apply -f simple-pod.yaml

# 查看Pod状态
kubectl get pods

# 查看Pod详细信息
kubectl describe pod nginx-pod

# 删除Pod
kubectl delete pod nginx-pod</code></pre>
          </div>

          <h3><i class="fas fa-cogs"></i> 4.2 完整Pod配置</h3>
          <p>下面是一个包含更多配置选项的完整Pod示例：</p>

          <pre class="yaml-example"><code># 文件名：complete-pod.yaml
apiVersion: v1
kind: Pod
metadata:
  name: web-app-pod
  namespace: default              # 命名空间
  labels:
    app: web-app                  # 应用标签
    version: v1.0                 # 版本标签
    environment: production       # 环境标签
  annotations:
    description: "生产环境的Web应用"
    maintainer: "<EMAIL>"
spec:
  # 容器配置
  containers:
  - name: web-server              # 主容器
    image: nginx:1.20             # 镜像版本
    imagePullPolicy: IfNotPresent # 镜像拉取策略

    # 端口配置
    ports:
    - name: http                  # 端口名称
      containerPort: 80           # 容器端口
      protocol: TCP               # 协议（默认TCP）

    # 环境变量
    env:
    - name: ENV                   # 环境变量名
      value: "production"         # 环境变量值
    - name: DEBUG
      value: "false"

    # 资源限制
    resources:
      requests:                   # 最小资源需求
        memory: "64Mi"            # 内存请求
        cpu: "250m"               # CPU请求（250毫核）
      limits:                     # 最大资源限制
        memory: "128Mi"           # 内存限制
        cpu: "500m"               # CPU限制

    # 健康检查
    livenessProbe:                # 存活探针
      httpGet:                    # HTTP检查
        path: /                   # 检查路径
        port: 80                  # 检查端口
      initialDelaySeconds: 30     # 初始延迟
      periodSeconds: 10           # 检查间隔

    readinessProbe:               # 就绪探针
      httpGet:
        path: /
        port: 80
      initialDelaySeconds: 5
      periodSeconds: 5

  # Pod级别配置
  restartPolicy: Always           # 重启策略
  dnsPolicy: ClusterFirst         # DNS策略

  # 节点选择
  nodeSelector:                   # 节点选择器
    disktype: ssd                 # 只在有SSD的节点上运行</code></pre>

          <h3><i class="fas fa-info-circle"></i> 4.3 重要配置项解释</h3>

          <h4><i class="fas fa-download"></i> 4.3.1 镜像拉取策略</h4>
          <div class="info-box">
            <strong><i class="fas fa-info-circle"></i> imagePullPolicy选项：</strong>
            <ul>
              <li><strong>Always：</strong>总是拉取最新镜像</li>
              <li><strong>IfNotPresent：</strong>本地没有时才拉取（推荐）</li>
              <li><strong>Never：</strong>从不拉取，只使用本地镜像</li>
            </ul>
          </div>

          <h4><i class="fas fa-memory"></i> 4.3.2 资源配置详解</h4>
          <pre class="yaml-example"><code>resources:
  requests:                       # 资源请求（保证分配）
    memory: "64Mi"                # 内存：64兆字节
    cpu: "250m"                   # CPU：250毫核（0.25核）
    ephemeral-storage: "1Gi"      # 临时存储：1GB
  limits:                         # 资源限制（不能超过）
    memory: "128Mi"               # 内存上限：128兆字节
    cpu: "500m"                   # CPU上限：500毫核（0.5核）
    ephemeral-storage: "2Gi"      # 临时存储上限：2GB</code></pre>

          <div class="warning-box">
            <strong><i class="fas fa-exclamation-triangle"></i> 资源配置注意事项：</strong>
            <ul>
              <li><strong>内存单位：</strong>Mi（兆字节）、Gi（吉字节）</li>
              <li><strong>CPU单位：</strong>m（毫核）、1000m = 1核</li>
              <li><strong>requests ≤ limits：</strong>请求不能大于限制</li>
              <li><strong>超过limits：</strong>内存超限会被杀死，CPU超限会被限流</li>
            </ul>
          </div>

          <h4><i class="fas fa-heartbeat"></i> 4.3.3 健康检查配置</h4>
          <pre class="yaml-example"><code># 存活探针（livenessProbe）- 检查容器是否还活着
livenessProbe:
  httpGet:                        # HTTP检查方式
    path: /health                 # 检查路径
    port: 8080                    # 检查端口
    httpHeaders:                  # 自定义HTTP头（可选）
    - name: Custom-Header
      value: Awesome
  initialDelaySeconds: 30         # 容器启动后多久开始检查
  periodSeconds: 10               # 检查间隔
  timeoutSeconds: 5               # 检查超时时间
  failureThreshold: 3             # 连续失败多少次认为不健康

# 就绪探针（readinessProbe）- 检查容器是否准备好接收流量
readinessProbe:
  tcpSocket:                      # TCP检查方式
    port: 8080
  initialDelaySeconds: 5
  periodSeconds: 5

# 启动探针（startupProbe）- 检查容器是否已经启动
startupProbe:
  exec:                           # 命令检查方式
    command:
    - cat
    - /tmp/healthy
  failureThreshold: 30
  periodSeconds: 10</code></pre>
        </section>

        <section id="deployment-yaml">
          <h2><span class="step-number">5</span>Deployment配置</h2>

          <h3><i class="fas fa-rocket"></i> 5.1 为什么使用Deployment</h3>
          <div class="info-box">
            <strong><i class="fas fa-info-circle"></i> Deployment的优势：</strong>
            <ul>
              <li><strong>副本管理：</strong>自动维护指定数量的Pod副本</li>
              <li><strong>滚动更新：</strong>无停机更新应用</li>
              <li><strong>回滚功能：</strong>快速回滚到之前的版本</li>
              <li><strong>自愈能力：</strong>Pod故障时自动重新创建</li>
            </ul>
          </div>

          <h3><i class="fas fa-file-code"></i> 5.2 基础Deployment示例</h3>
          <pre class="yaml-example"><code># 文件名：nginx-deployment.yaml
apiVersion: apps/v1               # Deployment使用apps/v1
kind: Deployment                  # 资源类型
metadata:
  name: nginx-deployment          # Deployment名称
  labels:
    app: nginx                    # Deployment标签
spec:
  replicas: 3                     # 副本数量（运行3个Pod）
  selector:                       # 选择器（重要！）
    matchLabels:
      app: nginx                  # 选择带有app=nginx标签的Pod
  template:                       # Pod模板
    metadata:
      labels:
        app: nginx                # Pod标签（必须与selector匹配）
    spec:                         # Pod规格（与Pod YAML相同）
      containers:
      - name: nginx
        image: nginx:1.20
        ports:
        - containerPort: 80</code></pre>

          <div class="warning-box">
            <strong><i class="fas fa-exclamation-triangle"></i> 重要提醒：</strong>
            <ul>
              <li><strong>selector.matchLabels</strong> 必须与 <strong>template.metadata.labels</strong> 匹配
              </li>
              <li>这样Deployment才能管理正确的Pod</li>
              <li>标签不匹配会导致Deployment无法工作</li>
            </ul>
          </div>

          <h3><i class="fas fa-cogs"></i> 5.3 完整Deployment配置</h3>
          <pre class="yaml-example"><code># 文件名：web-app-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: web-app-deployment
  namespace: production
  labels:
    app: web-app
    version: v1.0
  annotations:
    description: "Web应用的Deployment"
spec:
  # 副本配置
  replicas: 5                     # 运行5个Pod副本

  # 选择器配置
  selector:
    matchLabels:
      app: web-app                # 必须与template.labels匹配
      version: v1.0

  # 更新策略
  strategy:
    type: RollingUpdate           # 滚动更新策略
    rollingUpdate:
      maxUnavailable: 1           # 更新时最多1个Pod不可用
      maxSurge: 1                 # 更新时最多多创建1个Pod

  # Pod模板
  template:
    metadata:
      labels:
        app: web-app              # 与selector匹配
        version: v1.0
    spec:
      containers:
      - name: web-app
        image: nginx:1.20
        imagePullPolicy: IfNotPresent

        ports:
        - name: http
          containerPort: 80
          protocol: TCP

        # 环境变量
        env:
        - name: ENV
          value: "production"
        - name: LOG_LEVEL
          value: "info"

        # 资源配置
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"

        # 健康检查
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10

        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5

      # Pod级别配置
      restartPolicy: Always
      terminationGracePeriodSeconds: 30  # 优雅停止时间</code></pre>

          <h3><i class="fas fa-sync-alt"></i> 5.4 更新策略详解</h3>
          <div class="info-box">
            <strong><i class="fas fa-info-circle"></i> 更新策略类型：</strong>
            <ul>
              <li><strong>RollingUpdate：</strong>滚动更新（推荐，零停机）</li>
              <li><strong>Recreate：</strong>重新创建（会有停机时间）</li>
            </ul>
          </div>

          <pre class="yaml-example"><code># 滚动更新策略详细配置
strategy:
  type: RollingUpdate
  rollingUpdate:
    maxUnavailable: 25%           # 可以是数字或百分比
    maxSurge: 25%                 # 可以是数字或百分比

# 示例：如果replicas=4
# maxUnavailable: 1 表示最多1个Pod不可用（至少3个运行）
# maxSurge: 1 表示最多多创建1个Pod（最多5个Pod同时存在）</code></pre>
        </section>

        <section id="service-yaml">
          <h2><span class="step-number">6</span>Service配置</h2>

          <h3><i class="fas fa-network-wired"></i> 6.1 Service的作用</h3>
          <div class="info-box">
            <strong><i class="fas fa-info-circle"></i> 为什么需要Service：</strong>
            <ul>
              <li><strong>Pod IP不固定：</strong>Pod重启后IP会变化</li>
              <li><strong>负载均衡：</strong>将流量分发到多个Pod</li>
              <li><strong>服务发现：</strong>提供稳定的访问入口</li>
              <li><strong>端口映射：</strong>将服务端口映射到Pod端口</li>
            </ul>
          </div>

          <h3><i class="fas fa-file-code"></i> 6.2 基础Service示例</h3>
          <pre class="yaml-example"><code># 文件名：nginx-service.yaml
apiVersion: v1                    # Service使用v1版本
kind: Service                     # 资源类型
metadata:
  name: nginx-service             # Service名称
  labels:
    app: nginx                    # Service标签
spec:
  type: ClusterIP                 # Service类型（默认）
  selector:                       # 选择器（选择要代理的Pod）
    app: nginx                    # 选择带有app=nginx标签的Pod
  ports:                          # 端口配置
  - name: http                    # 端口名称
    port: 80                      # Service端口（外部访问）
    targetPort: 80                # Pod端口（容器端口）
    protocol: TCP                 # 协议</code></pre>

          <h3><i class="fas fa-list"></i> 6.3 Service类型详解</h3>
          <table>
            <tr>
              <th>类型</th>
              <th>访问范围</th>
              <th>使用场景</th>
            </tr>
            <tr>
              <td><strong>ClusterIP</strong></td>
              <td>集群内部</td>
              <td>内部服务通信</td>
            </tr>
            <tr>
              <td><strong>NodePort</strong></td>
              <td>集群外部</td>
              <td>测试环境外部访问</td>
            </tr>
            <tr>
              <td><strong>LoadBalancer</strong></td>
              <td>集群外部</td>
              <td>云环境生产部署</td>
            </tr>
            <tr>
              <td><strong>ExternalName</strong></td>
              <td>外部服务</td>
              <td>访问外部服务</td>
            </tr>
          </table>

          <h3><i class="fas fa-cogs"></i> 6.4 不同类型Service示例</h3>

          <h4><i class="fas fa-home"></i> 6.4.1 ClusterIP Service</h4>
          <pre class="yaml-example"><code># 集群内部访问（默认类型）
apiVersion: v1
kind: Service
metadata:
  name: web-app-service
spec:
  type: ClusterIP                 # 可以省略，这是默认值
  selector:
    app: web-app
  ports:
  - name: http
    port: 80                      # Service端口
    targetPort: 8080              # Pod端口（可以不同）</code></pre>

          <h4><i class="fas fa-external-link-alt"></i> 6.4.2 NodePort Service</h4>
          <pre class="yaml-example"><code># 通过节点端口对外提供服务
apiVersion: v1
kind: Service
metadata:
  name: web-app-nodeport
spec:
  type: NodePort
  selector:
    app: web-app
  ports:
  - name: http
    port: 80                      # Service端口
    targetPort: 8080              # Pod端口
    nodePort: 30080               # 节点端口（30000-32767）</code></pre>

          <div class="tip-box">
            <strong><i class="fas fa-lightbulb"></i> NodePort访问方式：</strong>
            <ul>
              <li><strong>集群内：</strong>http://web-app-nodeport:80</li>
              <li><strong>集群外：</strong>http://节点IP:30080</li>
            </ul>
          </div>
        </section>

        <section id="configmap-secret">
          <h2><span class="step-number">7</span>ConfigMap和Secret</h2>

          <h3><i class="fas fa-key"></i> 7.1 ConfigMap配置</h3>
          <div class="info-box">
            <strong><i class="fas fa-info-circle"></i> ConfigMap的作用：</strong>
            <ul>
              <li><strong>配置分离：</strong>将配置数据与应用代码分离</li>
              <li><strong>环境变量：</strong>为容器提供环境变量</li>
              <li><strong>配置文件：</strong>挂载配置文件到容器</li>
              <li><strong>命令参数：</strong>为容器提供启动参数</li>
            </ul>
          </div>

          <h4>基本ConfigMap示例：</h4>
          <pre class="yaml-example"><code># 文件名：app-configmap.yaml
apiVersion: v1                    # ConfigMap使用v1版本
kind: ConfigMap                   # 资源类型
metadata:
  name: app-config               # ConfigMap名称
  namespace: default             # 命名空间（可选，默认default）
data:                           # 配置数据（键值对形式）
  # 简单的键值对配置
  database_host: "mysql.example.com"    # 数据库主机地址
  database_port: "3306"                 # 数据库端口
  log_level: "info"                     # 日志级别
  max_connections: "100"                # 最大连接数
  timeout: "30"                         # 超时时间（秒）

  # 完整的配置文件内容（使用 | 保持换行格式）
  app.properties: |
    # 应用服务器配置
    server.port=8080
    server.host=0.0.0.0

    # 数据库连接配置
    database.url=****************************************
    database.username=user
    database.password=password
    database.pool.min=5
    database.pool.max=20

    # 日志配置
    logging.level=info
    logging.file=/var/log/app.log

  # Nginx配置文件
  nginx.conf: |
    # Nginx反向代理配置
    server {
        listen 80;                          # 监听端口
        server_name localhost;              # 服务器名称

        # 健康检查端点
        location /health {
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # 主要应用代理
        location / {
            proxy_pass http://backend:8080;  # 后端服务地址
            proxy_set_header Host $host;     # 传递原始Host头
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
    }</code></pre>

          <h4>在Pod中使用ConfigMap：</h4>
          <pre class="yaml-example"><code># 文件名：configmap-pod.yaml
apiVersion: v1                    # Pod使用v1版本
kind: Pod                         # 资源类型
metadata:
  name: app-pod                   # Pod名称
  labels:
    app: configmap-demo           # Pod标签
spec:
  containers:
  - name: app                     # 容器名称
    image: nginx:1.20            # 容器镜像

    # 方式1：将ConfigMap的值作为环境变量注入
    env:
    - name: DATABASE_HOST         # 环境变量名
      valueFrom:
        configMapKeyRef:
          name: app-config        # 引用的ConfigMap名称
          key: database_host      # ConfigMap中的键名
    - name: DATABASE_PORT
      valueFrom:
        configMapKeyRef:
          name: app-config
          key: database_port
    - name: LOG_LEVEL
      valueFrom:
        configMapKeyRef:
          name: app-config
          key: log_level

    # 方式2：将ConfigMap挂载为文件
    volumeMounts:
    - name: config-volume         # 卷名称
      mountPath: /etc/config      # 挂载路径
      readOnly: true              # 只读挂载
    - name: nginx-config          # 另一个卷
      mountPath: /etc/nginx/conf.d # Nginx配置目录
      readOnly: true

    # 容器端口配置
    ports:
    - containerPort: 80
      name: http

  # 定义卷（将ConfigMap映射为卷）
  volumes:
  - name: config-volume           # 卷名称
    configMap:
      name: app-config            # 引用的ConfigMap
      items:                      # 选择特定的键
      - key: app.properties       # ConfigMap中的键
        path: application.properties  # 挂载后的文件名
        mode: 0644                # 文件权限
  - name: nginx-config
    configMap:
      name: app-config
      items:
      - key: nginx.conf
        path: default.conf        # 挂载为 /etc/nginx/conf.d/default.conf
        mode: 0644</code></pre>

          <h3><i class="fas fa-lock"></i> 7.2 Secret配置</h3>
          <div class="warning-box">
            <strong><i class="fas fa-exclamation-triangle"></i> Secret安全注意：</strong>
            <ul>
              <li><strong>Base64编码：</strong>Secret数据会被Base64编码，但不是加密</li>
              <li><strong>访问控制：</strong>使用RBAC控制Secret的访问权限</li>
              <li><strong>传输安全：</strong>确保集群内通信使用TLS</li>
              <li><strong>存储安全：</strong>考虑使用etcd加密</li>
            </ul>
          </div>

          <h4>创建Secret的几种方式：</h4>
          <pre class="yaml-example"><code># 文件名：secrets.yaml
# 方式1：直接在YAML中定义（需要手动Base64编码）
apiVersion: v1                    # Secret使用v1版本
kind: Secret                      # 资源类型
metadata:
  name: mysecret                  # Secret名称
  namespace: default              # 命名空间
  labels:
    app: my-app                   # 标签
type: Opaque                      # Secret类型（通用类型）
data:                            # Base64编码的数据
  username: YWRtaW4=             # "admin"的Base64编码
  password: MWYyZDFlMmU2N2Rm     # "1f2d1e2e67df"的Base64编码
  api-key: bXktc2VjcmV0LWFwaS1rZXk=  # "my-secret-api-key"的Base64编码

---
# 方式2：使用stringData（推荐，自动Base64编码）
apiVersion: v1
kind: Secret
metadata:
  name: mysecret-string           # 不同的Secret名称
  namespace: default
  labels:
    app: my-app
type: Opaque
stringData:                      # 明文数据，K8s会自动编码
  username: admin                # 用户名（明文）
  password: 1f2d1e2e67df         # 密码（明文）
  database-url: "mysql://user:pass@db:3306/mydb"  # 数据库连接字符串
  api-key: my-secret-api-key     # API密钥（明文）

---
# 方式3：TLS证书Secret
apiVersion: v1
kind: Secret
metadata:
  name: tls-secret
  namespace: default
type: kubernetes.io/tls          # TLS证书类型
data:
  tls.crt: LS0tLS1CRUdJTi...     # Base64编码的证书文件
  tls.key: LS0tLS1CRUdJTi...     # Base64编码的私钥文件

---
# 方式4：Docker镜像拉取Secret
apiVersion: v1
kind: Secret
metadata:
  name: regcred                   # 镜像仓库凭证
  namespace: default
type: kubernetes.io/dockerconfigjson  # Docker配置类型
data:
  .dockerconfigjson: ****************************************************************************************************************************************************************************************

# 注意：上面的.dockerconfigjson是以下JSON的Base64编码：
# {
#   "auths": {
#     "https://index.docker.io/v1/": {
#       "username": "myuser",
#       "password": "mypass",
#       "email": "<EMAIL>",
#       "auth": "bXl1c2VyOm15cGFzcw=="
#     }
#   }
# }</code></pre>

          <h4>在Pod中使用Secret：</h4>
          <pre class="yaml-example"><code># 文件名：secret-pod.yaml
apiVersion: v1                    # Pod使用v1版本
kind: Pod                         # 资源类型
metadata:
  name: secret-pod                # Pod名称
  labels:
    app: secret-demo              # Pod标签
spec:
  containers:
  - name: app                     # 容器名称
    image: nginx:1.20            # 容器镜像

    # 方式1：将Secret的值作为环境变量注入
    env:
    - name: SECRET_USERNAME       # 环境变量名
      valueFrom:
        secretKeyRef:
          name: mysecret          # 引用的Secret名称
          key: username           # Secret中的键名
    - name: SECRET_PASSWORD
      valueFrom:
        secretKeyRef:
          name: mysecret
          key: password
    - name: API_KEY               # API密钥环境变量
      valueFrom:
        secretKeyRef:
          name: mysecret
          key: api-key

    # 方式2：将Secret挂载为文件
    volumeMounts:
    - name: secret-volume         # 卷名称
      mountPath: /etc/secrets     # 挂载路径
      readOnly: true              # 只读挂载（安全考虑）
    - name: tls-certs             # TLS证书卷
      mountPath: /etc/ssl/certs
      readOnly: true

    # 容器端口配置
    ports:
    - containerPort: 80
      name: http
    - containerPort: 443
      name: https

  # 定义卷（将Secret映射为卷）
  volumes:
  - name: secret-volume           # 卷名称
    secret:
      secretName: mysecret        # 引用的Secret名称
      defaultMode: 0400           # 文件权限（只读）
      items:                      # 选择特定的键（可选）
      - key: username
        path: db-username         # 挂载后的文件名
      - key: password
        path: db-password
  - name: tls-certs
    secret:
      secretName: tls-secret      # TLS证书Secret
      defaultMode: 0400

  # 使用镜像拉取Secret（用于私有镜像仓库）
  imagePullSecrets:
  - name: regcred                 # Docker镜像拉取凭证</code></pre>

          <div class="success-box">
            <strong><i class="fas fa-check-circle"></i> 最佳实践：</strong>
            <ul>
              <li><strong>最小权限：</strong>只给需要的Pod访问Secret的权限</li>
              <li><strong>定期轮换：</strong>定期更新Secret中的敏感信息</li>
              <li><strong>避免日志：</strong>确保Secret不会被记录到日志中</li>
              <li><strong>使用工具：</strong>考虑使用Vault等专业密钥管理工具</li>
            </ul>
          </div>
        </section>

        <section id="volume-storage">
          <h2><span class="step-number">8</span>存储配置</h2>

          <h3><i class="fas fa-hdd"></i> 8.1 存储类型概述</h3>
          <div class="info-box">
            <strong><i class="fas fa-info-circle"></i> K8s存储类型：</strong>
            <ul>
              <li><strong>emptyDir：</strong>临时存储，Pod删除时数据丢失</li>
              <li><strong>hostPath：</strong>挂载主机目录，数据持久化</li>
              <li><strong>PV/PVC：</strong>持久化存储，生产环境推荐</li>
              <li><strong>ConfigMap/Secret：</strong>配置和密钥存储</li>
            </ul>
          </div>

          <h4>emptyDir示例：</h4>
          <pre class="yaml-example"><code># 文件名：emptydir-pod.yaml
apiVersion: v1                    # Pod使用v1版本
kind: Pod                         # 资源类型
metadata:
  name: emptydir-pod              # Pod名称
  labels:
    app: emptydir-demo            # Pod标签
spec:
  containers:
  # 主应用容器
  - name: app                     # 容器名称
    image: nginx:1.20            # 容器镜像
    volumeMounts:
    - name: cache-volume          # 卷名称
      mountPath: /tmp/cache       # 挂载路径（用于缓存）
    - name: shared-volume         # 共享卷
      mountPath: /var/log/shared  # 挂载路径（用于日志共享）
    ports:
    - containerPort: 80
      name: http

  # 边车容器（用于日志收集）
  - name: sidecar                 # 边车容器名称
    image: busybox               # 轻量级镜像
    # 持续写入日志到共享目录
    command: ['sh', '-c', 'while true; do echo "$(date): Application log entry" >> /tmp/shared/log.txt; sleep 30; done']
    volumeMounts:
    - name: shared-volume         # 与主容器共享同一个卷
      mountPath: /tmp/shared      # 挂载路径

  # 定义卷
  volumes:
  - name: cache-volume            # 缓存卷
    emptyDir: {}                  # 空目录，默认使用节点存储
  - name: shared-volume           # 共享卷
    emptyDir:
      sizeLimit: 1Gi              # 限制卷大小为1GB
      medium: Memory              # 可选：使用内存作为存储介质（tmpfs）</code></pre>

          <h4>hostPath示例：</h4>
          <pre class="yaml-example"><code># 文件名：hostpath-pod.yaml
apiVersion: v1                    # Pod使用v1版本
kind: Pod                         # 资源类型
metadata:
  name: hostpath-pod              # Pod名称
  labels:
    app: hostpath-demo            # Pod标签
spec:
  containers:
  - name: app                     # 容器名称
    image: nginx:1.20            # 容器镜像
    volumeMounts:
    - name: host-volume           # 卷名称
      mountPath: /usr/share/nginx/html  # 容器内挂载路径
    - name: log-volume            # 日志卷
      mountPath: /var/log/nginx   # Nginx日志目录
    - name: config-volume         # 配置卷
      mountPath: /etc/nginx/conf.d
      readOnly: true              # 只读挂载
    ports:
    - containerPort: 80
      name: http

  # 定义卷
  volumes:
  - name: host-volume             # 网站内容卷
    hostPath:
      path: /data/nginx/html      # 主机上的路径
      type: DirectoryOrCreate     # 如果目录不存在则创建
  - name: log-volume              # 日志卷
    hostPath:
      path: /var/log/containers/nginx  # 主机日志路径
      type: DirectoryOrCreate
  - name: config-volume           # 配置卷
    hostPath:
      path: /etc/nginx-config     # 主机配置路径
      type: Directory             # 目录必须存在

# hostPath类型说明：
# - DirectoryOrCreate: 目录不存在时创建，权限0755
# - Directory: 目录必须存在
# - FileOrCreate: 文件不存在时创建
# - File: 文件必须存在
# - Socket: Unix socket必须存在
# - CharDevice: 字符设备必须存在
# - BlockDevice: 块设备必须存在</code></pre>

          <h3><i class="fas fa-database"></i> 8.2 PersistentVolume和PersistentVolumeClaim</h3>

          <h4>PersistentVolume (PV)：</h4>
          <pre class="yaml-example"><code># 文件名：persistent-volume.yaml
apiVersion: v1                    # PV使用v1版本
kind: PersistentVolume            # 资源类型
metadata:
  name: pv-example                # PV名称
  labels:
    type: local                   # PV标签
    environment: production       # 环境标签
spec:
  capacity:
    storage: 10Gi                 # 存储容量
  accessModes:                    # 访问模式
    - ReadWriteOnce               # RWO：单节点读写
    # - ReadOnlyMany              # ROX：多节点只读
    # - ReadWriteMany             # RWX：多节点读写
  persistentVolumeReclaimPolicy: Retain  # 回收策略
  # Retain: 保留数据，需要手动清理
  # Recycle: 自动删除数据（已废弃）
  # Delete: 自动删除PV和底层存储
  storageClassName: manual        # 存储类名称
  volumeMode: Filesystem          # 卷模式（Filesystem或Block）
  hostPath:                       # 存储后端（这里使用hostPath）
    path: /data/pv-example        # 主机路径
    type: DirectoryOrCreate       # 路径类型</code></pre>

          <h4>PersistentVolumeClaim (PVC)：</h4>
          <pre class="yaml-example"><code># 文件名：persistent-volume-claim.yaml
apiVersion: v1                    # PVC使用v1版本
kind: PersistentVolumeClaim       # 资源类型
metadata:
  name: pvc-example               # PVC名称
  namespace: default              # 命名空间
  labels:
    app: web-app                  # 应用标签
spec:
  accessModes:                    # 访问模式（必须与PV匹配）
    - ReadWriteOnce               # 单节点读写
  resources:                      # 资源请求
    requests:
      storage: 5Gi                # 请求的存储大小（不能超过PV容量）
  storageClassName: manual        # 存储类名称（必须与PV匹配）
  volumeMode: Filesystem          # 卷模式
  selector:                       # 选择器（可选）
    matchLabels:
      type: local                 # 选择带有type=local标签的PV</code></pre>

          <h4>在Pod中使用PVC：</h4>
          <pre class="yaml-example"><code># 文件名：pvc-pod.yaml
apiVersion: v1                    # Pod使用v1版本
kind: Pod                         # 资源类型
metadata:
  name: pvc-pod                   # Pod名称
  labels:
    app: web-app                  # Pod标签
spec:
  containers:
  - name: app                     # 容器名称
    image: nginx:1.20            # 容器镜像
    volumeMounts:
    - name: storage               # 卷名称
      mountPath: /usr/share/nginx/html  # 挂载路径
    - name: logs                  # 日志卷
      mountPath: /var/log/nginx
    ports:
    - containerPort: 80
      name: http

    # 资源限制
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"
      limits:
        memory: "128Mi"
        cpu: "500m"

  # 定义卷
  volumes:
  - name: storage                 # 卷名称
    persistentVolumeClaim:
      claimName: pvc-example      # 引用的PVC名称
  - name: logs                    # 日志卷（使用emptyDir）
    emptyDir: {}</code></pre>

          <div class="tip-box">
            <strong><i class="fas fa-lightbulb"></i> 存储选择建议：</strong>
            <ul>
              <li><strong>开发环境：</strong>emptyDir或hostPath即可</li>
              <li><strong>生产环境：</strong>使用PV/PVC确保数据安全</li>
              <li><strong>配置数据：</strong>使用ConfigMap和Secret</li>
              <li><strong>临时数据：</strong>使用emptyDir节省资源</li>
            </ul>
          </div>
        </section>

        <section id="ingress-yaml">
          <h2><span class="step-number">9</span>Ingress配置</h2>

          <h3><i class="fas fa-globe"></i> 9.1 Ingress的作用</h3>
          <div class="info-box">
            <strong><i class="fas fa-info-circle"></i> 为什么需要Ingress：</strong>
            <ul>
              <li><strong>统一入口：</strong>为集群提供统一的HTTP/HTTPS入口</li>
              <li><strong>域名路由：</strong>根据域名将请求路由到不同服务</li>
              <li><strong>路径路由：</strong>根据URL路径将请求路由到不同服务</li>
              <li><strong>SSL终止：</strong>在入口处处理SSL/TLS加密</li>
              <li><strong>负载均衡：</strong>在多个后端服务间分发流量</li>
            </ul>
          </div>

          <h4>基本Ingress示例：</h4>
          <pre class="yaml-example"><code># 文件名：basic-ingress.yaml
apiVersion: networking.k8s.io/v1  # Ingress使用networking.k8s.io/v1版本
kind: Ingress                     # 资源类型
metadata:
  name: web-ingress               # Ingress名称
  namespace: default              # 命名空间
  labels:
    app: web-app                  # 标签
  annotations:                    # 注解（用于配置Ingress Controller）
    nginx.ingress.kubernetes.io/rewrite-target: /  # URL重写规则
    nginx.ingress.kubernetes.io/ssl-redirect: "false"  # 禁用SSL重定向
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"  # 请求体大小限制
spec:
  # 可选：默认后端（当没有规则匹配时使用）
  defaultBackend:
    service:
      name: default-service
      port:
        number: 80

  # 路由规则
  rules:
  # 规则1：主网站
  - host: example.com             # 域名
    http:
      paths:
      - path: /                   # 路径
        pathType: Prefix          # 路径类型：Prefix（前缀匹配）
        backend:                  # 后端服务
          service:
            name: web-service     # Service名称
            port:
              number: 80          # Service端口

  # 规则2：API服务
  - host: api.example.com         # API域名
    http:
      paths:
      - path: /                   # 根路径
        pathType: Prefix
        backend:
          service:
            name: api-service     # API服务
            port:
              number: 8080        # API端口</code></pre>

          <h4>路径路由示例：</h4>
          <pre class="yaml-example"><code># 文件名：path-routing-ingress.yaml
apiVersion: networking.k8s.io/v1  # Ingress使用networking.k8s.io/v1版本
kind: Ingress                     # 资源类型
metadata:
  name: path-ingress              # Ingress名称
  namespace: default              # 命名空间
  labels:
    app: multi-service            # 标签
  annotations:                    # 注解配置
    nginx.ingress.kubernetes.io/rewrite-target: /$2  # URL重写：$2是第二个捕获组
    nginx.ingress.kubernetes.io/use-regex: "true"    # 启用正则表达式
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "600"  # 连接超时
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"     # 发送超时
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"     # 读取超时
spec:
  rules:
  - host: myapp.com               # 域名
    http:
      paths:
      # 路径1：Web前端服务
      - path: /web(/|$)(.*)       # 正则路径：/web/xxx -> /xxx
        pathType: Prefix          # 路径类型
        backend:
          service:
            name: web-service     # 前端服务
            port:
              number: 80          # HTTP端口

      # 路径2：API服务
      - path: /api(/|$)(.*)       # 正则路径：/api/xxx -> /xxx
        pathType: Prefix
        backend:
          service:
            name: api-service     # API服务
            port:
              number: 8080        # API端口

      # 路径3：管理后台
      - path: /admin(/|$)(.*)     # 正则路径：/admin/xxx -> /xxx
        pathType: Prefix
        backend:
          service:
            name: admin-service   # 管理服务
            port:
              number: 3000        # 管理端口

      # 路径4：静态资源（精确匹配）
      - path: /static
        pathType: Exact           # 精确匹配
        backend:
          service:
            name: static-service  # 静态资源服务
            port:
              number: 80

# 路径类型说明：
# - Exact: 精确匹配URL路径
# - Prefix: 前缀匹配
# - ImplementationSpecific: 由Ingress Controller决定匹配方式</code></pre>

          <h3><i class="fas fa-lock"></i> 9.2 HTTPS配置</h3>

          <h4>创建TLS Secret：</h4>
          <pre class="yaml-example"><code># 文件名：tls-secret.yaml
apiVersion: v1                    # Secret使用v1版本
kind: Secret                      # 资源类型
metadata:
  name: tls-secret                # Secret名称
  namespace: default              # 命名空间
  labels:
    app: secure-app               # 标签
type: kubernetes.io/tls           # TLS类型的Secret
data:
  # 注意：以下是示例，实际使用时需要替换为真实的证书内容
  tls.crt: LS0tLS1CRUdJTi...     # Base64编码的SSL证书
  tls.key: LS0tLS1CRUdJTi...     # Base64编码的私钥

# 创建TLS Secret的命令行方式：
# kubectl create secret tls tls-secret \
#   --cert=path/to/tls.crt \
#   --key=path/to/tls.key</code></pre>

          <h4>HTTPS Ingress示例：</h4>
          <pre class="yaml-example"><code># 文件名：https-ingress.yaml
apiVersion: networking.k8s.io/v1  # Ingress使用networking.k8s.io/v1版本
kind: Ingress                     # 资源类型
metadata:
  name: https-ingress             # Ingress名称
  namespace: default              # 命名空间
  labels:
    app: secure-app               # 标签
  annotations:                    # HTTPS相关配置
    nginx.ingress.kubernetes.io/ssl-redirect: "true"        # 强制SSL重定向
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"  # 强制HTTPS
    nginx.ingress.kubernetes.io/ssl-protocols: "TLSv1.2 TLSv1.3"  # TLS版本
    nginx.ingress.kubernetes.io/ssl-ciphers: "ECDHE-RSA-AES128-GCM-SHA256,ECDHE-RSA-AES256-GCM-SHA384"  # 加密套件
    nginx.ingress.kubernetes.io/hsts: "true"                # 启用HSTS
    nginx.ingress.kubernetes.io/hsts-max-age: "31536000"    # HSTS最大年龄
    nginx.ingress.kubernetes.io/hsts-include-subdomains: "true"  # HSTS包含子域名
spec:
  # TLS配置
  tls:
  - hosts:                        # 证书覆盖的域名列表
    - secure.example.com          # 主域名
    - www.secure.example.com      # www子域名
    secretName: tls-secret        # 引用的TLS Secret

  # 路由规则
  rules:
  - host: secure.example.com      # 域名
    http:
      paths:
      - path: /                   # 根路径
        pathType: Prefix          # 路径类型
        backend:
          service:
            name: secure-service  # 后端服务
            port:
              number: 80          # 服务端口

  # 支持www子域名
  - host: www.secure.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: secure-service
            port:
              number: 80</code></pre>

          <div class="warning-box">
            <strong><i class="fas fa-exclamation-triangle"></i> Ingress注意事项：</strong>
            <ul>
              <li><strong>Ingress Controller：</strong>需要先安装Ingress Controller（如nginx-ingress）</li>
              <li><strong>DNS配置：</strong>确保域名正确解析到集群入口</li>
              <li><strong>证书管理：</strong>考虑使用cert-manager自动管理SSL证书</li>
              <li><strong>路径匹配：</strong>注意pathType的选择（Exact、Prefix、ImplementationSpecific）</li>
            </ul>
          </div>
        </section>

        <section id="advanced-features">
          <h2><span class="step-number">10</span>高级特性</h2>

          <h3><i class="fas fa-cogs"></i> 10.1 资源限制和请求</h3>
          <div class="info-box">
            <strong><i class="fas fa-info-circle"></i> 资源管理的重要性：</strong>
            <ul>
              <li><strong>requests：</strong>容器启动时保证的最小资源</li>
              <li><strong>limits：</strong>容器能使用的最大资源</li>
              <li><strong>QoS：</strong>影响Pod的服务质量等级</li>
              <li><strong>调度：</strong>影响Pod的调度决策</li>
            </ul>
          </div>

          <h4>资源限制示例：</h4>
          <pre class="yaml-example"><code>apiVersion: v1
kind: Pod
metadata:
  name: resource-pod
spec:
  containers:
  - name: app
    image: nginx:1.20
    resources:
      requests:
        memory: "64Mi"
        cpu: "250m"      # 0.25 CPU核心
      limits:
        memory: "128Mi"
        cpu: "500m"      # 0.5 CPU核心
    # 临时存储限制
    ephemeral-storage:
      requests: "1Gi"
      limits: "2Gi"</code></pre>

          <h3><i class="fas fa-heartbeat"></i> 10.2 健康检查进阶</h3>

          <h4>完整健康检查配置：</h4>
          <pre class="yaml-example"><code>apiVersion: v1
kind: Pod
metadata:
  name: health-check-pod
spec:
  containers:
  - name: app
    image: nginx:1.20
    ports:
    - containerPort: 80

    # 启动探针 - 容器启动时检查
    startupProbe:
      httpGet:
        path: /health
        port: 80
      initialDelaySeconds: 10
      periodSeconds: 5
      timeoutSeconds: 3
      failureThreshold: 30  # 启动探针失败次数较多

    # 存活探针 - 检查容器是否还活着
    livenessProbe:
      httpGet:
        path: /health
        port: 80
      initialDelaySeconds: 30
      periodSeconds: 10
      timeoutSeconds: 5
      failureThreshold: 3

    # 就绪探针 - 检查容器是否准备好接收流量
    readinessProbe:
      httpGet:
        path: /ready
        port: 80
      initialDelaySeconds: 5
      periodSeconds: 5
      timeoutSeconds: 3
      failureThreshold: 3</code></pre>

          <h3><i class="fas fa-tags"></i> 10.3 节点选择和亲和性</h3>

          <h4>节点选择器：</h4>
          <pre class="yaml-example"><code>apiVersion: v1
kind: Pod
metadata:
  name: node-selector-pod
spec:
  nodeSelector:
    disktype: ssd
    zone: us-west-1
  containers:
  - name: app
    image: nginx:1.20</code></pre>

          <h4>节点亲和性：</h4>
          <pre class="yaml-example"><code>apiVersion: v1
kind: Pod
metadata:
  name: node-affinity-pod
spec:
  affinity:
    nodeAffinity:
      # 必须满足的条件
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: disktype
            operator: In
            values:
            - ssd
      # 优先满足的条件
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        preference:
          matchExpressions:
          - key: zone
            operator: In
            values:
            - us-west-1
  containers:
  - name: app
    image: nginx:1.20</code></pre>

          <h4>Pod亲和性和反亲和性：</h4>
          <pre class="yaml-example"><code>apiVersion: v1
kind: Pod
metadata:
  name: pod-affinity-pod
  labels:
    app: web
spec:
  affinity:
    # Pod亲和性 - 希望与某些Pod在同一节点
    podAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
      - labelSelector:
          matchExpressions:
          - key: app
            operator: In
            values:
            - database
        topologyKey: kubernetes.io/hostname

    # Pod反亲和性 - 希望与某些Pod不在同一节点
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
            - key: app
              operator: In
              values:
              - web
          topologyKey: kubernetes.io/hostname
  containers:
  - name: app
    image: nginx:1.20</code></pre>

          <h3><i class="fas fa-shield-alt"></i> 10.4 安全上下文</h3>

          <h4>安全配置示例：</h4>
          <pre class="yaml-example"><code>apiVersion: v1
kind: Pod
metadata:
  name: security-pod
spec:
  securityContext:
    runAsUser: 1000
    runAsGroup: 3000
    fsGroup: 2000
    seccompProfile:
      type: RuntimeDefault
  containers:
  - name: app
    image: nginx:1.20
    securityContext:
      allowPrivilegeEscalation: false
      readOnlyRootFilesystem: true
      runAsNonRoot: true
      capabilities:
        drop:
        - ALL
        add:
        - NET_BIND_SERVICE
    volumeMounts:
    - name: tmp-volume
      mountPath: /tmp
    - name: var-cache
      mountPath: /var/cache/nginx
  volumes:
  - name: tmp-volume
    emptyDir: {}
  - name: var-cache
    emptyDir: {}</code></pre>

          <div class="success-box">
            <strong><i class="fas fa-check-circle"></i> 高级特性最佳实践：</strong>
            <ul>
              <li><strong>资源限制：</strong>总是设置requests和limits</li>
              <li><strong>健康检查：</strong>根据应用特点配置合适的探针</li>
              <li><strong>节点调度：</strong>合理使用亲和性提高可用性</li>
              <li><strong>安全加固：</strong>遵循最小权限原则</li>
            </ul>
          </div>
        </section>

        <section id="best-practices">
          <h2><span class="step-number">11</span>最佳实践</h2>

          <h3><i class="fas fa-star"></i> 11.1 YAML编写规范</h3>
          <div class="success-box">
            <strong><i class="fas fa-check-circle"></i> 推荐做法：</strong>
            <ul>
              <li><strong>统一缩进：</strong>使用2个空格，不要使用Tab</li>
              <li><strong>添加注释：</strong>解释复杂配置的用途</li>
              <li><strong>使用标签：</strong>合理使用labels进行资源分类</li>
              <li><strong>资源限制：</strong>总是设置resources.requests和limits</li>
              <li><strong>健康检查：</strong>配置适当的探针</li>
              <li><strong>命名规范：</strong>使用有意义的名称</li>
            </ul>
          </div>

          <h3><i class="fas fa-tools"></i> 11.2 常用验证命令</h3>
          <pre class="yaml-example"><code># 验证YAML语法（不实际创建）
kubectl apply -f my-app.yaml --dry-run=client -o yaml

# 验证并显示将要创建的资源
kubectl apply -f my-app.yaml --dry-run=server -o yaml

# 查看资源详细信息
kubectl describe pod my-pod
kubectl describe deployment my-deployment

# 查看资源的YAML定义
kubectl get pod my-pod -o yaml
kubectl get deployment my-deployment -o yaml</code></pre>
        </section>

        <section id="troubleshooting">
          <h2><span class="step-number">12</span>常见错误</h2>

          <h3><i class="fas fa-bug"></i> 12.1 语法错误</h3>
          <div class="danger-box">
            <strong><i class="fas fa-times-circle"></i> 常见错误：</strong>
            <ul>
              <li><strong>缩进错误：</strong>混用空格和Tab，缩进不一致</li>
              <li><strong>冒号后缺少空格：</strong>name:value 应该是 name: value</li>
              <li><strong>列表格式错误：</strong>- 后面缺少空格</li>
              <li><strong>引号不匹配：</strong>字符串引号没有正确闭合</li>
            </ul>
          </div>

          <h3><i class="fas fa-exclamation-triangle"></i> 12.2 配置错误</h3>
          <div class="warning-box">
            <strong><i class="fas fa-exclamation-triangle"></i> 常见配置问题：</strong>
            <ul>
              <li><strong>标签不匹配：</strong>Deployment的selector与Pod标签不匹配</li>
              <li><strong>端口配置错误：</strong>Service的targetPort与容器端口不匹配</li>
              <li><strong>资源名称冲突：</strong>同一命名空间下资源名称重复</li>
              <li><strong>镜像拉取失败：</strong>镜像名称错误或网络问题</li>
            </ul>
          </div>

          <div class="success-box">
            <strong><i class="fas fa-graduation-cap"></i> 恭喜！</strong>您已经学会了K8s
            YAML的基础编写方法。继续实践和学习，您将能够熟练配置各种K8s资源。记住：多练习、多查文档、多实验！
          </div>
        </section>
      </div>
    </div>
  </div>

  <!-- 返回顶部按钮 -->
  <a href="#" class="back-to-top" id="backToTop">
    <i class="fas fa-arrow-up"></i>
  </a>

  <script>
    // 移动端菜单切换
    document.getElementById('mobileMenuBtn').addEventListener('click', function () {
      document.getElementById('sidebar').classList.toggle('active');
    });

    // 返回顶部功能
    window.addEventListener('scroll', function () {
      const backToTop = document.getElementById('backToTop');
      if (window.pageYOffset > 300) {
        backToTop.style.display = 'flex';
      } else {
        backToTop.style.display = 'none';
      }
    });

    document.getElementById('backToTop').addEventListener('click', function (e) {
      e.preventDefault();
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });

    // 侧边栏导航高亮
    window.addEventListener('scroll', function () {
      const sections = document.querySelectorAll('section[id]');
      const navLinks = document.querySelectorAll('.sidebar a[href^="#"]');

      let current = '';
      sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (pageYOffset >= sectionTop - 200) {
          current = section.getAttribute('id');
        }
      });

      navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === '#' + current) {
          link.classList.add('active');
        }
      });
    });

    // 平滑滚动
    document.querySelectorAll('.sidebar a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }

        // 移动端关闭菜单
        if (window.innerWidth <= 768) {
          document.getElementById('sidebar').classList.remove('active');
        }
      });
    });
  </script>
</body>

</html>