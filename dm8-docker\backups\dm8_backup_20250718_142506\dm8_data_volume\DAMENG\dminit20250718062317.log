start init database: V8, 2025-07-18 06:23:17
init params:
	db path: /opt/dmdbms/data/DAMENG
	db name: DAMENG
	auto overwrite: 1
	page size: 32768
	extent size: 16
	char_fix_storage: 0
	sql_log_forbid: 0
	secur_flag: 2
	enable mac: 0
	time zone: +08:00
	string case sensitive: 0
	charset: 0
	page check mode: 3
	page check algorithm id: 0
	priv flag: 0
	env label: 0
	rlog enc flag: 0
	use new hash: 1
	blank pad mode: 0
	sec priv mode: 0
	huge with delta: 1
	rlog gen for huge: 1
	pseg_mgr_flag: 0
	auto_adj_para: 1
	auto_adj_cpus: 0
	auto_adj_mem: 0

 log file path: /opt/dmdbms/data/DAMENG/DAMENG01.log


 log file path: /opt/dmdbms/data/DAMENG/DAMENG02.log

create ini file /opt/dmdbms/data/DAMENG/dm.ini success.

create rlog file /opt/dmdbms/data/DAMENG/DAMENG01.log success.

create rlog file /opt/dmdbms/data/DAMENG/DAMENG02.log success.

SYSTEM file : /opt/dmdbms/data/DAMENG/SYSTEM.DBF

MAIN file : /opt/dmdbms/data/DAMENG/MAIN.DBF

ROLL file : /opt/dmdbms/data/DAMENG/ROLL.DBF

create dm database success. 2025-07-18 06:23:19
