# DM8 数据库完整备份脚本
# 版本: 1.0
# 作者: AI Assistant
# 日期: 2025-07-17

param(
    [string]$BackupDir = ".\backups",
    [switch]$ForceBackup,
    [switch]$VerboseLog
)

# 全局变量
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$BackupPath = Join-Path $BackupDir $Timestamp
$TempDir = Join-Path (Get-Location) "temp_backup_$Timestamp"
$LogFile = Join-Path $BackupPath "backup_log.txt"

# 日志记录函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $Time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogContent = "[$Time] [$Level] $Message"

    switch ($Level) {
        "ERROR" { Write-Host $LogContent -ForegroundColor Red }
        "WARN"  { Write-Host $LogContent -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $LogContent -ForegroundColor Green }
        default { Write-Host $LogContent -ForegroundColor White }
    }

    if (Test-Path $BackupPath) {
        $LogContent | Add-Content -Path $LogFile -Encoding UTF8
    }
}

# 进度显示函数
function Show-Progress {
    param([string]$Activity, [string]$Status, [int]$PercentComplete)
    Write-Progress -Activity $Activity -Status $Status -PercentComplete $PercentComplete
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "DM8 数据库完整备份脚本 v1.0" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Log "开始DM8完整备份"
Write-Log "备份目录: $BackupPath"
Write-Log "临时目录: $TempDir"

# 创建目录
try {
    New-Item -ItemType Directory -Path $BackupPath -Force | Out-Null
    New-Item -ItemType Directory -Path $TempDir -Force | Out-Null
    Write-Log "备份目录创建成功" "SUCCESS"
} catch {
    Write-Log "创建备份目录失败: $($_.Exception.Message)" "ERROR"
    exit 1
}

try {
    # 步骤1: 检查Docker环境
    Show-Progress "环境检查" "检查Docker环境..." 10
    Write-Log "检查Docker环境..."
    $dockerVersion = docker --version 2>$null
    if ($LASTEXITCODE -ne 0) {
        throw "Docker未运行或未安装"
    }
    Write-Log "Docker版本: $dockerVersion" "SUCCESS"

    # 步骤2: 检查容器状态
    Show-Progress "环境检查" "检查DM8容器状态..." 20
    Write-Log "检查DM8容器状态..."
    $ContainerStatus = docker inspect dm8-database --format='{{.State.Status}}' 2>$null
    if ($ContainerStatus -ne "running") {
        Write-Log "警告: DM8容器未运行，状态: $ContainerStatus" "WARN"
        if (-not $ForceBackup) {
            throw "容器未运行，使用 -ForceBackup 参数强制备份"
        }
    } else {
        Write-Log "DM8容器运行正常" "SUCCESS"
    }

    # 步骤3: 备份配置文件
    Show-Progress "备份配置" "备份DM8配置文件..." 40
    Write-Log "开始备份配置文件"
    try {
        $configBackup = Join-Path $TempDir "dm8_config_backup.zip"

        # 从容器中复制配置文件
        docker exec dm8-database tar czf /tmp/config_backup.tar.gz -C /home/<USER>/dmdbms . --exclude='data/*' --exclude='log/*' 2>$null
        docker cp dm8-database:/tmp/config_backup.tar.gz $configBackup
        docker exec dm8-database rm -f /tmp/config_backup.tar.gz

        if (Test-Path $configBackup) {
            Write-Log "配置文件备份成功" "SUCCESS"
        } else {
            throw "配置文件备份失败"
        }
    } catch {
        Write-Log "配置文件备份失败: $($_.Exception.Message)" "WARN"
    }

    # 步骤4: 数据卷备份说明
    Show-Progress "数据卷备份" "数据卷包含完整数据库数据..." 60
    Write-Log "数据卷备份包含完整数据库数据，无需额外SQL导出" "INFO"

    # 步骤5: 备份Docker配置文件
    Show-Progress "备份配置" "备份Docker配置文件..." 75
    Write-Log "备份Docker配置文件"
    $ConfigFiles = @("docker-compose.yml", "Dockerfile", "dm.ini")
    $BackedUpFiles = 0
    foreach ($File in $ConfigFiles) {
        if (Test-Path $File) {
            Copy-Item $File -Destination $BackupPath -Force
            Write-Log "已备份: $File" "SUCCESS"
            $BackedUpFiles++
        } else {
            Write-Log "文件不存在: $File" "WARN"
        }
    }
    Write-Log "Docker配置备份完成，已备份 $BackedUpFiles 个文件" "SUCCESS"

    # 步骤6: 停止容器确保数据一致性
    if ($ContainerStatus -eq "running") {
        Show-Progress "准备数据卷" "停止容器确保数据一致性..." 80
        Write-Log "停止DM8容器确保数据一致性..."
        docker-compose stop dm8-database 2>&1 | Out-Null
        Start-Sleep -Seconds 5
        Write-Log "容器已停止" "SUCCESS"
    }

    # 步骤7: 备份数据卷
    Show-Progress "备份数据卷" "备份DM8数据卷..." 85
    Write-Log "备份DM8数据卷..."

    # 备份主数据卷 - 使用正确的数据卷名称
    Write-Log "备份主数据卷: dm8_database_data"
    try {
        $DataBackupContainer = docker create -v dm8_database_data:/data alpine 2>&1
        if ($LASTEXITCODE -eq 0) {
            docker cp "${DataBackupContainer}:/data" "${TempDir}/dm8_data" 2>&1 | Out-Null
            docker rm $DataBackupContainer 2>&1 | Out-Null

            if (Test-Path "${TempDir}/dm8_data") {
                Compress-Archive -Path "${TempDir}/dm8_data" -DestinationPath "${TempDir}/dm8_data_volume.zip" -Force
                Write-Log "主数据卷备份完成" "SUCCESS"
            } else {
                Write-Log "主数据卷数据复制失败" "ERROR"
            }
        } else {
            Write-Log "创建备份容器失败: $DataBackupContainer" "ERROR"
        }
    } catch {
        Write-Log "主数据卷备份异常: $($_.Exception.Message)" "ERROR"
    }

    # 备份日志数据卷
    Write-Log "备份日志数据卷: dm8_database_logs"
    try {
        $LogsBackupContainer = docker create -v dm8_database_logs:/logs alpine 2>&1
        if ($LASTEXITCODE -eq 0) {
            docker cp "${LogsBackupContainer}:/logs" "${TempDir}/dm8_logs" 2>&1 | Out-Null
            docker rm $LogsBackupContainer 2>&1 | Out-Null

            if (Test-Path "${TempDir}/dm8_logs") {
                Compress-Archive -Path "${TempDir}/dm8_logs" -DestinationPath "${TempDir}/dm8_logs_volume.zip" -Force
                Write-Log "日志数据卷备份完成" "SUCCESS"
            } else {
                Write-Log "日志数据卷数据复制失败" "WARN"
            }
        } else {
            Write-Log "创建日志备份容器失败: $LogsBackupContainer" "WARN"
        }
    } catch {
        Write-Log "日志数据卷备份异常: $($_.Exception.Message)" "WARN"
    }

    # 移动备份文件到备份目录
    $DataFile = Join-Path $TempDir "dm8_data_volume.zip"
    $LogsFile = Join-Path $TempDir "dm8_logs_volume.zip"
    $ConfigFile = Join-Path $TempDir "dm8_config_backup.zip"

    # 数据库导出文件（可能有多个）
    $SqlFiles = Get-ChildItem -Path $TempDir -Filter "*.sql.zip" -ErrorAction SilentlyContinue
    $TxtFiles = Get-ChildItem -Path $TempDir -Filter "*.txt" -ErrorAction SilentlyContinue

    # 移动主要备份文件
    foreach ($File in @($DataFile, $LogsFile, $ConfigFile)) {
        if (Test-Path $File) {
            $TargetFile = Join-Path $BackupPath (Split-Path $File -Leaf)
            Move-Item $File $TargetFile -Force
            $Size = [math]::Round((Get-Item $TargetFile).Length / 1MB, 2)
            Write-Log "已移动: $(Split-Path $File -Leaf) (${Size}MB)" "SUCCESS"
        }
    }

    # 移动数据库导出文件
    foreach ($File in $SqlFiles) {
        $TargetFile = Join-Path $BackupPath $File.Name
        Move-Item $File.FullName $TargetFile -Force
        $Size = [math]::Round((Get-Item $TargetFile).Length / 1MB, 2)
        Write-Log "已移动: $($File.Name) (${Size}MB)" "SUCCESS"
    }

    # 移动系统信息文件
    foreach ($File in $TxtFiles) {
        $TargetFile = Join-Path $BackupPath $File.Name
        Move-Item $File.FullName $TargetFile -Force
        Write-Log "已移动: $($File.Name)" "SUCCESS"
    }

    Write-Log "数据卷备份完成" "SUCCESS"

    # 步骤8: 重启所有服务
    if ($ContainerStatus -eq "running") {
        Show-Progress "恢复服务" "重启所有服务..." 90
        Write-Log "重启所有服务..."
        docker-compose up -d 2>&1 | Out-Null

        # 等待DM8启动
        Write-Log "等待30秒让DM8重启..."
        Start-Sleep -Seconds 30

        # 检查容器状态
        $NewContainerStatus = docker inspect dm8-database --format='{{.State.Status}}' 2>$null
        if ($NewContainerStatus -eq "running") {
            Write-Log "DM8服务重启成功" "SUCCESS"
        } else {
            Write-Log "DM8服务重启可能有问题，状态: $NewContainerStatus" "WARN"
        }
    }

} catch {
    Write-Log "备份失败: $($_.Exception.Message)" "ERROR"
    exit 1
} finally {
    # 清理临时目录
    if (Test-Path $TempDir) {
        Remove-Item $TempDir -Recurse -Force
        Write-Log "已清理临时目录" "INFO"
    }
    Write-Progress -Activity "备份完成" -Completed
}

# 步骤9: 创建恢复说明
Show-Progress "生成文档" "创建恢复说明..." 95
Write-Log "创建恢复说明文档..."

$RestoreInstructions = @"
# DM8 数据库完整备份恢复说明
备份时间: $Timestamp
备份类型: 完整备份（配置文件 + SQL数据 + 数据卷）

## 备份内容
1. 配置文件: docker-compose.yml, Dockerfile, dm.ini
2. 数据卷 (包含完整数据库数据):
   - dm8_data_volume.zip (主数据 - 包含所有数据库文件)
   - dm8_logs_volume.zip (日志数据)
   - dm8_config_backup.zip (配置)

## 恢复命令
.\backup-scripts\dm8-restore-cn.ps1 -BackupPath ".\backups\$Timestamp"

## 注意事项
- 恢复将完全替换现有数据
- 确保Docker Desktop正在运行
- 恢复期间现有服务将被停止
- 恢复完成后服务将自动启动

## 访问信息
- DM8: localhost:5236
- 用户名: SYSDBA
- 密码: SYSdba123456
"@

$RestoreInstructions | Out-File -FilePath (Join-Path $BackupPath "restore_instructions.txt") -Encoding UTF8

# 计算备份大小
$BackupSize = (Get-ChildItem $BackupPath -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
$backupSizeStr = "$([math]::Round($BackupSize, 2)) MB"

Show-Progress "备份完成" "备份已完成!" 100
Write-Host "`n========================================" -ForegroundColor Green
Write-Host "备份完成!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Log "备份位置: $BackupPath" "SUCCESS"
Write-Log "备份大小: $backupSizeStr" "SUCCESS"

Write-Host "`n========================================" -ForegroundColor Green
Write-Host "备份完成!" -ForegroundColor Green
Write-Host "备份位置: $BackupPath" -ForegroundColor Cyan
Write-Host "备份大小: $backupSizeStr" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Green

Write-Progress -Activity "备份完成" -Completed
