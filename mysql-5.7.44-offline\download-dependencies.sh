#!/bin/bash

# MySQL 5.7.44 离线部署依赖下载脚本
# 此脚本需要在有网络的CentOS 7环境中运行，用于下载所有必需的RPM包

set -e

# 创建下载目录
DOWNLOAD_DIR="./rpm-packages"
mkdir -p $DOWNLOAD_DIR

echo "开始下载MySQL 5.7.44离线部署所需的RPM包..."

# 清理yum缓存
yum clean all

# 设置yum只下载不安装
yum install -y yum-utils

# 下载基础依赖包
echo "下载基础工具包..."
yumdownloader --destdir=$DOWNLOAD_DIR --resolve \
    tar \
    telnet \
    libaio-devel \
    libaio \
    numactl \
    numactl-libs \
    numactl-devel \
    ncurses-compat-libs \
    ncurses-libs \
    psmisc

# 下载系统库依赖
echo "下载系统库依赖..."
yumdownloader --destdir=$DOWNLOAD_DIR --resolve \
    glibc \
    glibc-common \
    glibc-devel \
    glibc-headers \
    libgcc \
    libstdc++ \
    zlib \
    openssl-libs \
    krb5-libs \
    libcom_err \
    libselinux \
    libsepol \
    keyutils-libs \
    pcre \
    xz-libs \
    elfutils-libelf \
    nss-softokn-freebl

# 下载网络相关依赖
echo "下载网络工具依赖..."
yumdownloader --destdir=$DOWNLOAD_DIR --resolve \
    net-tools \
    iproute \
    iputils

# 下载进程管理工具依赖
echo "下载进程管理工具依赖..."
yumdownloader --destdir=$DOWNLOAD_DIR --resolve \
    procps-ng \
    util-linux

# 下载文件系统工具
echo "下载文件系统工具..."
yumdownloader --destdir=$DOWNLOAD_DIR --resolve \
    coreutils \
    findutils \
    which

# 下载编辑器和基础工具
echo "下载基础编辑工具..."
yumdownloader --destdir=$DOWNLOAD_DIR --resolve \
    vim-minimal \
    less \
    grep \
    sed \
    gawk

# 下载时区数据
echo "下载时区数据..."
yumdownloader --destdir=$DOWNLOAD_DIR --resolve \
    tzdata

# 创建RPM包列表文件
echo "创建RPM包列表..."
ls -la $DOWNLOAD_DIR/*.rpm > $DOWNLOAD_DIR/package-list.txt

# 统计下载的包数量
PACKAGE_COUNT=$(ls -1 $DOWNLOAD_DIR/*.rpm | wc -l)
echo "总共下载了 $PACKAGE_COUNT 个RPM包"

# 计算总大小
TOTAL_SIZE=$(du -sh $DOWNLOAD_DIR | cut -f1)
echo "总大小: $TOTAL_SIZE"

echo "依赖包下载完成！"
echo "下载目录: $DOWNLOAD_DIR"
echo ""
echo "请将以下文件复制到离线环境："
echo "1. $DOWNLOAD_DIR/ 目录（包含所有RPM包）"
echo "2. mysql-5.7.44-linux-glibc2.12-x86_64.tar.gz"
echo "3. Dockerfile"
echo "4. my.cnf"
echo "5. docker-compose.yaml"
echo ""
echo "然后在离线环境中运行: docker build -t mysql:5.7.44-offline ."
